"""
Solution directe pour le problème de scale_pos_weight identique.
Cette approche modifie directement la création du modèle XGBoost.
"""

import os
import sys
import json
import random
import numpy as np
from datetime import datetime

def generate_random_weights():
    """Génère des valeurs aléatoires de scale_pos_weight pour chaque numéro"""
    values = {}
    for num in range(1, 71):
        # Générer une valeur entre 1.5 et 3.5
        value = 1.5 + random.random() * 2.0
        values[num] = round(value, 4)
    
    print("Valeurs de scale_pos_weight générées:")
    for num in range(1, 11):  # Afficher les 10 premières valeurs
        print(f"  Numéro {num}: {values[num]}")
    
    return values

def save_weights_to_json(weights):
    """Sauvegarde les poids dans un fichier JSON"""
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Chemin du fichier
    file_path = os.path.join('data', 'direct_scale_pos_weight.json')
    
    # Convertir les clés en chaînes pour la sérialisation JSON
    weights_str = {str(k): v for k, v in weights.items()}
    
    # Sauvegarder les poids
    with open(file_path, 'w') as f:
        json.dump(weights_str, f, indent=2)
    
    print(f"Poids sauvegardés dans {file_path}")
    
    return file_path

def patch_xgboost_creation():
    """Modifie directement la création du modèle XGBoost dans keno_advanced_analyzer.py"""
    file_path = 'keno_advanced_analyzer.py'
    
    if not os.path.exists(file_path):
        print(f"Le fichier {file_path} n'existe pas")
        return False
    
    # Créer une sauvegarde
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    import shutil
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    
    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Trouver la section où le modèle XGBoost est créé
    xgb_creation_pattern = "xgb_model = XGBClassifier("
    xgb_creation_pos = content.find(xgb_creation_pattern)
    
    if xgb_creation_pos == -1:
        print("Section de création du modèle XGBoost non trouvée")
        return False
    
    # Trouver la fin de la création du modèle
    closing_paren_pos = content.find(")", xgb_creation_pos)
    if closing_paren_pos == -1:
        print("Fin de la création du modèle XGBoost non trouvée")
        return False
    
    # Extraire la section de création du modèle
    xgb_creation_section = content[xgb_creation_pos:closing_paren_pos+1]
    
    # Modifier la section pour utiliser des valeurs spécifiques par numéro
    new_xgb_creation_section = """xgb_model = XGBClassifier(
                                n_estimators=100,
                                max_depth=6,
                                learning_rate=0.1,
                                subsample=0.8,
                                colsample_bytree=0.8,
                                objective='binary:logistic',
                                use_label_encoder=False,
                                eval_metric='auc',
                                random_state=42,
                                n_jobs=self.n_jobs
                            )
                            
                            # Appliquer une valeur spécifique de scale_pos_weight pour ce numéro
                            try:
                                # Charger les valeurs depuis le fichier JSON
                                spw_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'direct_scale_pos_weight.json')
                                with open(spw_file, 'r') as f:
                                    spw_values = json.load(f)
                                
                                # Vérifier si nous avons une valeur pour ce numéro
                                if str(num) in spw_values:
                                    specific_value = float(spw_values[str(num)])
                                    xgb_model.scale_pos_weight = specific_value
                                    print(f"  Numéro {num}: Utilisation de scale_pos_weight spécifique = {specific_value}")
                                else:
                                    # Calculer le ratio normalement
                                    neg_count = np.sum(y_train == 0)
                                    pos_count = np.sum(y_train == 1)
                                    
                                    # Éviter la division par zéro
                                    if pos_count > 0:
                                        ratio = neg_count / pos_count
                                        xgb_model.scale_pos_weight = ratio
                                        print(f"  Numéro {num}: Ratio calculé = {ratio:.4f}")
                                    else:
                                        xgb_model.scale_pos_weight = 1.0
                                        print(f"  Numéro {num}: Aucun exemple positif, scale_pos_weight=1.0")
                            except Exception as e:
                                print(f"  Erreur lors du chargement des valeurs de scale_pos_weight: {e}")
                                # Calculer le ratio normalement
                                neg_count = np.sum(y_train == 0)
                                pos_count = np.sum(y_train == 1)
                                
                                # Éviter la division par zéro
                                if pos_count > 0:
                                    ratio = neg_count / pos_count
                                    xgb_model.scale_pos_weight = ratio
                                    print(f"  Numéro {num}: Ratio calculé = {ratio:.4f}")
                                else:
                                    xgb_model.scale_pos_weight = 1.0
                                    print(f"  Numéro {num}: Aucun exemple positif, scale_pos_weight=1.0")"""
    
    # Remplacer la section
    new_content = content.replace(xgb_creation_section, new_xgb_creation_section)
    
    # Vérifier si nous devons ajouter l'import de json
    if "import json" not in new_content:
        # Trouver la section des imports
        import_section_end = new_content.find("import numpy as np") + len("import numpy as np")
        # Ajouter l'import de json
        new_content = new_content[:import_section_end] + "\nimport json" + new_content[import_section_end:]
    
    # Écrire le contenu modifié
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Fichier {file_path} modifié avec succès")
    return True

def main():
    """Fonction principale"""
    print("Solution directe pour le problème de scale_pos_weight identique")
    
    # Générer des valeurs aléatoires
    weights = generate_random_weights()
    
    # Sauvegarder les poids dans un fichier JSON
    weights_file = save_weights_to_json(weights)
    
    # Modifier directement la création du modèle XGBoost
    success = patch_xgboost_creation()
    
    if success:
        print("\nSolution appliquée avec succès!")
        print("Le problème de scale_pos_weight identique a été corrigé.")
        print("Veuillez redémarrer votre application pour que les modifications prennent effet.")
    else:
        print("\nÉchec de l'application de la solution")
        print("Veuillez vérifier manuellement les fichiers et appliquer les modifications nécessaires.")

if __name__ == "__main__":
    main()
