"""Configuration centralisée pour l'application Keno"""

import os
import sys
import site

# Ajouter le répertoire courant et les répertoires de packages au chemin de recherche Python
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, BASE_DIR)

# Ajouter les répertoires de packages utilisateur au chemin de recherche
for site_path in site.getsitepackages():
    if site_path not in sys.path:
        sys.path.append(site_path)

# Supprimer tous les avertissements
import warnings
warnings.filterwarnings('ignore')

# Supprimer les avertissements de TensorFlow
import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR

# Afficher les informations de débogage
print(f"Python executable: {sys.executable}")
print(f"Python version: {sys.version}")
print(f"Python path: {sys.path}")

# Chemins
DATA_DIR = os.path.join(BASE_DIR, "data")
MODELS_DIR = os.path.join(BASE_DIR, "models")
LOGS_DIR = os.path.join(BASE_DIR, "logs")

# Paramètres du jeu
DEFAULT_MAX_NUMBER = 70
DEFAULT_NUMBERS_PER_DRAW = 20
DEFAULT_PREDICTION_COUNT = 10

# Paramètres d'interface
GUI_WIDTH = 1200
GUI_HEIGHT = 800
GUI_MIN_WIDTH = 800
GUI_MIN_HEIGHT = 600

# Créer les répertoires nécessairest
for directory in [DATA_DIR, MODELS_DIR, LOGS_DIR]:
    os.makedirs(directory, exist_ok=True)

from keno_gui import KenoGUI
import sys
import traceback

def main():
    try:
        # Vérifier que les modules de feedback sont disponibles
        try:
            from keno_feedback import KenoFeedback
            from keno_feedback_dialog import KenoFeedbackDialog
            print("Modules de feedback disponibles")
        except ImportError as e:
            print(f"Modules de feedback non disponibles: {e}")

        app = KenoGUI()

        # Charger les paramètres avancés s'ils existent
        if hasattr(app.analyzer, 'advanced_analyzer') and app.analyzer.advanced_analyzer:
            try:
                from keno_settings import load_advanced_settings
                load_advanced_settings(app.analyzer.advanced_analyzer)
            except Exception as e:
                print(f"Erreur lors du chargement des paramètres avancés: {e}")
                traceback.print_exc()

        # Exécuter l'application et récupérer le résultat
        run_result = app.run()

        # Vérifier si l'exécution a réussi
        if run_result is False:  # Vérification explicite pour False
            print("L'application s'est terminée avec une erreur.")
            return 1

        print("L'application s'est terminée normalement.")
        return 0
    except Exception as e:
        print(f"Erreur fatale: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        result = main()
        print(f"Application terminée avec le code: {result}")
        sys.exit(result)
    except SystemExit as e:
        # Capturer l'exception SystemExit pour éviter qu'elle ne se propage
        print(f"SystemExit capturé avec code: {e.code}")
        sys.exit(e.code)
    except Exception as e:
        print(f"Exception non gérée: {e}")
        traceback.print_exc()
        sys.exit(1)


