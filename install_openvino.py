"""
Script d'installation d'OpenVINO pour l'accélération NPU sur processeurs Intel
"""

import subprocess
import sys
import platform
import os

def install_openvino():
    """Installe OpenVINO et ses dépendances"""
    print("Installation d'OpenVINO pour l'accélération NPU sur processeurs Intel...")
    
    # Vérifier si nous sommes sur Windows
    is_windows = platform.system() == "Windows"
    
    # Installer OpenVINO
    try:
        print("\n1. Installation du package OpenVINO...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "openvino"])
        print("✓ OpenVINO installé avec succès!")
    except subprocess.CalledProcessError:
        print("❌ Erreur lors de l'installation d'OpenVINO")
        return False
    
    # Installer les dépendances supplémentaires
    try:
        print("\n2. Installation des dépendances supplémentaires...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "openvino-dev"])
        print("✓ Dépendances supplémentaires installées avec succès!")
    except subprocess.CalledProcessError:
        print("⚠️ Avertissement: Impossible d'installer openvino-dev, mais OpenVINO de base est installé")
    
    # Vérifier l'installation
    try:
        print("\n3. Vérification de l'installation...")
        import openvino as ov
        print(f"✓ OpenVINO version {ov.__version__} installé avec succès!")
        
        # Vérifier les dispositifs disponibles
        core = ov.Core()
        available_devices = core.available_devices
        print(f"\nDispositifs disponibles: {available_devices}")
        
        # Afficher des informations détaillées sur chaque dispositif
        for device in available_devices:
            device_name = device
            try:
                full_name = core.get_property(device_name, "FULL_DEVICE_NAME")
                print(f"\nDispositif: {device_name}")
                print(f"Nom complet: {full_name}")
                
                # Afficher les capacités si disponibles
                try:
                    capabilities = core.get_property(device_name, "OPTIMIZATION_CAPABILITIES")
                    print(f"Capacités: {capabilities}")
                except:
                    pass
            except Exception as e:
                print(f"Erreur lors de l'analyse du dispositif {device}: {e}")
        
        return True
    except ImportError:
        print("❌ Erreur: OpenVINO n'a pas pu être importé après l'installation")
        return False
    except Exception as e:
        print(f"❌ Erreur lors de la vérification de l'installation: {e}")
        return False

if __name__ == "__main__":
    print("=== Installation d'OpenVINO pour l'accélération NPU ===")
    print("Ce script va installer OpenVINO pour activer l'accélération NPU sur les processeurs Intel.")
    print("L'installation peut prendre quelques minutes.")
    
    success = install_openvino()
    
    if success:
        print("\n=== Installation terminée avec succès! ===")
        print("Vous pouvez maintenant utiliser l'accélération NPU dans l'application Keno.")
        print("Pour l'activer, ouvrez l'application, allez dans Configuration avancée > Matériel,")
        print("puis sélectionnez 'npu' comme mode d'accélération et cliquez sur Appliquer.")
    else:
        print("\n=== L'installation a échoué ===")
        print("Veuillez vérifier les messages d'erreur ci-dessus et réessayer.")
        print("Si le problème persiste, vous pouvez toujours utiliser l'application avec le mode CPU ou GPU.")
