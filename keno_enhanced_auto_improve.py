#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Système d'auto-amélioration optimisé pour les prédictions Keno
Ce module implémente des stratégies avancées pour améliorer la précision des prédictions
"""

import os
import sys
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Imports conditionnels pour les modèles ML
try:
    from sklearn.ensemble import RandomForestClassifier, VotingClassifier
    from sklearn.model_selection import train_test_split, GridSearchCV, RandomizedSearchCV
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
    from sklearn.preprocessing import StandardScaler
    from sklearn.feature_selection import SelectKBest, f_classif, RFE
    sklearn_available = True
except ImportError:
    sklearn_available = False

try:
    import xgboost as xgb
    xgboost_available = True
except ImportError:
    xgboost_available = False

try:
    import lightgbm as lgb
    lightgbm_available = True
except ImportError:
    lightgbm_available = False

class KenoEnhancedAutoImprove:
    """Système d'auto-amélioration optimisé pour les prédictions Keno"""

    def __init__(self, data_manager, analyzer):
        self.data_manager = data_manager
        self.analyzer = analyzer
        self.enhanced_features = {}
        self.ensemble_models = {}
        self.feature_importance_history = {}
        self.prediction_accuracy_history = {}

        # Configuration optimisée
        self.config = {
            'feature_engineering': {
                'use_temporal_patterns': True,
                'use_frequency_analysis': True,
                'use_gap_analysis': True,
                'use_correlation_features': True,
                'use_statistical_features': True,
                'lookback_windows': [5, 10, 20, 50, 100],
                'rolling_windows': [3, 7, 14, 30]
            },
            'model_optimization': {
                'use_ensemble': True,
                'use_feature_selection': True,
                'use_hyperparameter_tuning': True,
                'cross_validation_folds': 5,
                'optimization_metric': 'f1',  # Meilleur pour les classes déséquilibrées
                'early_stopping': True
            },
            'prediction_strategy': {
                'use_confidence_scoring': True,
                'use_pattern_matching': True,
                'use_trend_analysis': True,
                'combine_multiple_timeframes': True,
                'weight_recent_data': True
            }
        }

    def create_enhanced_features(self, number, lookback_days=365):
        """Crée des caractéristiques avancées pour un numéro spécifique"""
        print(f"Création de caractéristiques avancées pour le numéro {number}...")

        draws = self.data_manager.draws
        if len(draws) < 100:
            print("Pas assez de données pour créer des caractéristiques avancées")
            return None

        features_data = []
        targets = []

        # Filtrer les données récentes si spécifié
        if lookback_days:
            cutoff_date = datetime.now() - timedelta(days=lookback_days)
            draws = [d for d in draws if d.draw_date and d.draw_date >= cutoff_date]

        for i in range(50, len(draws)):  # Commencer à 50 pour avoir assez d'historique
            current_draw = draws[i]
            if not hasattr(current_draw, 'numbers') or not current_draw.numbers:
                continue

            features = {}

            # 1. Caractéristiques de fréquence avancées
            for window in self.config['feature_engineering']['lookback_windows']:
                start_idx = max(0, i - window)
                recent_draws = draws[start_idx:i]

                # Fréquence dans la fenêtre
                freq = sum(1 for d in recent_draws if hasattr(d, 'numbers') and number in d.numbers)
                features[f'freq_{window}'] = freq / window

                # Fréquence relative (comparée à la moyenne)
                avg_freq = window * 20 / 70  # Fréquence attendue
                features[f'freq_rel_{window}'] = freq / avg_freq if avg_freq > 0 else 0

            # 2. Analyse des gaps (intervalles entre apparitions)
            gaps = self._calculate_gaps(draws[:i], number)
            if gaps:
                features['gap_mean'] = np.mean(gaps)
                features['gap_std'] = np.std(gaps)
                features['gap_min'] = np.min(gaps)
                features['gap_max'] = np.max(gaps)
                features['gap_current'] = i - self._last_appearance(draws[:i], number)
                features['gap_trend'] = self._calculate_gap_trend(gaps)
            else:
                features.update({
                    'gap_mean': 0, 'gap_std': 0, 'gap_min': 0,
                    'gap_max': 0, 'gap_current': i, 'gap_trend': 0
                })

            # 3. Caractéristiques temporelles
            if current_draw.draw_date:
                features['day_of_week'] = current_draw.draw_date.weekday()
                features['hour'] = current_draw.draw_date.hour
                features['month'] = current_draw.draw_date.month
                features['is_weekend'] = 1 if current_draw.draw_date.weekday() >= 5 else 0

            # 4. Patterns de co-occurrence
            cooccurrence_scores = self._calculate_cooccurrence(draws[:i], number, current_draw.numbers)
            features.update(cooccurrence_scores)

            # 5. Caractéristiques statistiques avancées
            recent_positions = self._get_recent_positions(draws[max(0, i-20):i], number)
            if recent_positions:
                features['position_variance'] = np.var(recent_positions)
                features['position_trend'] = self._calculate_position_trend(recent_positions)
            else:
                features['position_variance'] = 0
                features['position_trend'] = 0

            # 6. Momentum et tendances
            momentum_scores = self._calculate_momentum(draws[:i], number)
            features.update(momentum_scores)

            features_data.append(list(features.values()))
            targets.append(1 if number in current_draw.numbers else 0)

        # Créer le DataFrame
        feature_names = list(features.keys())
        df = pd.DataFrame(features_data, columns=feature_names)

        self.enhanced_features[number] = {
            'data': df,
            'targets': targets,
            'feature_names': feature_names
        }

        print(f"Créé {len(feature_names)} caractéristiques pour {len(df)} échantillons")
        return df, targets, feature_names

    def _calculate_gaps(self, draws, number):
        """Calcule les intervalles entre les apparitions d'un numéro"""
        appearances = []
        for i, draw in enumerate(draws):
            if hasattr(draw, 'numbers') and number in draw.numbers:
                appearances.append(i)

        if len(appearances) < 2:
            return []

        gaps = []
        for i in range(1, len(appearances)):
            gaps.append(appearances[i] - appearances[i-1])

        return gaps

    def _last_appearance(self, draws, number):
        """Trouve la dernière apparition d'un numéro"""
        for i in range(len(draws) - 1, -1, -1):
            if hasattr(draws[i], 'numbers') and number in draws[i].numbers:
                return i
        return -1

    def _calculate_gap_trend(self, gaps):
        """Calcule la tendance des gaps (croissante/décroissante)"""
        if len(gaps) < 3:
            return 0

        recent_gaps = gaps[-3:]
        if len(recent_gaps) >= 2:
            return (recent_gaps[-1] - recent_gaps[0]) / len(recent_gaps)
        return 0

    def _calculate_cooccurrence(self, draws, target_number, current_numbers):
        """Calcule les scores de co-occurrence avec les autres numéros"""
        cooccurrence_counts = {}
        total_appearances = 0

        for draw in draws:
            if hasattr(draw, 'numbers') and target_number in draw.numbers:
                total_appearances += 1
                for num in draw.numbers:
                    if num != target_number:
                        cooccurrence_counts[num] = cooccurrence_counts.get(num, 0) + 1

        # Calculer le score de co-occurrence pour le tirage actuel
        cooccurrence_score = 0
        if total_appearances > 0:
            for num in current_numbers:
                if num in cooccurrence_counts:
                    cooccurrence_score += cooccurrence_counts[num] / total_appearances

        return {
            'cooccurrence_score': cooccurrence_score,
            'cooccurrence_strength': len([n for n in current_numbers if n in cooccurrence_counts])
        }

    def _get_recent_positions(self, draws, number):
        """Obtient les positions récentes d'un numéro dans les tirages"""
        positions = []
        for draw in draws:
            if hasattr(draw, 'numbers') and number in draw.numbers:
                # Position relative dans la liste triée
                sorted_numbers = sorted(draw.numbers)
                position = sorted_numbers.index(number) / len(sorted_numbers)
                positions.append(position)
        return positions

    def _calculate_position_trend(self, positions):
        """Calcule la tendance des positions"""
        if len(positions) < 2:
            return 0

        # Régression linéaire simple
        x = np.arange(len(positions))
        y = np.array(positions)

        if len(x) > 1:
            slope = np.polyfit(x, y, 1)[0]
            return slope
        return 0

    def _calculate_momentum(self, draws, number):
        """Calcule le momentum d'un numéro"""
        momentum_features = {}

        # Momentum à court terme (5 derniers tirages)
        recent_5 = draws[-5:] if len(draws) >= 5 else draws
        short_momentum = sum(1 for d in recent_5 if hasattr(d, 'numbers') and number in d.numbers)
        momentum_features['momentum_short'] = short_momentum / len(recent_5)

        # Momentum à moyen terme (20 derniers tirages)
        recent_20 = draws[-20:] if len(draws) >= 20 else draws
        medium_momentum = sum(1 for d in recent_20 if hasattr(d, 'numbers') and number in d.numbers)
        momentum_features['momentum_medium'] = medium_momentum / len(recent_20)

        # Accélération du momentum
        if len(draws) >= 10:
            first_half = draws[-10:-5] if len(draws) >= 10 else []
            second_half = draws[-5:]

            first_freq = sum(1 for d in first_half if hasattr(d, 'numbers') and number in d.numbers) / max(1, len(first_half))
            second_freq = sum(1 for d in second_half if hasattr(d, 'numbers') and number in d.numbers) / max(1, len(second_half))

            momentum_features['momentum_acceleration'] = second_freq - first_freq
        else:
            momentum_features['momentum_acceleration'] = 0

        return momentum_features

    def optimize_model_ensemble(self, number, X, y, test_size=0.2):
        """Optimise un ensemble de modèles pour un numéro spécifique"""
        print(f"Optimisation de l'ensemble de modèles pour le numéro {number}...")

        if not sklearn_available:
            print("Scikit-learn non disponible pour l'optimisation")
            return None

        # Diviser les données
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=42, stratify=y
        )

        # Sélection des caractéristiques
        if self.config['model_optimization']['use_feature_selection']:
            X_train, X_test, selected_features = self._select_best_features(
                X_train, X_test, y_train, number
            )
        else:
            selected_features = list(range(X.shape[1]))

        models = {}

        # 1. Random Forest optimisé
        if sklearn_available:
            print("  Optimisation de Random Forest...")
            rf_model = self._optimize_random_forest(X_train, y_train, number)
            if rf_model:
                models['random_forest'] = rf_model

        # 2. XGBoost optimisé
        if xgboost_available:
            print("  Optimisation de XGBoost...")
            xgb_model = self._optimize_xgboost(X_train, y_train, number)
            if xgb_model:
                models['xgboost'] = xgb_model

        # 3. LightGBM optimisé
        if lightgbm_available:
            print("  Optimisation de LightGBM...")
            lgb_model = self._optimize_lightgbm(X_train, y_train, number)
            if lgb_model:
                models['lightgbm'] = lgb_model

        # 4. Créer un ensemble voting
        if len(models) > 1 and self.config['model_optimization']['use_ensemble']:
            print("  Création de l'ensemble voting...")
            ensemble_model = self._create_voting_ensemble(models, X_train, y_train)
            if ensemble_model:
                models['ensemble'] = ensemble_model

        # Évaluer tous les modèles
        best_model = None
        best_score = 0

        for model_name, model in models.items():
            try:
                y_pred = model.predict(X_test)
                score = f1_score(y_test, y_pred, zero_division=0)
                accuracy = accuracy_score(y_test, y_pred)
                precision = precision_score(y_test, y_pred, zero_division=0)
                recall = recall_score(y_test, y_pred, zero_division=0)

                print(f"  {model_name}: F1={score:.4f}, Acc={accuracy:.4f}, Prec={precision:.4f}, Rec={recall:.4f}")

                if score > best_score:
                    best_score = score
                    best_model = {
                        'model': model,
                        'name': model_name,
                        'f1_score': score,
                        'accuracy': accuracy,
                        'precision': precision,
                        'recall': recall,
                        'selected_features': selected_features
                    }
            except Exception as e:
                print(f"  Erreur lors de l'évaluation de {model_name}: {e}")

        if best_model:
            self.ensemble_models[number] = best_model
            print(f"  Meilleur modèle pour le numéro {number}: {best_model['name']} (F1: {best_score:.4f})")

        return best_model

    def _select_best_features(self, X_train, X_test, y_train, number, k=20):
        """Sélectionne les meilleures caractéristiques"""
        try:
            # Utiliser SelectKBest avec f_classif
            selector = SelectKBest(score_func=f_classif, k=min(k, X_train.shape[1]))
            X_train_selected = selector.fit_transform(X_train, y_train)
            X_test_selected = selector.transform(X_test)

            selected_features = selector.get_support(indices=True)

            print(f"    Sélectionné {len(selected_features)} caractéristiques sur {X_train.shape[1]}")
            return X_train_selected, X_test_selected, selected_features

        except Exception as e:
            print(f"    Erreur lors de la sélection des caractéristiques: {e}")
            return X_train, X_test, list(range(X_train.shape[1]))

    def _optimize_random_forest(self, X_train, y_train, number):
        """Optimise les hyperparamètres de Random Forest"""
        try:
            param_grid = {
                'n_estimators': [100, 200, 300],
                'max_depth': [10, 15, 20, None],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4],
                'class_weight': ['balanced', 'balanced_subsample']
            }

            rf = RandomForestClassifier(random_state=42)

            # Utiliser RandomizedSearchCV pour plus d'efficacité
            search = RandomizedSearchCV(
                rf, param_grid, n_iter=20, cv=3,
                scoring=self.config['model_optimization']['optimization_metric'],
                random_state=42, n_jobs=-1
            )

            search.fit(X_train, y_train)
            return search.best_estimator_

        except Exception as e:
            print(f"    Erreur lors de l'optimisation de Random Forest: {e}")
            # Retourner un modèle par défaut
            return RandomForestClassifier(
                n_estimators=200, max_depth=15, class_weight='balanced', random_state=42
            ).fit(X_train, y_train)

    def _optimize_xgboost(self, X_train, y_train, number):
        """Optimise les hyperparamètres de XGBoost"""
        try:
            # Calculer le ratio de classes pour scale_pos_weight
            pos_count = np.sum(y_train == 1)
            neg_count = np.sum(y_train == 0)
            scale_pos_weight = neg_count / pos_count if pos_count > 0 else 1

            param_grid = {
                'n_estimators': [100, 200, 300],
                'max_depth': [3, 6, 9],
                'learning_rate': [0.01, 0.1, 0.2],
                'subsample': [0.8, 0.9, 1.0],
                'colsample_bytree': [0.8, 0.9, 1.0],
                'scale_pos_weight': [scale_pos_weight * 0.5, scale_pos_weight, scale_pos_weight * 2]
            }

            xgb_model = xgb.XGBClassifier(
                objective='binary:logistic',
                eval_metric='auc',
                use_label_encoder=False,
                random_state=42
            )

            search = RandomizedSearchCV(
                xgb_model, param_grid, n_iter=20, cv=3,
                scoring=self.config['model_optimization']['optimization_metric'],
                random_state=42, n_jobs=-1
            )

            search.fit(X_train, y_train)
            return search.best_estimator_

        except Exception as e:
            print(f"    Erreur lors de l'optimisation de XGBoost: {e}")
            # Retourner un modèle par défaut
            return xgb.XGBClassifier(
                n_estimators=200, max_depth=6, learning_rate=0.1,
                scale_pos_weight=scale_pos_weight, random_state=42
            ).fit(X_train, y_train)

    def _optimize_lightgbm(self, X_train, y_train, number):
        """Optimise les hyperparamètres de LightGBM"""
        try:
            # Calculer le ratio de classes
            pos_count = np.sum(y_train == 1)
            neg_count = np.sum(y_train == 0)
            scale_pos_weight = neg_count / pos_count if pos_count > 0 else 1

            param_grid = {
                'n_estimators': [100, 200, 300],
                'max_depth': [3, 6, 9],
                'learning_rate': [0.01, 0.1, 0.2],
                'subsample': [0.8, 0.9, 1.0],
                'colsample_bytree': [0.8, 0.9, 1.0],
                'scale_pos_weight': [scale_pos_weight * 0.5, scale_pos_weight, scale_pos_weight * 2]
            }

            lgb_model = lgb.LGBMClassifier(
                objective='binary',
                metric='auc',
                random_state=42,
                verbose=-1
            )

            search = RandomizedSearchCV(
                lgb_model, param_grid, n_iter=20, cv=3,
                scoring=self.config['model_optimization']['optimization_metric'],
                random_state=42, n_jobs=-1
            )

            search.fit(X_train, y_train)
            return search.best_estimator_

        except Exception as e:
            print(f"    Erreur lors de l'optimisation de LightGBM: {e}")
            # Retourner un modèle par défaut
            return lgb.LGBMClassifier(
                n_estimators=200, max_depth=6, learning_rate=0.1,
                scale_pos_weight=scale_pos_weight, random_state=42, verbose=-1
            ).fit(X_train, y_train)

    def _create_voting_ensemble(self, models, X_train, y_train):
        """Crée un ensemble voting avec les meilleurs modèles"""
        try:
            estimators = [(name, model) for name, model in models.items()]

            ensemble = VotingClassifier(
                estimators=estimators,
                voting='soft'  # Utilise les probabilités
            )

            ensemble.fit(X_train, y_train)
            return ensemble

        except Exception as e:
            print(f"    Erreur lors de la création de l'ensemble: {e}")
            return None

    def run_enhanced_auto_improve(self, numbers_to_train=None, max_time_per_number=300):
        """Exécute l'auto-amélioration optimisée"""
        print("=== Démarrage de l'auto-amélioration optimisée ===")

        if not numbers_to_train:
            numbers_to_train = list(range(1, 71))  # Tous les numéros Keno

        results = {
            'trained_numbers': [],
            'failed_numbers': [],
            'best_models': {},
            'performance_summary': {}
        }

        total_numbers = len(numbers_to_train)

        for i, number in enumerate(numbers_to_train):
            print(f"\n--- Traitement du numéro {number} ({i+1}/{total_numbers}) ---")

            try:
                # 1. Créer les caractéristiques avancées
                X, y, feature_names = self.create_enhanced_features(number)

                if X is None or len(X) < 100:
                    print(f"Pas assez de données pour le numéro {number}")
                    results['failed_numbers'].append(number)
                    continue

                # 2. Optimiser l'ensemble de modèles
                best_model = self.optimize_model_ensemble(number, X, y)

                if best_model:
                    results['trained_numbers'].append(number)
                    results['best_models'][number] = best_model

                    # Stocker les métriques de performance
                    results['performance_summary'][number] = {
                        'f1_score': best_model['f1_score'],
                        'accuracy': best_model['accuracy'],
                        'precision': best_model['precision'],
                        'recall': best_model['recall'],
                        'model_type': best_model['name'],
                        'num_features': len(best_model['selected_features'])
                    }

                    print(f"✓ Numéro {number} traité avec succès")
                else:
                    results['failed_numbers'].append(number)
                    print(f"✗ Échec du traitement du numéro {number}")

            except Exception as e:
                print(f"✗ Erreur lors du traitement du numéro {number}: {e}")
                results['failed_numbers'].append(number)

        # Résumé des résultats
        print(f"\n=== Résumé de l'auto-amélioration ===")
        print(f"Numéros traités avec succès: {len(results['trained_numbers'])}")
        print(f"Numéros échoués: {len(results['failed_numbers'])}")

        if results['performance_summary']:
            avg_f1 = np.mean([p['f1_score'] for p in results['performance_summary'].values()])
            avg_accuracy = np.mean([p['accuracy'] for p in results['performance_summary'].values()])
            print(f"F1-score moyen: {avg_f1:.4f}")
            print(f"Précision moyenne: {avg_accuracy:.4f}")

        # Sauvegarder les résultats
        self._save_enhanced_models(results)

        return results

    def predict_next_draw_enhanced(self, num_predictions=10):
        """Génère des prédictions optimisées pour le prochain tirage"""
        print("=== Génération de prédictions optimisées ===")

        if not self.ensemble_models:
            print("Aucun modèle optimisé disponible. Exécutez d'abord l'auto-amélioration.")
            return []

        predictions = {}

        for number in range(1, 71):
            if number not in self.ensemble_models:
                continue

            try:
                # Créer les caractéristiques pour la prédiction
                current_features = self._create_prediction_features(number)

                if current_features is None:
                    continue

                model_info = self.ensemble_models[number]
                model = model_info['model']
                selected_features = model_info['selected_features']

                # Sélectionner les bonnes caractéristiques
                if len(selected_features) < len(current_features):
                    current_features = [current_features[i] for i in selected_features]

                # Prédire la probabilité
                X_pred = np.array(current_features).reshape(1, -1)

                if hasattr(model, 'predict_proba'):
                    prob = model.predict_proba(X_pred)[0][1]  # Probabilité de la classe positive
                else:
                    prob = model.predict(X_pred)[0]

                # Ajuster la probabilité avec des facteurs de confiance
                confidence_factor = self._calculate_confidence_factor(number, model_info)
                adjusted_prob = prob * confidence_factor

                predictions[number] = {
                    'probability': adjusted_prob,
                    'raw_probability': prob,
                    'confidence': confidence_factor,
                    'model_type': model_info['name'],
                    'f1_score': model_info['f1_score']
                }

            except Exception as e:
                print(f"Erreur lors de la prédiction pour le numéro {number}: {e}")

        # Trier par probabilité et sélectionner les meilleurs
        sorted_predictions = sorted(
            predictions.items(),
            key=lambda x: x[1]['probability'],
            reverse=True
        )

        # Sélectionner les top numéros avec diversification
        selected_numbers = self._diversify_predictions(sorted_predictions, num_predictions)

        print(f"Prédictions générées pour {len(selected_numbers)} numéros:")
        for i, (number, info) in enumerate(selected_numbers[:num_predictions]):
            print(f"  {i+1}. Numéro {number}: {info['probability']:.4f} "
                  f"(confiance: {info['confidence']:.3f}, modèle: {info['model_type']})")

        return [num for num, _ in selected_numbers[:num_predictions]]

    def _create_prediction_features(self, number):
        """Crée les caractéristiques pour la prédiction du prochain tirage"""
        try:
            draws = self.data_manager.draws
            if len(draws) < 50:
                return None

            # Utiliser la même logique que create_enhanced_features mais pour le dernier état
            features = {}

            # 1. Caractéristiques de fréquence
            for window in self.config['feature_engineering']['lookback_windows']:
                recent_draws = draws[-window:] if len(draws) >= window else draws
                freq = sum(1 for d in recent_draws if hasattr(d, 'numbers') and number in d.numbers)
                features[f'freq_{window}'] = freq / len(recent_draws)

                avg_freq = len(recent_draws) * 20 / 70
                features[f'freq_rel_{window}'] = freq / avg_freq if avg_freq > 0 else 0

            # 2. Analyse des gaps
            gaps = self._calculate_gaps(draws, number)
            if gaps:
                features['gap_mean'] = np.mean(gaps)
                features['gap_std'] = np.std(gaps)
                features['gap_min'] = np.min(gaps)
                features['gap_max'] = np.max(gaps)
                features['gap_current'] = len(draws) - self._last_appearance(draws, number) - 1
                features['gap_trend'] = self._calculate_gap_trend(gaps)
            else:
                features.update({
                    'gap_mean': 0, 'gap_std': 0, 'gap_min': 0,
                    'gap_max': 0, 'gap_current': len(draws), 'gap_trend': 0
                })

            # 3. Caractéristiques temporelles (pour le prochain tirage prévu)
            next_draw_time = datetime.now()  # Approximation
            features['day_of_week'] = next_draw_time.weekday()
            features['hour'] = next_draw_time.hour
            features['month'] = next_draw_time.month
            features['is_weekend'] = 1 if next_draw_time.weekday() >= 5 else 0

            # 4. Co-occurrence avec les numéros récents
            if len(draws) > 0 and hasattr(draws[-1], 'numbers'):
                recent_numbers = draws[-1].numbers
                cooccurrence_scores = self._calculate_cooccurrence(draws, number, recent_numbers)
                features.update(cooccurrence_scores)
            else:
                features['cooccurrence_score'] = 0
                features['cooccurrence_strength'] = 0

            # 5. Positions récentes
            recent_positions = self._get_recent_positions(draws[-20:], number)
            if recent_positions:
                features['position_variance'] = np.var(recent_positions)
                features['position_trend'] = self._calculate_position_trend(recent_positions)
            else:
                features['position_variance'] = 0
                features['position_trend'] = 0

            # 6. Momentum
            momentum_scores = self._calculate_momentum(draws, number)
            features.update(momentum_scores)

            return list(features.values())

        except Exception as e:
            print(f"Erreur lors de la création des caractéristiques de prédiction pour {number}: {e}")
            return None

    def _calculate_confidence_factor(self, number, model_info):
        """Calcule un facteur de confiance pour ajuster les prédictions"""
        base_confidence = 1.0

        # Ajuster selon la performance du modèle
        f1_score = model_info.get('f1_score', 0)
        performance_factor = min(1.5, max(0.5, f1_score * 2))

        # Ajuster selon la récence des données d'entraînement
        recency_factor = 1.0  # Pourrait être amélioré avec des métadonnées temporelles

        # Ajuster selon la stabilité historique du numéro
        stability_factor = self._calculate_number_stability(number)

        confidence = base_confidence * performance_factor * recency_factor * stability_factor
        return min(2.0, max(0.3, confidence))  # Limiter entre 0.3 et 2.0

    def _calculate_number_stability(self, number):
        """Calcule la stabilité d'un numéro basée sur son historique"""
        try:
            draws = self.data_manager.draws
            if len(draws) < 50:
                return 1.0

            # Calculer la variance des intervalles entre apparitions
            gaps = self._calculate_gaps(draws, number)
            if len(gaps) < 3:
                return 0.8  # Moins de confiance pour les numéros rares

            gap_variance = np.var(gaps)
            gap_mean = np.mean(gaps)

            # Coefficient de variation (plus c'est bas, plus c'est stable)
            cv = gap_variance / gap_mean if gap_mean > 0 else 1.0

            # Convertir en facteur de stabilité (inverse du CV, normalisé)
            stability = 1.0 / (1.0 + cv)
            return min(1.2, max(0.7, stability))

        except Exception:
            return 1.0

    def _diversify_predictions(self, sorted_predictions, num_predictions):
        """Diversifie les prédictions pour éviter la sur-concentration"""
        if len(sorted_predictions) <= num_predictions:
            return sorted_predictions

        selected = []
        remaining = list(sorted_predictions)

        # Prendre le meilleur
        selected.append(remaining.pop(0))

        while len(selected) < num_predictions and remaining:
            best_candidate = None
            best_score = -1
            best_idx = -1

            for i, (number, info) in enumerate(remaining):
                # Score de diversité basé sur la distance avec les numéros déjà sélectés
                diversity_score = self._calculate_diversity_score(
                    number, [s[0] for s in selected]
                )

                # Score combiné: probabilité * diversité
                combined_score = info['probability'] * (1 + diversity_score * 0.3)

                if combined_score > best_score:
                    best_score = combined_score
                    best_candidate = (number, info)
                    best_idx = i

            if best_candidate:
                selected.append(best_candidate)
                remaining.pop(best_idx)
            else:
                break

        return selected

    def _calculate_diversity_score(self, candidate_number, selected_numbers):
        """Calcule un score de diversité pour un numéro candidat"""
        if not selected_numbers:
            return 1.0

        # Distance minimale avec les numéros déjà sélectionnés
        min_distance = min(abs(candidate_number - selected) for selected in selected_numbers)

        # Normaliser la distance (max distance possible = 69)
        normalized_distance = min_distance / 69.0

        return normalized_distance

    def _save_enhanced_models(self, results):
        """Sauvegarde les modèles optimisés"""
        try:
            models_dir = os.path.join(os.path.dirname(__file__), 'models')
            os.makedirs(models_dir, exist_ok=True)

            # Sauvegarder le résumé des résultats
            summary_file = os.path.join(models_dir, 'enhanced_auto_improve_summary.json')
            summary_data = {
                'timestamp': datetime.now().isoformat(),
                'trained_numbers': results['trained_numbers'],
                'failed_numbers': results['failed_numbers'],
                'performance_summary': results['performance_summary']
            }

            with open(summary_file, 'w') as f:
                json.dump(summary_data, f, indent=2)

            print(f"Résumé sauvegardé dans: {summary_file}")

        except Exception as e:
            print(f"Erreur lors de la sauvegarde: {e}")


def integrate_enhanced_auto_improve(analyzer):
    """Intègre le système d'auto-amélioration optimisé dans l'analyseur existant"""

    if not hasattr(analyzer, 'data_manager'):
        print("L'analyseur doit avoir un data_manager")
        return None

    # Créer l'instance d'amélioration optimisée
    enhanced_improver = KenoEnhancedAutoImprove(analyzer.data_manager, analyzer)

    # Ajouter les méthodes à l'analyseur
    analyzer.enhanced_auto_improve = enhanced_improver.run_enhanced_auto_improve
    analyzer.predict_enhanced = enhanced_improver.predict_next_draw_enhanced

    print("Système d'auto-amélioration optimisé intégré avec succès")
    return enhanced_improver


# Fonction utilitaire pour créer un analyseur complet si nécessaire
def create_complete_analyzer_from_data_manager(data_manager):
    """Crée un analyseur complet à partir d'un data_manager"""
    try:
        from keno_analyzer import KenoAnalyzer
        analyzer = KenoAnalyzer(data_manager)
        return analyzer
    except Exception as e:
        print(f"Erreur lors de la création de l'analyseur: {e}")
        return None
