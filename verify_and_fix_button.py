#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de vérification et correction du bouton Auto-Amélioration
"""

import os
import re

def check_button_text():
    """Vérifie le texte actuel du bouton"""
    
    print("=== VÉRIFICATION DU BOUTON AUTO-AMÉLIORATION ===")
    
    gui_file = 'keno_gui.py'
    
    if not os.path.exists(gui_file):
        print(f"✗ Fichier {gui_file} non trouvé")
        return False
    
    try:
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Chercher toutes les occurrences de boutons avec "Auto-Amélioration"
        patterns = [
            r'text="[^"]*Auto-Amélioration[^"]*"',
            r"text='[^']*Auto-Amélioration[^']*'",
            r'text="[^"]*auto.amélioration[^"]*"',
            r'text="[^"]*Auto.amélioration[^"]*"'
        ]
        
        found_buttons = []
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            found_buttons.extend(matches)
        
        if found_buttons:
            print("Boutons trouvés:")
            for i, button in enumerate(found_buttons):
                print(f"  {i+1}. {button}")
            return True, found_buttons
        else:
            print("✗ Aucun bouton Auto-Amélioration trouvé")
            return False, []
            
    except Exception as e:
        print(f"✗ Erreur lors de la lecture: {e}")
        return False, []

def force_update_button():
    """Force la mise à jour du bouton"""
    
    print("\n=== MISE À JOUR FORCÉE DU BOUTON ===")
    
    gui_file = 'keno_gui.py'
    
    try:
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remplacements multiples pour être sûr
        replacements = [
            ('text="Auto-Amélioration"', 'text="🚀 Auto-Amélioration ULTRA 🚀"'),
            ("text='Auto-Amélioration'", "text='🚀 Auto-Amélioration ULTRA 🚀'"),
            ('text="Auto-amélioration"', 'text="🚀 Auto-Amélioration ULTRA 🚀"'),
            ("text='Auto-amélioration'", "text='🚀 Auto-Amélioration ULTRA 🚀'"),
            ('text="auto-amélioration"', 'text="🚀 Auto-Amélioration ULTRA 🚀"'),
            ("text='auto-amélioration'", "text='🚀 Auto-Amélioration ULTRA 🚀'")
        ]
        
        changes_made = 0
        for old, new in replacements:
            if old in content:
                content = content.replace(old, new)
                changes_made += 1
                print(f"✓ Remplacé: {old}")
        
        if changes_made > 0:
            # Sauvegarder
            with open(gui_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ {changes_made} modification(s) appliquée(s)")
            return True
        else:
            print("✗ Aucune modification nécessaire")
            return False
            
    except Exception as e:
        print(f"✗ Erreur lors de la mise à jour: {e}")
        return False

def add_ultra_method_if_missing():
    """Ajoute la méthode ultra si elle manque"""
    
    print("\n=== VÉRIFICATION DE LA MÉTHODE ULTRA ===")
    
    gui_file = 'keno_gui.py'
    
    try:
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifier si la méthode _start_ultra_auto_improve existe
        if '_start_ultra_auto_improve' in content:
            print("✓ Méthode ultra trouvée")
            return True
        
        print("✗ Méthode ultra manquante, ajout en cours...")
        
        # Ajouter la méthode ultra simplifiée
        ultra_method = '''
    def _start_ultra_auto_improve(self, mode):
        """Lance l'auto-amélioration ultra-optimisée (version simplifiée)"""
        try:
            from keno_ultra_optimizer import KenoUltraOptimizer
            
            # Déterminer les numéros selon le mode
            if mode == "fast":
                numbers = [7, 21, 35, 49, 63]
                mode_name = "RAPIDE"
            elif mode == "medium":
                numbers = [1, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70, 3, 17, 31, 45]
                mode_name = "MOYEN"
            else:  # complete
                numbers = list(range(1, 71))
                mode_name = "COMPLET"
            
            # Fenêtre de progression simple
            progress_window = tk.Toplevel(self)
            progress_window.title(f"🚀 AUTO-AMÉLIORATION ULTRA - {mode_name}")
            progress_window.geometry("600x400")
            progress_window.transient(self)
            
            title_label = tk.Label(progress_window, 
                                  text=f"🚀 AUTO-AMÉLIORATION ULTRA - {mode_name} 🚀", 
                                  font=("Helvetica", 14, "bold"))
            title_label.pack(pady=10)
            
            status_var = tk.StringVar(value="Initialisation...")
            status_label = tk.Label(progress_window, textvariable=status_var)
            status_label.pack(pady=5)
            
            # Zone de texte
            text_frame = tk.Frame(progress_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            scrollbar = tk.Scrollbar(text_frame)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            text_widget = tk.Text(text_frame, yscrollcommand=scrollbar.set)
            text_widget.pack(fill=tk.BOTH, expand=True)
            scrollbar.config(command=text_widget.yview)
            
            def update_text(message):
                text_widget.insert(tk.END, message + "\\n")
                text_widget.see(tk.END)
                progress_window.update()
            
            # Entraînement dans un thread
            import threading
            
            def train():
                try:
                    ultra_optimizer = KenoUltraOptimizer(self.data_manager, self.analyzer)
                    
                    update_text(f"🚀 Démarrage AUTO-AMÉLIORATION ULTRA - Mode {mode_name}")
                    update_text(f"📊 Entraînement de {len(numbers)} numéros")
                    status_var.set("Entraînement ultra en cours...")
                    
                    trained = 0
                    total_f1 = 0
                    
                    for i, number in enumerate(numbers):
                        update_text(f"Traitement numéro {number} ({i+1}/{len(numbers)})")
                        status_var.set(f"Numéro {number} ({i+1}/{len(numbers)})")
                        
                        result = ultra_optimizer.create_ultra_features(number)
                        if result:
                            X, y, features = result
                            update_text(f"  ✨ {len(features)} caractéristiques ultra créées")
                            
                            if len(X) > 50:
                                from sklearn.ensemble import RandomForestClassifier
                                from sklearn.model_selection import train_test_split
                                from sklearn.metrics import f1_score
                                
                                X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
                                model = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
                                model.fit(X_train, y_train)
                                y_pred = model.predict(X_test)
                                f1 = f1_score(y_test, y_pred, zero_division=0)
                                
                                update_text(f"  🎯 F1-score: {f1:.4f}")
                                total_f1 += f1
                                trained += 1
                            else:
                                update_text(f"  ⚠️ Pas assez de données")
                        else:
                            update_text(f"  ❌ Échec création features")
                    
                    if trained > 0:
                        avg_f1 = total_f1 / trained
                        update_text(f"\\n🔥 SUCCÈS ! F1-score moyen: {avg_f1:.4f}")
                        status_var.set("✅ AUTO-AMÉLIORATION ULTRA TERMINÉE !")
                    else:
                        update_text("\\n❌ Aucun numéro entraîné")
                        status_var.set("❌ Échec")
                        
                except Exception as e:
                    update_text(f"\\n❌ Erreur: {e}")
                    status_var.set("❌ Erreur")
                
                close_btn = tk.Button(progress_window, text="Fermer", 
                                     command=progress_window.destroy)
                close_btn.pack(pady=10)
            
            thread = threading.Thread(target=train)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur ultra: {e}")
    
    def _start_standard_auto_improve(self):
        """Lance l'ancien système (fallback)"""
        messagebox.showinfo("Auto-amélioration", "Ancien système d'auto-amélioration")
'''
        
        # Ajouter avant la fin du fichier
        end_pos = content.rfind("if __name__ == \"__main__\":")
        if end_pos != -1:
            content = content[:end_pos] + ultra_method + "\n\n" + content[end_pos:]
            
            with open(gui_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✓ Méthode ultra ajoutée")
            return True
        else:
            print("✗ Impossible de trouver où ajouter la méthode")
            return False
            
    except Exception as e:
        print(f"✗ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    
    print("=== VÉRIFICATION ET CORRECTION DU BOUTON ULTRA ===")
    
    # 1. Vérifier l'état actuel
    found, buttons = check_button_text()
    
    # 2. Forcer la mise à jour du bouton
    button_updated = force_update_button()
    
    # 3. Ajouter la méthode si nécessaire
    method_added = add_ultra_method_if_missing()
    
    if button_updated or method_added:
        print("\n✅ CORRECTIONS APPLIQUÉES !")
        print("🚀 Relancez votre application:")
        print("  python main.py")
        print("\n🎯 Le bouton Auto-Amélioration devrait maintenant afficher:")
        print("  '🚀 Auto-Amélioration ULTRA 🚀'")
        return 0
    else:
        print("\n⚠️ Aucune correction nécessaire ou possible")
        print("Le bouton pourrait déjà être correct.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
