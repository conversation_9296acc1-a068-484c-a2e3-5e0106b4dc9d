"""
Module pour sauvegarder et charger les données d'apprentissage
"""

import os
import json
import pickle
import numpy as np
import time

class NumpyEncoder(json.JSONEncoder):
    """Encodeur JSON personnalisé pour gérer les types numpy"""
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, tuple):
            return str(obj)
        return super(NumpyEncoder, self).default(obj)

def convert_to_serializable(data):
    """Convertit récursivement les structures de données pour les rendre sérialisables en JSON

    Args:
        data: Données à convertir (dict, list, tuple, etc.)

    Returns:
        Données converties en format sérialisable
    """
    if isinstance(data, dict):
        # Convertir un dictionnaire
        result = {}
        for key, value in data.items():
            # Convertir la clé si c'est un tuple
            if isinstance(key, tuple):
                new_key = str(key)
            else:
                new_key = key
            # Convertir récursivement la valeur
            result[new_key] = convert_to_serializable(value)
        return result
    elif isinstance(data, list):
        # Convertir une liste
        return [convert_to_serializable(item) for item in data]
    elif isinstance(data, tuple):
        # Convertir un tuple
        if len(data) == 2 and not isinstance(data[0], (list, dict, tuple)):
            # Cas spécial pour les paires (clé, valeur) comme dans Counter.most_common()
            return (convert_to_serializable(data[0]), data[1])
        else:
            # Autres tuples
            return str(data)
    elif isinstance(data, np.ndarray):
        # Convertir un tableau numpy
        return data.tolist()
    elif isinstance(data, (np.integer, np.floating)):
        # Convertir les types numpy scalaires
        return float(data) if isinstance(data, np.floating) else int(data)
    elif isinstance(data, (int, float, str, bool, type(None))):
        # Types de base déjà sérialisables
        return data
    else:
        # Autres types: essayer de convertir en chaîne
        try:
            return str(data)
        except:
            return None

def save_learning_data(analyzer, file_path):
    """
    Sauvegarde les données d'apprentissage de l'analyseur

    Args:
        analyzer: L'analyseur contenant les modèles et données
        file_path (str): Chemin du fichier de sauvegarde

    Returns:
        bool: True si la sauvegarde a réussi, False sinon
    """
    try:
        # Créer le répertoire si nécessaire
        os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)

        # Préparer les données à sauvegarder
        data = {
            'timestamp': time.time(),
            'version': '1.0',
            'stats': {}
        }

        # Sauvegarder les statistiques de base
        if hasattr(analyzer, 'data_manager') and analyzer.data_manager:
            data['stats']['draws_count'] = len(analyzer.data_manager.draws)
            data['stats']['max_number'] = analyzer.data_manager.max_number
            data['stats']['numbers_per_draw'] = analyzer.data_manager.numbers_per_draw

        # Sauvegarder les données d'apprentissage avancées si disponibles
        if hasattr(analyzer, 'advanced_analyzer') and analyzer.advanced_analyzer:
            adv = analyzer.advanced_analyzer

            # Sauvegarder les motifs
            if hasattr(adv, 'patterns') and adv.patterns:
                data['patterns'] = convert_to_serializable(adv.patterns)

            # Sauvegarder les corrélations
            if hasattr(adv, 'correlations') and adv.correlations is not None:
                data['correlations'] = convert_to_serializable(adv.correlations)

            # Sauvegarder les séries
            if hasattr(adv, 'series') and adv.series:
                data['series'] = convert_to_serializable(adv.series)

            # Sauvegarder les métriques des modèles (mais pas les modèles eux-mêmes)
            if hasattr(adv, 'models') and adv.models:
                models_metrics = {}
                if 'targets' in adv.models:
                    for num, target_info in adv.models['targets'].items():
                        if 'accuracy' in target_info:
                            models_metrics[str(num)] = {
                                'accuracy': target_info['accuracy'],
                                'type': target_info.get('type', 'unknown')
                            }
                data['models_metrics'] = convert_to_serializable(models_metrics)

        # Sauvegarder en format JSON
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, cls=NumpyEncoder, indent=2)

        print(f"Données d'apprentissage sauvegardées dans {file_path}")
        return True

    except Exception as e:
        print(f"Erreur lors de la sauvegarde des données d'apprentissage: {e}")
        return False

def load_learning_data(analyzer, file_path, auto_improve=False):
    """
    Charge les données d'apprentissage dans l'analyseur

    Args:
        analyzer: L'analyseur qui recevra les données
        file_path (str): Chemin du fichier de données
        auto_improve (bool): Si True, lance l'auto-amélioration après le chargement (défaut: False)

    Returns:
        bool: True si le chargement a réussi, False sinon
    """
    try:
        # Vérifier que le fichier existe
        if not os.path.exists(file_path):
            print(f"Le fichier {file_path} n'existe pas")
            return False

        # Déterminer le format de fichier
        _, ext = os.path.splitext(file_path)

        if ext.lower() == '.json':
            # Charger depuis JSON
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        elif ext.lower() == '.pkl':
            # Charger depuis Pickle
            with open(file_path, 'rb') as f:
                data = pickle.load(f)
        else:
            print(f"Format de fichier non pris en charge: {ext}")
            return False

        # Vérifier la version
        if 'version' not in data:
            print("Format de données non reconnu (version manquante)")
            return False

        # Charger les données dans l'analyseur avancé si disponible
        if hasattr(analyzer, 'advanced_analyzer') and analyzer.advanced_analyzer:
            adv = analyzer.advanced_analyzer

            # Charger les motifs
            if 'patterns' in data and data['patterns']:
                # Convertir les clés de chaîne en tuples si nécessaire
                patterns = {}
                for key, value in data['patterns'].items():
                    if key.startswith('(') and key.endswith(')'):
                        # Convertir la chaîne en tuple
                        try:
                            # Évaluer la chaîne comme un tuple Python
                            tuple_key = eval(key)
                            patterns[tuple_key] = value
                        except:
                            # En cas d'erreur, garder la clé comme chaîne
                            patterns[key] = value
                    else:
                        patterns[key] = value
                adv.patterns = patterns

            # Charger les corrélations
            if 'correlations' in data and data['correlations'] is not None:
                adv.correlations = np.array(data['correlations']) if isinstance(data['correlations'], list) else data['correlations']

            # Charger les séries
            if 'series' in data and data['series']:
                adv.series = data['series']

            # Lancer l'auto-amélioration si demandé
            if auto_improve and hasattr(analyzer, 'auto_improve'):
                print("Lancement de l'auto-amélioration après chargement des données...")
                analyzer.auto_improve()

        print(f"Données d'apprentissage chargées depuis {file_path}")
        return True

    except Exception as e:
        print(f"Erreur lors du chargement des données d'apprentissage: {e}")
        return False
