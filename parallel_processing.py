#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module pour le traitement parallèle des analyses et entraînements de modèles Keno
Optimise l'utilisation des ressources CPU et GPU pour accélérer les calculs
"""

import os
import sys
import time
import multiprocessing
from concurrent.futures import <PERSON>PoolExecutor, ThreadPoolExecutor, as_completed
from collections import Counter, defaultdict
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier

# Vérifier si XGBoost est disponible
try:
    import xgboost
    from xgboost import XGBClassifier
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

# Vérifier si LightGBM est disponible
try:
    import lightgbm
    from lightgbm import LGBMClassifier
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

# Vérifier si CUDA est disponible
def is_cuda_available():
    """Vérifie si CUDA est disponible pour l'accélération GPU"""
    try:
        # Vérifier via XGBoost
        if XGBOOST_AVAILABLE:
            import xgboost as xgb
            try:
                # Créer un petit jeu de données pour tester
                data = np.random.rand(10, 10)
                label = np.random.randint(2, size=10)
                dtrain = xgb.DMatrix(data, label=label)

                # Essayer de créer un modèle avec GPU
                param = {'tree_method': 'gpu_hist'}
                bst = xgb.train(param, dtrain, num_boost_round=1)
                return True
            except Exception:
                pass

        # Vérifier via LightGBM
        if LIGHTGBM_AVAILABLE:
            import lightgbm as lgb
            try:
                # Créer un petit jeu de données pour tester
                data = np.random.rand(10, 10)
                label = np.random.randint(2, size=10)
                lgb_train = lgb.Dataset(data, label=label)

                # Essayer de créer un modèle avec GPU
                param = {'device': 'gpu'}
                bst = lgb.train(param, lgb_train, num_boost_round=1)
                return True
            except Exception:
                pass

        # Vérifier via TensorFlow
        try:
            import tensorflow as tf
            return tf.test.is_gpu_available()
        except (ImportError, AttributeError):
            try:
                import tensorflow as tf
                return len(tf.config.list_physical_devices('GPU')) > 0
            except (ImportError, AttributeError):
                pass

        return False
    except Exception:
        return False

# Variable globale pour stocker le nombre de workers
_OPTIMAL_WORKERS = None

def get_optimal_workers(max_limit=None):
    """Détermine le nombre optimal de workers pour le traitement parallèle

    Args:
        max_limit (int, optional): Limite maximale du nombre de workers. Si None, utilise une valeur par défaut.

    Returns:
        int: Nombre optimal de workers
    """
    global _OPTIMAL_WORKERS

    # Si un max_limit est spécifié, ignorer la valeur cachée
    if max_limit is not None:
        try:
            # Utiliser la fonction patchée pour éviter les problèmes
            num_cores = patched_count_physical_cores()

            # Réserver un cœur pour l'interface utilisateur et le système
            optimal_workers = max(1, num_cores - 1)

            # Appliquer la limite spécifiée
            return min(optimal_workers, max_limit)
        except Exception:
            # Valeur par défaut sécurisée
            return min(4, max_limit)

    # Si déjà calculé et pas de max_limit, retourner la valeur cachée
    if _OPTIMAL_WORKERS is not None:
        return _OPTIMAL_WORKERS

    try:
        # Utiliser la fonction patchée pour éviter les problèmes
        num_cores = patched_count_physical_cores()

        # Réserver un cœur pour l'interface utilisateur et le système
        optimal_workers = max(1, num_cores - 1)

        # Utiliser une limite plus élevée (22 au lieu de 16)
        _OPTIMAL_WORKERS = min(optimal_workers, 22)  # Augmenté à 22 pour améliorer les performances
        return _OPTIMAL_WORKERS
    except Exception:
        # Valeur par défaut sécurisée
        _OPTIMAL_WORKERS = 4
        return _OPTIMAL_WORKERS

def train_model_worker(params):
    """Fonction worker pour entraîner un modèle dans un processus séparé"""
    try:
        # Ajouter un délai pour éviter les problèmes d'initialisation
        import time
        time.sleep(0.5)  # Attendre 0.5 seconde avant de démarrer

        number = params['number']
        print(f"Démarrage de l'entraînement pour le numéro {number}")
        X = params['X']
        y = params['y']
        test_size = params.get('test_size', 0.2)
        random_state = params.get('random_state', 42)
        use_gpu = params.get('use_gpu', False)
        n_jobs = params.get('n_jobs', 1)
        fast_mode = params.get('fast_mode', False)  # Récupérer le paramètre fast_mode
        print(f"Numéro {number}: Mode rapide = {fast_mode}")

        # Convertir les données pandas en numpy si nécessaire
        if isinstance(X, pd.DataFrame):
            X = X.values
        if isinstance(y, pd.Series):
            y = y.values

        # Diviser en ensembles d'entraînement et de test
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state
        )

        # Normaliser les caractéristiques
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)

        # Entraîner plusieurs modèles et choisir le meilleur
        models = {}

        # 1. Random Forest
        try:
            # Paramètres optimisés pour la vitesse en mode rapide
            if fast_mode:
                rf_params = {
                    'n_estimators': 100,  # Réduit pour accélérer l'entraînement
                    'max_depth': 8,       # Réduit pour accélérer l'entraînement
                    'min_samples_split': 5,
                    'min_samples_leaf': 3,
                    'max_features': 'sqrt',
                    'bootstrap': True,
                    'random_state': random_state,
                    'n_jobs': n_jobs,
                    'class_weight': 'balanced_subsample'
                }
                print(f"Mode rapide: RandomForest optimisé pour le numéro {number}")
            else:
                rf_params = {
                    'n_estimators': 300,
                    'max_depth': 15,
                    'min_samples_split': 4,
                    'min_samples_leaf': 2,
                    'max_features': 'sqrt',
                    'bootstrap': True,
                    'random_state': random_state,
                    'n_jobs': n_jobs,
                    'class_weight': 'balanced_subsample'
                }
            rf_model = RandomForestClassifier(**rf_params)
            rf_model.fit(X_train_scaled, y_train)
            rf_pred = rf_model.predict(X_test_scaled)
            rf_accuracy = accuracy_score(y_test, rf_pred)
            # Calculer F1 score avec gestion d'erreur
            try:
                rf_f1 = f1_score(y_test, rf_pred, average='weighted')
            except Exception:
                # Fallback sur une version plus simple
                rf_f1 = f1_score(y_test, rf_pred, average='binary')

            # Calculer AUC si possible
            try:
                rf_proba = rf_model.predict_proba(X_test_scaled)[:, 1]
                rf_auc = roc_auc_score(y_test, rf_proba)
            except:
                rf_auc = 0.5

            models['random_forest'] = {
                'model': rf_model,
                'accuracy': rf_accuracy,
                'f1': rf_f1,
                'auc': rf_auc
            }
        except Exception as e:
            print(f"Erreur lors de l'entraînement du Random Forest pour le numéro {number}: {e}")

        # 2. Gradient Boosting
        try:
            # Paramètres optimisés pour la vitesse en mode rapide
            if fast_mode:
                gb_params = {
                    'n_estimators': 80,       # Réduit pour accélérer l'entraînement
                    'max_depth': 4,           # Réduit pour accélérer l'entraînement
                    'learning_rate': 0.1,      # Augmenté pour converger plus rapidement
                    'subsample': 0.7,          # Réduit pour accélérer l'entraînement
                    'min_samples_split': 5,
                    'min_samples_leaf': 3,
                    'max_features': 'sqrt',
                    'random_state': random_state
                }
                print(f"Mode rapide: GradientBoosting optimisé pour le numéro {number}")
            else:
                gb_params = {
                    'n_estimators': 200,
                    'max_depth': 6,
                    'learning_rate': 0.05,
                    'subsample': 0.8,
                    'min_samples_split': 4,
                    'min_samples_leaf': 2,
                    'max_features': 'sqrt',
                    'random_state': random_state
                }
            gb_model = GradientBoostingClassifier(**gb_params)
            gb_model.fit(X_train_scaled, y_train)
            gb_pred = gb_model.predict(X_test_scaled)
            gb_accuracy = accuracy_score(y_test, gb_pred)
            # Calculer F1 score avec gestion d'erreur
            try:
                gb_f1 = f1_score(y_test, gb_pred, average='weighted')
            except Exception:
                # Fallback sur une version plus simple
                gb_f1 = f1_score(y_test, gb_pred, average='binary')

            # Calculer AUC si possible
            try:
                gb_proba = gb_model.predict_proba(X_test_scaled)[:, 1]
                gb_auc = roc_auc_score(y_test, gb_proba)
            except:
                gb_auc = 0.5

            models['gradient_boosting'] = {
                'model': gb_model,
                'accuracy': gb_accuracy,
                'f1': gb_f1,
                'auc': gb_auc
            }
        except Exception as e:
            print(f"Erreur lors de l'entraînement du Gradient Boosting pour le numéro {number}: {e}")

        # 3. XGBoost si disponible
        if XGBOOST_AVAILABLE:
            try:
                # Configurer XGBoost avec des paramètres optimisés
                if fast_mode:
                    xgb_params = {
                        'n_estimators': 100,      # Réduit pour accélérer l'entraînement
                        'max_depth': 5,           # Réduit pour accélérer l'entraînement
                        'learning_rate': 0.1,      # Augmenté pour converger plus rapidement
                        'min_child_weight': 3,
                        'gamma': 0.1,
                        'subsample': 0.7,          # Réduit pour accélérer l'entraînement
                        'colsample_bytree': 0.7,    # Réduit pour accélérer l'entraînement
                        'colsample_bylevel': 0.7,   # Réduit pour accélérer l'entraînement
                        'reg_alpha': 0.1,
                        'reg_lambda': 1.0,
                        'scale_pos_weight': 1.0,
                        'verbosity': 0,
                        'random_state': random_state,
                        'n_jobs': n_jobs
                    }
                    print(f"Mode rapide: XGBoost optimisé pour le numéro {number}")
                else:
                    xgb_params = {
                        'n_estimators': 300,
                        'max_depth': 8,
                        'learning_rate': 0.03,
                        'min_child_weight': 3,
                        'gamma': 0.1,
                        'subsample': 0.85,
                        'colsample_bytree': 0.85,
                        'colsample_bylevel': 0.9,
                        'reg_alpha': 0.1,
                        'reg_lambda': 1.0,
                        'scale_pos_weight': 1.0,
                        'verbosity': 0,
                        'random_state': random_state,
                        'n_jobs': n_jobs
                    }

                # Configurer l'accélération GPU si disponible
                if use_gpu and is_cuda_available():
                    xgb_params.update({
                        'tree_method': 'gpu_hist',
                        'gpu_id': 0,
                        'predictor': 'gpu_predictor'
                    })
                else:
                    xgb_params['tree_method'] = 'hist'

                # Entraîner le modèle XGBoost avec gestion d'erreur complète et vérification des données
                try:
                    # Vérifier que les données sont valides
                    if X_train_scaled.shape[0] == 0 or X_train_scaled.shape[1] == 0:
                        raise ValueError(f"Données d'entraînement invalides: {X_train_scaled.shape}")

                    # Vérifier que les étiquettes sont valides
                    if len(y_train) == 0 or len(np.unique(y_train)) < 2:
                        raise ValueError(f"Étiquettes d'entraînement invalides: {len(y_train)} échantillons, {len(np.unique(y_train))} classes")

                    # Ajouter des paramètres de sécurité supplémentaires pour XGBoost
                    xgb_params.update({
                        'min_child_weight': 1.0,  # Valeur minimale pour éviter les erreurs
                        'gamma': 0.1,            # Valeur minimale pour éviter les erreurs
                        'objective': 'binary:logistic',  # Forcer l'objectif binaire
                        'eval_metric': 'logloss',        # Métrique standard pour la classification binaire
                        'use_label_encoder': False,      # Éviter les avertissements
                        'verbosity': 0                   # Désactiver les messages
                    })

                    # Créer et entraîner le modèle
                    xgb_model = XGBClassifier(**xgb_params)
                    xgb_model.fit(X_train_scaled, y_train)

                    # Prédire avec gestion d'erreur
                    try:
                        xgb_pred = xgb_model.predict(X_test_scaled)
                        xgb_accuracy = accuracy_score(y_test, xgb_pred)
                    except Exception as pred_err:
                        print(f"Erreur lors de la prédiction avec XGBoost: {pred_err}")
                        raise

                    # Calculer F1 score avec gestion d'erreur
                    try:
                        # Forcer l'utilisation de binary pour éviter les problèmes
                        xgb_f1 = f1_score(y_test, xgb_pred, average='binary')
                    except Exception as f1_err:
                        print(f"Erreur lors du calcul du F1 score pour XGBoost: {f1_err}")
                        xgb_f1 = 0.0

                    # Calculer AUC si possible
                    try:
                        xgb_proba = xgb_model.predict_proba(X_test_scaled)[:, 1]
                        xgb_auc = roc_auc_score(y_test, xgb_proba)
                    except Exception as auc_err:
                        print(f"Erreur lors du calcul de l'AUC pour XGBoost: {auc_err}")
                        xgb_auc = 0.5

                    # Ajouter le modèle au dictionnaire
                    models['xgboost'] = {
                        'model': xgb_model,
                        'accuracy': xgb_accuracy,
                        'f1': xgb_f1,
                        'auc': xgb_auc
                    }
                except Exception as train_err:
                    print(f"Erreur lors de l'entraînement du modèle XGBoost pour le numéro {number}: {train_err}")
            except Exception as e:
                print(f"Erreur lors de l'entraînement de XGBoost pour le numéro {number}: {e}")

        # 4. LightGBM désactivé pour éviter les erreurs
        # Note: LightGBM est désactivé car il cause des erreurs "cannot unpack non-iterable int object"
        # Nous utilisons uniquement RandomForest, GradientBoosting et XGBoost pour l'entraînement parallèle
        if False and LIGHTGBM_AVAILABLE:  # Désactivé intentionnellement
            print(f"LightGBM désactivé pour le numéro {number} pour éviter les erreurs")

        # Choisir le meilleur modèle
        if not models:
            return {
                'number': number,
                'success': False,
                'message': "Aucun modèle n'a pu être entraîné"
            }

        # Trouver le meilleur modèle basé sur l'accuracy
        best_model_name = max(models, key=lambda k: models[k]['accuracy'])
        best_model_info = models[best_model_name]

        return {
            'number': number,
            'success': True,
            'model': best_model_info['model'],
            'model_type': best_model_name,
            'metrics': {
                'accuracy': best_model_info['accuracy'],
                'f1': best_model_info['f1'],
                'auc': best_model_info['auc']
            },
            'scaler': scaler
        }

    except Exception as e:
        import traceback
        traceback.print_exc()
        return {
            'number': params.get('number', 0),
            'success': False,
            'message': str(e)
        }

# Patch pour éviter les problèmes avec _count_physical_cores de joblib
import joblib.externals.loky.backend.context as joblib_context

# Sauvegarder la fonction originale
original_count_physical_cores = joblib_context._count_physical_cores

# Remplacer par une version simplifiée qui ne lance pas de processus
def patched_count_physical_cores():
    """Version simplifiée qui retourne un nombre fixe de cœurs"""
    try:
        import os
        return os.cpu_count() or 4
    except:
        return 4

# Appliquer le patch
joblib_context._count_physical_cores = patched_count_physical_cores

def train_models_parallel(models_params, max_workers=None, use_gpu=False, stop_flag=None):
    """
    Entraîne plusieurs modèles en parallèle

    Args:
        models_params: Liste de dictionnaires contenant les paramètres pour chaque modèle
        max_workers: Nombre maximum de workers à utiliser (None = auto)
        use_gpu: Utiliser l'accélération GPU si disponible
        stop_flag: Référence à un objet qui indique si l'entraînement doit être arrêté

    Returns:
        dict: Dictionnaire des résultats d'entraînement par numéro
    """
    # Vérifier si au moins un des paramètres est en mode rapide
    any_fast_mode = any(params.get('fast_mode', False) for params in models_params)
    print(f"Mode rapide détecté: {any_fast_mode}")

    # Utiliser un nombre de workers adapté en fonction du mode
    if max_workers is None:
        # Utiliser 22 workers par défaut (optimisé pour un processeur à 24 cœurs)
        available_cores = patched_count_physical_cores()
        max_workers = 22
        print(f"Utilisation de {max_workers} workers (sur {available_cores} cœurs disponibles)")

        # Ajuster si nécessaire pour les systèmes avec moins de cœurs
        if available_cores < 24:
            # Laisser 2 cœurs pour le système
            max_workers = max(1, available_cores - 2)
            print(f"Ajustement pour un système avec moins de cœurs: utilisation de {max_workers} workers")
    else:
        # Utiliser le nombre spécifié par l'utilisateur, mais vérifier qu'il est raisonnable
        available_cores = patched_count_physical_cores()
        if max_workers > available_cores:
            print(f"Attention: Le nombre de workers spécifié ({max_workers}) est supérieur au nombre de cœurs disponibles ({available_cores})")
            print(f"Utilisation de {available_cores} workers au maximum")
            max_workers = available_cores

    print(f"Entraînement parallèle avec {max_workers} workers" + (" et accélération GPU" if use_gpu else ""))

    # Ajouter le paramètre use_gpu à chaque modèle
    for params in models_params:
        params['use_gpu'] = use_gpu
        params['n_jobs'] = 1  # Chaque processus utilise 1 cœur pour éviter la surcharge

    results = {}

    # Utiliser ProcessPoolExecutor pour la parallélisation avec une gestion d'erreur améliorée
    try:
        # Créer un contexte ProcessPoolExecutor avec le nombre de workers déterminé
        # Utiliser un nombre de workers adapté à la charge de travail
        pool_workers = max_workers  # Utiliser le nombre de workers déterminé précédemment
        print(f"Utilisation de {pool_workers} workers pour le pool de processus")

        # Ajouter un délai pour éviter les problèmes d'initialisation
        import time
        print("Initialisation du pool de processus...")
        time.sleep(1)  # Attendre 1 seconde avant de démarrer

        with ProcessPoolExecutor(max_workers=pool_workers) as executor:
            # Soumettre les tâches
            future_to_number = {}
            for params in models_params:
                try:
                    future = executor.submit(train_model_worker, params)
                    future_to_number[future] = params['number']
                except Exception as e:
                    print(f"Erreur lors de la soumission de la tâche pour le numéro {params['number']}: {e}")
                    results[params['number']] = {
                        'number': params['number'],
                        'success': False,
                        'message': f"Erreur de soumission: {str(e)}"
                    }

            # Traiter les résultats au fur et à mesure qu'ils sont disponibles
            for future in as_completed(future_to_number):
                # Vérifier si l'arrêt a été demandé
                if stop_flag is not None and hasattr(stop_flag, 'value') and stop_flag.value:
                    print("Arrêt de l'entraînement demandé par l'utilisateur")
                    # Annuler les futures en cours de manière sécurisée
                    for f in list(future_to_number.keys()):
                        if not f.done() and not f.cancelled():
                            try:
                                f.cancel()
                            except Exception:
                                pass  # Ignorer les erreurs lors de l'annulation
                    break

                number = future_to_number[future]
                try:
                    print(f"Attente du résultat pour le numéro {number}...")
                    # Utiliser un timeout plus court pour détecter les blocages plus rapidement
                    result = future.result(timeout=30)  # 30 secondes maximum
                    print(f"Résultat reçu pour le numéro {number}")
                    results[number] = result

                    # Afficher le progrès
                    if result['success']:
                        metrics = result['metrics']
                        print(f"Numéro {number}: {result['model_type']} - "
                              f"Précision: {metrics['accuracy']:.4f}, "
                              f"F1: {metrics['f1']:.4f}, "
                              f"AUC: {metrics['auc']:.4f}")
                    else:
                        print(f"Numéro {number}: Échec - {result.get('message', 'Erreur inconnue')}")

                except Exception as e:
                    print(f"Numéro {number}: Exception - {e}")
                    results[number] = {
                        'number': number,
                        'success': False,
                        'message': str(e)
                    }
    except Exception as e:
        print(f"Erreur globale lors de l'entraînement parallèle: {e}")
        # Assurer que tous les numéros ont un résultat
        for params in models_params:
            number = params['number']
            if number not in results:
                results[number] = {
                    'number': number,
                    'success': False,
                    'message': f"Erreur globale: {str(e)}"
                }

    # Calculer les statistiques globales
    success_count = sum(1 for r in results.values() if r['success'])
    total_count = len(results)
    success_rate = success_count / total_count if total_count > 0 else 0

    print(f"\nEntraînement terminé: {success_count}/{total_count} modèles entraînés avec succès ({success_rate:.1%})")

    return results

def analyze_patterns_parallel(draws, max_workers=None):
    """
    Analyse les motifs dans les tirages en parallèle

    Args:
        draws: Liste des tirages à analyser
        max_workers: Nombre maximum de workers à utiliser (None = auto)

    Returns:
        dict: Dictionnaire des motifs analysés
    """
    if max_workers is None:
        # Utiliser un nombre optimal de workers basé sur le nombre de cœurs disponibles
        available_cores = patched_count_physical_cores()
        # Utiliser jusqu'à 16 workers ou 100% des cœurs disponibles
        max_workers = min(16, available_cores)
        print(f"Utilisation de {max_workers} workers pour l'analyse des motifs (sur {available_cores} cœurs disponibles)")

    # Fonction pour analyser un sous-ensemble de tirages
    def analyze_subset(subset_draws, start_idx, end_idx):
        from collections import Counter, defaultdict

        # Initialiser les compteurs
        pair_counts = Counter()
        sequence_counts = Counter()
        follow_patterns = defaultdict(list)

        # Analyser les paires
        for draw in subset_draws:
            nums = draw.draw_numbers
            # Générer toutes les paires possibles
            pairs = [(min(nums[i], nums[j]), max(nums[i], nums[j]))
                    for i in range(len(nums))
                    for j in range(i+1, len(nums))]
            pair_counts.update(pairs)

        # Analyser les séquences
        for i in range(len(subset_draws) - 1):
            current_draw = set(subset_draws[i].draw_numbers)
            next_draw = set(subset_draws[i+1].draw_numbers)

            # Compter les numéros qui se répètent
            repeats = len(current_draw.intersection(next_draw))
            sequence_counts[repeats] += 1

            # Analyser les motifs de suivi
            for num in current_draw:
                follow_patterns[num].append(1 if num in next_draw else 0)

        return {
            'pair_counts': pair_counts,
            'sequence_counts': sequence_counts,
            'follow_patterns': follow_patterns,
            'range': (start_idx, end_idx)
        }

    # Diviser les tirages en sous-ensembles
    n_draws = len(draws)
    chunk_size = max(1, n_draws // max_workers)
    chunks = []

    for i in range(0, n_draws, chunk_size):
        end_idx = min(i + chunk_size, n_draws)
        if end_idx > i:  # S'assurer que le chunk n'est pas vide
            chunks.append((draws[i:end_idx], i, end_idx))

    # Traiter les chunks en parallèle
    results = []
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(analyze_subset, chunk[0], chunk[1], chunk[2]) for chunk in chunks]
        for future in as_completed(futures):
            try:
                results.append(future.result())
            except Exception as e:
                print(f"Erreur lors de l'analyse des motifs: {e}")

    # Fusionner les résultats
    merged_pair_counts = Counter()
    merged_sequence_counts = Counter()
    merged_follow_patterns = defaultdict(list)

    for result in results:
        merged_pair_counts.update(result['pair_counts'])
        merged_sequence_counts.update(result['sequence_counts'])

        for num, patterns in result['follow_patterns'].items():
            merged_follow_patterns[num].extend(patterns)

    # Calculer les probabilités de suivi
    follow_probabilities = {}
    for num, patterns in merged_follow_patterns.items():
        if patterns:
            follow_probabilities[num] = sum(patterns) / len(patterns)

    return {
        'common_pairs': merged_pair_counts.most_common(30),
        'sequence_counts': merged_sequence_counts,
        'follow_probabilities': follow_probabilities
    }

def analyze_series_parallel(draws, max_workers=None):
    """
    Analyse les séries temporelles dans les tirages en parallèle

    Args:
        draws: Liste des tirages à analyser
        max_workers: Nombre maximum de workers à utiliser (None = auto)

    Returns:
        dict: Dictionnaire des séries analysées
    """
    if max_workers is None:
        # Utiliser un nombre optimal de workers basé sur le nombre de cœurs disponibles
        available_cores = patched_count_physical_cores()
        # Utiliser jusqu'à 16 workers ou 100% des cœurs disponibles
        max_workers = min(16, available_cores)
        print(f"Utilisation de {max_workers} workers pour l'analyse des séries (sur {available_cores} cœurs disponibles)")

    # Fonction pour analyser les séries d'un numéro
    def analyze_number_series(number, all_draws):
        try:
            # Créer une série temporelle pour ce numéro
            time_series = []
            for i, draw in enumerate(all_draws):
                time_series.append(1 if number in draw.draw_numbers else 0)

            # Calculer les statistiques de base
            appearances = sum(time_series)
            if appearances == 0:
                return {
                    'number': number,
                    'appearances': 0,
                    'frequency': 0,
                    'longest_absence': 0,
                    'current_absence': 0,
                    'patterns': []
                }

            frequency = appearances / len(time_series)

            # Calculer les absences
            absences = []
            current_absence = 0
            for val in time_series:
                if val == 0:
                    current_absence += 1
                else:
                    if current_absence > 0:
                        absences.append(current_absence)
                    current_absence = 0

            # Ajouter l'absence en cours si elle existe
            if current_absence > 0:
                absences.append(current_absence)

            longest_absence = max(absences) if absences else 0

            # Calculer l'absence actuelle
            current_absence = 0
            for val in reversed(time_series):
                if val == 0:
                    current_absence += 1
                else:
                    break

            # Identifier les motifs
            patterns = []
            if len(time_series) >= 10:
                # Chercher des motifs de longueur 3 à 5
                for pattern_length in range(3, 6):
                    if len(time_series) >= pattern_length * 2:
                        pattern_counts = {}
                        for i in range(len(time_series) - pattern_length + 1):
                            pattern = tuple(time_series[i:i+pattern_length])
                            if pattern not in pattern_counts:
                                pattern_counts[pattern] = 0
                            pattern_counts[pattern] += 1

                        # Garder les motifs qui apparaissent au moins 3 fois
                        significant_patterns = {p: c for p, c in pattern_counts.items() if c >= 3}
                        if significant_patterns:
                            patterns.append({
                                'length': pattern_length,
                                'patterns': list(significant_patterns.items())
                            })

            return {
                'number': number,
                'appearances': appearances,
                'frequency': frequency,
                'longest_absence': longest_absence,
                'current_absence': current_absence,
                'patterns': patterns
            }

        except Exception as e:
            print(f"Erreur lors de l'analyse de la série pour le numéro {number}: {e}")
            return {
                'number': number,
                'error': str(e)
            }

    # Déterminer les numéros à analyser
    max_number = 70  # Pour le Keno

    # Traiter les numéros en parallèle
    results = {}
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {executor.submit(analyze_number_series, num, draws): num for num in range(1, max_number + 1)}
        for future in as_completed(futures):
            num = futures[future]
            try:
                results[num] = future.result()
            except Exception as e:
                print(f"Erreur lors de l'analyse de la série pour le numéro {num}: {e}")
                results[num] = {
                    'number': num,
                    'error': str(e)
                }

    return results

if __name__ == "__main__":
    # Test du module
    print("Module de traitement parallèle pour Keno")
    print(f"Nombre optimal de workers: {get_optimal_workers()}")
    print(f"CUDA disponible: {is_cuda_available()}")
    print(f"XGBoost disponible: {XGBOOST_AVAILABLE}")
    print(f"LightGBM disponible: {LIGHTGBM_AVAILABLE}")
