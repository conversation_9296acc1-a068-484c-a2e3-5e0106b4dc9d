#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de test pour vérifier que les fonctionnalités avancées de deep learning fonctionnent correctement
"""

import os
import sys
import time

# Ajouter le répertoire parent au chemin de recherche des modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Importer les modules nécessaires
try:
    from keno_deep_learning import KenoDeepLearning
    from keno_data import KenoDataManager
    from keno_advanced_analyzer import KenoAdvancedAnalyzer
    import numpy as np
    
    # Fonction principale de test
    def test_advanced_deep_learning():
        print("\n=== Test des fonctionnalités avancées de deep learning ===\n")
        
        # Créer un gestionnaire de données
        data_manager = KenoDataManager()
        
        # Charger les données
        data_file = "data/keno_data.csv"
        if os.path.exists(data_file):
            print(f"Chargement des données depuis {data_file}...")
            data_manager.load_from_csv(data_file)
        else:
            print(f"Fichier {data_file} introuvable. Utilisation de données de test...")
            # Créer des données de test
            import random
            from datetime import datetime, timedelta
            
            # Générer 1000 tirages aléatoires
            base_date = datetime.now() - timedelta(days=1000)
            for i in range(1000):
                draw_date = base_date + timedelta(days=i)
                draw_numbers = random.sample(range(1, 71), 20)
                data_manager.add_draw(draw_date, draw_numbers)
            
            print(f"Données de test générées: {len(data_manager.draws)} tirages")
        
        # Créer un analyseur avancé
        analyzer = KenoAdvancedAnalyzer(data_manager)
        
        # Vérifier si le deep learning est disponible
        if not hasattr(analyzer, 'deep_learning') or not analyzer.deep_learning:
            print("Le module de deep learning n'est pas disponible")
            return
        
        # Préparer les données
        print("Préparation des données...")
        analyzer.prepare_data()
        
        # Tester l'optimisation des hyperparamètres
        print("\n1. Test de l'optimisation des hyperparamètres...")
        
        # Créer des données de test
        X = np.random.rand(500, 10)
        y = np.random.randint(0, 2, 500)
        
        start_time = time.time()
        optimized = analyzer.deep_learning.optimize_hyperparameters(
            X, y, model_type='simple', n_trials=3
        )
        end_time = time.time()
        
        if optimized:
            print(f"Optimisation des hyperparamètres réussie en {end_time - start_time:.2f} secondes")
            print(f"Meilleurs hyperparamètres: {optimized['best_params']}")
            if 'model' in optimized:
                print(f"Précision du modèle optimisé: {optimized['model']['accuracy']:.4f}")
        else:
            print("Erreur lors de l'optimisation des hyperparamètres")
        
        # Tester la création d'un ensemble de modèles
        print("\n2. Test de la création d'un ensemble de modèles...")
        
        start_time = time.time()
        ensemble = analyzer.deep_learning.create_ensemble_model(
            X, y, n_models=3, model_type='simple'
        )
        end_time = time.time()
        
        if ensemble:
            print(f"Création de l'ensemble réussie en {end_time - start_time:.2f} secondes")
            print(f"Nombre de modèles dans l'ensemble: {ensemble['n_models']}")
            print(f"Précision de l'ensemble: {ensemble['accuracy']:.4f}")
            
            # Tester la prédiction avec l'ensemble
            print("\nTest de prédiction avec l'ensemble...")
            X_test = np.random.rand(5, 10)
            predictions = ensemble['model'].predict_proba(X_test)
            
            print("Prédictions:")
            for i, pred in enumerate(predictions):
                print(f"  Exemple {i+1}: {pred[1]:.4f}")
        else:
            print("Erreur lors de la création de l'ensemble")
        
        # Tester la visualisation des modèles
        print("\n3. Test de la visualisation des modèles...")
        
        if ensemble:
            start_time = time.time()
            viz_path = analyzer.deep_learning.visualize_model(
                ensemble['model'], X, y, num=1
            )
            end_time = time.time()
            
            if viz_path:
                print(f"Visualisation générée en {end_time - start_time:.2f} secondes")
                print(f"Visualisation sauvegardée dans {viz_path}")
            else:
                print("Erreur lors de la génération de la visualisation")
        
        print("\n=== Test terminé ===\n")
    
    # Exécuter le test si le script est exécuté directement
    if __name__ == "__main__":
        test_advanced_deep_learning()

except ImportError as e:
    print(f"Erreur d'importation: {e}")
except Exception as e:
    import traceback
    print(f"Erreur: {e}")
    traceback.print_exc()
