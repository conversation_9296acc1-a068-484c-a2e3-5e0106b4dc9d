"""
Script pour restaurer les fichiers de sauvegarde et revenir à une version fonctionnelle.
"""

import os
import glob
import shutil
from datetime import datetime

def list_backup_files():
    """
    Liste tous les fichiers de sauvegarde
    
    Returns:
        dict: Dictionnaire des fichiers de sauvegarde par fichier original
    """
    backup_files = {}
    
    # Rechercher tous les fichiers .bak
    for backup_file in glob.glob('*.bak.*'):
        # Extraire le nom du fichier original
        original_file = backup_file.split('.bak.')[0]
        
        if original_file not in backup_files:
            backup_files[original_file] = []
        
        backup_files[original_file].append(backup_file)
    
    # Trier les sauvegardes par date (la plus récente en premier)
    for original_file in backup_files:
        backup_files[original_file].sort(reverse=True)
    
    return backup_files

def restore_file(original_file, backup_file):
    """
    Restaure un fichier à partir d'une sauvegarde
    
    Args:
        original_file: Chemin du fichier original
        backup_file: Chemin de la sauvegarde
        
    Returns:
        bool: True si la restauration a réussi, False sinon
    """
    try:
        shutil.copy2(backup_file, original_file)
        print(f"Fichier {original_file} restauré à partir de {backup_file}")
        return True
    except Exception as e:
        print(f"Erreur lors de la restauration du fichier {original_file}: {e}")
        return False

def main():
    """Fonction principale"""
    print("Restauration des fichiers de sauvegarde")
    
    # Lister les fichiers de sauvegarde
    backup_files = list_backup_files()
    
    if not backup_files:
        print("Aucun fichier de sauvegarde trouvé")
        return
    
    print("Fichiers de sauvegarde trouvés:")
    for original_file, backups in backup_files.items():
        print(f"  {original_file}: {len(backups)} sauvegarde(s)")
        for i, backup in enumerate(backups):
            # Extraire la date de la sauvegarde
            date_str = backup.split('.bak.')[1]
            try:
                date = datetime.strptime(date_str, '%Y%m%d%H%M%S')
                date_formatted = date.strftime('%d/%m/%Y %H:%M:%S')
            except:
                date_formatted = date_str
            
            print(f"    {i+1}. {backup} ({date_formatted})")
    
    # Demander à l'utilisateur quels fichiers restaurer
    print("\nQuels fichiers souhaitez-vous restaurer?")
    print("1. Tous les fichiers (dernière sauvegarde)")
    print("2. Sélectionner des fichiers spécifiques")
    print("3. Annuler")
    
    choice = input("Votre choix (1-3): ")
    
    if choice == '1':
        # Restaurer tous les fichiers
        for original_file, backups in backup_files.items():
            if backups:
                restore_file(original_file, backups[0])
        
        print("\nTous les fichiers ont été restaurés à leur dernière sauvegarde")
    
    elif choice == '2':
        # Sélectionner des fichiers spécifiques
        for original_file, backups in backup_files.items():
            print(f"\nFichier: {original_file}")
            print("0. Ne pas restaurer ce fichier")
            for i, backup in enumerate(backups):
                date_str = backup.split('.bak.')[1]
                try:
                    date = datetime.strptime(date_str, '%Y%m%d%H%M%S')
                    date_formatted = date.strftime('%d/%m/%Y %H:%M:%S')
                except:
                    date_formatted = date_str
                
                print(f"{i+1}. Restaurer à partir de {backup} ({date_formatted})")
            
            file_choice = input(f"Votre choix (0-{len(backups)}): ")
            
            try:
                file_choice = int(file_choice)
                if 1 <= file_choice <= len(backups):
                    restore_file(original_file, backups[file_choice-1])
            except:
                print(f"Fichier {original_file} non restauré")
        
        print("\nLes fichiers sélectionnés ont été restaurés")
    
    else:
        print("\nRestauration annulée")

if __name__ == "__main__":
    main()
