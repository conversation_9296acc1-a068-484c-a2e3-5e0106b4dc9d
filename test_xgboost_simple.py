"""
Script de test simplifié pour l'optimiseur XGBoost
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_recall_fscore_support

# Importer l'optimiseur XGBoost
from keno_xgboost_optimizer_simple import XGBoostOptimizer

# Générer des données de test
np.random.seed(42)
n_samples = 1000
X = np.random.rand(n_samples, 10)
y = np.zeros(n_samples)
positive_indices = np.random.choice(n_samples, size=int(n_samples * 0.2), replace=False)
y[positive_indices] = 1

print(f"Données générées: {n_samples} échantillons, {sum(y)} positifs ({sum(y)/n_samples*100:.1f}%)")

# Créer l'optimiseur
optimizer = XGBoostOptimizer(use_gpu=False, n_jobs=-1, verbose=1)
print("Optimiseur XGBoost créé")

# Diviser les données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Entraîner un modèle avec l'optimiseur
print("\nEntraînement avec optimisation des hyperparamètres...")
result = optimizer.train_model(X_train, y_train, num=1, optimize=True, fast_mode=True)

# Évaluer le modèle
if result and 'model' in result:
    y_pred = result['model'].predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    precision, recall, f1, _ = precision_recall_fscore_support(
        y_test, y_pred, average='binary', zero_division=0
    )
    
    print(f"\nRésultats:")
    print(f"  - Précision: {accuracy:.4f}")
    print(f"  - F1-score: {f1:.4f}")
    print(f"  - Précision: {precision:.4f}")
    print(f"  - Rappel: {recall:.4f}")
    
    # Afficher les paramètres optimaux
    print(f"\nParamètres optimaux:")
    for key, value in result['params'].items():
        print(f"  - {key}: {value}")
else:
    print("Erreur: Échec de l'entraînement du modèle")
