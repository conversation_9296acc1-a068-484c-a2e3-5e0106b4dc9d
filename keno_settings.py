"""
Module pour la gestion des paramètres de l'application Keno
"""

import os
import json
import traceback

# Chemin du fichier de configuration
CONFIG_FILE = "keno_advanced_config.json"

def save_advanced_settings(advanced_analyzer):
    """
    Sauvegarde les paramètres avancés dans un fichier JSON
    
    Args:
        advanced_analyzer: Instance de KenoAdvancedAnalyzer contenant les paramètres
        
    Returns:
        bool: True si la sauvegarde a réussi, False sinon
    """
    try:
        # Créer le dictionnaire de configuration
        config = {
            'n_jobs': advanced_analyzer.n_jobs,
            'max_workers': advanced_analyzer.max_workers,
            'hardware_acceleration': advanced_analyzer.hardware_acceleration,
            'model_params': advanced_analyzer.model_params
        }
        
        # Sauvegarder dans le fichier
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4)
            
        print(f"Paramètres avancés sauvegardés dans {CONFIG_FILE}")
        return True
    except Exception as e:
        print(f"Erreur lors de la sauvegarde des paramètres avancés: {e}")
        traceback.print_exc()
        return False

def load_advanced_settings(advanced_analyzer):
    """
    Charge les paramètres avancés depuis un fichier JSON
    
    Args:
        advanced_analyzer: Instance de KenoAdvancedAnalyzer où charger les paramètres
        
    Returns:
        bool: True si le chargement a réussi, False sinon
    """
    try:
        # Vérifier si le fichier existe
        if not os.path.exists(CONFIG_FILE):
            print(f"Fichier de configuration {CONFIG_FILE} introuvable")
            return False
            
        # Charger le fichier
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        # Appliquer les paramètres
        if 'n_jobs' in config:
            advanced_analyzer.set_cpu_cores(config['n_jobs'])
            
        if 'max_workers' in config:
            advanced_analyzer.set_max_workers(config['max_workers'])
            
        if 'hardware_acceleration' in config:
            advanced_analyzer.set_hardware_acceleration(config['hardware_acceleration'])
            
        if 'model_params' in config:
            advanced_analyzer.model_params = config['model_params']
            
        print(f"Paramètres avancés chargés depuis {CONFIG_FILE}")
        return True
    except Exception as e:
        print(f"Erreur lors du chargement des paramètres avancés: {e}")
        traceback.print_exc()
        return False
