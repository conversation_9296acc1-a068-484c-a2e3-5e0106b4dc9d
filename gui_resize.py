#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module pour agrandir l'interface graphique de l'application Keno
"""

import tkinter as tk
from tkinter import ttk

def resize_gui(gui):
    """
    Agrandit l'interface graphique et les boutons

    Args:
        gui: Instance de KenoGUI
    """
    # Agrandir la fenêtre principale
    gui.root.geometry("1400x900")  # Taille plus grande
    gui.root.minsize(1200, 800)    # Taille minimale plus grande

    # Mettre à jour les styles pour des éléments plus grands
    style = ttk.Style()

    # Styles de base
    style.configure('TButton', padding=12, font=('Helvetica', 13))
    style.configure('TLabel', font=('Helvetica', 13))
    style.configure('TEntry', font=('Helvetica', 13))
    style.configure('TCombobox', font=('Helvetica', 13))
    style.configure('TSpinbox', font=('Helvetica', 13))
    style.configure('TC<PERSON><PERSON>button', font=('Helvetica', 13))
    style.configure('TRadiobutton', font=('Helvetica', 13))

    # Styles pour les cadres et titres
    style.configure('TLabelframe', font=('Helvetica', 14))
    style.configure('TLabelframe.Label', font=('Helvetica', 14, 'bold'))
    style.configure('TNotebook.Tab', font=('Helvetica', 13, 'bold'), padding=[15, 5])

    # Styles pour les boutons d'action
    style.configure('Action.TButton', padding=12, font=('Helvetica', 13, 'bold'))
    style.configure('Highlight.TButton', padding=12, font=('Helvetica', 13, 'bold'))
    style.configure('BigButton.TButton', padding=15, font=('Helvetica', 14, 'bold'))

    # Mettre à jour les mappages de couleurs
    style.map('Highlight.TButton',
              background=[('!active', '#27ae60'), ('active', '#2ecc71')],
              foreground=[('!active', 'white'), ('active', 'white')])
    style.map('BigButton.TButton',
              background=[('!active', '#3498db'), ('active', '#2980b9')],
              foreground=[('!active', 'white'), ('active', 'white')])

    print("Interface graphique agrandie avec succès")
    return True

def create_big_buttons(gui):
    """
    Crée des boutons plus grands pour les actions principales

    Args:
        gui: Instance de KenoGUI
    """
    # Créer un cadre pour les boutons d'action rapide
    action_frame = ttk.LabelFrame(gui.root, text="Actions rapides")
    action_frame.pack(fill=tk.X, padx=20, pady=10, before=gui.progress_bar)

    # Créer une grille de boutons
    button_frame = ttk.Frame(action_frame)
    button_frame.pack(fill=tk.X, padx=10, pady=10)

    # Vérifier les méthodes disponibles
    has_save_data = hasattr(gui, 'save_data')
    has_load_data = hasattr(gui, 'load_data')
    has_generate_prediction = hasattr(gui, 'generate_prediction')

    # Définir les boutons d'action rapide
    actions = [
        {"text": "Importer des données", "command": gui.browse_csv, "row": 0, "column": 0},
        {"text": "Générer prédiction", "command": lambda: gui.notebook.select(1), "row": 0, "column": 1},
        {"text": "Visualiser statistiques", "command": lambda: gui.notebook.select(2), "row": 0, "column": 2},
        {"text": "Auto-amélioration", "command": gui.show_auto_improve_dialog, "row": 1, "column": 0},
        {"text": "Sauvegarder BDD", "command": gui.save_database, "row": 1, "column": 1},
        {"text": "Charger BDD", "command": gui.load_database, "row": 1, "column": 2}
    ]

    # Créer les boutons
    for action in actions:
        btn = ttk.Button(button_frame, text=action["text"], command=action["command"], style="BigButton.TButton")
        btn.grid(row=action["row"], column=action["column"], padx=10, pady=10, sticky="nsew")

    # Configurer la grille pour que les boutons s'étendent
    for i in range(3):
        button_frame.columnconfigure(i, weight=1)
    for i in range(2):
        button_frame.rowconfigure(i, weight=1)

    # Ajouter une méthode pour afficher un onglet spécifique
    def show_tab(tab_name):
        for i in range(gui.notebook.index("end")):
            if gui.notebook.tab(i, "text") == tab_name:
                gui.notebook.select(i)
                break

    # Ajouter la méthode à l'instance de GUI
    gui.show_tab = show_tab

    print("Boutons d'action rapide ajoutés avec succès")
    return True

def load_css_style(gui):
    """
    Charge le fichier CSS de style si disponible

    Args:
        gui: Instance de KenoGUI
    """
    import os
    css_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "keno_style.css")

    if os.path.exists(css_path):
        try:
            # Essayer de charger le style CSS si tkinter le supporte
            style = ttk.Style()

            # Lire le fichier CSS
            with open(css_path, "r") as f:
                css_content = f.read()

            print(f"Fichier CSS chargé: {css_path}")
            return True
        except Exception as e:
            print(f"Erreur lors du chargement du fichier CSS: {e}")
    else:
        print(f"Fichier CSS non trouvé: {css_path}")

    return False

def apply_gui_enhancements(gui):
    """
    Applique toutes les améliorations à l'interface graphique

    Args:
        gui: Instance de KenoGUI
    """
    # Agrandir l'interface
    resize_gui(gui)

    # Ajouter des boutons d'action rapide
    create_big_buttons(gui)

    # Charger le style CSS
    load_css_style(gui)

    # Stocker une référence au notebook pour pouvoir y accéder
    for child in gui.root.winfo_children():
        if isinstance(child, ttk.Frame):
            for grandchild in child.winfo_children():
                if isinstance(grandchild, ttk.Notebook):
                    gui.notebook = grandchild
                    break

    print("Améliorations de l'interface graphique appliquées avec succès")
    return True
