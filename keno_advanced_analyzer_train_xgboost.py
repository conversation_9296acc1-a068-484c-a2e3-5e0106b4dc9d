    def train_xgboost_model(self, num, X, y, feature_cols, random_state=42, optimize=True, fast_mode=True):
        """Entraîne un modèle XGBoost optimisé pour un numéro spécifique
        
        Args:
            num (int): Numéro Keno
            X: Caractéristiques
            y: Étiquettes
            feature_cols (list): Noms des caractéristiques
            random_state (int): Graine aléatoire
            optimize (bool): Si True, optimise les hyperparamètres
            fast_mode (bool): Si True, utilise une optimisation plus rapide
            
        Returns:
            dict: Informations sur le modèle entraîné
        """
        # Vérifier si l'optimiseur XGBoost est disponible
        if not self.xgboost_optimizer_available or not self.xgboost_optimizer:
            print(f"Optimiseur XGBoost non disponible pour le numéro {num}")
            return None
        
        try:
            print(f"Entraînement du modèle XGBoost optimisé pour le numéro {num}")
            
            # Entraîner le modèle avec l'optimiseur
            result = self.xgboost_optimizer.train_model(
                X, y, num, 
                params=None,  # Utiliser les paramètres optimaux ou par défaut
                random_state=random_state,
                optimize=optimize,
                fast_mode=fast_mode
            )
            
            # Analyser l'importance des caractéristiques
            if feature_cols and 'model' in result:
                feature_importance = self.xgboost_optimizer.analyze_feature_importance(
                    result['model'], feature_cols
                )
                
                if feature_importance:
                    # Afficher les 5 caractéristiques les plus importantes
                    print(f"Caractéristiques les plus importantes pour le numéro {num}:")
                    for feature, importance in feature_importance[:5]:
                        print(f"  - {feature}: {importance:.4f}")
                    
                    # Stocker l'importance des caractéristiques
                    result['feature_importance_details'] = feature_importance
            
            return result
        except Exception as e:
            print(f"Erreur lors de l'entraînement du modèle XGBoost pour le numéro {num}: {e}")
            import traceback
            traceback.print_exc()
            return None
