#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script pour lancer l'application Keno avec les améliorations
Ce script applique les améliorations de prédiction avant de lancer l'application.
"""

import os
import sys
import time
import json
import argparse

# Ajouter le répertoire courant au chemin de recherche Python
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, BASE_DIR)

# Supprimer les avertissements
import warnings
warnings.filterwarnings('ignore')

# Supprimer les avertissements de TensorFlow
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR

def check_dependencies():
    """Vérifie que toutes les dépendances sont installées"""
    print("Vérification des dépendances...")
    
    dependencies = {
        'numpy': False,
        'pandas': False,
        'matplotlib': False,
        'scikit-learn': False,
        'tensorflow': False,
        'xgboost': False,
        'lightgbm': False,
        'joblib': False,
        'tqdm': False
    }
    
    # Vérifier chaque dépendance
    try:
        import numpy
        dependencies['numpy'] = True
    except ImportError:
        pass
    
    try:
        import pandas
        dependencies['pandas'] = True
    except ImportError:
        pass
    
    try:
        import matplotlib
        dependencies['matplotlib'] = True
    except ImportError:
        pass
    
    try:
        import sklearn
        dependencies['scikit-learn'] = True
    except ImportError:
        pass
    
    try:
        import tensorflow
        dependencies['tensorflow'] = True
    except ImportError:
        pass
    
    try:
        import xgboost
        dependencies['xgboost'] = True
    except ImportError:
        pass
    
    try:
        import lightgbm
        dependencies['lightgbm'] = True
    except ImportError:
        pass
    
    try:
        import joblib
        dependencies['joblib'] = True
    except ImportError:
        pass
    
    try:
        import tqdm
        dependencies['tqdm'] = True
    except ImportError:
        pass
    
    # Afficher les résultats
    print("\nStatut des dépendances:")
    for dep, installed in dependencies.items():
        status = "✓ Installé" if installed else "✗ Manquant"
        print(f"  {dep}: {status}")
    
    # Vérifier si toutes les dépendances sont installées
    all_installed = all(dependencies.values())
    if not all_installed:
        print("\nCertaines dépendances sont manquantes. Voulez-vous les installer? (o/n)")
        choice = input().lower()
        if choice == 'o' or choice == 'oui':
            install_dependencies([dep for dep, installed in dependencies.items() if not installed])
    
    return all_installed

def install_dependencies(missing_deps):
    """Installe les dépendances manquantes"""
    print(f"Installation des dépendances manquantes: {', '.join(missing_deps)}")
    
    import subprocess
    
    # Installer chaque dépendance
    for dep in missing_deps:
        print(f"Installation de {dep}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"  {dep} installé avec succès")
        except subprocess.CalledProcessError:
            print(f"  Erreur lors de l'installation de {dep}")

def apply_improvements(args):
    """Applique les améliorations de prédiction"""
    print("Application des améliorations de prédiction...")
    
    # Analyser les patterns sur 48 heures
    if args.patterns:
        print("\nAnalyse des patterns sur 48 heures...")
        from analyze_48h_patterns import analyze_48h_patterns
        analyze_48h_patterns()
    
    # Calculer les poids des numéros
    if args.weights:
        print("\nCalcul des poids des numéros...")
        from optimize_number_weights import calculate_combined_weights, optimize_scale_pos_weight_with_weights
        calculate_combined_weights()
        optimize_scale_pos_weight_with_weights()
    
    # Optimiser les paramètres XGBoost
    if args.xgboost:
        print("\nOptimisation des paramètres XGBoost...")
        from improve_predictions import optimize_xgboost_params
        optimize_xgboost_params()
    
    # Exécuter l'auto-amélioration
    if args.auto_improve:
        print("\nExécution de l'auto-amélioration...")
        from improve_predictions import run_auto_improve
        run_auto_improve(timeout=args.timeout, fast_mode=args.fast, ultra_fast=args.ultra_fast)
    
    print("\nAméliorations appliquées avec succès")

def launch_app():
    """Lance l'application Keno"""
    print("\nLancement de l'application Keno...")
    
    # Importer le module principal
    from main import main
    
    # Lancer l'application
    main()

def parse_arguments():
    """Parse les arguments de la ligne de commande"""
    parser = argparse.ArgumentParser(description="Lance l'application Keno avec des améliorations de prédiction")
    
    parser.add_argument('--patterns', action='store_true', help="Analyser les patterns sur 48 heures")
    parser.add_argument('--weights', action='store_true', help="Calculer les poids des numéros")
    parser.add_argument('--xgboost', action='store_true', help="Optimiser les paramètres XGBoost")
    parser.add_argument('--auto-improve', action='store_true', help="Exécuter l'auto-amélioration")
    parser.add_argument('--all', action='store_true', help="Appliquer toutes les améliorations")
    parser.add_argument('--timeout', type=int, default=7200, help="Timeout pour l'auto-amélioration (en secondes)")
    parser.add_argument('--fast', action='store_true', help="Utiliser le mode rapide pour l'auto-amélioration")
    parser.add_argument('--ultra-fast', action='store_true', help="Utiliser le mode ultra-rapide pour l'auto-amélioration")
    
    args = parser.parse_args()
    
    # Si --all est spécifié, activer toutes les améliorations
    if args.all:
        args.patterns = True
        args.weights = True
        args.xgboost = True
        args.auto_improve = True
    
    return args

if __name__ == "__main__":
    # Parser les arguments
    args = parse_arguments()
    
    # Vérifier les dépendances
    check_dependencies()
    
    # Appliquer les améliorations
    if args.patterns or args.weights or args.xgboost or args.auto_improve:
        apply_improvements(args)
    
    # Lancer l'application
    launch_app()
