"""
Script pour corriger l'erreur d'indentation dans keno_xgboost_optimizer.py
"""

import os
import sys
import json
import shutil
from datetime import datetime

def backup_file(file_path):
    """Crée une sauvegarde du fichier"""
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def fix_file(file_path):
    """Corrige l'erreur d'indentation dans le fichier"""
    # Créer une sauvegarde
    backup_file(file_path)
    
    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Chemin du fichier de valeurs
    values_file = os.path.join('data', 'fixed_spw_values.json')
    
    # Générer des valeurs aléatoires
    import random
    values = {}
    for num in range(1, 71):
        # Générer une valeur entre 1.5 et 3.5
        value = 1.5 + random.random() * 2.0
        values[num] = round(value, 4)
    
    # Convertir les clés en chaînes pour la sérialisation JSON
    values_str = {str(k): v for k, v in values.items()}
    
    # Sauvegarder les valeurs
    with open(values_file, 'w') as f:
        json.dump(values_str, f, indent=2)
    
    print(f"Valeurs de scale_pos_weight sauvegardées dans {values_file}")
    
    # Modifier la méthode calculate_optimal_scale_pos_weight
    new_method = """    def calculate_optimal_scale_pos_weight(self, y_train, num=None):
        \"\"\"
        Calcule la valeur optimale de scale_pos_weight basée sur la distribution des classes
        
        Args:
            y_train: Étiquettes d'entraînement
            num: Numéro Keno (pour utiliser une valeur spécifique)
            
        Returns:
            float: Valeur optimale de scale_pos_weight
        \"\"\"
        # Correction pour le problème de scale_pos_weight identique
        # Charger les valeurs depuis le fichier JSON
        spw_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'fixed_spw_values.json')
        try:
            with open(spw_file, 'r') as f:
                spw_values = json.load(f)
            
            # Si un numéro est spécifié, utiliser la valeur spécifique
            if num is not None and str(num) in spw_values:
                specific_value = float(spw_values[str(num)])
                
                if self.verbose > 0:
                    print(f"  Numéro {num}: Utilisation de scale_pos_weight spécifique = {specific_value}")
                
                return specific_value
        except Exception as e:
            if self.verbose > 0:
                print(f"  Erreur lors du chargement des valeurs de scale_pos_weight: {e}")
        
        # Sinon, calculer normalement
        # Compter les exemples positifs et négatifs
        neg_count = np.sum(y_train == 0)
        pos_count = np.sum(y_train == 1)
        
        # Éviter la division par zéro
        if pos_count > 0:
            # Calculer le ratio
            ratio = neg_count / pos_count
            
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
                print(f"  Numéro {num}: Ratio calculé = {ratio:.4f}")
            
            return ratio
        else:
            # Valeur par défaut si aucun exemple positif
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Aucun exemple positif trouvé, utilisation de scale_pos_weight=1.0")
            
            return 1.0
"""
    
    # Trouver la méthode calculate_optimal_scale_pos_weight
    start_line = -1
    end_line = -1
    
    for i, line in enumerate(lines):
        if "def calculate_optimal_scale_pos_weight" in line:
            start_line = i
        elif start_line != -1 and i > start_line and line.strip().startswith("def "):
            end_line = i
            break
    
    if start_line == -1:
        print("Méthode calculate_optimal_scale_pos_weight non trouvée")
        return False
    
    if end_line == -1:
        end_line = len(lines)
    
    # Remplacer la méthode
    new_lines = lines[:start_line] + [new_method + "\n"] + lines[end_line:]
    
    # Écrire le contenu modifié
    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(new_lines)
    
    print(f"Fichier {file_path} corrigé avec succès")
    return True

def main():
    """Fonction principale"""
    print("Correction de l'erreur d'indentation dans keno_xgboost_optimizer.py")
    
    # Corriger le fichier
    success = fix_file('keno_xgboost_optimizer.py')
    
    if success:
        print("\nCorrection terminée!")
        print("L'erreur d'indentation a été corrigée.")
        print("Veuillez redémarrer votre application pour que les modifications prennent effet.")
    else:
        print("\nÉchec de la correction")
        print("Veuillez vérifier manuellement le fichier et appliquer les modifications nécessaires.")

if __name__ == "__main__":
    main()
