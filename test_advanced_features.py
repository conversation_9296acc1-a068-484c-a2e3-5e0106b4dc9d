#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de test pour vérifier que les fonctionnalités avancées fonctionnent correctement
"""

import os
import sys
import time
from datetime import datetime, timedelta
import random

# Ajouter le répertoire parent au chemin de recherche des modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Importer les modules nécessaires
try:
    from keno_data import KenoDataManager, KenoDrawData
    from keno_advanced_analyzer import KenoAdvancedAnalyzer
    import numpy as np

    # Fonction principale de test
    def test_advanced_features():
        print("\n=== Test des fonctionnalités avancées ===\n")

        # Créer un gestionnaire de données
        data_manager = KenoDataManager()

        # Charger les données
        data_file = "data/keno_data.csv"
        if os.path.exists(data_file):
            print(f"Chargement des données depuis {data_file}...")
            data_manager.load_from_csv(data_file)
        else:
            print(f"Fichier {data_file} introuvable. Utilisation de données de test...")
            # Créer des données de test
            base_date = datetime.now() - timedelta(days=1000)
            for i in range(1000):
                draw_date = base_date + timedelta(days=i)
                draw_numbers = random.sample(range(1, 71), 20)
                data_manager.add_draw(draw_date, draw_numbers)

            print(f"Données de test générées: {len(data_manager.draws)} tirages")

        # Créer un analyseur avancé
        analyzer = KenoAdvancedAnalyzer(data_manager)

        # Configurer l'accélération matérielle
        print("\nConfiguration de l'accélération matérielle...")
        hardware_info = analyzer.set_hardware_acceleration('auto')
        print(f"Accélération matérielle: {hardware_info}")

        # Préparer les données
        print("\nPréparation des données...")
        analyzer.prepare_data()

        # Tester la détection d'anomalies
        print("\n1. Test de la détection d'anomalies...")
        if analyzer.anomaly_detector_available:
            start_time = time.time()
            anomalies = analyzer.detect_anomalies()
            end_time = time.time()

            if anomalies is not None:
                print(f"Détection d'anomalies réussie en {end_time - start_time:.2f} secondes")
                print(f"Nombre d'anomalies détectées: {len(anomalies)}")
            else:
                print("Erreur lors de la détection d'anomalies")
        else:
            print("Module de détection d'anomalies non disponible")

        # Tester l'analyse de séquences temporelles
        print("\n2. Test de l'analyse de séquences temporelles...")
        if analyzer.time_series_available:
            start_time = time.time()
            results = analyzer.analyze_time_series(resample_freq='W')  # Analyse hebdomadaire
            end_time = time.time()

            if results is not None:
                print(f"Analyse de séquences temporelles réussie en {end_time - start_time:.2f} secondes")
                print(f"Nombre de numéros analysés: {len(results)}")

                # Tester les prévisions
                print("\nTest des prévisions de séquences temporelles...")
                hot_numbers = analyzer.time_series_analyzer.get_hot_numbers()
                cold_numbers = analyzer.time_series_analyzer.get_cold_numbers()

                if hot_numbers:
                    print("\nNuméros 'chauds' (en hausse):")
                    for num, score in hot_numbers[:5]:
                        print(f"  Numéro {num}: Score {score:.4f}")

                if cold_numbers:
                    print("\nNuméros 'froids' (en baisse):")
                    for num, score in cold_numbers[:5]:
                        print(f"  Numéro {num}: Score {score:.4f}")
            else:
                print("Erreur lors de l'analyse de séquences temporelles")
        else:
            print("Module d'analyse de séquences temporelles non disponible")

        # Tester la prédiction avec intervalles de confiance
        print("\n3. Test de la prédiction avec intervalles de confiance...")
        if analyzer.confidence_estimator_available:
            start_time = time.time()
            predictions = analyzer.predict_with_confidence(num_predictions=10)
            end_time = time.time()

            if predictions is not None:
                print(f"Prédiction avec intervalles de confiance réussie en {end_time - start_time:.2f} secondes")
                print(f"Nombre de numéros prédits: {len(predictions)}")
            else:
                print("Erreur lors de la prédiction avec intervalles de confiance")
        else:
            print("Module d'estimation d'intervalles de confiance non disponible")

        # Tester le deep learning avancé
        print("\n4. Test du deep learning avancé...")
        if analyzer.deep_learning_available:
            # Préparer les données pour le deep learning
            print("Préparation des données pour le deep learning...")
            X = np.random.rand(500, 10)
            y = np.random.randint(0, 2, 500)

            # Tester l'optimisation des hyperparamètres
            print("\nTest de l'optimisation des hyperparamètres...")
            start_time = time.time()
            optimized = analyzer.deep_learning.optimize_hyperparameters(
                X, y, model_type='simple', n_trials=3
            )
            end_time = time.time()

            if optimized:
                print(f"Optimisation des hyperparamètres réussie en {end_time - start_time:.2f} secondes")
                print(f"Meilleurs hyperparamètres: {optimized['best_params']}")
                if 'model' in optimized:
                    print(f"Précision du modèle optimisé: {optimized['model']['accuracy']:.4f}")
                if 'score' in optimized:
                    print(f"Score du modèle optimisé: {optimized['score']:.4f}")
            else:
                print("Erreur lors de l'optimisation des hyperparamètres")

            # Tester le transfert d'apprentissage
            print("\nTest du transfert d'apprentissage...")
            if optimized and 'model' in optimized:
                start_time = time.time()
                transfer_model = analyzer.deep_learning.train_transfer_model(
                    optimized['model'], X, y
                )
                end_time = time.time()

                if transfer_model:
                    print(f"Transfert d'apprentissage réussi en {end_time - start_time:.2f} secondes")
                    print(f"Précision du modèle de transfert: {transfer_model['accuracy']:.4f}")
                else:
                    print("Erreur lors du transfert d'apprentissage")
        else:
            print("Module de deep learning non disponible")

        print("\n=== Test terminé ===\n")

    # Exécuter le test si le script est exécuté directement
    if __name__ == "__main__":
        test_advanced_features()

except ImportError as e:
    print(f"Erreur d'importation: {e}")
except Exception as e:
    import traceback
    print(f"Erreur: {e}")
    traceback.print_exc()
