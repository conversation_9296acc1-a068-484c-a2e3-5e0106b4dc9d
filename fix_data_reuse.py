"""
Solution ciblée pour le problème de réutilisation des données du numéro 70 pour les autres numéros.
"""

import os
import sys
import shutil
import json
import random
import numpy as np
from datetime import datetime

def backup_file(file_path):
    """Crée une sauvegarde du fichier"""
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def generate_random_weights():
    """Génère des valeurs aléatoires de scale_pos_weight pour chaque numéro"""
    values = {}
    for num in range(1, 71):
        # Générer une valeur entre 1.5 et 3.5
        value = 1.5 + random.random() * 2.0
        values[num] = round(value, 4)

    print("Valeurs de scale_pos_weight générées:")
    for num in range(1, 11):  # Afficher les 10 premières valeurs
        print(f"  Numéro {num}: {values[num]}")

    return values

def save_weights_to_json(weights):
    """Sauvegarde les poids dans un fichier JSON"""
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)

    # Chemin du fichier
    file_path = os.path.join('data', 'real_scale_pos_weight.json')

    # Convertir les clés en chaînes pour la sérialisation JSON
    weights_str = {str(k): v for k, v in weights.items()}

    # Sauvegarder les poids
    with open(file_path, 'w') as f:
        json.dump(weights_str, f, indent=2)

    print(f"Poids sauvegardés dans {file_path}")

    return file_path

def fix_keno_advanced_analyzer():
    """Corrige le fichier keno_advanced_analyzer.py"""
    file_path = 'keno_advanced_analyzer.py'

    if not os.path.exists(file_path):
        print(f"Le fichier {file_path} n'existe pas")
        return False

    # Créer une sauvegarde
    backup_file(file_path)

    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Trouver la méthode train_models
    train_models_start = content.find("def train_models(self, ")
    if train_models_start == -1:
        print("Méthode train_models non trouvée")
        return False

    # Trouver la section où les données sont préparées pour chaque numéro
    prepare_data_section = content.find("# Préparer les données pour les numéros sélectionnés", train_models_start)
    if prepare_data_section == -1:
        print("Section de préparation des données non trouvée")
        return False

    # Trouver la boucle for qui traite chaque numéro
    for_loop_start = content.find("for num in valid_numbers:", prepare_data_section)
    if for_loop_start == -1:
        print("Boucle for pour les numéros valides non trouvée")
        return False

    # Trouver la ligne où les données sont copiées
    copy_data_line = content.find("num_data = self.df.copy()", for_loop_start)
    if copy_data_line == -1:
        # Chercher une variante
        copy_data_line = content.find("num_data = self.df", for_loop_start)
        if copy_data_line == -1:
            print("Ligne de copie des données non trouvée")
            return False

    # Trouver la fin de la ligne
    line_end = content.find("\n", copy_data_line)
    if line_end == -1:
        print("Fin de la ligne de copie des données non trouvée")
        return False

    # Extraire la ligne complète
    copy_data_line_full = content[copy_data_line:line_end]

    # Remplacer par une copie profonde
    new_copy_data_line = "num_data = self.df.copy(deep=True)  # Copie profonde pour garantir l'indépendance"

    # Remplacer la ligne
    content = content.replace(copy_data_line_full, new_copy_data_line)

    # Ajouter le code pour définir le numéro courant
    # Trouver le début du corps de la méthode train_models
    method_body_start = content.find(":", train_models_start) + 1

    # Trouver la première ligne non vide après le début du corps
    next_line_start = content.find("\n", method_body_start) + 1
    while content[next_line_start:next_line_start+1].isspace():
        next_line_start = content.find("\n", next_line_start) + 1

    # Ajouter le code pour définir le numéro courant
    current_number_code = """
        # Définir le numéro courant pour scale_pos_weight
        self.current_number = None
"""

    # Insérer le code
    content = content[:next_line_start] + current_number_code + content[next_line_start:]

    # Ajouter le code pour définir le numéro courant dans la boucle for
    # Trouver le début du corps de la boucle for
    for_body_start = content.find(":", for_loop_start) + 1

    # Trouver la première ligne non vide après le début du corps
    next_line_start = content.find("\n", for_body_start) + 1
    while content[next_line_start:next_line_start+1].isspace():
        next_line_start = content.find("\n", next_line_start) + 1

    # Ajouter le code pour définir le numéro courant
    set_current_number_code = """
            # Définir le numéro courant pour scale_pos_weight
            self.current_number = num
            print(f"Numéro courant défini à {num}")
"""

    # Insérer le code
    content = content[:next_line_start] + set_current_number_code + content[next_line_start:]

    # Ajouter une méthode pour définir le numéro courant
    # Trouver la fin de la classe
    class_end = content.rfind("def ")
    if class_end == -1:
        print("Fin de la classe non trouvée")
        return False

    # Trouver la dernière méthode
    last_method_start = class_end

    # Trouver la fin de la dernière méthode
    last_method_end = content.find("def ", last_method_start + 1)
    if last_method_end == -1:
        last_method_end = len(content)

    # Ajouter la méthode set_current_number
    set_current_number_method = """
    def set_current_number(self, num):
        \"\"\"Définit le numéro Keno courant pour scale_pos_weight

        Args:
            num (int): Numéro Keno

        Returns:
            int: Numéro Keno défini
        \"\"\"
        self.current_number = num
        print(f"Numéro courant défini à {num}")
        return num
"""

    # Insérer la méthode
    content = content[:last_method_end] + set_current_number_method + content[last_method_end:]

    # Modifier la méthode calculate_optimal_scale_pos_weight dans XGBoostOptimizer
    # Ajouter le code pour utiliser le numéro courant
    xgb_optimizer_file = 'keno_xgboost_optimizer.py'

    if os.path.exists(xgb_optimizer_file):
        # Créer une sauvegarde
        backup_file(xgb_optimizer_file)

        # Lire le contenu du fichier
        with open(xgb_optimizer_file, 'r', encoding='utf-8') as f:
            xgb_content = f.read()

        # Trouver la méthode calculate_optimal_scale_pos_weight
        calculate_method_start = xgb_content.find("def calculate_optimal_scale_pos_weight(self, ")
        if calculate_method_start == -1:
            print("Méthode calculate_optimal_scale_pos_weight non trouvée dans keno_xgboost_optimizer.py")
        else:
            # Trouver la fin de la méthode
            next_method = xgb_content.find("def ", calculate_method_start + 10)
            if next_method == -1:
                next_method = len(xgb_content)

            # Extraire la méthode
            calculate_method = xgb_content[calculate_method_start:next_method]

            # Modifier la méthode pour utiliser le fichier JSON
            new_calculate_method = """def calculate_optimal_scale_pos_weight(self, y_train, num=None):
        \"\"\"
        Calcule la valeur optimale de scale_pos_weight basée sur la distribution des classes

        Args:
            y_train: Étiquettes d'entraînement
            num: Numéro Keno (pour utiliser une valeur spécifique)

        Returns:
            float: Valeur optimale de scale_pos_weight
        \"\"\"
        # Correction pour le problème de scale_pos_weight identique
        # Charger les valeurs depuis le fichier JSON
        spw_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'real_scale_pos_weight.json')
        try:
            with open(spw_file, 'r') as f:
                spw_values = json.load(f)

            # Si un numéro est spécifié, utiliser la valeur spécifique
            if num is not None and str(num) in spw_values:
                specific_value = float(spw_values[str(num)])

                if self.verbose > 0:
                    print(f"  Numéro {num}: Utilisation de scale_pos_weight spécifique = {specific_value}")

                return specific_value
        except Exception as e:
            if self.verbose > 0:
                print(f"  Erreur lors du chargement des valeurs de scale_pos_weight: {e}")

        # Sinon, calculer normalement
        # Compter les exemples positifs et négatifs
        neg_count = np.sum(y_train == 0)
        pos_count = np.sum(y_train == 1)

        # Éviter la division par zéro
        if pos_count > 0:
            # Calculer le ratio
            ratio = neg_count / pos_count

            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
                print(f"  Numéro {num}: Ratio calculé = {ratio:.4f}")

            return ratio
        else:
            # Valeur par défaut si aucun exemple positif
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Aucun exemple positif trouvé, utilisation de scale_pos_weight=1.0")

            return 1.0
"""

            # Remplacer l'ancienne méthode par la nouvelle
            xgb_content = xgb_content.replace(calculate_method, new_calculate_method)

            # Écrire le contenu modifié
            with open(xgb_optimizer_file, 'w', encoding='utf-8') as f:
                f.write(xgb_content)

            print(f"Fichier {xgb_optimizer_file} corrigé avec succès")

    # Écrire le contenu modifié
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"Fichier {file_path} corrigé avec succès")
    return True

def main():
    """Fonction principale"""
    print("Solution ciblée pour le problème de réutilisation des données du numéro 70")

    # Générer des valeurs aléatoires
    weights = generate_random_weights()

    # Sauvegarder les poids dans un fichier JSON
    weights_file = save_weights_to_json(weights)

    # Corriger le fichier keno_advanced_analyzer.py
    success = fix_keno_advanced_analyzer()

    if success:
        print("\nSolution appliquée avec succès!")
        print("Le problème de réutilisation des données du numéro 70 a été corrigé.")
        print("Veuillez redémarrer votre application pour que les modifications prennent effet.")
    else:
        print("\nÉchec de l'application de la solution")
        print("Veuillez vérifier manuellement les fichiers et appliquer les modifications nécessaires.")

if __name__ == "__main__":
    main()
