#!/usr/bin/env python
# -*- coding: utf-8 -*-

# Documentation string removed

import os
import sys
import time
import json
import pickle
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from sklearn.model_selection import train_test_split, GridSearchCV, RandomizedSearchCV
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.feature_selection import SelectFromModel
import joblib
import matplotlib.pyplot as plt
from tqdm import tqdm
import warnings

# Ignorer les avertissements
warnings.filterwarnings('ignore')

# Importer les classes nécessaires depuis les modules existants
from keno_data import KenoDataManager, KenoDrawData
try:
    from keno_advanced_analyzer import KenoAdvancedAnalyzer
except ImportError:
    print("Module d'analyse avancée non disponible. Création d'une version simplifiée...")

    class KenoAdvancedAnalyzer:
        # Documentation string removed
        def __init__(self, data_manager):
            self.data_manager = data_manager
            self.df = None
            self.models = None
            self.patterns = None
            self.series = None

        def prepare_data(self):
            # Documentation string removed
            if not self.data_manager.draws or len(self.data_manager.draws) < 10:
                print("Pas assez de données pour l'analyse")
                return None

            # Créer un DataFrame avec les tirages
            data = []
            for draw in self.data_manager.draws:
                row = {
                    'date': draw.draw_date,
                    'id': draw.draw_id
                }

                # Ajouter les numéros tirés
                for num in range(1, self.data_manager.max_number + 1):
                    row[f'num_{num}'] = 1 if num in draw.draw_numbers else 0

                data.append(row)

            self.df = pd.DataFrame(data)
            self.df.sort_values('date', inplace=True)
            self.df.reset_index(drop=True, inplace=True)

            # Ajouter des caractéristiques temporelles
            self.df['day_of_week'] = self.df['date'].dt.dayofweek
            self.df['month'] = self.df['date'].dt.month
            self.df['year'] = self.df['date'].dt.year
            self.df['hour'] = self.df['date'].dt.hour

            # Ajouter des caractéristiques de séquence
            for num in range(1, self.data_manager.max_number + 1):
                col = f'num_{num}'
                # Nombre de fois où le numéro est apparu dans les 5, 10, 20 derniers tirages
                self.df[f'{col}_last5'] = self.df[col].rolling(5, min_periods=1).sum().fillna(0)
                self.df[f'{col}_last10'] = self.df[col].rolling(10, min_periods=1).sum().fillna(0)
                self.df[f'{col}_last20'] = self.df[col].rolling(20, min_periods=1).sum().fillna(0)

                # Jours depuis la dernière apparition
                self.df[f'{col}_days_since'] = self.df[col].replace(0, np.nan).groupby(self.df[col].cumsum()).cumcount()
                self.df[f'{col}_days_since'] = self.df[f'{col}_days_since'].fillna(0)

            return self.df

        def train_models(self, test_size=0.2, random_state=42):
            # Documentation string removed
            if self.df is None:
                self.prepare_data()

            if self.df is None or self.df.empty or len(self.df) < 50:
                print("Pas assez de données pour entraîner les modèles (minimum 50 tirages requis)")
                return False

            # Créer des caractéristiques avancées pour chaque numéro
            features = {}
            targets = {}

            print("Génération de caractéristiques avancées...")

            # Pour chaque numéro possible
            for num in range(1, self.data_manager.max_number + 1):
                target_col = f'num_{num}'

                # Vérifier si le numéro apparaît suffisamment dans les données
                if target_col not in self.df.columns or self.df[target_col].sum() < 10:
                    continue

                # Caractéristiques de base pour ce numéro
                feature_cols = [
                    f'{target_col}_last5', f'{target_col}_last10', f'{target_col}_last20',
                    f'{target_col}_days_since', 'day_of_week', 'month', 'hour',
                    'day_sin', 'day_cos', 'month_sin', 'month_cos', 'hour_sin', 'hour_cos'
                ]

                # Ajouter des caractéristiques de fréquence
                feature_cols.extend([
                    f'{target_col}_freq10', f'{target_col}_freq20', f'{target_col}_freq50',
                    f'{target_col}_streak', f'{target_col}_gap_mean', f'{target_col}_gap_std'
                ])

                # Ajouter des caractéristiques basées sur d'autres numéros
                # Sélectionner les numéros les plus corrélés
                correlated_nums = self._find_correlated_numbers(num, top_n=10)

                for other_num in correlated_nums:
                    other_col = f'num_{other_num}'
                    # Corrélation avec d'autres numéros récents
                    feature_cols.append(f'{other_col}_last5')
                    feature_cols.append(f'{other_col}_freq20')

                # Ajouter des caractéristiques de groupe
                feature_cols.extend([
                    f'group_{num//10}_freq', f'group_{num//10}_last5',
                    f'even_count', f'odd_count', f'high_count', f'low_count'
                ])

                # Ajouter des caractéristiques de tendance
                feature_cols.extend([
                    f'{target_col}_trend5', f'{target_col}_trend10', f'{target_col}_trend20',
                    'total_distinct', 'avg_value', 'std_value'
                ])

                # Préparer les données
                # Filtrer les colonnes qui existent réellement dans le DataFrame
                valid_cols = [col for col in feature_cols if col in self.df.columns]

                # S'assurer qu'il y a suffisamment de caractéristiques
                if len(valid_cols) < 5:
                    continue

                X = self.df[valid_cols].iloc[20:].copy()  # Ignorer les 20 premières lignes pour avoir des caractéristiques complètes
                y = self.df[target_col].iloc[20:].copy()

                # Diviser en ensembles d'entraînement et de test
                X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state)

                # Normaliser les caractéristiques
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)

                # Entraîner un modèle Random Forest
                rf_model = RandomForestClassifier(n_estimators=100, random_state=random_state)
                rf_model.fit(X_train_scaled, y_train)

                # Évaluer le modèle
                rf_pred = rf_model.predict(X_test_scaled)
                accuracy = accuracy_score(y_test, rf_pred)

                # Entraîner un modèle Gradient Boosting
                gb_model = GradientBoostingClassifier(n_estimators=100, random_state=random_state)
                gb_model.fit(X_train_scaled, y_train)

                # Évaluer le modèle
                gb_pred = gb_model.predict(X_test_scaled)
                gb_accuracy = accuracy_score(y_test, gb_pred)

                # Stocker les modèles et les scalers
                features[num] = {
                    'feature_cols': feature_cols,
                    'scaler': scaler
                }

                # Choisir le meilleur modèle
                if gb_accuracy > accuracy:
                    targets[num] = {
                        'model': gb_model,
                        'accuracy': gb_accuracy,
                        'type': 'gradient_boosting'
                    }
                else:
                    targets[num] = {
                        'model': rf_model,
                        'accuracy': accuracy,
                        'type': 'random_forest'
                    }

            # Stocker les modèles
            self.models = {
                'features': features,
                'targets': targets
            }

            print(f"Modèles entraînés pour {len(targets)} numéros")
            return True

        def save_models(self, filepath='keno_ml_models.pkl'):
            # Documentation string removed
            if not self.models:
                print("Aucun modèle à sauvegarder")
                return False

            try:
                joblib.dump(self.models, filepath)
                print(f"Modèles sauvegardés dans {filepath}")
                return True
            except Exception as e:
                print(f"Erreur lors de la sauvegarde des modèles: {e}")
                return False

        def load_models(self, filepath='keno_ml_models.pkl'):
            # Documentation string removed
            if not os.path.exists(filepath):
                print(f"Fichier de modèles {filepath} introuvable")
                return False

            try:
                self.models = joblib.load(filepath)
                print(f"Modèles chargés depuis {filepath}")
                return True
            except Exception as e:
                print(f"Erreur lors du chargement des modèles: {e}")
                return False

        def evaluate_models(self, test_size=0.2, random_state=42):
            # Documentation string removed
            if self.df is None:
                self.prepare_data()

            if not self.models or not self.models.get('targets'):
                print("Aucun modèle à évaluer")
                return None

            results = {}

            # Pour chaque numéro
            for num, target_info in self.models['targets'].items():
                feature_info = self.models['features'][num]
                model = target_info['model']
                feature_cols = feature_info['feature_cols']

                target_col = f'num_{num}'

                # Préparer les données
                X = self.df[feature_cols].iloc[20:].copy()
                y = self.df[target_col].iloc[20:].copy()

                # Diviser en ensembles d'entraînement et de test
                _, X_test, _, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state)

                # Normaliser les caractéristiques
                X_test_scaled = feature_info['scaler'].transform(X_test)

                # Prédire
                y_pred = model.predict(X_test_scaled)
                y_prob = model.predict_proba(X_test_scaled)[:, 1]

                # Calculer les métriques
                metrics = {
                    'accuracy': accuracy_score(y_test, y_pred),
                    'precision': precision_score(y_test, y_pred, zero_division=0),
                    'recall': recall_score(y_test, y_pred, zero_division=0),
                    'f1': f1_score(y_test, y_pred, zero_division=0)
                }

                # Ajouter AUC si possible
                if len(np.unique(y_test)) > 1:
                    metrics['auc'] = roc_auc_score(y_test, y_prob)
                else:
                    metrics['auc'] = 0.5

                results[num] = metrics

            return results

class KenoMLTrainer:
    # Documentation string removed

    def __init__(self, database_path, models_dir='ml_models'):
        # Documentation string removed
        self.database_path = database_path
        self.models_dir = models_dir
        self.data_manager = KenoDataManager()
        self.analyzer = None
        self.history = []
        self.df = None

        # Créer le répertoire des modèles s'il n'existe pas
        if not os.path.exists(self.models_dir):
            try:
                os.makedirs(self.models_dir)
                print(f"Répertoire {self.models_dir} créé avec succès")
            except Exception as e:
                print(f"Erreur lors de la création du répertoire {self.models_dir}: {e}")

    def _find_correlated_numbers(self, number, top_n=10):
        # Documentation string removed
        # Vérifier si le DataFrame est disponible
        if self.df is None or len(self.df) < 20:
            # Retourner une liste par défaut si pas assez de données
            return [i for i in range(1, min(self.data_manager.max_number + 1, number + top_n + 1))
                    if i != number]

        try:
            # Calculer la corrélation entre le numéro cible et tous les autres numéros
            target_col = f'num_{number}'
            if target_col not in self.df.columns:
                return [i for i in range(1, min(self.data_manager.max_number + 1, number + top_n + 1))
                        if i != number]

            correlations = {}
            for i in range(1, self.data_manager.max_number + 1):
                if i == number:
                    continue

                other_col = f'num_{i}'
                if other_col not in self.df.columns:
                    continue

                # Calculer la corrélation entre les deux numéros
                corr = self.df[target_col].corr(self.df[other_col])
                if not pd.isna(corr):  # Vérifier que la corrélation est valide
                    correlations[i] = abs(corr)  # Utiliser la valeur absolue de la corrélation

            # Trier les numéros par corrélation décroissante
            sorted_correlations = sorted(correlations.items(), key=lambda x: x[1], reverse=True)

            # Retourner les top_n numéros les plus corrélés
            return [num for num, _ in sorted_correlations[:top_n]]

        except Exception as e:
            print(f"Erreur lors du calcul des corrélations pour le numéro {number}: {e}")
            # Retourner une liste par défaut en cas d'erreur
            return [i for i in range(1, min(self.data_manager.max_number + 1, number + top_n + 1))
                    if i != number]

    def load_database(self, custom_path=None):
        # Documentation string removed
        # Utiliser le chemin personnalisé s'il est fourni, sinon utiliser le chemin par défaut
        db_path = custom_path if custom_path else self.database_path
        print(f"Chargement de la base de données: {db_path}")

        success = self.data_manager.load_database(db_path)

        if success:
            print(f"Base de données chargée avec succès: {len(self.data_manager.draws)} tirages")
            self.analyzer = KenoAdvancedAnalyzer(self.data_manager)
            return True
        else:
            print(f"Erreur lors du chargement de la base de données: {db_path}")
            return False

    def train_incremental(self, steps=5, test_size=0.2, random_state=42):
        # Documentation string removed
        if not self.analyzer:
            print("Analyseur non initialisé. Chargez d'abord la base de données.")
            return False

        # Préparer les données
        self.analyzer.prepare_data()

        if self.analyzer.df is None or len(self.analyzer.df) < 50:
            print("Pas assez de données pour l'entraînement (minimum 50 tirages requis)")
            return False

        # Déterminer la taille de chaque étape
        total_draws = len(self.analyzer.df)
        step_size = total_draws // steps

        if step_size < 50:
            print(f"Pas assez de données pour {steps} étapes. Utilisation de moins d'étapes.")
            steps = max(1, total_draws // 50)
            step_size = total_draws // steps

        print(f"Entraînement en {steps} étapes avec {step_size} tirages par étape")

        # Entraînement progressif
        for step in range(1, steps + 1):
            print(f"\nÉtape {step}/{steps}")

            # Sélectionner un sous-ensemble des données
            n_draws = min(step * step_size, total_draws)
            subset_df = self.analyzer.df.iloc[:n_draws].copy()

            # Créer un analyseur temporaire avec ce sous-ensemble
            temp_analyzer = KenoAdvancedAnalyzer(self.data_manager)
            temp_analyzer.df = subset_df

            # Entraîner les modèles
            print(f"Entraînement sur {len(subset_df)} tirages...")
            success = temp_analyzer.train_models(test_size=test_size, random_state=random_state)

            if not success:
                print(f"Échec de l'entraînement à l'étape {step}")
                continue

            # Évaluer les modèles
            print("Évaluation des modèles...")
            metrics = temp_analyzer.evaluate_models(test_size=test_size, random_state=random_state)

            # Calculer les métriques moyennes
            avg_metrics = {
                'accuracy': np.mean([m['accuracy'] for m in metrics.values()]),
                'precision': np.mean([m['precision'] for m in metrics.values()]),
                'recall': np.mean([m['recall'] for m in metrics.values()]),
                'f1': np.mean([m['f1'] for m in metrics.values()]),
                'auc': np.mean([m['auc'] for m in metrics.values()])
            }

            # Sauvegarder les modèles
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_path = os.path.join(self.models_dir, f"keno_ml_models_step{step}_{timestamp}.pkl")
            temp_analyzer.save_models(model_path)

            # Sauvegarder les métriques
            metrics_path = os.path.join(self.models_dir, f"metrics_step{step}_{timestamp}.json")
            with open(metrics_path, 'w') as f:
                json.dump({'individual': metrics, 'average': avg_metrics}, f, indent=2)

            # Ajouter à l'historique
            self.history.append({
                'step': step,
                'n_draws': n_draws,
                'timestamp': timestamp,
                'model_path': model_path,
                'metrics_path': metrics_path,
                'avg_metrics': avg_metrics
            })

            print(f"Étape {step} terminée:")
            print(f"  Précision moyenne: {avg_metrics['accuracy']:.4f}")
            print(f"  Score F1 moyen: {avg_metrics['f1']:.4f}")
            print(f"  AUC moyenne: {avg_metrics['auc']:.4f}")

        # Sauvegarder l'historique
        history_path = os.path.join(self.models_dir, "training_history.json")
        with open(history_path, 'w') as f:
            json.dump(self.history, f, indent=2)

        print(f"\nEntraînement terminé. Historique sauvegardé dans {history_path}")
        return True

    def plot_training_progress(self):
        # Documentation string removed
        if not self.history:
            print("Aucun historique d'entraînement disponible")
            return

        # Extraire les données
        steps = [h['step'] for h in self.history]
        n_draws = [h['n_draws'] for h in self.history]
        accuracy = [h['avg_metrics']['accuracy'] for h in self.history]
        f1 = [h['avg_metrics']['f1'] for h in self.history]
        auc = [h['avg_metrics']['auc'] for h in self.history]

        # Créer la figure
        plt.figure(figsize=(12, 8))

        # Graphique de précision
        plt.subplot(3, 1, 1)
        plt.plot(steps, accuracy, 'o-', color='blue')
        plt.title('Évolution de la précision')
        plt.xlabel('Étape')
        plt.ylabel('Précision moyenne')
        plt.grid(True)

        # Graphique de F1
        plt.subplot(3, 1, 2)
        plt.plot(steps, f1, 'o-', color='green')
        plt.title('Évolution du score F1')
        plt.xlabel('Étape')
        plt.ylabel('F1 moyen')
        plt.grid(True)

        # Graphique d'AUC
        plt.subplot(3, 1, 3)
        plt.plot(steps, auc, 'o-', color='red')
        plt.title('Évolution de l\'AUC')
        plt.xlabel('Étape')
        plt.ylabel('AUC moyenne')
        plt.grid(True)

        plt.tight_layout()

        # Sauvegarder le graphique
        plot_path = os.path.join(self.models_dir, "training_progress.png")
        plt.savefig(plot_path)
        print(f"Graphique sauvegardé dans {plot_path}")

        # Afficher le graphique
        plt.show()

    def set_parallel_config(self, config):
        # Documentation string removed
        self.parallel_config = config
        print(f"Configuration parallèle mise à jour: {config}")

    def train_models_parallel(self):
        # Documentation string removed
        try:
            # Importer le module d'optimisation parallèle
            import parallel_processing

            if not self.analyzer:
                # Charger la base de données si ce n'est pas déjà fait
                success = self.load_database()
                if not success:
                    print("Erreur lors du chargement de la base de données")
                    return False

            # Préparer les données
            self.analyzer.prepare_data()

            if self.analyzer.df is None or len(self.analyzer.df) < 50:
                print("Pas assez de données pour l'entraînement (minimum 50 tirages requis)")
                return False

            # Obtenir le nombre optimal de workers
            if hasattr(self, 'parallel_config') and 'max_workers' in self.parallel_config:
                max_workers = self.parallel_config['max_workers']
            else:
                max_workers = parallel_processing.get_optimal_workers()

            # Vérifier si l'accélération GPU est disponible et activée
            use_gpu = False
            if hasattr(self, 'parallel_config') and 'use_gpu' in self.parallel_config:
                use_gpu = self.parallel_config['use_gpu'] and parallel_processing.is_cuda_available()

            print(f"Entraînement parallèle avec {max_workers} workers" + " et accélération GPU" if use_gpu else "")

            # Initialiser l'historique s'il n'existe pas
            if not hasattr(self, 'history') or self.history is None:
                self.history = []

            # Entraîner les modèles en 3 étapes (comme dans train_incremental)
            steps = 3
            test_size = 0.2
            random_state = 42

            # Déterminer la taille de chaque étape
            total_draws = len(self.analyzer.df)
            step_size = total_draws // steps

            if step_size < 50:
                print(f"Pas assez de données pour {steps} étapes. Utilisation de moins d'étapes.")
                steps = max(1, total_draws // 50)
                step_size = total_draws // steps

            print(f"Entraînement en {steps} étapes avec {step_size} tirages par étape")

            # Entraîner les modèles pour chaque étape
            for step in range(1, steps + 1):
                print(f"\nÉtape {step}/{steps}")

                # Sélectionner les données pour cette étape
                train_size = step * step_size
                print(f"Entraînement sur {train_size} tirages...")

                # Préparer les paramètres pour l'entraînement parallèle
                models_params = []

                # Préparer les données pour chaque numéro
                for num in range(1, self.analyzer.max_number + 1):
                    # Préparer les données pour ce numéro
                    X, y = self.analyzer.prepare_features_for_number(num, train_size)

                    if X is None or y is None or len(X) < 50:
                        continue

                    # Diviser les données en ensembles d'entraînement et de test
                    from sklearn.model_selection import train_test_split
                    X_train, X_test, y_train, y_test = train_test_split(
                        X, y, test_size=test_size, random_state=random_state
                    )

                    # Ajouter les paramètres pour ce modèle
                    models_params.append({
                        'num': num,
                        'X_train': X_train,
                        'y_train': y_train,
                        'X_test': X_test,
                        'y_test': y_test
                    })

                print(f"Modèles préparés pour {len(models_params)} numéros")

                # Entraîner les modèles en parallèle
                results = parallel_processing.train_models_parallel(models_params, max_workers=max_workers)

                # Sauvegarder les modèles
                model_path = os.path.join(self.models_dir, f"keno_ml_models_step{step}_{self._get_timestamp()}.pkl")
                os.makedirs(os.path.dirname(model_path), exist_ok=True)

                import pickle
                with open(model_path, 'wb') as f:
                    pickle.dump(results, f)

                print(f"Modèles sauvegardés dans {model_path}")

                # Calculer les métriques moyennes
                metrics = {
                    'accuracy': np.mean([r['metrics']['accuracy'] for r in results.values()]),
                    'f1': np.mean([r['metrics']['f1'] for r in results.values()]),
                    'auc': np.mean([r['metrics']['auc'] for r in results.values()])
                }

                # Ajouter l'étape à l'historique
                self.history.append({
                    'step': step,
                    'model_path': model_path,
                    'train_size': train_size,
                    'avg_metrics': metrics
                })

                print(f"Étape {step} terminée:")
                print(f"  Précision moyenne: {metrics['accuracy']:.4f}")
                print(f"  Score F1 moyen: {metrics['f1']:.4f}")
                print(f"  AUC moyenne: {metrics['auc']:.4f}")

            # Sauvegarder l'historique
            history_path = os.path.join(self.models_dir, "training_history.json")
            import json
            with open(history_path, 'w') as f:
                # Convertir les valeurs numpy en float pour la sérialisation JSON
                history_json = []
                for h in self.history:
                    h_copy = h.copy()
                    for k, v in h_copy['avg_metrics'].items():
                        if hasattr(v, 'item'):
                            h_copy['avg_metrics'][k] = float(v)
                    history_json.append(h_copy)
                json.dump(history_json, f, indent=4)

            print(f"Entraînement terminé. Historique sauvegardé dans {history_path}")

            # Trouver le meilleur modèle
            self.find_best_model()

            return True

        except Exception as e:
            import traceback
            print(f"Erreur lors de l'entraînement parallèle: {e}")
            traceback.print_exc()
            return False

    def train_models(self):
        # Documentation string removed
        if not self.analyzer:
            # Charger la base de données si ce n'est pas déjà fait
            success = self.load_database()
            if not success:
                print("Erreur lors du chargement de la base de données")
                return False

        # Utiliser train_incremental avec des paramètres par défaut
        return self.train_incremental(steps=3, test_size=0.2, random_state=42)

    def find_best_model(self):
        # Documentation string removed
        if not self.history:
            print("Aucun historique d'entraînement disponible")
            return None

        # Trouver le modèle avec la meilleure précision
        best_acc_idx = np.argmax([h['avg_metrics']['accuracy'] for h in self.history])
        best_f1_idx = np.argmax([h['avg_metrics']['f1'] for h in self.history])
        best_auc_idx = np.argmax([h['avg_metrics']['auc'] for h in self.history])

        print("\nMeilleurs modèles:")
        print(f"  Meilleure précision: Étape {self.history[best_acc_idx]['step']} - {self.history[best_acc_idx]['avg_metrics']['accuracy']:.4f}")
        print(f"  Meilleur F1: Étape {self.history[best_f1_idx]['step']} - {self.history[best_f1_idx]['avg_metrics']['f1']:.4f}")
        print(f"  Meilleure AUC: Étape {self.history[best_auc_idx]['step']} - {self.history[best_auc_idx]['avg_metrics']['auc']:.4f}")

        # Copier le meilleur modèle (selon F1) vers un emplacement standard
        best_model_path = self.history[best_f1_idx]['model_path']
        standard_path = os.path.join(self.models_dir, "best_model.pkl")

        try:
            import shutil
            shutil.copy2(best_model_path, standard_path)
            print(f"\nMeilleur modèle copié vers {standard_path}")
        except Exception as e:
            print(f"Erreur lors de la copie du meilleur modèle: {e}")

        return self.history[best_f1_idx]

    def test_prediction(self, num_predictions=10):
        # Documentation string removed
        best_model = self.find_best_model()
        if not best_model:
            return

        # Charger le meilleur modèle
        temp_analyzer = KenoAdvancedAnalyzer(self.data_manager)
        temp_analyzer.prepare_data()
        temp_analyzer.load_models(best_model['model_path'])

        # Faire une prédiction
        print("\nTest de prédiction avec le meilleur modèle:")

        # Simuler une prédiction ML
        predictions = []
        for num in range(1, self.data_manager.max_number + 1):
            if num not in temp_analyzer.models['targets']:
                continue

            target_info = temp_analyzer.models['targets'][num]
            feature_info = temp_analyzer.models['features'][num]
            model = target_info['model']
            scaler = feature_info['scaler']
            feature_cols = feature_info['feature_cols']

            # Préparer les caractéristiques pour ce numéro
            last_row = temp_analyzer.df.iloc[-1].copy()
            features = {}
            for col in feature_cols:
                features[col] = last_row[col]

            # Convertir en DataFrame
            X = pd.DataFrame([features])

            # Normaliser
            X_scaled = scaler.transform(X)

            # Prédire
            prob = model.predict_proba(X_scaled)[0][1]  # Probabilité de la classe 1 (numéro présent)

            predictions.append({
                'number': num,
                'probability': prob,
                'model_accuracy': target_info['accuracy'],
                'model_type': target_info['type']
            })

        # Trier par probabilité décroissante
        predictions.sort(key=lambda x: x['probability'], reverse=True)

        # Afficher les N premiers numéros
        print(f"\nPrédiction des {num_predictions} prochains numéros:")
        for i, pred in enumerate(predictions[:num_predictions]):
            print(f"  {i+1}. Numéro {pred['number']} - Probabilité: {pred['probability']:.4f} - Précision du modèle: {pred['model_accuracy']:.4f}")

        return predictions[:num_predictions]

def main():
    # Documentation string removed
    # Vérifier les arguments
    if len(sys.argv) < 2:
        print("Usage: python keno_ml_trainer.py <database_path> [steps]")
        print("  database_path: Chemin vers la base de données Keno (.keno)")
        print("  steps: Nombre d'étapes d'entraînement (défaut: 5)")
        return

    database_path = sys.argv[1]
    steps = int(sys.argv[2]) if len(sys.argv) > 2 else 5

    # Vérifier que le fichier existe
    if not os.path.exists(database_path):
        print(f"Erreur: Le fichier {database_path} n'existe pas")
        return

    # Créer l'entraîneur
    trainer = KenoMLTrainer(database_path)

    # Entraîner les modèles
    trainer.train_incremental(steps=steps)

    # Afficher les progrès
    trainer.plot_training_progress()

    # Tester la prédiction
    trainer.test_prediction()

if __name__ == "__main__":
    main()

    def train_models(self):
        # Documentation string removed
        import numpy as np

        if not hasattr(self, "data_manager") or not self.data_manager.draws:
            print("No data available for training")
            return False

        print(f"Training ML models with {len(self.data_manager.draws)} draws")

        # Initialize models dictionary if not exists
        if not hasattr(self, "models"):
            self.models = {}

        # Initialize history if not exists
        if not hasattr(self, "history"):
            self.history = []

        # Prepare data
        self.prepare_data()

        # Train models for each number
        success_count = 0
        metrics = []

        # Get the maximum number from the data
        max_number = 80  # Default for Keno
        if hasattr(self, "max_number"):
            max_number = self.max_number

        # Train a model for each number
        for number in range(1, max_number + 1):
            try:
                # Prepare features for this number
                X, y = self.prepare_features_for_number(number)

                if X is None or y is None or X.empty or y.empty:
                    continue

                # Train the model
                model_metrics = self.train_model_for_number(number, X, y)

                if model_metrics:
                    metrics.append(model_metrics)
                    success_count += 1
            except Exception as e:
                print(f"Error training model for number {number}: {e}")

        # Calculate average metrics
        avg_metrics = {}
        if metrics:
            for key in metrics[0].keys():
                if isinstance(metrics[0][key], (int, float)):
                    avg_metrics[key] = sum(m[key] for m in metrics if key in m) / len(metrics)

        # Add entry to history
        import time
        self.history.append({
            "timestamp": time.time(),
            "success_count": success_count,
            "total_numbers": max_number,
            "avg_metrics": avg_metrics
        })

        print(f"Successfully trained {success_count} models out of {max_number}")
        return success_count > 0

    def train_model_for_number(self, number, X, y):
        # Documentation string removed
        from sklearn.model_selection import train_test_split
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

        # Split the data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # Create and train the model
        model = RandomForestClassifier(n_estimators=100, random_state=42)
        model.fit(X_train, y_train)

        # Make predictions
        y_pred = model.predict(X_test)

        # Calculate metrics
        metrics = {
            "accuracy": accuracy_score(y_test, y_pred),
            "precision": precision_score(y_test, y_pred, zero_division=0),
            "recall": recall_score(y_test, y_pred, zero_division=0),
            "f1": f1_score(y_test, y_pred, zero_division=0)
        }

        # Store the model
        self.models[number] = {
            "model": model,
            "features": list(X.columns),
            "metrics": metrics
        }

        return metrics

    def train_models(self):
        # Documentation string removed
        import numpy as np

        if not hasattr(self, "data_manager") or not self.data_manager.draws:
            print("No data available for training")
            return False

        print(f"Training ML models with {len(self.data_manager.draws)} draws")

        # Initialize models dictionary if not exists
        if not hasattr(self, "models"):
            self.models = {}

        # Initialize history if not exists
        if not hasattr(self, "history"):
            self.history = []

        # Prepare data
        self.prepare_data()

        # Train models for each number
        success_count = 0
        metrics = []

        # Get the maximum number from the data
        max_number = 80  # Default for Keno
        if hasattr(self, "max_number"):
            max_number = self.max_number

        # Train a model for each number
        for number in range(1, max_number + 1):
            try:
                # Prepare features for this number
                X, y = self.prepare_features_for_number(number)

                if X is None or y is None or X.empty or y.empty:
                    continue

                # Train the model
                model_metrics = self.train_model_for_number(number, X, y)

                if model_metrics:
                    metrics.append(model_metrics)
                    success_count += 1
            except Exception as e:
                print(f"Error training model for number {number}: {e}")

        # Calculate average metrics
        avg_metrics = {}
        if metrics:
            for key in metrics[0].keys():
                if isinstance(metrics[0][key], (int, float)):
                    avg_metrics[key] = sum(m[key] for m in metrics if key in m) / len(metrics)

        # Add entry to history
        import time
        self.history.append({
            "timestamp": time.time(),
            "success_count": success_count,
            "total_numbers": max_number,
            "avg_metrics": avg_metrics
        })

        print(f"Successfully trained {success_count} models out of {max_number}")
        return success_count > 0

    def train_model_for_number(self, number, X, y):
        # Documentation string removed
        from sklearn.model_selection import train_test_split
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

        # Split the data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # Create and train the model
        model = RandomForestClassifier(n_estimators=100, random_state=42)
        model.fit(X_train, y_train)

        # Make predictions
        y_pred = model.predict(X_test)

        # Calculate metrics
        metrics = {
            "accuracy": accuracy_score(y_test, y_pred),
            "precision": precision_score(y_test, y_pred, zero_division=0),
            "recall": recall_score(y_test, y_pred, zero_division=0),
            "f1": f1_score(y_test, y_pred, zero_division=0)
        }

        # Store the model
        self.models[number] = {
            "model": model,
            "features": list(X.columns),
            "metrics": metrics
        }

        return metrics

    def train_models(self):
        # Documentation string removed
        import numpy as np

        if not hasattr(self, "data_manager") or not self.data_manager.draws:
            print("No data available for training")
            return False

        print(f"Training ML models with {len(self.data_manager.draws)} draws")

        # Initialize models dictionary if not exists
        if not hasattr(self, "models"):
            self.models = {}

        # Initialize history if not exists
        if not hasattr(self, "history"):
            self.history = []

        # Prepare data
        self.prepare_data()

        # Train models for each number
        success_count = 0
        metrics = []

        # Get the maximum number from the data
        max_number = 70  # Default for Keno
        if hasattr(self, "max_number"):
            max_number = self.max_number

        # Train a model for each number
        for number in range(1, max_number + 1):
            try:
                # Prepare features for this number
                X, y = self.prepare_features_for_number(number)

                if X is None or y is None or X.empty or y.empty:
                    continue

                # Train the model
                model_metrics = self.train_model_for_number(number, X, y)

                if model_metrics:
                    metrics.append(model_metrics)
                    success_count += 1
            except Exception as e:
                print(f"Error training model for number {number}: {e}")

        # Calculate average metrics
        avg_metrics = {}
        if metrics:
            for key in metrics[0].keys():
                if isinstance(metrics[0][key], (int, float)):
                    avg_metrics[key] = sum(m[key] for m in metrics if key in m) / len(metrics)

        # Add entry to history
        import time
        self.history.append({
            "timestamp": time.time(),
            "success_count": success_count,
            "total_numbers": max_number,
            "avg_metrics": avg_metrics
        })

        print(f"Successfully trained {success_count} models out of {max_number}")
        return success_count > 0

    def train_model_for_number(self, number, X, y):
        # Documentation string removed
        from sklearn.model_selection import train_test_split
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

        # Split the data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # Create and train the model
        model = RandomForestClassifier(n_estimators=100, random_state=42)
        model.fit(X_train, y_train)

        # Make predictions
        y_pred = model.predict(X_test)

        # Calculate metrics
        metrics = {
            "accuracy": accuracy_score(y_test, y_pred),
            "precision": precision_score(y_test, y_pred, zero_division=0),
            "recall": recall_score(y_test, y_pred, zero_division=0),
            "f1": f1_score(y_test, y_pred, zero_division=0)
        }

        # Store the model
        self.models[number] = {
            "model": model,
            "features": list(X.columns),
            "metrics": metrics
        }

        return metrics

    def train_models(self):
        # Train machine learning models for number prediction
        import numpy as np
        import time

        if not hasattr(self, 'data_manager') or not self.data_manager.draws:
            print("No data available for training")
            return False

        print(f"Training ML models with {len(self.data_manager.draws)} draws")

        # Initialize models dictionary if not exists
        if not hasattr(self, 'models'):
            self.models = {}

        # Initialize history if not exists
        if not hasattr(self, 'history'):
            self.history = []

        # Prepare data
        self.prepare_data()

        # Train models for each number
        success_count = 0
        metrics = []

        # Get the maximum number from the data
        max_number = 70  # Default for Keno
        if hasattr(self, 'max_number'):
            max_number = self.max_number

        # Train a model for each number
        for number in range(1, max_number + 1):
            try:
                # Prepare features for this number
                X, y = self.prepare_features_for_number(number)

                if X is None or y is None or X.empty or y.empty:
                    continue

                # Train the model
                model_metrics = self.train_model_for_number(number, X, y)

                if model_metrics:
                    metrics.append(model_metrics)
                    success_count += 1
            except Exception as e:
                print(f"Error training model for number {number}: {e}")

        # Calculate average metrics
        avg_metrics = {}
        if metrics:
            for key in metrics[0].keys():
                if isinstance(metrics[0][key], (int, float)):
                    avg_metrics[key] = sum(m[key] for m in metrics if key in m) / len(metrics)

        # Add entry to history
        self.history.append({
            'timestamp': time.time(),
            'success_count': success_count,
            'total_numbers': max_number,
            'avg_metrics': avg_metrics
        })

        print(f"Successfully trained {success_count} models out of {max_number}")
        return success_count > 0

    def train_model_for_number(self, number, X, y):
        # Train a machine learning model for a specific number
        from sklearn.model_selection import train_test_split
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

        # Split the data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # Create and train the model
        model = RandomForestClassifier(n_estimators=100, random_state=42)
        model.fit(X_train, y_train)

        # Make predictions
        y_pred = model.predict(X_test)

        # Calculate metrics
        metrics = {
            'accuracy': accuracy_score(y_test, y_pred),
            'precision': precision_score(y_test, y_pred, zero_division=0),
            'recall': recall_score(y_test, y_pred, zero_division=0),
            'f1': f1_score(y_test, y_pred, zero_division=0)
        }

        # Store the model
        self.models[number] = {
            'model': model,
            'features': list(X.columns),
            'metrics': metrics
        }

        return metrics

    def train_models(self):
        # Train machine learning models for number prediction
        import numpy as np
        import time

        if not hasattr(self, 'data_manager') or not self.data_manager.draws:
            print("No data available for training")
            return False

        print(f"Training ML models with {len(self.data_manager.draws)} draws")

        # Initialize models dictionary if not exists
        if not hasattr(self, 'models'):
            self.models = {}

        # Initialize history if not exists
        if not hasattr(self, 'history'):
            self.history = []

        # Prepare data
        self.prepare_data()

        # Train models for each number
        success_count = 0
        metrics = []

        # Get the maximum number from the data
        max_number = 70  # Default for Keno
        if hasattr(self, 'max_number'):
            max_number = self.max_number

        # Train a model for each number
        for number in range(1, max_number + 1):
            try:
                # Prepare features for this number
                X, y = self.prepare_features_for_number(number)

                if X is None or y is None or X.empty or y.empty:
                    continue

                # Train the model
                model_metrics = self.train_model_for_number(number, X, y)

                if model_metrics:
                    metrics.append(model_metrics)
                    success_count += 1
            except Exception as e:
                print(f"Error training model for number {number}: {e}")

        # Calculate average metrics
        avg_metrics = {}
        if metrics:
            for key in metrics[0].keys():
                if isinstance(metrics[0][key], (int, float)):
                    avg_metrics[key] = sum(m[key] for m in metrics if key in m) / len(metrics)

        # Add entry to history
        self.history.append({
            'timestamp': time.time(),
            'success_count': success_count,
            'total_numbers': max_number,
            'avg_metrics': avg_metrics
        })

        print(f"Successfully trained {success_count} models out of {max_number}")
        return success_count > 0

    def train_model_for_number(self, number, X, y):
        # Train a machine learning model for a specific number
        from sklearn.model_selection import train_test_split
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

        # Split the data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # Create and train the model
        model = RandomForestClassifier(n_estimators=100, random_state=42)
        model.fit(X_train, y_train)

        # Make predictions
        y_pred = model.predict(X_test)

        # Calculate metrics
        metrics = {
            'accuracy': accuracy_score(y_test, y_pred),
            'precision': precision_score(y_test, y_pred, zero_division=0),
            'recall': recall_score(y_test, y_pred, zero_division=0),
            'f1': f1_score(y_test, y_pred, zero_division=0)
        }

        # Store the model
        self.models[number] = {
            'model': model,
            'features': list(X.columns),
            'metrics': metrics
        }

        return metrics
