"""
Script pour trouver où les données du numéro 70 sont réutilisées pour les autres numéros.
"""

import os
import sys
import re
import shutil
from datetime import datetime

def backup_file(file_path):
    """Crée une sauvegarde du fichier"""
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def find_files_with_pattern(pattern):
    """Trouve les fichiers contenant un pattern spécifique"""
    found_files = []
    
    for file in os.listdir('.'):
        if file.endswith('.py'):
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if re.search(pattern, content):
                        found_files.append(file)
            except:
                pass
    
    return found_files

def analyze_file(file_path, pattern):
    """Analyse un fichier pour trouver un pattern spécifique"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # Trouver toutes les occurrences du pattern
            matches = re.finditer(pattern, content)
            
            # Extraire les lignes autour de chaque occurrence
            results = []
            for match in matches:
                start_pos = max(0, match.start() - 500)
                end_pos = min(len(content), match.end() + 500)
                
                # Extraire le contexte
                context = content[start_pos:end_pos]
                
                # Trouver les numéros de ligne
                line_start = content[:start_pos].count('\n') + 1
                line_end = line_start + context.count('\n')
                
                results.append({
                    'match': match.group(0),
                    'context': context,
                    'line_start': line_start,
                    'line_end': line_end
                })
            
            return results
    except Exception as e:
        print(f"Erreur lors de l'analyse du fichier {file_path}: {e}")
        return []

def fix_keno_advanced_analyzer():
    """Corrige le fichier keno_advanced_analyzer.py"""
    file_path = 'keno_advanced_analyzer.py'
    
    if not os.path.exists(file_path):
        print(f"Le fichier {file_path} n'existe pas")
        return False
    
    # Créer une sauvegarde
    backup_file(file_path)
    
    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Rechercher le pattern de réutilisation des données
    # Nous cherchons où les données du numéro 70 sont réutilisées pour les autres numéros
    patterns = [
        r'for\s+num\s+in\s+range\s*\([^)]*\)\s*:',
        r'def\s+train_models\s*\([^)]*\)\s*:',
        r'def\s+auto_improve\s*\([^)]*\)\s*:',
        r'def\s+process_number\s*\([^)]*\)\s*:'
    ]
    
    for pattern in patterns:
        matches = re.finditer(pattern, content)
        for match in matches:
            # Extraire le contexte
            start_pos = max(0, match.start() - 500)
            end_pos = min(len(content), match.end() + 500)
            context = content[start_pos:end_pos]
            
            # Vérifier si le contexte contient des indices de réutilisation des données
            if 'X =' in context or 'y =' in context or 'X_train' in context or 'y_train' in context:
                print(f"Indice de réutilisation des données trouvé dans {file_path}:")
                print(f"  Pattern: {match.group(0)}")
                print(f"  Contexte: {context[:100]}...")
                
                # Vérifier si le contexte contient des variables qui pourraient être réutilisées
                if 'self.X' in context or 'self.y' in context or 'self.data' in context:
                    print("  Variables potentiellement réutilisées: self.X, self.y, self.data")
    
    # Rechercher les variables de classe qui pourraient être réutilisées
    class_vars = re.findall(r'self\.([A-Za-z_][A-Za-z0-9_]*)\s*=', content)
    print(f"Variables de classe trouvées dans {file_path}:")
    for var in set(class_vars):
        print(f"  - self.{var}")
    
    # Rechercher les méthodes qui pourraient réutiliser des données
    methods = re.findall(r'def\s+([A-Za-z_][A-Za-z0-9_]*)\s*\(', content)
    print(f"Méthodes trouvées dans {file_path}:")
    for method in set(methods):
        print(f"  - {method}")
    
    return True

def main():
    """Fonction principale"""
    print("Recherche de la réutilisation des données du numéro 70")
    
    # Trouver les fichiers contenant des patterns liés à l'auto-amélioration
    patterns = [
        r'auto[_-]?improve',
        r'train_models',
        r'process_number',
        r'for\s+num\s+in\s+range'
    ]
    
    found_files = []
    for pattern in patterns:
        files = find_files_with_pattern(pattern)
        found_files.extend(files)
    
    # Éliminer les doublons
    found_files = list(set(found_files))
    
    print(f"Fichiers trouvés: {found_files}")
    
    # Analyser le fichier keno_advanced_analyzer.py
    fix_keno_advanced_analyzer()
    
    print("\nRecherche terminée!")
    print("Veuillez examiner les résultats pour identifier où les données du numéro 70 sont réutilisées.")

if __name__ == "__main__":
    main()
