"""
Script de vérification des accélérateurs matériels disponibles pour l'application Keno
"""

import sys
import platform
import os
import subprocess

def check_cpu():
    """Vérifie les informations du CPU"""
    print("\n=== Informations CPU ===")
    
    # Nombre de cœurs
    import multiprocessing
    num_cores = multiprocessing.cpu_count()
    print(f"Nombre de cœurs CPU: {num_cores}")
    
    # Informations sur le processeur
    if platform.system() == "Windows":
        try:
            import cpuinfo
            info = cpuinfo.get_cpu_info()
            print(f"Processeur: {info['brand_raw']}")
            print(f"Architecture: {info['arch']}")
            print(f"Bits: {info['bits']}")
            
            # Vérifier les instructions spéciales
            flags = info.get('flags', [])
            avx_support = any(flag in flags for flag in ['avx', 'avx2', 'avx512'])
            sse_support = any(flag in flags for flag in ['sse', 'sse2', 'sse3', 'sse4_1', 'sse4_2'])
            vnni_support = 'avx512_vnni' in flags
            
            print(f"Support AVX: {'Oui' if avx_support else 'Non'}")
            print(f"Support SSE: {'Oui' if sse_support else 'Non'}")
            print(f"Support VNNI: {'Oui' if vnni_support else 'Non'}")
        except ImportError:
            print("Module py-cpuinfo non installé. Installation en cours...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "py-cpuinfo"])
                print("py-cpuinfo installé. Veuillez relancer le script.")
            except:
                print("Impossible d'installer py-cpuinfo.")
                print(f"Processeur: {platform.processor()}")
    else:
        print(f"Processeur: {platform.processor()}")

def check_gpu():
    """Vérifie les informations du GPU"""
    print("\n=== Informations GPU ===")
    
    # Vérifier NVIDIA GPU avec nvidia-smi
    nvidia_available = False
    try:
        result = subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        if result.returncode == 0:
            print("GPU NVIDIA détecté:")
            # Extraire les informations importantes
            lines = result.stdout.split('\n')
            for line in lines[:10]:  # Afficher seulement les premières lignes
                if "NVIDIA" in line and "Driver Version" in line:
                    print(line.strip())
                elif "|" in line and "%" in line and "MiB" in line:
                    print(line.strip())
            nvidia_available = True
    except:
        print("Aucun GPU NVIDIA détecté ou nvidia-smi non disponible")
    
    # Vérifier avec TensorFlow
    try:
        import tensorflow as tf
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"\nGPU(s) détecté(s) par TensorFlow: {len(gpus)}")
            for gpu in gpus:
                print(f"  {gpu.name}")
            
            # Vérifier si CUDA est disponible
            cuda_available = tf.test.is_built_with_cuda()
            print(f"TensorFlow compilé avec CUDA: {'Oui' if cuda_available else 'Non'}")
            
            # Vérifier la version de CUDA
            if cuda_available:
                try:
                    cuda_version = tf.sysconfig.get_build_info()['cuda_version']
                    print(f"Version CUDA: {cuda_version}")
                except:
                    pass
        else:
            print("\nAucun GPU détecté par TensorFlow")
    except ImportError:
        print("\nTensorFlow n'est pas installé")
    except Exception as e:
        print(f"\nErreur lors de la vérification des GPU avec TensorFlow: {e}")

def check_npu():
    """Vérifie les informations du NPU"""
    print("\n=== Informations NPU ===")
    
    # Vérifier OpenVINO
    try:
        import openvino as ov
        print(f"OpenVINO détecté (version {ov.__version__})")
        
        # Créer un Core OpenVINO pour accéder aux dispositifs
        core = ov.Core()
        available_devices = core.available_devices
        print(f"Dispositifs OpenVINO disponibles: {available_devices}")
        
        # Afficher des informations détaillées sur chaque dispositif
        for device in available_devices:
            try:
                device_name = device
                full_name = core.get_property(device_name, "FULL_DEVICE_NAME")
                print(f"\nDispositif: {device_name}")
                print(f"Nom complet: {full_name}")
                
                # Afficher les capacités si disponibles
                try:
                    capabilities = core.get_property(device_name, "OPTIMIZATION_CAPABILITIES")
                    print(f"Capacités: {capabilities}")
                except:
                    pass
            except Exception as e:
                print(f"Erreur lors de l'analyse du dispositif {device}: {e}")
    except ImportError:
        print("OpenVINO n'est pas installé")
        print("Utilisez le script install_openvino.py pour l'installer")
    except Exception as e:
        print(f"Erreur lors de la vérification d'OpenVINO: {e}")
    
    # Vérifier TensorFlow pour les NPU/TPU
    try:
        import tensorflow as tf
        devices = tf.config.list_physical_devices()
        npu_devices = [d for d in devices if any(x in d.name.upper() for x in ['NPU', 'TPU', 'NEURAL'])]
        
        if npu_devices:
            print("\nNPU/TPU détecté(s) par TensorFlow:")
            for device in npu_devices:
                print(f"  {device.name}")
        else:
            print("\nAucun NPU/TPU détecté par TensorFlow")
    except ImportError:
        print("\nTensorFlow n'est pas installé")
    except Exception as e:
        print(f"\nErreur lors de la vérification des NPU avec TensorFlow: {e}")

def main():
    """Fonction principale"""
    print("=== Vérification des accélérateurs matériels pour l'application Keno ===")
    print(f"Système: {platform.system()} {platform.release()}")
    print(f"Python: {platform.python_version()}")
    
    # Vérifier le CPU
    check_cpu()
    
    # Vérifier le GPU
    check_gpu()
    
    # Vérifier le NPU
    check_npu()
    
    print("\n=== Recommandations ===")
    print("1. Si vous avez un processeur Intel récent, installez OpenVINO avec install_openvino.py")
    print("2. Si vous avez un GPU NVIDIA, assurez-vous que CUDA est installé")
    print("3. Dans l'application Keno, utilisez le mode 'auto' pour la détection automatique")
    print("   ou sélectionnez manuellement 'cpu', 'gpu' ou 'npu' selon votre matériel")

if __name__ == "__main__":
    main()
