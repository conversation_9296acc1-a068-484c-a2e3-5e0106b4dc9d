"""
Script de test pour l'optimiseur XGBoost
Ce script teste l'optimiseur XGBoost en entraînant un modèle pour un numéro spécifique.
"""

import os
import sys
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, precision_recall_fscore_support

# Importer l'optimiseur XGBoost
try:
    from keno_xgboost_optimizer import XGBoostOptimizer
    print("Module d'optimisation XGBoost importé avec succès")
except ImportError:
    print("Erreur: Module d'optimisation XGBoost non disponible")
    sys.exit(1)

# Importer le gestionnaire de données Keno
try:
    from keno_data import KenoDataManager
    print("Module de gestion des données Keno importé avec succès")
except ImportError:
    print("Erreur: Module de gestion des données Keno non disponible")
    sys.exit(1)

def generate_test_data():
    """Génère des données de test si aucune donnée réelle n'est disponible"""
    print("Génération de données de test...")
    
    # Créer un DataFrame avec des données aléatoires
    np.random.seed(42)
    n_samples = 1000
    
    # Créer des caractéristiques aléatoires
    X = np.random.rand(n_samples, 10)
    
    # Créer une cible avec un biais pour tester scale_pos_weight
    y = np.zeros(n_samples)
    # 20% d'exemples positifs
    positive_indices = np.random.choice(n_samples, size=int(n_samples * 0.2), replace=False)
    y[positive_indices] = 1
    
    # Créer un DataFrame
    df = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(10)])
    df['target'] = y
    
    return df

def test_optimizer():
    """Teste l'optimiseur XGBoost"""
    print("Test de l'optimiseur XGBoost...")
    
    # Créer l'optimiseur
    optimizer = XGBoostOptimizer(use_gpu=False, n_jobs=-1, verbose=2)
    print("Optimiseur XGBoost créé avec succès")
    
    # Générer des données de test
    df = generate_test_data()
    
    # Diviser les données
    X = df.drop('target', axis=1)
    y = df['target']
    
    # Diviser en ensembles d'entraînement et de test
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    
    # Normaliser les caractéristiques
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Entraîner un modèle avec l'optimiseur
    print("\nTest 1: Entraînement avec optimisation des hyperparamètres")
    result1 = optimizer.train_model(
        X_train_scaled, y_train, num=1, 
        optimize=True, fast_mode=True
    )
    
    # Évaluer le modèle
    if result1 and 'model' in result1:
        y_pred = result1['model'].predict(X_test_scaled)
        accuracy = accuracy_score(y_test, y_pred)
        precision, recall, f1, _ = precision_recall_fscore_support(
            y_test, y_pred, average='binary', zero_division=0
        )
        
        print(f"Résultats du Test 1:")
        print(f"  - Précision: {accuracy:.4f}")
        print(f"  - F1-score: {f1:.4f}")
        print(f"  - Précision: {precision:.4f}")
        print(f"  - Rappel: {recall:.4f}")
        
        # Afficher les paramètres optimaux
        print(f"Paramètres optimaux:")
        for key, value in result1['params'].items():
            print(f"  - {key}: {value}")
    else:
        print("Erreur: Échec de l'entraînement du modèle avec optimisation")
    
    # Entraîner un modèle sans optimisation
    print("\nTest 2: Entraînement sans optimisation des hyperparamètres")
    result2 = optimizer.train_model(
        X_train_scaled, y_train, num=2, 
        optimize=False, fast_mode=True
    )
    
    # Évaluer le modèle
    if result2 and 'model' in result2:
        y_pred = result2['model'].predict(X_test_scaled)
        accuracy = accuracy_score(y_test, y_pred)
        precision, recall, f1, _ = precision_recall_fscore_support(
            y_test, y_pred, average='binary', zero_division=0
        )
        
        print(f"Résultats du Test 2:")
        print(f"  - Précision: {accuracy:.4f}")
        print(f"  - F1-score: {f1:.4f}")
        print(f"  - Précision: {precision:.4f}")
        print(f"  - Rappel: {recall:.4f}")
    else:
        print("Erreur: Échec de l'entraînement du modèle sans optimisation")
    
    # Tester l'optimisation de scale_pos_weight
    print("\nTest 3: Optimisation de scale_pos_weight")
    
    # Diviser les données pour l'optimisation
    X_train_inner, X_val, y_train_inner, y_val = train_test_split(
        X_train_scaled, y_train, test_size=0.2, random_state=42
    )
    
    # Calculer scale_pos_weight optimal
    base_config = {
        'n_estimators': 100,
        'max_depth': 5,
        'learning_rate': 0.1,
        'objective': 'binary:logistic',
        'eval_metric': 'auc',
        'verbosity': 0,
        'random_state': 42
    }
    
    optimal_spw = optimizer.optimize_scale_pos_weight(
        X_train_inner, y_train_inner, X_val, y_val, base_config, num=3
    )
    
    print(f"scale_pos_weight optimal: {optimal_spw}")
    
    print("\nTests terminés avec succès!")

if __name__ == "__main__":
    test_optimizer()
