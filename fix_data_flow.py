"""
Script pour modifier le flux de données et garantir des distributions spécifiques pour chaque numéro.
Cette solution s'attaque à la racine du problème en modifiant la façon dont les données sont préparées.
"""

import os
import sys
import re
import json
import shutil
import random
import numpy as np
from datetime import datetime

def backup_file(file_path):
    """
    Crée une sauvegarde du fichier
    
    Args:
        file_path: Chemin du fichier à sauvegarder
        
    Returns:
        str: Chemin de la sauvegarde
    """
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def find_data_preparation_files():
    """
    Trouve les fichiers qui préparent les données pour l'entraînement
    
    Returns:
        list: Liste des fichiers trouvés
    """
    potential_files = [
        'keno_advanced_analyzer.py',
        'keno_analyzer.py',
        'keno_ml_trainer.py',
        'keno_data.py'
    ]
    
    found_files = []
    for file in potential_files:
        if os.path.exists(file):
            found_files.append(file)
    
    print(f"Fichiers de préparation des données trouvés: {found_files}")
    return found_files

def analyze_file_content(file_path):
    """
    Analyse le contenu d'un fichier pour trouver les sections pertinentes
    
    Args:
        file_path: Chemin du fichier à analyser
        
    Returns:
        dict: Informations sur le contenu du fichier
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Rechercher des patterns spécifiques
    patterns = {
        'prepare_data': r'def\s+prepare_data|def\s+prepare_training_data|def\s+get_training_data',
        'train_models': r'def\s+train_models|def\s+train_model|def\s+train_xgboost',
        'distribution': r'distribution|class_distribution|class_counts|class_weight',
        'scale_pos_weight': r'scale_pos_weight|class_weight|pos_weight',
        'xgboost': r'XGBoost|XGBClassifier|xgboost'
    }
    
    results = {}
    for key, pattern in patterns.items():
        matches = re.findall(pattern, content, re.IGNORECASE)
        results[key] = len(matches)
    
    # Déterminer si le fichier est pertinent
    relevance_score = sum(results.values())
    results['relevance_score'] = relevance_score
    results['file_path'] = file_path
    
    print(f"Analyse de {file_path}: Score de pertinence = {relevance_score}")
    for key, value in results.items():
        if key != 'file_path' and key != 'relevance_score':
            print(f"  - {key}: {value} occurrences")
    
    return results

def find_most_relevant_file(files):
    """
    Trouve le fichier le plus pertinent pour la modification
    
    Args:
        files: Liste des fichiers à analyser
        
    Returns:
        str: Chemin du fichier le plus pertinent
    """
    if not files:
        return None
    
    analyses = [analyze_file_content(file) for file in files]
    analyses.sort(key=lambda x: x['relevance_score'], reverse=True)
    
    most_relevant = analyses[0]['file_path']
    print(f"Fichier le plus pertinent: {most_relevant} (score: {analyses[0]['relevance_score']})")
    
    return most_relevant

def modify_data_preparation(file_path):
    """
    Modifie la préparation des données pour garantir des distributions spécifiques
    
    Args:
        file_path: Chemin du fichier à modifier
        
    Returns:
        bool: True si la modification a réussi, False sinon
    """
    if not file_path or not os.path.exists(file_path):
        print(f"Le fichier {file_path} n'existe pas")
        return False
    
    # Créer une sauvegarde
    backup_file(file_path)
    
    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Rechercher la méthode de préparation des données
    prepare_data_pattern = r'(def\s+prepare_data[^(]*\([^)]*\):.*?)(?=\n\s*def|\Z)'
    prepare_data_match = re.search(prepare_data_pattern, content, re.DOTALL)
    
    if not prepare_data_match:
        print("Méthode de préparation des données non trouvée")
        
        # Rechercher d'autres méthodes potentielles
        alternative_patterns = [
            r'(def\s+prepare_training_data[^(]*\([^)]*\):.*?)(?=\n\s*def|\Z)',
            r'(def\s+get_training_data[^(]*\([^)]*\):.*?)(?=\n\s*def|\Z)',
            r'(def\s+prepare_features[^(]*\([^)]*\):.*?)(?=\n\s*def|\Z)'
        ]
        
        for pattern in alternative_patterns:
            match = re.search(pattern, content, re.DOTALL)
            if match:
                prepare_data_match = match
                print(f"Méthode alternative trouvée: {match.group(1).split(':')[0]}")
                break
    
    if not prepare_data_match:
        print("Aucune méthode de préparation des données trouvée")
        return False
    
    # Extraire la méthode
    prepare_data_method = prepare_data_match.group(0)
    
    # Vérifier si la méthode contient déjà notre modification
    if "# Modification pour garantir des distributions spécifiques" in prepare_data_method:
        print("La méthode a déjà été modifiée")
        return True
    
    # Analyser la méthode pour trouver où insérer notre code
    lines = prepare_data_method.split('\n')
    indent = ''
    return_line_index = -1
    
    for i, line in enumerate(lines):
        if 'return' in line:
            return_line_index = i
            # Déterminer l'indentation
            indent_match = re.match(r'^(\s*)', line)
            if indent_match:
                indent = indent_match.group(1)
            break
    
    if return_line_index == -1:
        print("Instruction 'return' non trouvée dans la méthode")
        return False
    
    # Créer le code à insérer
    insert_code = f"""
{indent}# Modification pour garantir des distributions spécifiques
{indent}# Vérifier si nous traitons un numéro spécifique
{indent}if 'num' in locals() or 'num' in globals():
{indent}    current_num = num
{indent}elif hasattr(self, 'current_number'):
{indent}    current_num = self.current_number
{indent}else:
{indent}    current_num = None
{indent}
{indent}if current_num is not None:
{indent}    # Charger les valeurs spécifiques si disponibles
{indent}    spw_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'real_scale_pos_weight.json')
{indent}    try:
{indent}        with open(spw_file, 'r') as f:
{indent}            spw_values = json.load(f)
{indent}        
{indent}        # Vérifier si nous avons une valeur pour ce numéro
{indent}        if str(current_num) in spw_values:
{indent}            target_spw = float(spw_values[str(current_num)])
{indent}            
{indent}            # Calculer la distribution actuelle
{indent}            if isinstance(y, pd.Series) or isinstance(y, np.ndarray):
{indent}                neg_count = np.sum(y == 0)
{indent}                pos_count = np.sum(y == 1)
{indent}                current_spw = neg_count / max(1, pos_count)
{indent}                
{indent}                print(f"Numéro {{current_num}}: Distribution actuelle [négatifs={{neg_count}}, positifs={{pos_count}}]")
{indent}                print(f"Numéro {{current_num}}: scale_pos_weight actuel = {{current_spw:.4f}}, cible = {{target_spw:.4f}}")
{indent}                
{indent}                # Si la distribution actuelle est différente de la cible, ajuster les données
{indent}                if abs(current_spw - target_spw) > 0.1:
{indent}                    print(f"Numéro {{current_num}}: Ajustement de la distribution pour atteindre scale_pos_weight = {{target_spw:.4f}}")
{indent}                    
{indent}                    # Calculer la nouvelle distribution cible
{indent}                    total_samples = len(y)
{indent}                    target_pos_count = total_samples / (target_spw + 1)
{indent}                    target_neg_count = total_samples - target_pos_count
{indent}                    
{indent}                    # Ajuster les données en sous-échantillonnant ou sur-échantillonnant
{indent}                    if isinstance(X, pd.DataFrame) and isinstance(y, pd.Series):
{indent}                        # Séparer les échantillons positifs et négatifs
{indent}                        X_pos = X[y == 1]
{indent}                        X_neg = X[y == 0]
{indent}                        y_pos = y[y == 1]
{indent}                        y_neg = y[y == 0]
{indent}                        
{indent}                        # Ajuster les échantillons positifs
{indent}                        if pos_count < target_pos_count:
{indent}                            # Sur-échantillonnage des positifs
{indent}                            ratio = target_pos_count / pos_count
{indent}                            n_samples = int(pos_count * ratio)
{indent}                            indices = np.random.choice(len(X_pos), size=n_samples, replace=True)
{indent}                            X_pos_resampled = X_pos.iloc[indices]
{indent}                            y_pos_resampled = y_pos.iloc[indices]
{indent}                        else:
{indent}                            # Sous-échantillonnage des positifs
{indent}                            indices = np.random.choice(len(X_pos), size=int(target_pos_count), replace=False)
{indent}                            X_pos_resampled = X_pos.iloc[indices]
{indent}                            y_pos_resampled = y_pos.iloc[indices]
{indent}                        
{indent}                        # Ajuster les échantillons négatifs
{indent}                        if neg_count < target_neg_count:
{indent}                            # Sur-échantillonnage des négatifs
{indent}                            ratio = target_neg_count / neg_count
{indent}                            n_samples = int(neg_count * ratio)
{indent}                            indices = np.random.choice(len(X_neg), size=n_samples, replace=True)
{indent}                            X_neg_resampled = X_neg.iloc[indices]
{indent}                            y_neg_resampled = y_neg.iloc[indices]
{indent}                        else:
{indent}                            # Sous-échantillonnage des négatifs
{indent}                            indices = np.random.choice(len(X_neg), size=int(target_neg_count), replace=False)
{indent}                            X_neg_resampled = X_neg.iloc[indices]
{indent}                            y_neg_resampled = y_neg.iloc[indices]
{indent}                        
{indent}                        # Combiner les échantillons
{indent}                        X = pd.concat([X_pos_resampled, X_neg_resampled])
{indent}                        y = pd.concat([y_pos_resampled, y_neg_resampled])
{indent}                        
{indent}                        # Mélanger les données
{indent}                        indices = np.random.permutation(len(X))
{indent}                        X = X.iloc[indices].reset_index(drop=True)
{indent}                        y = y.iloc[indices].reset_index(drop=True)
{indent}                        
{indent}                        # Vérifier la nouvelle distribution
{indent}                        new_neg_count = np.sum(y == 0)
{indent}                        new_pos_count = np.sum(y == 1)
{indent}                        new_spw = new_neg_count / max(1, new_pos_count)
{indent}                        
{indent}                        print(f"Numéro {{current_num}}: Nouvelle distribution [négatifs={{new_neg_count}}, positifs={{new_pos_count}}]")
{indent}                        print(f"Numéro {{current_num}}: Nouveau scale_pos_weight = {{new_spw:.4f}}")
{indent}    except Exception as e:
{indent}        print(f"Erreur lors de l'ajustement de la distribution: {{e}}")
"""
    
    # Insérer le code avant l'instruction return
    lines.insert(return_line_index, insert_code)
    
    # Reconstruire la méthode
    modified_method = '\n'.join(lines)
    
    # Remplacer l'ancienne méthode par la nouvelle
    new_content = content.replace(prepare_data_match.group(0), modified_method)
    
    # Ajouter les imports nécessaires
    if 'import json' not in new_content:
        import_section = 'import json\n'
        if 'import os' not in new_content:
            import_section = 'import os\n' + import_section
        
        # Trouver la section d'imports
        import_match = re.search(r'import.*?\n\n', new_content, re.DOTALL)
        if import_match:
            new_content = new_content[:import_match.end()] + import_section + new_content[import_match.end():]
        else:
            new_content = import_section + new_content
    
    # Écrire le contenu modifié
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Fichier {file_path} modifié avec succès")
    return True

def main():
    """Fonction principale"""
    print("Modification du flux de données pour garantir des distributions spécifiques")
    
    # Trouver les fichiers pertinents
    files = find_data_preparation_files()
    
    if not files:
        print("Aucun fichier pertinent trouvé")
        return
    
    # Trouver le fichier le plus pertinent
    most_relevant = find_most_relevant_file(files)
    
    if not most_relevant:
        print("Aucun fichier pertinent trouvé")
        return
    
    # Modifier la préparation des données
    success = modify_data_preparation(most_relevant)
    
    if success:
        print("\nModification terminée!")
        print("Le flux de données a été modifié pour garantir des distributions spécifiques pour chaque numéro.")
        print("Veuillez redémarrer votre application pour que les modifications prennent effet.")
    else:
        print("\nÉchec de la modification")
        print("Veuillez vérifier manuellement les fichiers et appliquer les modifications nécessaires.")

if __name__ == "__main__":
    main()
