"""
Script de lancement pour l'application Keno avec gestion d'erreurs améliorée.
Ce script lance l'application principale et gère les erreurs de manière robuste.
"""

import os
import sys
import traceback
import subprocess

def run_main_app():
    """
    Lance l'application principale et gère les erreurs
    
    Returns:
        int: Code de sortie (0 pour succès, autre pour erreur)
    """
    try:
        print("Lancement de l'application Keno...")
        
        # Obtenir le chemin du script main.py
        main_script = os.path.join(os.path.dirname(os.path.abspath(__file__)), "main.py")
        
        # Vérifier que le script existe
        if not os.path.exists(main_script):
            print(f"Erreur: Le script {main_script} n'existe pas.")
            return 1
        
        # Lancer le script main.py avec Python
        result = subprocess.run([sys.executable, main_script], 
                               stdout=subprocess.PIPE, 
                               stderr=subprocess.PIPE,
                               text=True)
        
        # Afficher la sortie standard
        if result.stdout:
            print("=== Sortie standard ===")
            print(result.stdout)
        
        # Afficher les erreurs
        if result.stderr:
            print("=== Erreurs ===")
            print(result.stderr)
        
        # Retourner le code de sortie
        return result.returncode
        
    except Exception as e:
        print(f"Erreur lors du lancement de l'application: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        exit_code = run_main_app()
        print(f"Application terminée avec le code: {exit_code}")
        sys.exit(exit_code)
    except Exception as e:
        print(f"Erreur non gérée: {e}")
        traceback.print_exc()
        sys.exit(1)
