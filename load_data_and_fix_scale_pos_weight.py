"""
Script pour charger les donn<PERSON>, analyser la distribution réelle de chaque numéro,
calculer les valeurs correctes de scale_pos_weight et modifier les fichiers de l'optimiseur XGBoost.
"""

import os
import sys
import json
import shutil
import numpy as np
import pandas as pd
from collections import Counter
from datetime import datetime
import matplotlib.pyplot as plt

def backup_file(file_path):
    """
    Crée une sauvegarde du fichier
    
    Args:
        file_path: Chemin du fichier à sauvegarder
        
    Returns:
        str: Chemin de la sauvegarde
    """
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def load_keno_data():
    """
    Charge les données Keno en utilisant différentes approches
    
    Returns:
        list: Liste des tirages Keno
    """
    print("Tentative de chargement des données Keno...")
    
    # Approche 1: Utiliser le module keno_data
    try:
        from keno_data import KenoDataManager
        print("Module keno_data importé avec succès")
        
        data_manager = KenoDataManager()
        
        # Essayer différentes méthodes de chargement
        if hasattr(data_manager, 'load_data'):
            data_loaded = data_manager.load_data()
            print("Méthode load_data() utilisée")
        elif hasattr(data_manager, 'load'):
            data_loaded = data_manager.load()
            print("Méthode load() utilisée")
        elif hasattr(data_manager, 'load_draws'):
            data_loaded = data_manager.load_draws()
            print("Méthode load_draws() utilisée")
        else:
            # Si aucune méthode de chargement n'est trouvée, vérifier si les données sont déjà chargées
            data_loaded = hasattr(data_manager, 'draws') and len(data_manager.draws) > 0
            print("Vérification des données déjà chargées")
        
        if data_loaded and hasattr(data_manager, 'draws') and data_manager.draws:
            print(f"Données chargées avec succès: {len(data_manager.draws)} tirages")
            return data_manager.draws
        else:
            print("Échec du chargement des données avec le module keno_data")
    except Exception as e:
        print(f"Erreur lors du chargement des données avec le module keno_data: {e}")
    
    # Approche 2: Utiliser le module main
    try:
        import main
        print("Module main importé avec succès")
        
        # Vérifier si le module main a une fonction pour charger les données
        if hasattr(main, 'load_keno_data'):
            draws = main.load_keno_data()
            if draws:
                print(f"Données chargées avec succès via main.load_keno_data(): {len(draws)} tirages")
                return draws
        
        # Vérifier si le module main a un gestionnaire de données
        if hasattr(main, 'data_manager'):
            data_manager = main.data_manager
            if hasattr(data_manager, 'draws') and data_manager.draws:
                print(f"Données chargées avec succès via main.data_manager: {len(data_manager.draws)} tirages")
                return data_manager.draws
        
        print("Échec du chargement des données avec le module main")
    except Exception as e:
        print(f"Erreur lors du chargement des données avec le module main: {e}")
    
    # Approche 3: Demander à l'utilisateur de charger les données manuellement
    print("\nImpossible de charger automatiquement les données Keno.")
    print("Veuillez charger les données manuellement et les fournir au script.")
    
    # Demander à l'utilisateur s'il souhaite continuer avec des valeurs aléatoires
    print("\nSouhaitez-vous continuer avec des valeurs aléatoires pour scale_pos_weight? (o/n)")
    choice = input().lower()
    
    if choice == 'o':
        print("Utilisation de valeurs aléatoires pour scale_pos_weight")
        return None
    else:
        print("Annulation du script")
        sys.exit(1)

def analyze_keno_distribution(draws):
    """
    Analyse la distribution des numéros dans les tirages Keno
    
    Args:
        draws: Liste des tirages Keno
        
    Returns:
        dict: Statistiques de distribution pour chaque numéro
    """
    if not draws:
        print("Aucun tirage disponible pour l'analyse")
        return {}
    
    print(f"Analyse de la distribution des numéros dans {len(draws)} tirages...")
    
    # Compter les occurrences de chaque numéro
    occurrences = Counter()
    total_draws = len(draws)
    
    for draw in draws:
        if hasattr(draw, 'draw_numbers') and draw.draw_numbers:
            for num in draw.draw_numbers:
                if 1 <= num <= 70:  # Vérifier que le numéro est valide
                    occurrences[num] += 1
    
    # Calculer les statistiques pour chaque numéro
    stats = {}
    
    for num in range(1, 71):
        # Nombre de fois où le numéro est apparu
        pos_count = occurrences.get(num, 0)
        
        # Nombre de fois où le numéro n'est pas apparu
        neg_count = total_draws - pos_count
        
        # Calculer le ratio négatif/positif (scale_pos_weight)
        if pos_count > 0:
            ratio = neg_count / pos_count
        else:
            ratio = 2.57  # Valeur par défaut si le numéro n'est jamais apparu
        
        # Calculer la fréquence d'apparition
        frequency = pos_count / total_draws if total_draws > 0 else 0
        
        # Stocker les statistiques
        stats[num] = {
            'occurrences': pos_count,
            'total_draws': total_draws,
            'frequency': frequency,
            'neg_count': neg_count,
            'pos_count': pos_count,
            'scale_pos_weight': ratio
        }
        
        print(f"Numéro {num}: Apparitions: {pos_count}/{total_draws} ({frequency:.4f}), scale_pos_weight: {ratio:.4f}")
    
    return stats

def generate_random_weights():
    """
    Génère des valeurs aléatoires de scale_pos_weight pour chaque numéro
    
    Returns:
        dict: Dictionnaire des valeurs par numéro
    """
    import random
    
    # Générer des valeurs aléatoires autour de 2.57
    values = {}
    for num in range(1, 71):
        # Générer une valeur entre 1.5 et 3.5
        value = 1.5 + random.random() * 2.0
        values[num] = round(value, 4)
    
    print("Valeurs de scale_pos_weight générées aléatoirement:")
    for num in range(1, 11):  # Afficher les 10 premières valeurs
        print(f"  Numéro {num}: {values[num]}")
    
    return values

def calculate_scale_pos_weights(stats=None):
    """
    Calcule les valeurs de scale_pos_weight pour chaque numéro
    
    Args:
        stats: Statistiques de distribution pour chaque numéro
        
    Returns:
        dict: Dictionnaire des valeurs de scale_pos_weight par numéro
    """
    if not stats:
        print("Aucune statistique disponible, génération de valeurs aléatoires...")
        return generate_random_weights()
    
    print("Calcul des valeurs de scale_pos_weight basées sur la distribution réelle...")
    
    # Extraire les valeurs de scale_pos_weight
    weights = {}
    for num in range(1, 71):
        if num in stats:
            weights[num] = stats[num]['scale_pos_weight']
        else:
            weights[num] = 2.57  # Valeur par défaut
    
    return weights

def save_weights_to_json(weights):
    """
    Sauvegarde les poids dans un fichier JSON
    
    Args:
        weights: Dictionnaire des poids par numéro
        
    Returns:
        str: Chemin du fichier créé
    """
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Chemin du fichier
    file_path = os.path.join('data', 'real_scale_pos_weight.json')
    
    # Convertir les clés en chaînes pour la sérialisation JSON
    weights_str = {str(k): v for k, v in weights.items()}
    
    # Sauvegarder les poids
    with open(file_path, 'w') as f:
        json.dump(weights_str, f, indent=2)
    
    print(f"Poids sauvegardés dans {file_path}")
    
    return file_path

def create_visualization(stats=None, weights=None):
    """
    Crée des visualisations des distributions et des poids
    
    Args:
        stats: Statistiques de distribution pour chaque numéro
        weights: Dictionnaire des poids par numéro
    """
    if not stats and not weights:
        print("Aucune donnée disponible pour la visualisation")
        return
    
    # Créer un DataFrame pour l'analyse
    df = pd.DataFrame({
        'number': list(range(1, 71)),
        'scale_pos_weight': [weights.get(num, 2.57) for num in range(1, 71)]
    })
    
    if stats:
        df['frequency'] = [stats.get(num, {}).get('frequency', 0) for num in range(1, 71)]
        df['occurrences'] = [stats.get(num, {}).get('occurrences', 0) for num in range(1, 71)]
    
    # Créer un graphique des poids
    plt.figure(figsize=(12, 6))
    plt.bar(df['number'], df['scale_pos_weight'])
    plt.axhline(y=df['scale_pos_weight'].mean(), color='r', linestyle='-', label=f'Moyenne ({df["scale_pos_weight"].mean():.4f})')
    plt.xlabel('Numéro Keno')
    plt.ylabel('scale_pos_weight')
    plt.title('Valeurs de scale_pos_weight pour chaque numéro Keno')
    plt.legend()
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Sauvegarder le graphique
    plt.savefig('scale_pos_weight_distribution.png')
    print("Graphique des poids sauvegardé dans scale_pos_weight_distribution.png")
    
    if stats:
        # Créer un graphique des fréquences
        plt.figure(figsize=(12, 6))
        plt.bar(df['number'], df['frequency'])
        plt.axhline(y=df['frequency'].mean(), color='r', linestyle='-', label=f'Moyenne ({df["frequency"].mean():.4f})')
        plt.xlabel('Numéro Keno')
        plt.ylabel('Fréquence d\'apparition')
        plt.title('Fréquence d\'apparition de chaque numéro Keno')
        plt.legend()
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        
        # Sauvegarder le graphique
        plt.savefig('number_frequency_distribution.png')
        print("Graphique des fréquences sauvegardé dans number_frequency_distribution.png")
        
        # Créer un graphique de corrélation entre fréquence et scale_pos_weight
        plt.figure(figsize=(10, 6))
        plt.scatter(df['frequency'], df['scale_pos_weight'])
        plt.xlabel('Fréquence d\'apparition')
        plt.ylabel('scale_pos_weight')
        plt.title('Corrélation entre fréquence d\'apparition et scale_pos_weight')
        plt.grid(True, alpha=0.3)
        
        # Sauvegarder le graphique
        plt.savefig('frequency_vs_scale_pos_weight.png')
        print("Graphique de corrélation sauvegardé dans frequency_vs_scale_pos_weight.png")

def modify_optimizer_file(file_path, weights_file):
    """
    Modifie le fichier de l'optimiseur XGBoost
    
    Args:
        file_path: Chemin du fichier à modifier
        weights_file: Chemin du fichier de poids
        
    Returns:
        bool: True si la modification a réussi, False sinon
    """
    if not os.path.exists(file_path):
        print(f"Le fichier {file_path} n'existe pas")
        return False
    
    # Créer une sauvegarde
    backup_file(file_path)
    
    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Ajouter l'import du fichier de poids
    import_code = """import os
import json
import numpy as np
import pandas as pd"""
    
    weights_import_code = """import os
import json
import numpy as np
import pandas as pd

# Charger les poids scale_pos_weight spécifiques à chaque numéro
REAL_SPW_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'real_scale_pos_weight.json')
try:
    with open(REAL_SPW_FILE, 'r') as f:
        REAL_SPW_VALUES = json.load(f)
    print(f"Valeurs de scale_pos_weight chargées depuis {REAL_SPW_FILE}")
except Exception as e:
    print(f"Erreur lors du chargement des valeurs de scale_pos_weight: {e}")
    REAL_SPW_VALUES = {}"""
    
    new_content = content.replace(import_code, weights_import_code)
    
    # Remplacer la méthode calculate_optimal_scale_pos_weight
    old_method = """    def calculate_optimal_scale_pos_weight(self, y_train):
        \"\"\"
        Calcule la valeur optimale de scale_pos_weight basée sur la distribution des classes
        
        Args:
            y_train: Étiquettes d'entraînement
            
        Returns:
            float: Valeur optimale de scale_pos_weight
        \"\"\"
        # Compter les exemples positifs et négatifs
        neg_count = np.sum(y_train == 0)
        pos_count = np.sum(y_train == 1)
        
        # Éviter la division par zéro
        if pos_count > 0:
            # Calculer le ratio
            ratio = neg_count / pos_count
            return ratio
        else:
            # Valeur par défaut si aucun exemple positif
            return 1.0"""
    
    new_method = """    def calculate_optimal_scale_pos_weight(self, y_train, num=None):
        \"\"\"
        Calcule la valeur optimale de scale_pos_weight basée sur la distribution des classes
        
        Args:
            y_train: Étiquettes d'entraînement
            num: Numéro Keno (pour utiliser une valeur spécifique)
            
        Returns:
            float: Valeur optimale de scale_pos_weight
        \"\"\"
        # Si un numéro est spécifié, utiliser la valeur spécifique
        if num is not None and str(num) in REAL_SPW_VALUES:
            specific_value = float(REAL_SPW_VALUES[str(num)])
            
            if self.verbose > 0:
                print(f"  Numéro {num}: Utilisation de scale_pos_weight spécifique = {specific_value:.4f}")
            
            return specific_value
        
        # Sinon, calculer normalement
        # Compter les exemples positifs et négatifs
        neg_count = np.sum(y_train == 0)
        pos_count = np.sum(y_train == 1)
        
        # Éviter la division par zéro
        if pos_count > 0:
            # Calculer le ratio
            ratio = neg_count / pos_count
            
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
                print(f"  Numéro {num}: Ratio calculé = {ratio:.4f}")
            
            return ratio
        else:
            # Valeur par défaut si aucun exemple positif
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Aucun exemple positif trouvé, utilisation de scale_pos_weight=1.0")
            
            return 1.0"""
    
    new_content = new_content.replace(old_method, new_method)
    
    # Modifier les appels à la méthode
    # Dans optimize_hyperparameters
    old_call_1 = "scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train)"
    new_call_1 = "scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train, num=num)"
    
    new_content = new_content.replace(old_call_1, new_call_1)
    
    # Dans train_model
    old_call_2 = "scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train)"
    new_call_2 = "scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train, num=num)"
    
    new_content = new_content.replace(old_call_2, new_call_2)
    
    # Écrire le contenu modifié
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Fichier {file_path} modifié avec succès")
    
    return True

def main():
    """Fonction principale"""
    print("Chargement des données Keno et correction du problème de scale_pos_weight")
    
    # Charger les données Keno
    draws = load_keno_data()
    
    # Analyser la distribution des numéros
    stats = {}
    if draws:
        stats = analyze_keno_distribution(draws)
    
    # Calculer les valeurs de scale_pos_weight
    weights = calculate_scale_pos_weights(stats)
    
    # Sauvegarder les poids dans un fichier JSON
    weights_file = save_weights_to_json(weights)
    
    # Créer des visualisations
    create_visualization(stats, weights)
    
    # Modifier les fichiers de l'optimiseur
    files_to_modify = [
        'keno_xgboost_optimizer.py',
        'keno_xgboost_optimizer_simple.py'
    ]
    
    for file_path in files_to_modify:
        if os.path.exists(file_path):
            print(f"Modification du fichier {file_path}...")
            modify_optimizer_file(file_path, weights_file)
        else:
            print(f"Le fichier {file_path} n'existe pas")
    
    print("\nCorrection terminée!")
    print("Les fichiers de l'optimiseur XGBoost ont été modifiés pour utiliser des valeurs différentes de scale_pos_weight pour chaque numéro.")
    print("Veuillez redémarrer votre application pour que les modifications prennent effet.")

if __name__ == "__main__":
    main()
