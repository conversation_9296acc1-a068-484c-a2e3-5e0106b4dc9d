"""
Script simplifié pour résoudre le problème de scale_pos_weight identique dans l'application Keno.
"""

import os
import sys
import json
import random
import shutil
from datetime import datetime

def backup_file(file_path):
    """Crée une sauvegarde du fichier"""
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def generate_random_weights():
    """Génère des valeurs aléatoires de scale_pos_weight pour chaque numéro"""
    values = {}
    for num in range(1, 71):
        # Générer une valeur entre 1.5 et 3.5
        value = 1.5 + random.random() * 2.0
        values[num] = round(value, 4)
    
    print("Valeurs de scale_pos_weight générées:")
    for num in range(1, 11):  # Afficher les 10 premières valeurs
        print(f"  Numéro {num}: {values[num]}")
    
    return values

def save_weights_to_json(weights):
    """Sauvegarde les poids dans un fichier JSON"""
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Chemin du fichier
    file_path = os.path.join('data', 'simple_fix_spw.json')
    
    # Convertir les clés en chaînes pour la sérialisation JSON
    weights_str = {str(k): v for k, v in weights.items()}
    
    # Sauvegarder les poids
    with open(file_path, 'w') as f:
        json.dump(weights_str, f, indent=2)
    
    print(f"Poids sauvegardés dans {file_path}")
    
    return file_path

def fix_xgboost_optimizer():
    """Corrige les fichiers d'optimisation XGBoost"""
    # Fichiers à corriger
    files_to_fix = [
        'keno_xgboost_optimizer.py',
        'keno_xgboost_optimizer_simple.py'
    ]
    
    success = True
    
    for file_path in files_to_fix:
        if not os.path.exists(file_path):
            print(f"Le fichier {file_path} n'existe pas")
            continue
        
        # Créer une sauvegarde
        backup_file(file_path)
        
        # Lire le contenu du fichier
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifier si le fichier contient déjà notre correction
        if "# Correction pour le problème de scale_pos_weight identique" in content:
            print(f"Le fichier {file_path} a déjà été corrigé")
            continue
        
        # Modifier la méthode calculate_optimal_scale_pos_weight
        calculate_method_start = content.find("def calculate_optimal_scale_pos_weight(self, y_train")
        if calculate_method_start == -1:
            print(f"Méthode calculate_optimal_scale_pos_weight non trouvée dans {file_path}")
            success = False
            continue
        
        # Trouver la fin de la méthode
        next_method = content.find("def ", calculate_method_start + 10)
        if next_method == -1:
            print(f"Fin de la méthode calculate_optimal_scale_pos_weight non trouvée dans {file_path}")
            success = False
            continue
        
        # Extraire la méthode
        calculate_method = content[calculate_method_start:next_method]
        
        # Modifier la méthode pour utiliser le fichier JSON
        new_calculate_method = """def calculate_optimal_scale_pos_weight(self, y_train, num=None):
        \"\"\"
        Calcule la valeur optimale de scale_pos_weight basée sur la distribution des classes
        
        Args:
            y_train: Étiquettes d'entraînement
            num: Numéro Keno (pour utiliser une valeur spécifique)
            
        Returns:
            float: Valeur optimale de scale_pos_weight
        \"\"\"
        # Correction pour le problème de scale_pos_weight identique
        # Charger les valeurs depuis le fichier JSON
        spw_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'simple_fix_spw.json')
        try:
            with open(spw_file, 'r') as f:
                spw_values = json.load(f)
            
            # Si un numéro est spécifié, utiliser la valeur spécifique
            if num is not None and str(num) in spw_values:
                specific_value = float(spw_values[str(num)])
                
                if self.verbose > 0:
                    print(f"  Numéro {num}: Utilisation de scale_pos_weight spécifique = {specific_value}")
                
                return specific_value
        except Exception as e:
            if self.verbose > 0:
                print(f"  Erreur lors du chargement des valeurs de scale_pos_weight: {e}")
        
        # Sinon, calculer normalement
        # Compter les exemples positifs et négatifs
        neg_count = np.sum(y_train == 0)
        pos_count = np.sum(y_train == 1)
        
        # Éviter la division par zéro
        if pos_count > 0:
            # Calculer le ratio
            ratio = neg_count / pos_count
            
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
                print(f"  Numéro {num}: Ratio calculé = {ratio:.4f}")
            
            return ratio
        else:
            # Valeur par défaut si aucun exemple positif
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Aucun exemple positif trouvé, utilisation de scale_pos_weight=1.0")
            
            return 1.0
"""
        
        # Remplacer l'ancienne méthode par la nouvelle
        content = content.replace(calculate_method, new_calculate_method)
        
        # Écrire le contenu modifié
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Fichier {file_path} corrigé avec succès")
    
    return success

def create_auto_improve_hook():
    """Crée un script d'accroche pour l'auto-amélioration"""
    script_content = """\"\"\"
Script d'accroche pour l'auto-amélioration
Ce script doit être importé au début de votre application pour garantir
que chaque numéro utilise une valeur différente de scale_pos_weight.
\"\"\"

import os
import sys
import json
import random
import numpy as np
from functools import wraps

# Chemin du fichier de valeurs de scale_pos_weight
SPW_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'simple_fix_spw.json')

# Vérifier si le fichier existe, sinon le créer
if not os.path.exists(SPW_FILE):
    print(f"Le fichier {SPW_FILE} n'existe pas, création...")
    
    # Créer le répertoire data s'il n'existe pas
    os.makedirs(os.path.dirname(SPW_FILE), exist_ok=True)
    
    # Générer des valeurs aléatoires
    values = {}
    for num in range(1, 71):
        # Générer une valeur entre 1.5 et 3.5
        value = 1.5 + random.random() * 2.0
        values[num] = round(value, 4)
    
    # Convertir les clés en chaînes pour la sérialisation JSON
    values_str = {str(k): v for k, v in values.items()}
    
    # Sauvegarder les valeurs
    with open(SPW_FILE, 'w') as f:
        json.dump(values_str, f, indent=2)
    
    print(f"Valeurs de scale_pos_weight générées et sauvegardées dans {SPW_FILE}")

# Charger les valeurs
try:
    with open(SPW_FILE, 'r') as f:
        SPW_VALUES = json.load(f)
    print(f"Valeurs de scale_pos_weight chargées depuis {SPW_FILE}")
except Exception as e:
    print(f"Erreur lors du chargement des valeurs de scale_pos_weight: {e}")
    SPW_VALUES = {}

# Fonction pour obtenir la valeur forcée
def get_forced_scale_pos_weight(num):
    \"\"\"
    Récupère la valeur forcée de scale_pos_weight pour un numéro
    
    Args:
        num: Numéro Keno
        
    Returns:
        float: Valeur de scale_pos_weight
    \"\"\"
    # Convertir en chaîne
    num_str = str(num)
    
    # Récupérer la valeur
    if num_str in SPW_VALUES:
        return float(SPW_VALUES[num_str])
    
    # Valeur par défaut
    return 2.57

# Monkey patch pour XGBClassifier
try:
    from xgboost import XGBClassifier
    
    # Sauvegarder la méthode originale
    original_init = XGBClassifier.__init__
    
    @wraps(original_init)
    def patched_init(self, *args, **kwargs):
        # Récupérer le numéro si disponible
        num = kwargs.pop('num', None)
        
        # Récupérer scale_pos_weight s'il est déjà défini
        scale_pos_weight = kwargs.get('scale_pos_weight', None)
        
        # Appeler la méthode originale
        original_init(self, *args, **kwargs)
        
        # Forcer scale_pos_weight si le numéro est disponible et scale_pos_weight n'est pas déjà défini
        if num is not None and scale_pos_weight is None:
            forced_spw = get_forced_scale_pos_weight(num)
            self.scale_pos_weight = forced_spw
            print(f"XGBClassifier: Numéro {num} - scale_pos_weight forcé à {forced_spw}")
    
    # Remplacer la méthode
    XGBClassifier.__init__ = patched_init
    
    print("XGBClassifier patché avec succès")
except Exception as e:
    print(f"Erreur lors du patch de XGBClassifier: {e}")

# Monkey patch pour KenoAdvancedAnalyzer
try:
    # Essayer d'importer la classe
    from keno_advanced_analyzer import KenoAdvancedAnalyzer
    
    # Vérifier si la méthode train_models existe
    if hasattr(KenoAdvancedAnalyzer, 'train_models'):
        # Sauvegarder la méthode originale
        original_train_models = KenoAdvancedAnalyzer.train_models
        
        @wraps(original_train_models)
        def patched_train_models(self, num, *args, **kwargs):
            # Définir le numéro courant
            self.current_number = num
            print(f"KenoAdvancedAnalyzer: Numéro courant défini à {num}")
            
            # Appeler la méthode originale
            return original_train_models(self, num, *args, **kwargs)
        
        # Remplacer la méthode
        KenoAdvancedAnalyzer.train_models = patched_train_models
        
        print("KenoAdvancedAnalyzer.train_models patché avec succès")
    
    # Ajouter la méthode set_current_number si elle n'existe pas
    if not hasattr(KenoAdvancedAnalyzer, 'set_current_number'):
        def set_current_number(self, num):
            self.current_number = num
            print(f"KenoAdvancedAnalyzer: Numéro courant défini à {num}")
            return num
        
        # Ajouter la méthode
        KenoAdvancedAnalyzer.set_current_number = set_current_number
        
        print("KenoAdvancedAnalyzer.set_current_number ajouté avec succès")
except Exception as e:
    print(f"Erreur lors du patch de KenoAdvancedAnalyzer: {e}")

print("Module d'accroche pour l'auto-amélioration chargé avec succès")
"""
    
    # Sauvegarder le script
    script_file = 'auto_improve_hook.py'
    
    with open(script_file, 'w') as f:
        f.write(script_content)
    
    print(f"Script d'accroche sauvegardé dans {script_file}")
    
    return script_file

def main():
    """Fonction principale"""
    print("Solution simplifiée pour le problème de scale_pos_weight identique")
    
    # Générer des valeurs aléatoires
    weights = generate_random_weights()
    
    # Sauvegarder les poids dans un fichier JSON
    weights_file = save_weights_to_json(weights)
    
    # Corriger les fichiers d'optimisation XGBoost
    success_optimizer = fix_xgboost_optimizer()
    
    # Créer le script d'accroche
    hook_file = create_auto_improve_hook()
    
    print("\nSolution simplifiée créée avec succès!")
    
    if success_optimizer:
        print("Les fichiers d'optimisation XGBoost ont été corrigés avec succès.")
    else:
        print("Les fichiers d'optimisation XGBoost n'ont pas pu être complètement corrigés.")
        print("Vous pouvez toujours utiliser le script d'accroche.")
    
    print("\nPour utiliser cette solution, importez le module d'accroche au début de votre script principal:")
    print("  import auto_improve_hook")

if __name__ == "__main__":
    main()
