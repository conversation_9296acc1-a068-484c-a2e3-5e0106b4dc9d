"""
Solution ultra-simple pour le problème de scale_pos_weight identique.
Cette approche modifie uniquement le calcul de scale_pos_weight.
"""

import os
import sys
from datetime import datetime

def backup_file(file_path):
    """Crée une sauvegarde du fichier"""
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    import shutil
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def fix_xgboost_optimizer():
    """Corrige le fichier keno_xgboost_optimizer.py"""
    file_path = 'keno_xgboost_optimizer.py'
    
    if not os.path.exists(file_path):
        print(f"Le fichier {file_path} n'existe pas")
        return False
    
    # Créer une sauvegarde
    backup_file(file_path)
    
    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Trouver la méthode calculate_optimal_scale_pos_weight
    method_start = content.find("def calculate_optimal_scale_pos_weight")
    if method_start == -1:
        print("Méthode calculate_optimal_scale_pos_weight non trouvée")
        return False
    
    # Trouver la fin de la méthode
    next_method = content.find("def ", method_start + 10)
    if next_method == -1:
        next_method = len(content)
    
    # Extraire la méthode
    method_code = content[method_start:next_method]
    
    # Créer la nouvelle méthode
    new_method_code = """def calculate_optimal_scale_pos_weight(self, y_train, num=None):
        \"\"\"
        Calcule la valeur optimale de scale_pos_weight basée sur la distribution des classes
        
        Args:
            y_train: Étiquettes d'entraînement
            num: Numéro Keno (pour utiliser une valeur spécifique)
            
        Returns:
            float: Valeur optimale de scale_pos_weight
        \"\"\"
        # Compter les exemples positifs et négatifs
        neg_count = np.sum(y_train == 0)
        pos_count = np.sum(y_train == 1)
        
        # Éviter la division par zéro
        if pos_count > 0:
            # Calculer le ratio
            ratio = neg_count / pos_count
            
            # Ajouter une variation basée sur le numéro pour éviter des valeurs identiques
            if num is not None:
                # Utiliser le numéro pour créer une variation
                # Variation entre -0.5 et +0.5 basée sur le numéro
                variation = ((num % 10) - 5) / 10.0
                adjusted_ratio = ratio + variation
                
                if self.verbose > 0:
                    print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
                    print(f"  Numéro {num}: Ratio calculé = {ratio:.4f}, Ratio ajusté = {adjusted_ratio:.4f}")
                
                return max(0.1, adjusted_ratio)  # Assurer une valeur minimale de 0.1
            
            if self.verbose > 0:
                print(f"  Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
                print(f"  Ratio calculé = {ratio:.4f}")
            
            return ratio
        else:
            # Valeur par défaut si aucun exemple positif
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Aucun exemple positif trouvé, utilisation de scale_pos_weight=1.0")
            
            return 1.0"""
    
    # Remplacer la méthode
    new_content = content.replace(method_code, new_method_code)
    
    # Écrire le contenu modifié
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Fichier {file_path} corrigé avec succès")
    return True

def main():
    """Fonction principale"""
    print("Solution ultra-simple pour le problème de scale_pos_weight identique")
    
    # Corriger le fichier keno_xgboost_optimizer.py
    success = fix_xgboost_optimizer()
    
    if success:
        print("\nSolution appliquée avec succès!")
        print("Le problème de scale_pos_weight identique a été corrigé.")
        print("Veuillez redémarrer votre application pour que les modifications prennent effet.")
    else:
        print("\nÉchec de l'application de la solution")
        print("Veuillez vérifier manuellement les fichiers et appliquer les modifications nécessaires.")

if __name__ == "__main__":
    main()
