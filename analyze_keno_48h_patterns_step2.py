"""
Script pour analyser les motifs spécifiques entre tirages Keno sur 48h - Étape 2
Ce script identifie les motifs spécifiques et les numéros qui ont tendance à se répéter.
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter, defaultdict
from datetime import datetime, timedelta

# Essayer d'importer le gestionnaire de données Keno
try:
    from keno_data import KenoDataManager, KenoDrawData
    print("Module de gestion des données Keno importé avec succès")
except ImportError:
    print("Erreur: Module de gestion des données Keno non disponible")
    sys.exit(1)

# Importer les fonctions de l'étape 1
from analyze_keno_48h_patterns import group_draws_by_48h_periods

def analyze_number_specific_patterns(groups):
    """
    Analyse les motifs spécifiques à chaque numéro sur les périodes de 48h
    
    Args:
        groups: Liste de groupes de tirages
        
    Returns:
        dict: Statistiques sur les motifs spécifiques à chaque numéro
    """
    # Statistiques par numéro
    number_stats = {}
    
    # Initialiser les statistiques pour chaque numéro
    for num in range(1, 71):  # Keno utilise les numéros 1-70
        number_stats[num] = {
            'total_appearances': 0,       # Nombre total d'apparitions
            'total_groups': 0,            # Nombre de groupes où le numéro apparaît au moins une fois
            'consecutive_appearances': 0,  # Nombre d'apparitions dans des tirages consécutifs
            'appearance_patterns': Counter(),  # Motifs d'apparition (ex: "101" = présent, absent, présent)
            'position_appearances': defaultdict(int)  # Apparitions par position dans le groupe
        }
    
    # Analyser chaque groupe
    for group in groups:
        # Créer un ensemble des numéros qui apparaissent dans ce groupe
        group_numbers = set()
        for draw in group:
            group_numbers.update(draw.draw_numbers)
        
        # Pour chaque numéro qui apparaît dans ce groupe
        for num in group_numbers:
            if 1 <= num <= 70:  # Vérifier que le numéro est valide
                number_stats[num]['total_groups'] += 1
                
                # Créer le motif d'apparition pour ce numéro dans ce groupe
                pattern = ""
                for i, draw in enumerate(group):
                    if num in draw.draw_numbers:
                        pattern += "1"
                        number_stats[num]['total_appearances'] += 1
                        number_stats[num]['position_appearances'][i] += 1
                    else:
                        pattern += "0"
                
                # Enregistrer le motif
                number_stats[num]['appearance_patterns'][pattern] += 1
                
                # Vérifier les apparitions consécutives
                if "11" in pattern:
                    number_stats[num]['consecutive_appearances'] += 1
    
    # Calculer des statistiques supplémentaires
    for num in range(1, 71):
        stats = number_stats[num]
        
        # Calculer le pourcentage d'apparitions consécutives
        if stats['total_groups'] > 0:
            stats['consecutive_percentage'] = stats['consecutive_appearances'] / stats['total_groups'] * 100
        else:
            stats['consecutive_percentage'] = 0
        
        # Trouver le motif le plus fréquent
        if stats['appearance_patterns']:
            stats['most_common_pattern'], stats['most_common_pattern_count'] = stats['appearance_patterns'].most_common(1)[0]
            stats['most_common_pattern_percentage'] = stats['most_common_pattern_count'] / stats['total_groups'] * 100
        else:
            stats['most_common_pattern'] = ""
            stats['most_common_pattern_count'] = 0
            stats['most_common_pattern_percentage'] = 0
    
    return number_stats

def find_best_prediction_numbers(number_stats, groups):
    """
    Identifie les numéros avec les meilleurs potentiels de prédiction
    
    Args:
        number_stats: Statistiques par numéro
        groups: Liste de groupes de tirages
        
    Returns:
        list: Liste des numéros avec les meilleurs potentiels, triés par score
    """
    # Calculer un score pour chaque numéro
    number_scores = []
    
    for num in range(1, 71):
        stats = number_stats[num]
        
        # Ignorer les numéros avec trop peu de données
        if stats['total_groups'] < 10:
            continue
        
        # Calculer le score basé sur plusieurs facteurs
        score = 0
        
        # 1. Fréquence d'apparition
        appearance_frequency = stats['total_appearances'] / (stats['total_groups'] * 6)  # 6 tirages max par groupe
        score += appearance_frequency * 10  # Pondération: 10
        
        # 2. Prévisibilité (motif le plus fréquent)
        if stats['total_groups'] > 0:
            predictability = stats['most_common_pattern_count'] / stats['total_groups']
            score += predictability * 20  # Pondération: 20
        
        # 3. Apparitions consécutives
        if stats['total_groups'] > 0:
            consecutive_ratio = stats['consecutive_appearances'] / stats['total_groups']
            score += consecutive_ratio * 15  # Pondération: 15
        
        # 4. Bonus pour les motifs intéressants
        pattern = stats['most_common_pattern']
        if pattern:
            # Bonus pour les motifs qui montrent une tendance claire
            if pattern.count('1') >= 3 and "111" in pattern:
                score += 5  # Bonus pour les séquences de 3+ apparitions consécutives
            elif pattern.startswith('101') or pattern.endswith('101'):
                score += 3  # Bonus pour les motifs alternants
        
        # Ajouter le score à la liste
        number_scores.append({
            'number': num,
            'score': score,
            'appearance_frequency': appearance_frequency * 100,
            'predictability': stats['most_common_pattern_percentage'],
            'consecutive_percentage': stats['consecutive_percentage'],
            'most_common_pattern': stats['most_common_pattern']
        })
    
    # Trier par score décroissant
    number_scores.sort(key=lambda x: x['score'], reverse=True)
    
    return number_scores

def analyze_48h_specific_patterns():
    """
    Analyse les motifs spécifiques entre tirages sur une période de 48h
    """
    # Créer le gestionnaire de données
    data_manager = KenoDataManager()
    
    # Charger les données
    print("Chargement des données Keno...")
    data_loaded = data_manager.load_data()
    
    if not data_loaded or not data_manager.draws:
        print("Erreur: Impossible de charger les données Keno")
        return
    
    print(f"Données chargées: {len(data_manager.draws)} tirages")
    
    # Regrouper les tirages par périodes de 48h
    print("\nRegroupement des tirages par périodes de 48h...")
    groups = group_draws_by_48h_periods(data_manager)
    
    if not groups:
        print("Aucun groupe de tirages trouvé")
        return
    
    # Analyser les motifs spécifiques à chaque numéro
    print("\nAnalyse des motifs spécifiques à chaque numéro...")
    number_stats = analyze_number_specific_patterns(groups)
    
    # Trouver les numéros avec les meilleurs potentiels de prédiction
    print("\nRecherche des numéros avec les meilleurs potentiels de prédiction...")
    best_numbers = find_best_prediction_numbers(number_stats, groups)
    
    # Afficher les 20 meilleurs numéros
    print("\nTop 20 des numéros avec les meilleurs potentiels de prédiction:")
    print("Numéro | Score | Fréquence | Prévisibilité | Consécutifs | Motif le plus fréquent")
    print("-------|-------|-----------|--------------|-------------|--------------------")
    for i, num_data in enumerate(best_numbers[:20]):
        print(f"{num_data['number']:6d} | {num_data['score']:.2f} | {num_data['appearance_frequency']:.2f}% | {num_data['predictability']:.2f}% | {num_data['consecutive_percentage']:.2f}% | {num_data['most_common_pattern']}")
    
    # Créer un DataFrame avec les résultats
    results_df = pd.DataFrame(best_numbers)
    
    # Sauvegarder les résultats dans un fichier CSV
    output_file = 'keno_48h_best_numbers.csv'
    results_df.to_csv(output_file, index=False)
    print(f"\nRésultats sauvegardés dans {output_file}")
    
    # Créer un graphique des scores
    plt.figure(figsize=(12, 6))
    top_numbers = [num_data['number'] for num_data in best_numbers[:20]]
    top_scores = [num_data['score'] for num_data in best_numbers[:20]]
    
    plt.bar(top_numbers, top_scores)
    plt.xlabel('Numéro Keno')
    plt.ylabel('Score de prédictibilité')
    plt.title('Top 20 des numéros Keno avec les meilleurs potentiels de prédiction sur 48h')
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Sauvegarder le graphique
    plt.savefig('keno_48h_best_numbers.png')
    print("Graphique des meilleurs numéros sauvegardé dans keno_48h_best_numbers.png")
    
    # Analyser les motifs les plus fréquents
    print("\nAnalyse des motifs les plus fréquents sur 48h...")
    
    # Collecter tous les motifs
    all_patterns = Counter()
    for num in range(1, 71):
        for pattern, count in number_stats[num]['appearance_patterns'].items():
            all_patterns[pattern] += count
    
    # Afficher les 10 motifs les plus fréquents
    print("\nTop 10 des motifs les plus fréquents:")
    print("Motif | Occurrences | Description")
    print("------|-------------|------------")
    for pattern, count in all_patterns.most_common(10):
        # Créer une description du motif
        description = "Numéro "
        for i, char in enumerate(pattern):
            if char == '1':
                description += f"présent au tirage {i+1}, "
            else:
                description += f"absent au tirage {i+1}, "
        description = description[:-2]  # Supprimer la dernière virgule et espace
        
        print(f"{pattern} | {count:11d} | {description}")
    
    # Créer un graphique des motifs les plus fréquents
    plt.figure(figsize=(14, 6))
    top_patterns = [pattern for pattern, _ in all_patterns.most_common(10)]
    top_pattern_counts = [count for _, count in all_patterns.most_common(10)]
    
    plt.bar(top_patterns, top_pattern_counts)
    plt.xlabel('Motif (1=présent, 0=absent)')
    plt.ylabel('Nombre d\'occurrences')
    plt.title('Top 10 des motifs les plus fréquents sur 48h')
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    plt.xticks(rotation=45)
    
    # Sauvegarder le graphique
    plt.savefig('keno_48h_patterns.png')
    print("Graphique des motifs les plus fréquents sauvegardé dans keno_48h_patterns.png")
    
    # Retourner les résultats
    return {
        'number_stats': number_stats,
        'best_numbers': best_numbers,
        'all_patterns': all_patterns,
        'groups': groups
    }

if __name__ == "__main__":
    analyze_48h_specific_patterns()
