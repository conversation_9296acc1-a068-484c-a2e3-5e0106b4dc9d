"""
Script pour corriger proprement l'indentation dans keno_advanced_analyzer.py
"""

import os
import sys
import json
import random
import re
from datetime import datetime

def backup_file(file_path):
    """Crée une sauvegarde du fichier"""
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    import shutil
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def generate_random_weights():
    """Génère des valeurs aléatoires de scale_pos_weight pour chaque numéro"""
    values = {}
    for num in range(1, 71):
        # Générer une valeur entre 1.5 et 3.5
        value = 1.5 + random.random() * 2.0
        values[num] = round(value, 4)
    
    print("Valeurs de scale_pos_weight générées:")
    for num in range(1, 11):  # Afficher les 10 premières valeurs
        print(f"  Numéro {num}: {values[num]}")
    
    return values

def save_weights_to_json(weights):
    """Sauvegarde les poids dans un fichier JSON"""
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Chemin du fichier
    file_path = os.path.join('data', 'direct_scale_pos_weight.json')
    
    # Convertir les clés en chaînes pour la sérialisation JSON
    weights_str = {str(k): v for k, v in weights.items()}
    
    # Sauvegarder les poids
    with open(file_path, 'w') as f:
        json.dump(weights_str, f, indent=2)
    
    print(f"Poids sauvegardés dans {file_path}")
    
    return file_path

def fix_xgboost_creation():
    """Corrige la création du modèle XGBoost dans keno_advanced_analyzer.py"""
    file_path = 'keno_advanced_analyzer.py'
    
    if not os.path.exists(file_path):
        print(f"Le fichier {file_path} n'existe pas")
        return False
    
    # Créer une sauvegarde
    backup_file(file_path)
    
    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Vérifier si nous devons ajouter l'import de json
    if "import json" not in content:
        # Trouver la section des imports
        import_section_end = content.find("import numpy as np") + len("import numpy as np")
        # Ajouter l'import de json
        content = content[:import_section_end] + "\nimport json" + content[import_section_end:]
    
    # Trouver la section où le modèle XGBoost est créé
    pattern = r"xgb_model\s*=\s*XGBClassifier\s*\([^)]*\)"
    matches = list(re.finditer(pattern, content))
    
    if not matches:
        print("Section de création du modèle XGBoost non trouvée")
        return False
    
    # Utiliser la première occurrence
    match = matches[0]
    
    # Trouver la fin de la ligne
    line_end = content.find("\n", match.end())
    if line_end == -1:
        print("Fin de la ligne de création du modèle XGBoost non trouvée")
        return False
    
    # Extraire la ligne complète
    xgb_creation_line = content[match.start():line_end]
    
    # Déterminer l'indentation
    indentation = ""
    for char in content[match.start()::-1]:
        if char == '\n':
            break
        indentation = char + indentation
    
    # Créer la nouvelle ligne avec la bonne indentation
    new_xgb_creation = indentation + """xgb_model = XGBClassifier(
""" + indentation + """    n_estimators=100,
""" + indentation + """    max_depth=6,
""" + indentation + """    learning_rate=0.1,
""" + indentation + """    subsample=0.8,
""" + indentation + """    colsample_bytree=0.8,
""" + indentation + """    objective='binary:logistic',
""" + indentation + """    use_label_encoder=False,
""" + indentation + """    eval_metric='auc',
""" + indentation + """    random_state=42,
""" + indentation + """    n_jobs=self.n_jobs
""" + indentation + """)

""" + indentation + """# Appliquer une valeur spécifique de scale_pos_weight pour ce numéro
""" + indentation + """try:
""" + indentation + """    # Charger les valeurs depuis le fichier JSON
""" + indentation + """    spw_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'direct_scale_pos_weight.json')
""" + indentation + """    with open(spw_file, 'r') as f:
""" + indentation + """        spw_values = json.load(f)
""" + indentation + """    
""" + indentation + """    # Vérifier si nous avons une valeur pour ce numéro
""" + indentation + """    if str(num) in spw_values:
""" + indentation + """        specific_value = float(spw_values[str(num)])
""" + indentation + """        xgb_model.scale_pos_weight = specific_value
""" + indentation + """        print(f"  Numéro {num}: Utilisation de scale_pos_weight spécifique = {specific_value}")
""" + indentation + """    else:
""" + indentation + """        # Calculer le ratio normalement
""" + indentation + """        neg_count = np.sum(y_train == 0)
""" + indentation + """        pos_count = np.sum(y_train == 1)
""" + indentation + """        
""" + indentation + """        # Éviter la division par zéro
""" + indentation + """        if pos_count > 0:
""" + indentation + """            ratio = neg_count / pos_count
""" + indentation + """            xgb_model.scale_pos_weight = ratio
""" + indentation + """            print(f"  Numéro {num}: Ratio calculé = {ratio:.4f}")
""" + indentation + """        else:
""" + indentation + """            xgb_model.scale_pos_weight = 1.0
""" + indentation + """            print(f"  Numéro {num}: Aucun exemple positif, scale_pos_weight=1.0")
""" + indentation + """except Exception as e:
""" + indentation + """    print(f"  Erreur lors du chargement des valeurs de scale_pos_weight: {e}")
""" + indentation + """    # Calculer le ratio normalement
""" + indentation + """    neg_count = np.sum(y_train == 0)
""" + indentation + """    pos_count = np.sum(y_train == 1)
""" + indentation + """    
""" + indentation + """    # Éviter la division par zéro
""" + indentation + """    if pos_count > 0:
""" + indentation + """        ratio = neg_count / pos_count
""" + indentation + """        xgb_model.scale_pos_weight = ratio
""" + indentation + """        print(f"  Numéro {num}: Ratio calculé = {ratio:.4f}")
""" + indentation + """    else:
""" + indentation + """        xgb_model.scale_pos_weight = 1.0
""" + indentation + """        print(f"  Numéro {num}: Aucun exemple positif, scale_pos_weight=1.0")"""
    
    # Remplacer la ligne
    content = content.replace(xgb_creation_line, new_xgb_creation)
    
    # Écrire le contenu modifié
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fichier {file_path} corrigé avec succès")
    return True

def main():
    """Fonction principale"""
    print("Correction propre de l'indentation dans keno_advanced_analyzer.py")
    
    # Générer des valeurs aléatoires
    weights = generate_random_weights()
    
    # Sauvegarder les poids dans un fichier JSON
    weights_file = save_weights_to_json(weights)
    
    # Corriger la création du modèle XGBoost
    success = fix_xgboost_creation()
    
    if success:
        print("\nCorrection appliquée avec succès!")
        print("L'indentation a été corrigée et le problème de scale_pos_weight identique a été résolu.")
        print("Veuillez redémarrer votre application pour que les modifications prennent effet.")
    else:
        print("\nÉchec de la correction")
        print("Veuillez vérifier manuellement les fichiers et appliquer les modifications nécessaires.")

if __name__ == "__main__":
    main()
