#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script pour vérifier l'environnement Python
"""

import sys
import os

def main():
    print("=== Informations sur l'environnement Python ===")
    print(f"Python executable: {sys.executable}")
    print(f"Python version: {sys.version}")
    print(f"Python version info: {sys.version_info}")
    print(f"Platform: {sys.platform}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Script path: {os.path.abspath(__file__)}")
    print()
    
    print("=== Python path ===")
    for i, path in enumerate(sys.path):
        print(f"{i}: {path}")
    print()
    
    print("=== Variables d'environnement Python ===")
    python_vars = {k: v for k, v in os.environ.items() if 'PYTHON' in k.upper()}
    if python_vars:
        for key, value in python_vars.items():
            print(f"{key}: {value}")
    else:
        print("Aucune variable d'environnement Python trouvée")
    print()
    
    print("=== Test d'import de base ===")
    try:
        import tkinter
        print("✓ tkinter importé avec succès")
    except Exception as e:
        print(f"✗ Erreur lors de l'import de tkinter: {e}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
