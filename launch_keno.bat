@echo off
REM Script de lancement pour l'application Keno sur Windows

echo === Lanceur d'application Keno ===

REM Changer vers le répertoire du script
cd /d "%~dp0"

echo Répertoire de travail: %CD%

REM Essayer différents interpréteurs Python
echo === Recherche de l'interpréteur Python ===

REM Essayer py launcher d'abord (recommandé sur Windows)
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo Utilisation de py launcher
    py main.py
    goto :end
)

REM Essayer python3
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo Utilisation de python3
    python3 main.py
    goto :end
)

REM Essayer python
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Utilisation de python
    python main.py
    goto :end
)

REM Si aucun interpréteur n'est trouvé
echo ERREUR: Aucun interpréteur Python trouvé
echo Veuillez installer Python depuis https://www.python.org/
pause
exit /b 1

:end
echo Application terminée
pause
