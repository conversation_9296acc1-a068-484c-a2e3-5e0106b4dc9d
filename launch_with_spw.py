"""
Script de lancement qui injecte des valeurs différentes de scale_pos_weight avant de lancer l'application principale.
"""

import sys
import os
import importlib.util

def import_module(module_name, module_path):
    """
    Importe un module à partir de son chemin
    
    Args:
        module_name: Nom du module
        module_path: Chemin du module
        
    Returns:
        module: Module importé ou None en cas d'erreur
    """
    try:
        spec = importlib.util.spec_from_file_location(module_name, module_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        print(f"Module {module_name} importé avec succès")
        return module
    except Exception as e:
        print(f"Erreur lors de l'importation du module {module_name}: {e}")
        return None

def find_main_module():
    """
    Trouve le module principal de l'application
    
    Returns:
        str: Nom du module principal ou None si non trouvé
    """
    potential_modules = ['main.py', 'app.py', 'keno_app.py', 'keno_gui.py']
    
    for module in potential_modules:
        if os.path.exists(module):
            return module
    
    return None

def main():
    """Fonction principale"""
    print("Lancement de l'application avec injection de scale_pos_weight")
    
    # Importer l'injecteur
    injector = import_module('simple_spw_injector', 'simple_spw_injector.py')
    
    if not injector:
        print("Erreur lors de l'importation de l'injecteur")
        return
    
    # Trouver le module principal
    main_module = find_main_module()
    
    if not main_module:
        print("Module principal non trouvé")
        print("Veuillez spécifier le nom du module principal:")
        main_module = input()
    
    if not os.path.exists(main_module):
        print(f"Le module {main_module} n'existe pas")
        return
    
    print(f"Lancement du module principal: {main_module}")
    
    # Importer le module principal
    module_name = os.path.splitext(main_module)[0]
    main = import_module(module_name, main_module)
    
    if not main:
        print("Erreur lors de l'importation du module principal")
        return
    
    # Exécuter la fonction principale
    if hasattr(main, 'main'):
        print("Exécution de la fonction main()")
        main.main()
    else:
        print("Le module principal n'a pas de fonction main()")
        print("Veuillez spécifier la fonction à appeler:")
        func_name = input()
        
        if hasattr(main, func_name):
            print(f"Exécution de la fonction {func_name}()")
            getattr(main, func_name)()
        else:
            print(f"Le module principal n'a pas de fonction {func_name}()")

if __name__ == "__main__":
    main()
