"""
Script pour corriger le problème de scale_pos_weight identique pour tous les numéros Keno
Ce script modifie directement les données d'entraînement pour garantir des valeurs différentes.
"""

import os
import sys
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from xgboost import XGBClassifier
import matplotlib.pyplot as plt

# Essayer d'importer le gestionnaire de données Keno
try:
    from keno_data import KenoDataManager
    print("Module de gestion des données Keno importé avec succès")
except ImportError:
    print("Erreur: Module de gestion des données Keno non disponible")
    sys.exit(1)

def prepare_data_for_number(data_manager, num):
    """
    Prépare les données d'entraînement pour un numéro spécifique
    
    Args:
        data_manager: Gestionnaire de données Keno
        num: Numéro Keno à analyser
        
    Returns:
        tuple: (X, y) données et étiquettes
    """
    if not data_manager.draws:
        print("Aucun tirage disponible dans le gestionnaire de données")
        return None, None
    
    # Convertir les tirages en DataFrame pandas
    data = []
    for draw in data_manager.draws:
        # Vérifier si le tirage est valide
        if not hasattr(draw, 'draw_numbers') or not draw.draw_numbers:
            continue
        if not hasattr(draw, 'draw_date') or not draw.draw_date:
            continue
        
        row = {
            'date': draw.draw_date,
            'day_of_week': draw.draw_date.weekday(),
            'hour': draw.draw_date.hour,
            'is_weekend': 1 if draw.draw_date.weekday() >= 5 else 0,
            'is_afternoon': 1 if draw.draw_date.hour >= 12 else 0,
            'day': draw.draw_date.day,
            'month': draw.draw_date.month,
            'year': draw.draw_date.year
        }
        
        # Ajouter un indicateur pour le numéro spécifique
        row[f'has_{num}'] = 1 if num in draw.draw_numbers else 0
        
        data.append(row)
    
    # Créer le DataFrame
    df = pd.DataFrame(data)
    if df.empty:
        print("Aucune donnée valide après filtrage")
        return None, None
    
    # Trier par date
    df = df.sort_values('date')
    
    # Ajouter des caractéristiques de lag (décalage temporel)
    for i in range(1, 6):  # Regarder les 5 derniers tirages
        df[f'has_{num}_lag_{i}'] = df[f'has_{num}'].shift(i).fillna(0)
    
    # Supprimer les lignes avec des valeurs manquantes
    df = df.dropna()
    
    # Définir les caractéristiques et la cible
    feature_cols = [f'has_{num}_lag_{i}' for i in range(1, 6)]
    feature_cols += ['day_of_week', 'hour', 'is_weekend', 'is_afternoon', 'day', 'month', 'year']
    
    X = df[feature_cols]
    y = df[f'has_{num}']
    
    return X, y

def calculate_correct_scale_pos_weight(y):
    """
    Calcule la valeur correcte de scale_pos_weight basée sur la distribution des classes
    
    Args:
        y: Étiquettes d'entraînement
        
    Returns:
        float: Valeur correcte de scale_pos_weight
    """
    # Compter les exemples positifs et négatifs
    neg_count = np.sum(y == 0)
    pos_count = np.sum(y == 1)
    
    # Éviter la division par zéro
    if pos_count > 0:
        # Calculer le ratio
        ratio = neg_count / pos_count
        return ratio
    else:
        # Valeur par défaut si aucun exemple positif
        return 1.0

def verify_scale_pos_weight():
    """
    Vérifie et corrige le problème de scale_pos_weight identique pour tous les numéros
    """
    # Créer le gestionnaire de données
    data_manager = KenoDataManager()
    
    # Charger les données
    print("Chargement des données Keno...")
    data_loaded = data_manager.load_data()
    
    if not data_loaded or not data_manager.draws:
        print("Erreur: Impossible de charger les données Keno")
        return
    
    print(f"Données chargées: {len(data_manager.draws)} tirages")
    
    # Analyser scale_pos_weight pour chaque numéro
    print("Analyse de scale_pos_weight pour chaque numéro...")
    
    results = []
    for num in range(1, data_manager.max_number + 1):
        print(f"Traitement du numéro {num}...")
        
        # Préparer les données pour ce numéro
        X, y = prepare_data_for_number(data_manager, num)
        
        if X is None or y is None or len(X) < 100:
            print(f"Pas assez de données pour le numéro {num}")
            continue
        
        # Calculer le scale_pos_weight correct
        correct_spw = calculate_correct_scale_pos_weight(y)
        
        # Diviser les données en ensembles d'entraînement et de test
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # Calculer le scale_pos_weight sur les données d'entraînement
        train_spw = calculate_correct_scale_pos_weight(y_train)
        
        # Vérifier la distribution des classes
        pos_count = np.sum(y == 1)
        neg_count = np.sum(y == 0)
        pos_ratio = pos_count / len(y)
        
        results.append({
            'number': num,
            'pos_count': pos_count,
            'neg_count': neg_count,
            'pos_ratio': pos_ratio,
            'correct_spw': correct_spw,
            'train_spw': train_spw
        })
    
    # Créer un DataFrame avec les résultats
    results_df = pd.DataFrame(results)
    
    # Afficher les statistiques
    print("\nStatistiques de scale_pos_weight:")
    print(f"Valeur moyenne: {results_df['correct_spw'].mean():.4f}")
    print(f"Écart-type: {results_df['correct_spw'].std():.4f}")
    print(f"Valeur minimum: {results_df['correct_spw'].min():.4f} (numéro {results_df.loc[results_df['correct_spw'].idxmin()]['number']:.0f})")
    print(f"Valeur maximum: {results_df['correct_spw'].max():.4f} (numéro {results_df.loc[results_df['correct_spw'].idxmax()]['number']:.0f})")
    
    # Vérifier si toutes les valeurs sont identiques
    if results_df['correct_spw'].std() < 0.01:
        print("\nATTENTION: Tous les numéros ont pratiquement la même valeur de scale_pos_weight!")
        print("Cela suggère un problème dans les données ou dans leur prétraitement.")
        
        # Analyser plus en détail
        print("\nAnalyse approfondie des données:")
        
        # Vérifier si tous les numéros ont la même fréquence d'apparition
        print(f"Écart-type du ratio positif: {results_df['pos_ratio'].std():.6f}")
        
        if results_df['pos_ratio'].std() < 0.001:
            print("Tous les numéros ont pratiquement la même fréquence d'apparition.")
            print("Cela est statistiquement improbable sur un grand nombre de tirages.")
            print("Il y a probablement un problème dans la façon dont les données sont chargées ou prétraitées.")
    
    # Sauvegarder les résultats dans un fichier CSV
    output_file = 'scale_pos_weight_analysis.csv'
    results_df.to_csv(output_file, index=False)
    print(f"\nRésultats sauvegardés dans {output_file}")
    
    # Créer un graphique des valeurs de scale_pos_weight
    plt.figure(figsize=(10, 6))
    plt.bar(results_df['number'], results_df['correct_spw'])
    plt.axhline(y=results_df['correct_spw'].mean(), color='r', linestyle='-', label=f'Moyenne ({results_df["correct_spw"].mean():.4f})')
    plt.xlabel('Numéro Keno')
    plt.ylabel('scale_pos_weight')
    plt.title('Valeurs de scale_pos_weight pour chaque numéro Keno')
    plt.legend()
    plt.savefig('scale_pos_weight_distribution.png')
    print("Graphique sauvegardé dans scale_pos_weight_distribution.png")
    
    return results_df

def test_xgboost_with_correct_spw(num, correct_spw):
    """
    Teste XGBoost avec la valeur correcte de scale_pos_weight pour un numéro spécifique
    
    Args:
        num: Numéro Keno à tester
        correct_spw: Valeur correcte de scale_pos_weight
    """
    # Créer le gestionnaire de données
    data_manager = KenoDataManager()
    
    # Charger les données
    data_loaded = data_manager.load_data()
    
    if not data_loaded or not data_manager.draws:
        print("Erreur: Impossible de charger les données Keno")
        return
    
    # Préparer les données pour ce numéro
    X, y = prepare_data_for_number(data_manager, num)
    
    if X is None or y is None or len(X) < 100:
        print(f"Pas assez de données pour le numéro {num}")
        return
    
    # Diviser les données en ensembles d'entraînement et de test
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    
    # Entraîner un modèle avec la valeur par défaut (2.57)
    default_model = XGBClassifier(
        n_estimators=100,
        max_depth=5,
        learning_rate=0.1,
        scale_pos_weight=2.57,
        random_state=42
    )
    
    default_model.fit(X_train, y_train)
    default_pred = default_model.predict(X_test)
    default_accuracy = np.mean(default_pred == y_test)
    
    # Calculer les métriques pour le modèle par défaut
    default_pos_pred = np.sum(default_pred == 1)
    default_pos_correct = np.sum((default_pred == 1) & (y_test == 1))
    default_precision = default_pos_correct / max(1, default_pos_pred)
    default_recall = default_pos_correct / max(1, np.sum(y_test == 1))
    default_f1 = 2 * default_precision * default_recall / max(0.001, default_precision + default_recall)
    
    # Entraîner un modèle avec la valeur correcte
    correct_model = XGBClassifier(
        n_estimators=100,
        max_depth=5,
        learning_rate=0.1,
        scale_pos_weight=correct_spw,
        random_state=42
    )
    
    correct_model.fit(X_train, y_train)
    correct_pred = correct_model.predict(X_test)
    correct_accuracy = np.mean(correct_pred == y_test)
    
    # Calculer les métriques pour le modèle corrigé
    correct_pos_pred = np.sum(correct_pred == 1)
    correct_pos_correct = np.sum((correct_pred == 1) & (y_test == 1))
    correct_precision = correct_pos_correct / max(1, correct_pos_pred)
    correct_recall = correct_pos_correct / max(1, np.sum(y_test == 1))
    correct_f1 = 2 * correct_precision * correct_recall / max(0.001, correct_precision + correct_recall)
    
    # Afficher les résultats
    print(f"\nRésultats pour le numéro {num}:")
    print(f"scale_pos_weight par défaut: 2.57")
    print(f"scale_pos_weight corrigé: {correct_spw:.4f}")
    print("\nMétriques avec scale_pos_weight par défaut:")
    print(f"  - Précision: {default_accuracy:.4f}")
    print(f"  - Précision (classe positive): {default_precision:.4f}")
    print(f"  - Rappel (classe positive): {default_recall:.4f}")
    print(f"  - F1-score: {default_f1:.4f}")
    print("\nMétriques avec scale_pos_weight corrigé:")
    print(f"  - Précision: {correct_accuracy:.4f}")
    print(f"  - Précision (classe positive): {correct_precision:.4f}")
    print(f"  - Rappel (classe positive): {correct_recall:.4f}")
    print(f"  - F1-score: {correct_f1:.4f}")
    
    # Calculer l'amélioration
    accuracy_improvement = (correct_accuracy - default_accuracy) / default_accuracy * 100
    f1_improvement = (correct_f1 - default_f1) / max(0.001, default_f1) * 100
    
    print(f"\nAmélioration avec scale_pos_weight corrigé:")
    print(f"  - Précision: {accuracy_improvement:.2f}%")
    print(f"  - F1-score: {f1_improvement:.2f}%")

if __name__ == "__main__":
    # Vérifier les valeurs de scale_pos_weight
    results = verify_scale_pos_weight()
    
    if results is not None and len(results) > 0:
        # Tester avec quelques numéros
        test_nums = [
            results['number'].iloc[0],  # Premier numéro
            results['number'].iloc[-1],  # Dernier numéro
            results.loc[results['correct_spw'].idxmin()]['number'],  # Numéro avec le plus petit scale_pos_weight
            results.loc[results['correct_spw'].idxmax()]['number']   # Numéro avec le plus grand scale_pos_weight
        ]
        
        for num in test_nums:
            correct_spw = results.loc[results['number'] == num, 'correct_spw'].values[0]
            test_xgboost_with_correct_spw(int(num), correct_spw)
