"""
Script pour intégrer l'analyse des motifs sur 48h dans l'optimiseur XGBoost
Ce script améliore l'optimiseur XGBoost en ajoutant des caractéristiques basées sur les motifs sur 48h.
"""

import os
import sys
import numpy as np
import pandas as pd
import json
from datetime import datetime, timedelta

# Essayer d'importer le gestionnaire de données Keno
try:
    from keno_data import KenoDataManager, KenoDrawData
    print("Module de gestion des données Keno importé avec succès")
except ImportError:
    print("Erreur: Module de gestion des données Keno non disponible")
    sys.exit(1)

# Importer le prédicteur 48h
from keno_48h_predictor import Keno48hPredictor

# Essayer d'importer l'optimiseur XGBoost
try:
    from keno_xgboost_optimizer_simple import XGBoostOptimizer
    print("Module d'optimisation XGBoost importé avec succès")
except ImportError:
    print("Erreur: Module d'optimisation XGBoost non disponible")
    sys.exit(1)

def create_48h_features(data_manager):
    """
    Crée des caractéristiques basées sur les motifs sur 48h
    
    Args:
        data_manager: Gestionnaire de données Keno
        
    Returns:
        dict: Caractéristiques 48h pour chaque numéro
    """
    # Créer le prédicteur 48h
    predictor = Keno48hPredictor(data_manager)
    
    # Analyser les données historiques
    if not predictor.analyze_historical_data():
        print("Erreur lors de l'analyse des données historiques")
        return {}
    
    # Récupérer les tirages récents
    recent_draws = predictor.get_recent_draws(5)
    
    if not recent_draws:
        print("Aucun tirage récent trouvé")
        return {}
    
    # Créer les caractéristiques pour chaque numéro
    features = {}
    
    for num in range(1, 71):
        # Créer le motif d'apparition pour ce numéro dans les tirages récents
        pattern = ""
        for draw in recent_draws:
            if num in draw.draw_numbers:
                pattern += "1"
            else:
                pattern += "0"
        
        # Récupérer les poids du motif
        pattern_weight = 0.5
        next_appearance_prob = 0.5
        
        if pattern in predictor.pattern_weights:
            pattern_weight = predictor.pattern_weights[pattern]['weight']
            next_appearance_prob = predictor.pattern_weights[pattern]['next_appearance_prob']
        
        # Récupérer le poids du numéro
        number_weight = predictor.number_weights.get(num, 1.0)
        
        # Calculer d'autres caractéristiques
        recent_appearances = sum(1 for draw in recent_draws if num in draw.draw_numbers)
        appeared_in_last_draw = 1 if recent_draws and num in recent_draws[-1].draw_numbers else 0
        
        # Stocker les caractéristiques
        features[num] = {
            'pattern': pattern,
            'pattern_weight': pattern_weight,
            'next_appearance_prob': next_appearance_prob,
            'number_weight': number_weight,
            'recent_appearances': recent_appearances,
            'appeared_in_last_draw': appeared_in_last_draw
        }
    
    return features

def enhance_xgboost_with_48h_patterns():
    """
    Améliore l'optimiseur XGBoost en ajoutant des caractéristiques basées sur les motifs sur 48h
    """
    # Créer le gestionnaire de données
    data_manager = KenoDataManager()
    
    # Charger les données
    print("Chargement des données Keno...")
    data_loaded = data_manager.load_data()
    
    if not data_loaded or not data_manager.draws:
        print("Erreur: Impossible de charger les données Keno")
        return
    
    print(f"Données chargées: {len(data_manager.draws)} tirages")
    
    # Créer les caractéristiques 48h
    print("\nCréation des caractéristiques basées sur les motifs sur 48h...")
    features_48h = create_48h_features(data_manager)
    
    if not features_48h:
        print("Erreur lors de la création des caractéristiques 48h")
        return
    
    # Sauvegarder les caractéristiques 48h
    output_file = 'keno_48h_features.json'
    
    try:
        with open(output_file, 'w') as f:
            json.dump(features_48h, f, indent=2)
        
        print(f"Caractéristiques 48h sauvegardées dans {output_file}")
    except Exception as e:
        print(f"Erreur lors de la sauvegarde des caractéristiques 48h: {e}")
    
    # Créer un fichier Python pour intégrer ces caractéristiques dans l'optimiseur XGBoost
    integration_file = 'keno_xgboost_48h_enhancer.py'
    
    integration_code = """\"\"\"
Module pour améliorer l'optimiseur XGBoost avec des caractéristiques basées sur les motifs sur 48h
\"\"\"

import os
import json
import numpy as np
import pandas as pd

# Chemin du fichier de caractéristiques 48h
FEATURES_48H_FILE = 'keno_48h_features.json'

class XGBoost48hEnhancer:
    \"\"\"Classe pour améliorer l'optimiseur XGBoost avec des caractéristiques 48h\"\"\"
    
    def __init__(self):
        \"\"\"Initialise l'enhancer\"\"\"
        self.features_48h = self._load_features()
    
    def _load_features(self):
        \"\"\"Charge les caractéristiques 48h\"\"\"
        if not os.path.exists(FEATURES_48H_FILE):
            print(f"Le fichier {FEATURES_48H_FILE} n'existe pas")
            return {}
        
        try:
            with open(FEATURES_48H_FILE, 'r') as f:
                features = json.load(f)
            
            # Convertir les clés en entiers
            return {int(k): v for k, v in features.items()}
        except Exception as e:
            print(f"Erreur lors du chargement des caractéristiques 48h: {e}")
            return {}
    
    def enhance_features(self, X, num):
        \"\"\"
        Améliore les caractéristiques pour un numéro spécifique
        
        Args:
            X: DataFrame des caractéristiques
            num: Numéro Keno
            
        Returns:
            pd.DataFrame: DataFrame des caractéristiques améliorées
        \"\"\"
        if not self.features_48h or num not in self.features_48h:
            return X
        
        # Récupérer les caractéristiques 48h pour ce numéro
        num_features = self.features_48h[num]
        
        # Créer une copie du DataFrame
        X_enhanced = X.copy()
        
        # Ajouter les caractéristiques 48h
        X_enhanced['pattern_weight'] = num_features['pattern_weight']
        X_enhanced['next_appearance_prob'] = num_features['next_appearance_prob']
        X_enhanced['number_weight'] = num_features['number_weight']
        X_enhanced['recent_appearances'] = num_features['recent_appearances']
        X_enhanced['appeared_in_last_draw'] = num_features['appeared_in_last_draw']
        
        # Ajouter des caractéristiques dérivées
        X_enhanced['pattern_weight_squared'] = X_enhanced['pattern_weight'] ** 2
        X_enhanced['combined_weight'] = X_enhanced['pattern_weight'] * X_enhanced['number_weight']
        
        # Ajouter des caractéristiques d'interaction
        if 'has_{}_lag_1' in X_enhanced.columns.format(num):
            X_enhanced[f'lag1_x_pattern'] = X_enhanced[f'has_{num}_lag_1'] * X_enhanced['pattern_weight']
        
        return X_enhanced
    
    def enhance_model_training(self, optimizer, X, y, num, **kwargs):
        \"\"\"
        Améliore l'entraînement du modèle pour un numéro spécifique
        
        Args:
            optimizer: Optimiseur XGBoost
            X: DataFrame des caractéristiques
            y: Série des étiquettes
            num: Numéro Keno
            **kwargs: Arguments supplémentaires pour train_model
            
        Returns:
            dict: Résultat de l'entraînement
        \"\"\"
        # Améliorer les caractéristiques
        X_enhanced = self.enhance_features(X, num)
        
        # Entraîner le modèle avec les caractéristiques améliorées
        result = optimizer.train_model(X_enhanced, y, num, **kwargs)
        
        return result

# Fonction pour intégrer l'enhancer dans l'optimiseur XGBoost
def enhance_optimizer(optimizer):
    \"\"\"
    Intègre l'enhancer dans l'optimiseur XGBoost
    
    Args:
        optimizer: Optimiseur XGBoost
        
    Returns:
        XGBoostOptimizer: Optimiseur amélioré
    \"\"\"
    # Créer l'enhancer
    enhancer = XGBoost48hEnhancer()
    
    # Sauvegarder la méthode originale
    original_train_model = optimizer.train_model
    
    # Définir la nouvelle méthode
    def enhanced_train_model(X, y, num, **kwargs):
        # Améliorer les caractéristiques
        X_enhanced = enhancer.enhance_features(X, num)
        
        # Appeler la méthode originale
        return original_train_model(X_enhanced, y, num, **kwargs)
    
    # Remplacer la méthode
    optimizer.train_model = enhanced_train_model
    
    # Ajouter l'enhancer comme attribut
    optimizer.enhancer = enhancer
    
    return optimizer
"""
    
    try:
        with open(integration_file, 'w') as f:
            f.write(integration_code)
        
        print(f"Code d'intégration sauvegardé dans {integration_file}")
    except Exception as e:
        print(f"Erreur lors de la sauvegarde du code d'intégration: {e}")
    
    # Créer un exemple d'utilisation
    example_file = 'example_48h_enhanced_prediction.py'
    
    example_code = """\"\"\"
Exemple d'utilisation de l'optimiseur XGBoost amélioré avec les motifs sur 48h
\"\"\"

import numpy as np
import pandas as pd
from keno_data import KenoDataManager
from keno_xgboost_optimizer_simple import XGBoostOptimizer
from keno_xgboost_48h_enhancer import enhance_optimizer

def main():
    \"\"\"Fonction principale\"\"\"
    # Créer le gestionnaire de données
    data_manager = KenoDataManager()
    
    # Charger les données
    print("Chargement des données Keno...")
    data_loaded = data_manager.load_data()
    
    if not data_loaded or not data_manager.draws:
        print("Erreur: Impossible de charger les données Keno")
        return
    
    print(f"Données chargées: {len(data_manager.draws)} tirages")
    
    # Créer l'optimiseur XGBoost
    optimizer = XGBoostOptimizer(use_gpu=False, n_jobs=-1, verbose=1)
    
    # Améliorer l'optimiseur avec les motifs sur 48h
    enhanced_optimizer = enhance_optimizer(optimizer)
    
    # Préparer les données pour un numéro spécifique
    num = 7  # Exemple avec le numéro 7
    
    # Créer un DataFrame avec les données historiques
    data = []
    for draw in data_manager.draws:
        row = {
            'date': draw.draw_date,
            'has_{}'.format(num): 1 if num in draw.draw_numbers else 0
        }
        data.append(row)
    
    df = pd.DataFrame(data)
    df = df.sort_values('date')
    
    # Ajouter des caractéristiques de lag
    for i in range(1, 6):
        df['has_{}_lag_{}'.format(num, i)] = df['has_{}'.format(num)].shift(i).fillna(0)
    
    # Supprimer les lignes avec des valeurs manquantes
    df = df.dropna()
    
    # Définir les caractéristiques et la cible
    X = df[['has_{}_lag_{}'.format(num, i) for i in range(1, 6)]]
    y = df['has_{}'.format(num)]
    
    # Entraîner le modèle avec l'optimiseur amélioré
    print(f"\\nEntraînement du modèle pour le numéro {num} avec l'optimiseur amélioré...")
    result = enhanced_optimizer.train_model(X, y, num, optimize=True, fast_mode=True)
    
    # Afficher les résultats
    if result:
        print(f"\\nRésultats pour le numéro {num}:")
        print(f"Précision: {result['accuracy']:.4f}")
        print(f"F1-score: {result['f1']:.4f}")
        print(f"Précision: {result['precision']:.4f}")
        print(f"Rappel: {result['recall']:.4f}")
        
        # Afficher l'importance des caractéristiques
        if 'feature_importance' in result and result['feature_importance'] is not None:
            print("\\nImportance des caractéristiques:")
            feature_names = list(X.columns) + ['pattern_weight', 'next_appearance_prob', 'number_weight', 
                                              'recent_appearances', 'appeared_in_last_draw',
                                              'pattern_weight_squared', 'combined_weight', 'lag1_x_pattern']
            
            if len(feature_names) == len(result['feature_importance']):
                for i, importance in enumerate(result['feature_importance']):
                    print(f"  {feature_names[i]}: {importance:.4f}")

if __name__ == "__main__":
    main()
"""
    
    try:
        with open(example_file, 'w') as f:
            f.write(example_code)
        
        print(f"Exemple d'utilisation sauvegardé dans {example_file}")
    except Exception as e:
        print(f"Erreur lors de la sauvegarde de l'exemple d'utilisation: {e}")
    
    print("\nIntégration terminée!")
    print("Pour utiliser l'optimiseur XGBoost amélioré avec les motifs sur 48h:")
    print("1. Exécutez d'abord ce script pour créer les caractéristiques 48h")
    print("2. Importez l'enhancer dans votre code:")
    print("   from keno_xgboost_48h_enhancer import enhance_optimizer")
    print("3. Améliorez votre optimiseur:")
    print("   enhanced_optimizer = enhance_optimizer(optimizer)")
    print("4. Utilisez l'optimiseur amélioré pour entraîner vos modèles")
    print("5. Consultez l'exemple dans example_48h_enhanced_prediction.py")

if __name__ == "__main__":
    enhance_xgboost_with_48h_patterns()
