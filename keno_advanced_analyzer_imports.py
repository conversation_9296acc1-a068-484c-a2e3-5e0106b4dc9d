# Essayer d'importer LightGBM
try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    lgb = None
    LIGHTGBM_AVAILABLE = False
    print("Module lightgbm non disponible. Les fonctionnalités LightGBM seront désactivées.")

# Essayer d'importer l'optimiseur XGBoost
try:
    from keno_xgboost_optimizer import XGBoostOptimizer
    XGBOOST_OPTIMIZER_AVAILABLE = True
except ImportError:
    XGBOOST_OPTIMIZER_AVAILABLE = False
    print("Module d'optimisation XGBoost non disponible. L'optimisation XGBoost sera désactivée.")

# Essayer d'importer XGBoost
try:
    from xgboost import XGBClassifier
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("Module XGBoost non disponible. Les fonctionnalités XGBoost seront désactivées.")
