#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module pour la prédiction d'intervalles de confiance dans les tirages Keno
Utilise des techniques avancées pour estimer l'incertitude des prédictions
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
import joblib
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.preprocessing import StandardScaler
from scipy import stats
import warnings

# Désactiver les avertissements
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

class KenoConfidenceEstimator:
    """
    Classe pour la prédiction d'intervalles de confiance dans les tirages Keno
    """
    
    def __init__(self, data_manager=None):
        """
        Initialise l'estimateur d'intervalles de confiance
        
        Args:
            data_manager: Gestionnaire de données Keno
        """
        self.data_manager = data_manager
        self.models = {}
        self.df = None
        self.confidence_intervals = {}
        self.max_number = data_manager.max_number if data_manager else 70
        self.numbers_per_draw = data_manager.numbers_per_draw if data_manager else 20
    
    def prepare_data(self, df=None):
        """
        Prépare les données pour l'estimation d'intervalles de confiance
        
        Args:
            df: DataFrame pandas contenant les données (si None, utilise les données du data_manager)
            
        Returns:
            DataFrame: DataFrame contenant les caractéristiques pour l'estimation d'intervalles de confiance
        """
        if df is not None:
            self.df = df
        elif self.data_manager is not None and self.data_manager.draws:
            # Convertir les tirages en DataFrame pandas
            data = []
            for draw in self.data_manager.draws:
                row = {
                    'date': draw.draw_date,
                    'id': draw.draw_id
                }
                
                # Ajouter des indicateurs pour chaque numéro possible
                for i in range(1, self.max_number + 1):
                    row[f'has_{i}'] = 1 if i in draw.draw_numbers else 0
                
                data.append(row)
            
            # Créer le DataFrame
            self.df = pd.DataFrame(data)
            self.df = self.df.sort_values('date')
        else:
            print("Aucune donnée disponible pour l'estimation d'intervalles de confiance")
            return None
        
        return self.df
    
    def train_bootstrap_models(self, num, n_models=100, test_size=0.2, random_state=42):
        """
        Entraîne plusieurs modèles en utilisant le bootstrap pour estimer l'incertitude
        
        Args:
            num: Numéro à analyser
            n_models: Nombre de modèles à entraîner
            test_size: Proportion des données à utiliser pour le test
            random_state: Graine aléatoire pour la reproductibilité
            
        Returns:
            dict: Dictionnaire contenant les modèles et les métriques d'évaluation
        """
        if self.df is None:
            self.prepare_data()
        
        if self.df is None:
            print("Aucune donnée disponible pour l'entraînement des modèles")
            return None
        
        # Vérifier que le numéro est valide
        if num < 1 or num > self.max_number:
            print(f"Numéro {num} invalide")
            return None
        
        # Vérifier que la colonne existe
        if f'has_{num}' not in self.df.columns:
            print(f"Colonne 'has_{num}' introuvable dans les données")
            return None
        
        try:
            print(f"Entraînement de {n_models} modèles bootstrap pour le numéro {num}...")
            
            # Préparer les caractéristiques et la cible
            # Utiliser les 10 derniers tirages comme caractéristiques
            X = []
            y = []
            
            # Créer des caractéristiques basées sur l'historique des tirages
            for i in range(10, len(self.df)):
                # Extraire les 10 derniers tirages
                features = []
                for j in range(1, 11):
                    for k in range(1, self.max_number + 1):
                        if f'has_{k}' in self.df.iloc[i-j].index:
                            features.append(self.df.iloc[i-j][f'has_{k}'])
                
                # Ajouter des caractéristiques supplémentaires
                # Nombre de fois où le numéro est apparu dans les 10 derniers tirages
                count = sum(self.df.iloc[i-10:i][f'has_{num}'])
                features.append(count)
                
                # Nombre de tirages depuis la dernière apparition
                last_seen = 0
                for j in range(1, 11):
                    if self.df.iloc[i-j][f'has_{num}'] == 1:
                        last_seen = j
                        break
                features.append(last_seen)
                
                X.append(features)
                y.append(self.df.iloc[i][f'has_{num}'])
            
            X = np.array(X)
            y = np.array(y)
            
            # Diviser les données en ensembles d'entraînement et de test
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state)
            
            # Normaliser les données
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Entraîner plusieurs modèles en utilisant le bootstrap
            models = []
            predictions = []
            
            for i in range(n_models):
                # Créer un échantillon bootstrap
                indices = np.random.choice(len(X_train), len(X_train), replace=True)
                X_bootstrap = X_train_scaled[indices]
                y_bootstrap = y_train[indices]
                
                # Entraîner un modèle
                model = GradientBoostingClassifier(n_estimators=100, random_state=i)
                model.fit(X_bootstrap, y_bootstrap)
                
                # Faire des prédictions
                y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]
                y_pred = (y_pred_proba > 0.5).astype(int)
                
                # Calculer les métriques
                accuracy = accuracy_score(y_test, y_pred)
                precision = precision_score(y_test, y_pred, zero_division=0)
                recall = recall_score(y_test, y_pred, zero_division=0)
                f1 = f1_score(y_test, y_pred, zero_division=0)
                auc = roc_auc_score(y_test, y_pred_proba) if len(np.unique(y_test)) > 1 else 0.5
                
                # Stocker le modèle et les prédictions
                models.append({
                    'model': model,
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1': f1,
                    'auc': auc
                })
                
                predictions.append(y_pred_proba)
            
            # Calculer les statistiques des prédictions
            predictions = np.array(predictions)
            mean_predictions = np.mean(predictions, axis=0)
            std_predictions = np.std(predictions, axis=0)
            
            # Calculer les intervalles de confiance
            confidence_intervals = []
            for i in range(len(mean_predictions)):
                # Intervalle de confiance à 95%
                lower = mean_predictions[i] - 1.96 * std_predictions[i]
                upper = mean_predictions[i] + 1.96 * std_predictions[i]
                
                # Limiter les valeurs entre 0 et 1
                lower = max(0, min(1, lower))
                upper = max(0, min(1, upper))
                
                confidence_intervals.append((lower, upper))
            
            # Calculer la largeur moyenne des intervalles de confiance
            mean_interval_width = np.mean([upper - lower for lower, upper in confidence_intervals])
            
            # Calculer la calibration des intervalles de confiance
            # Proportion des valeurs réelles qui se trouvent dans l'intervalle de confiance
            in_interval = 0
            for i in range(len(y_test)):
                if y_test[i] == 1 and confidence_intervals[i][0] <= 1 <= confidence_intervals[i][1]:
                    in_interval += 1
                elif y_test[i] == 0 and confidence_intervals[i][0] <= 0 <= confidence_intervals[i][1]:
                    in_interval += 1
            
            calibration = in_interval / len(y_test)
            
            # Stocker les résultats
            self.models[num] = {
                'models': models,
                'scaler': scaler,
                'mean_predictions': mean_predictions,
                'std_predictions': std_predictions,
                'confidence_intervals': confidence_intervals,
                'mean_interval_width': mean_interval_width,
                'calibration': calibration,
                'X_train': X_train,
                'X_test': X_test,
                'y_train': y_train,
                'y_test': y_test
            }
            
            print(f"Modèles bootstrap entraînés avec succès pour le numéro {num}")
            print(f"  Largeur moyenne des intervalles de confiance: {mean_interval_width:.4f}")
            print(f"  Calibration des intervalles de confiance: {calibration:.4f}")
            
            return self.models[num]
        
        except Exception as e:
            print(f"Erreur lors de l'entraînement des modèles bootstrap pour le numéro {num}: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def predict_with_confidence(self, num, features):
        """
        Fait une prédiction avec intervalle de confiance
        
        Args:
            num: Numéro à prédire
            features: Caractéristiques d'entrée
            
        Returns:
            tuple: (prédiction, intervalle de confiance)
        """
        if num not in self.models:
            print(f"Aucun modèle disponible pour le numéro {num}. Entraînement des modèles...")
            self.train_bootstrap_models(num)
        
        if num not in self.models:
            print(f"Impossible d'entraîner des modèles pour le numéro {num}")
            return None
        
        try:
            # Récupérer les modèles et le scaler
            models_data = self.models[num]
            models = models_data['models']
            scaler = models_data['scaler']
            
            # Normaliser les caractéristiques
            features_scaled = scaler.transform(np.array(features).reshape(1, -1))
            
            # Faire des prédictions avec chaque modèle
            predictions = []
            for model_data in models:
                model = model_data['model']
                pred = model.predict_proba(features_scaled)[0, 1]
                predictions.append(pred)
            
            # Calculer la moyenne et l'écart-type des prédictions
            mean_prediction = np.mean(predictions)
            std_prediction = np.std(predictions)
            
            # Calculer l'intervalle de confiance à 95%
            lower = mean_prediction - 1.96 * std_prediction
            upper = mean_prediction + 1.96 * std_prediction
            
            # Limiter les valeurs entre 0 et 1
            lower = max(0, min(1, lower))
            upper = max(0, min(1, upper))
            
            # Stocker l'intervalle de confiance
            self.confidence_intervals[num] = (lower, upper)
            
            return (mean_prediction, (lower, upper))
        
        except Exception as e:
            print(f"Erreur lors de la prédiction avec intervalle de confiance pour le numéro {num}: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def visualize_confidence_intervals(self, num):
        """
        Visualise les intervalles de confiance pour un numéro
        
        Args:
            num: Numéro à visualiser
            
        Returns:
            str: Chemin du fichier image généré
        """
        if num not in self.models:
            print(f"Aucun modèle disponible pour le numéro {num}")
            return None
        
        try:
            # Récupérer les données
            models_data = self.models[num]
            mean_predictions = models_data['mean_predictions']
            confidence_intervals = models_data['confidence_intervals']
            y_test = models_data['y_test']
            
            # Créer la figure
            plt.figure(figsize=(12, 8))
            
            # Tracer les prédictions et les intervalles de confiance
            plt.subplot(2, 1, 1)
            
            # Trier les prédictions par valeur croissante pour une meilleure visualisation
            sorted_indices = np.argsort(mean_predictions)
            sorted_mean_predictions = mean_predictions[sorted_indices]
            sorted_y_test = y_test[sorted_indices]
            sorted_confidence_intervals = [confidence_intervals[i] for i in sorted_indices]
            
            # Tracer les prédictions
            plt.scatter(range(len(sorted_mean_predictions)), sorted_mean_predictions, label='Prédictions', alpha=0.7)
            
            # Tracer les valeurs réelles
            plt.scatter(range(len(sorted_y_test)), sorted_y_test, label='Valeurs réelles', alpha=0.7)
            
            # Tracer les intervalles de confiance
            for i, (lower, upper) in enumerate(sorted_confidence_intervals):
                plt.plot([i, i], [lower, upper], 'r-', alpha=0.3)
            
            plt.title(f'Prédictions avec intervalles de confiance pour le numéro {num}')
            plt.xlabel('Échantillon (trié par prédiction)')
            plt.ylabel('Probabilité')
            plt.legend()
            plt.grid(True)
            
            # Tracer la distribution des largeurs des intervalles de confiance
            plt.subplot(2, 1, 2)
            
            # Calculer les largeurs des intervalles de confiance
            interval_widths = [upper - lower for lower, upper in confidence_intervals]
            
            # Tracer l'histogramme
            sns.histplot(interval_widths, bins=20, kde=True)
            plt.axvline(x=models_data['mean_interval_width'], color='r', linestyle='--', 
                       label=f'Largeur moyenne: {models_data["mean_interval_width"]:.4f}')
            
            plt.title('Distribution des largeurs des intervalles de confiance')
            plt.xlabel('Largeur de l\'intervalle')
            plt.ylabel('Fréquence')
            plt.legend()
            plt.grid(True)
            
            plt.tight_layout()
            
            # Créer le dossier de visualisations s'il n'existe pas
            os.makedirs('visualisations', exist_ok=True)
            
            # Sauvegarder la figure
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'visualisations/confidence_intervals_{num}_{timestamp}.png'
            plt.savefig(filename)
            print(f"Visualisation sauvegardée dans {filename}")
            
            # Fermer la figure pour libérer la mémoire
            plt.close()
            
            return filename
        
        except Exception as e:
            print(f"Erreur lors de la visualisation des intervalles de confiance pour le numéro {num}: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def predict_all_numbers_with_confidence(self):
        """
        Prédit tous les numéros avec intervalles de confiance
        
        Returns:
            dict: Dictionnaire contenant les prédictions et les intervalles de confiance pour chaque numéro
        """
        if self.df is None:
            self.prepare_data()
        
        if self.df is None:
            print("Aucune donnée disponible pour les prédictions")
            return None
        
        predictions = {}
        
        # Préparer les caractéristiques pour la prédiction
        # Utiliser les 10 derniers tirages comme caractéristiques
        features = []
        
        # Extraire les 10 derniers tirages
        for j in range(1, 11):
            idx = len(self.df) - j
            if idx >= 0:
                for k in range(1, self.max_number + 1):
                    if f'has_{k}' in self.df.iloc[idx].index:
                        features.append(self.df.iloc[idx][f'has_{k}'])
            else:
                # Si nous n'avons pas assez de tirages, utiliser des zéros
                for k in range(1, self.max_number + 1):
                    features.append(0)
        
        # Faire des prédictions pour chaque numéro
        for num in range(1, self.max_number + 1):
            # Ajouter des caractéristiques spécifiques au numéro
            num_features = features.copy()
            
            # Nombre de fois où le numéro est apparu dans les 10 derniers tirages
            count = 0
            for j in range(1, 11):
                idx = len(self.df) - j
                if idx >= 0 and f'has_{num}' in self.df.iloc[idx].index:
                    count += self.df.iloc[idx][f'has_{num}']
            num_features.append(count)
            
            # Nombre de tirages depuis la dernière apparition
            last_seen = 10
            for j in range(1, 11):
                idx = len(self.df) - j
                if idx >= 0 and f'has_{num}' in self.df.iloc[idx].index and self.df.iloc[idx][f'has_{num}'] == 1:
                    last_seen = j
                    break
            num_features.append(last_seen)
            
            # Faire la prédiction avec intervalle de confiance
            prediction = self.predict_with_confidence(num, [num_features])
            
            if prediction is not None:
                predictions[num] = prediction
        
        return predictions
    
    def get_top_predictions_with_confidence(self, n=20):
        """
        Retourne les n numéros les plus probables avec leurs intervalles de confiance
        
        Args:
            n: Nombre de numéros à retourner
            
        Returns:
            list: Liste des n numéros les plus probables avec leurs intervalles de confiance
        """
        predictions = self.predict_all_numbers_with_confidence()
        
        if predictions is None:
            return None
        
        # Trier les prédictions par probabilité décroissante
        sorted_predictions = sorted(predictions.items(), key=lambda x: x[1][0], reverse=True)
        
        # Retourner les n premiers numéros
        return sorted_predictions[:n]
    
    def save_models(self, filepath='keno_confidence_models.pkl'):
        """
        Sauvegarde les modèles d'intervalles de confiance
        
        Args:
            filepath: Chemin du fichier où sauvegarder les modèles
            
        Returns:
            bool: True si la sauvegarde a réussi, False sinon
        """
        if self.models is None or len(self.models) == 0:
            print("Aucun modèle à sauvegarder")
            return False
        
        try:
            # Créer le répertoire parent si nécessaire
            os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)
            
            # Sauvegarder les modèles
            joblib.dump(self.models, filepath)
            
            print(f"Modèles d'intervalles de confiance sauvegardés dans {filepath}")
            return True
        except Exception as e:
            print(f"Erreur lors de la sauvegarde des modèles: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def load_models(self, filepath='keno_confidence_models.pkl'):
        """
        Charge les modèles d'intervalles de confiance
        
        Args:
            filepath: Chemin du fichier contenant les modèles
            
        Returns:
            bool: True si le chargement a réussi, False sinon
        """
        try:
            # Vérifier que le fichier existe
            if not os.path.exists(filepath):
                print(f"Fichier {filepath} introuvable")
                return False
            
            # Charger les modèles
            self.models = joblib.load(filepath)
            
            print(f"Modèles d'intervalles de confiance chargés depuis {filepath}")
            return True
        except Exception as e:
            print(f"Erreur lors du chargement des modèles: {e}")
            import traceback
            traceback.print_exc()
            return False
