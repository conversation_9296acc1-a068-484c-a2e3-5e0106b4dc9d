# Script pour remplacer toutes les occurrences de self.root par self dans keno_gui.py

with open('keno_gui.py', 'r', encoding='utf-8') as file:
    content = file.read()

# Remplacer toutes les occurrences de self.root par self
modified_content = content.replace('self.root', 'self')

with open('keno_gui.py', 'w', encoding='utf-8') as file:
    file.write(modified_content)

print("Remplacement terminé.")
