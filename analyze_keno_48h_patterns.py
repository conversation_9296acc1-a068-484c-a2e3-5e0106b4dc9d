"""
Script pour analyser les motifs entre tirages Keno sur une période de 48h
Ce script identifie les relations entre les tirages consécutifs sur 48h.
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter
from datetime import datetime, timedelta

# Essayer d'importer le gestionnaire de données Keno
try:
    from keno_data import KenoDataManager, KenoDrawData
    print("Module de gestion des données Keno importé avec succès")
except ImportError:
    print("Erreur: Module de gestion des données Keno non disponible")
    sys.exit(1)

def group_draws_by_48h_periods(data_manager):
    """
    Regroupe les tirages par périodes de 48h
    
    Args:
        data_manager: Gestionnaire de données Keno
        
    Returns:
        list: Liste de groupes de tirages, chaque groupe couvrant 48h
    """
    if not data_manager.draws:
        print("Aucun tirage disponible dans le gestionnaire de données")
        return []
    
    # Trier les tirages par date
    sorted_draws = sorted(data_manager.draws, key=lambda x: x.draw_date)
    
    # Regrouper les tirages par périodes de 48h
    groups = []
    current_group = []
    start_date = None
    
    for draw in sorted_draws:
        if not start_date:
            # Premier tirage du groupe
            start_date = draw.draw_date
            current_group = [draw]
        elif (draw.draw_date - start_date).total_seconds() <= 48 * 3600:
            # Tirage dans la période de 48h
            current_group.append(draw)
        else:
            # Nouveau groupe
            if len(current_group) >= 3:  # Au moins 3 tirages dans le groupe
                groups.append(current_group)
            start_date = draw.draw_date
            current_group = [draw]
    
    # Ajouter le dernier groupe s'il contient au moins 3 tirages
    if current_group and len(current_group) >= 3:
        groups.append(current_group)
    
    print(f"Nombre de groupes de 48h: {len(groups)}")
    
    # Afficher quelques statistiques sur les groupes
    group_sizes = [len(group) for group in groups]
    print(f"Taille moyenne des groupes: {np.mean(group_sizes):.2f} tirages")
    print(f"Taille minimale: {min(group_sizes)} tirages")
    print(f"Taille maximale: {max(group_sizes)} tirages")
    
    return groups

def analyze_number_repetitions(groups):
    """
    Analyse les répétitions de numéros entre les tirages d'un même groupe de 48h
    
    Args:
        groups: Liste de groupes de tirages
        
    Returns:
        dict: Statistiques sur les répétitions de numéros
    """
    # Statistiques globales
    total_repetitions = 0
    total_numbers = 0
    repetition_counts = Counter()
    
    # Statistiques par position dans le groupe
    position_repetitions = {}
    
    # Analyser chaque groupe
    for group in groups:
        # Extraire les numéros de chaque tirage
        draw_numbers = [set(draw.draw_numbers) for draw in group]
        
        # Compter les répétitions pour chaque numéro
        all_numbers = set()
        for numbers in draw_numbers:
            all_numbers.update(numbers)
        
        for num in all_numbers:
            # Compter dans combien de tirages ce numéro apparaît
            count = sum(1 for numbers in draw_numbers if num in numbers)
            repetition_counts[count] += 1
            total_numbers += 1
            if count > 1:
                total_repetitions += 1
        
        # Analyser les répétitions par position
        for i in range(len(group) - 1):
            position_key = f"Position {i} -> {i+1}"
            if position_key not in position_repetitions:
                position_repetitions[position_key] = {
                    'total': 0,
                    'repetitions': 0,
                    'percentage': 0
                }
            
            # Compter les répétitions entre deux tirages consécutifs
            current_numbers = set(group[i].draw_numbers)
            next_numbers = set(group[i+1].draw_numbers)
            common_numbers = current_numbers.intersection(next_numbers)
            
            position_repetitions[position_key]['total'] += len(current_numbers)
            position_repetitions[position_key]['repetitions'] += len(common_numbers)
    
    # Calculer les pourcentages pour les répétitions par position
    for key in position_repetitions:
        if position_repetitions[key]['total'] > 0:
            position_repetitions[key]['percentage'] = (
                position_repetitions[key]['repetitions'] / position_repetitions[key]['total'] * 100
            )
    
    # Calculer les statistiques globales
    repetition_percentage = total_repetitions / total_numbers * 100 if total_numbers > 0 else 0
    
    # Créer le dictionnaire de résultats
    results = {
        'total_numbers': total_numbers,
        'total_repetitions': total_repetitions,
        'repetition_percentage': repetition_percentage,
        'repetition_counts': dict(repetition_counts),
        'position_repetitions': position_repetitions
    }
    
    return results

def analyze_transition_patterns(groups):
    """
    Analyse les motifs de transition entre les tirages consécutifs
    
    Args:
        groups: Liste de groupes de tirages
        
    Returns:
        dict: Statistiques sur les motifs de transition
    """
    # Compteurs pour les différents types de transitions
    transitions = {
        'appeared_then_disappeared': 0,  # Numéro présent puis absent
        'disappeared_then_appeared': 0,  # Numéro absent puis présent
        'stayed_present': 0,             # Numéro présent dans deux tirages consécutifs
        'stayed_absent': 0,              # Numéro absent dans deux tirages consécutifs
        'total_transitions': 0           # Nombre total de transitions analysées
    }
    
    # Analyser chaque groupe
    for group in groups:
        # Analyser les transitions entre tirages consécutifs
        for i in range(len(group) - 1):
            current_numbers = set(group[i].draw_numbers)
            next_numbers = set(group[i+1].draw_numbers)
            
            # Pour chaque numéro possible
            for num in range(1, 71):  # Keno utilise les numéros 1-70
                # Vérifier la transition
                if num in current_numbers and num in next_numbers:
                    transitions['stayed_present'] += 1
                elif num in current_numbers and num not in next_numbers:
                    transitions['appeared_then_disappeared'] += 1
                elif num not in current_numbers and num in next_numbers:
                    transitions['disappeared_then_appeared'] += 1
                else:
                    transitions['stayed_absent'] += 1
                
                transitions['total_transitions'] += 1
    
    # Calculer les pourcentages
    for key in transitions:
        if key != 'total_transitions' and transitions['total_transitions'] > 0:
            transitions[f'{key}_percentage'] = (
                transitions[key] / transitions['total_transitions'] * 100
            )
    
    return transitions

def analyze_consecutive_draws():
    """
    Analyse les motifs entre tirages consécutifs sur une période de 48h
    """
    # Créer le gestionnaire de données
    data_manager = KenoDataManager()
    
    # Charger les données
    print("Chargement des données Keno...")
    data_loaded = data_manager.load_data()
    
    if not data_loaded or not data_manager.draws:
        print("Erreur: Impossible de charger les données Keno")
        return
    
    print(f"Données chargées: {len(data_manager.draws)} tirages")
    
    # Regrouper les tirages par périodes de 48h
    print("\nRegroupement des tirages par périodes de 48h...")
    groups = group_draws_by_48h_periods(data_manager)
    
    if not groups:
        print("Aucun groupe de tirages trouvé")
        return
    
    # Analyser les répétitions de numéros
    print("\nAnalyse des répétitions de numéros...")
    repetition_results = analyze_number_repetitions(groups)
    
    # Afficher les résultats des répétitions
    print("\nRésultats de l'analyse des répétitions:")
    print(f"Nombre total de numéros analysés: {repetition_results['total_numbers']}")
    print(f"Nombre de répétitions: {repetition_results['total_repetitions']}")
    print(f"Pourcentage de répétition: {repetition_results['repetition_percentage']:.2f}%")
    
    print("\nRépétitions par nombre d'occurrences:")
    for count, frequency in sorted(repetition_results['repetition_counts'].items()):
        percentage = frequency / repetition_results['total_numbers'] * 100
        print(f"  {count} occurrence(s): {frequency} numéros ({percentage:.2f}%)")
    
    print("\nRépétitions par position:")
    for position, stats in repetition_results['position_repetitions'].items():
        print(f"  {position}: {stats['repetitions']} répétitions sur {stats['total']} numéros ({stats['percentage']:.2f}%)")
    
    # Analyser les motifs de transition
    print("\nAnalyse des motifs de transition...")
    transition_results = analyze_transition_patterns(groups)
    
    # Afficher les résultats des transitions
    print("\nRésultats de l'analyse des transitions:")
    print(f"Nombre total de transitions analysées: {transition_results['total_transitions']}")
    print(f"Numéros restés présents: {transition_results['stayed_present']} ({transition_results['stayed_present_percentage']:.2f}%)")
    print(f"Numéros apparus puis disparus: {transition_results['appeared_then_disappeared']} ({transition_results['appeared_then_disappeared_percentage']:.2f}%)")
    print(f"Numéros disparus puis réapparus: {transition_results['disappeared_then_appeared']} ({transition_results['disappeared_then_appeared_percentage']:.2f}%)")
    print(f"Numéros restés absents: {transition_results['stayed_absent']} ({transition_results['stayed_absent_percentage']:.2f}%)")
    
    # Créer un graphique des répétitions
    plt.figure(figsize=(10, 6))
    counts = list(repetition_results['repetition_counts'].keys())
    frequencies = list(repetition_results['repetition_counts'].values())
    
    plt.bar(counts, frequencies)
    plt.xlabel('Nombre d\'occurrences')
    plt.ylabel('Nombre de numéros')
    plt.title('Distribution des répétitions de numéros sur 48h')
    plt.xticks(counts)
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Sauvegarder le graphique
    plt.savefig('keno_48h_repetitions.png')
    print("\nGraphique des répétitions sauvegardé dans keno_48h_repetitions.png")
    
    # Créer un graphique des transitions
    plt.figure(figsize=(12, 6))
    transition_types = ['stayed_present', 'appeared_then_disappeared', 'disappeared_then_appeared', 'stayed_absent']
    transition_values = [transition_results[t] for t in transition_types]
    transition_labels = [
        'Restés présents',
        'Apparus puis disparus',
        'Disparus puis réapparus',
        'Restés absents'
    ]
    
    plt.bar(transition_labels, transition_values)
    plt.xlabel('Type de transition')
    plt.ylabel('Nombre de transitions')
    plt.title('Types de transitions entre tirages consécutifs sur 48h')
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    plt.xticks(rotation=15)
    
    # Sauvegarder le graphique
    plt.savefig('keno_48h_transitions.png')
    print("Graphique des transitions sauvegardé dans keno_48h_transitions.png")
    
    # Retourner les résultats
    return {
        'repetition_results': repetition_results,
        'transition_results': transition_results,
        'groups': groups
    }

if __name__ == "__main__":
    analyze_consecutive_draws()
