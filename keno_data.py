import csv
from datetime import datetime
import os
import json
import pickle
import re
import time
from urllib.parse import urlparse

# Importations conditionnelles pour les dépendances optionnelles
try:
    import requests
    requests_available = True
except ImportError:
    requests_available = False

try:
    from bs4 import BeautifulSoup
    bs4_available = True
except ImportError:
    bs4_available = False

class KenoDrawData:
    # Documentation string removed
    def __init__(self, draw_date, draw_numbers, draw_id=None, source_file=None):
        # Documentation string removed
        self.draw_date = draw_date
        self.draw_numbers = draw_numbers
        self.draw_id = draw_id
        self.source_file = source_file

class KenoDataManager:
    """Gestionnaire de données pour le jeu Keno"""
    def __init__(self, max_number=70, numbers_per_draw=20):
        """Initialise le gestionnaire de données Keno"""
        self.max_number = max_number
        self.numbers_per_draw = numbers_per_draw
        self.draws = []
        self._modified = False
        self._last_save_time = time.time()

    def set_parameters(self, max_number, numbers_per_draw):
        """Définit les paramètres du jeu Keno

        Args:
            max_number (int): Le nombre maximum possible (généralement 70 ou 80)
            numbers_per_draw (int): Le nombre de numéros tirés par tirage (généralement 20)
        """
        self.max_number = max_number
        self.numbers_per_draw = numbers_per_draw
        self._modified = True

    def is_modified(self):
        """Indique si les données ont été modifiées depuis la dernière sauvegarde

        Returns:
            bool: True si les données ont été modifiées, False sinon
        """
        return self._modified

    def _process_batch(self, batch, id_index, date_index, time_index, numbers_start_index, existing_draws, file_path=None):
        # Documentation string removed
        skipped_count = 0
        imported_count = 0

        for row in batch:
            try:
                if len(row) < numbers_start_index + self.numbers_per_draw:
                    print(f"Ligne ignorée: pas assez de colonnes ({len(row)})")
                    continue

                # Extraire l'ID
                draw_id = row[id_index].strip()

                # Extraire et parser la date
                date_str = row[date_index].strip() if date_index < len(row) else ""
                time_str = row[time_index].strip().lower() if time_index < len(row) else ""

                if not date_str:
                    print("Date manquante, ligne ignorée")
                    continue

                # Convertir l'heure textuelle
                if time_str == 'midi':
                    time_obj = datetime.strptime('12:00:00', '%H:%M:%S').time()
                elif time_str == 'soir':
                    time_obj = datetime.strptime('19:00:00', '%H:%M:%S').time()
                elif time_str == 'matin':
                    time_obj = datetime.strptime('09:00:00', '%H:%M:%S').time()
                elif time_str in ['après-midi', 'apres-midi', 'aprem']:
                    time_obj = datetime.strptime('15:00:00', '%H:%M:%S').time()
                else:
                    # Valeur par défaut
                    time_obj = datetime.strptime('12:00:00', '%H:%M:%S').time()

                # Parser la date avec plusieurs formats possibles
                try:
                    # Essayer d'abord le format français standard
                    date_obj = datetime.strptime(date_str, '%d/%m/%Y').date()
                    draw_date = datetime.combine(date_obj, time_obj)
                except ValueError:
                    try:
                        # Essayer le format ISO
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                        draw_date = datetime.combine(date_obj, time_obj)
                    except ValueError:
                        try:
                            # Essayer le format américain
                            date_obj = datetime.strptime(date_str, '%m/%d/%Y').date()
                            draw_date = datetime.combine(date_obj, time_obj)
                        except ValueError:
                            print(f"Erreur de format de date: {date_str}")
                            continue

                # Extraire les numéros
                draw_numbers = []
                for i in range(numbers_start_index, numbers_start_index + self.numbers_per_draw):
                    if i < len(row) and row[i].strip():
                        try:
                            num = int(row[i].strip())
                            # Vérifier que le numéro est dans la plage valide
                            if 1 <= num <= self.max_number:
                                draw_numbers.append(num)
                            else:
                                print(f"Numéro hors plage ignoré: {num} (doit être entre 1 et {self.max_number})")
                        except ValueError:
                            print(f"Valeur non numérique ignorée: {row[i]}")

                # Vérifier que nous avons le bon nombre de numéros
                if len(draw_numbers) == self.numbers_per_draw:
                    # Vérifier si ce tirage existe déjà
                    is_duplicate = False
                    if draw_id and draw_id in existing_draws:
                        is_duplicate = True
                        skipped_count += 1
                    elif (draw_date.isoformat(), tuple(sorted(draw_numbers))) in existing_draws:
                        is_duplicate = True
                        skipped_count += 1

                    if not is_duplicate:
                        # Ajouter le tirage à la liste avec le nom du fichier source
                        source_file = os.path.basename(file_path) if 'file_path' in locals() else None
                        draw = KenoDrawData(draw_date, draw_numbers, draw_id, source_file)
                        self.draws.append(draw)

                        # Ajouter à l'ensemble des tirages existants
                        if draw_id:
                            existing_draws.add(draw_id)
                        else:
                            existing_draws.add((draw_date.isoformat(), tuple(sorted(draw_numbers))))
                else:
                    print(f"Nombre incorrect de numéros: {len(draw_numbers)} (attendu: {self.numbers_per_draw})")

            except Exception as e:
                print(f"Erreur lors du traitement d'une ligne: {e}")
                continue

        return skipped_count

    def load_csv(self, file_path, date_format="%d/%m/%Y",
                 date_column=1, numbers_start_column=4, has_header=True,
                 id_column=0, delimiter=';', french_date=True,
                 time_column=2, combine_date_time=True, clear_existing=True,
                 progress_callback=None, encoding='utf-8'):
        """Charge les données depuis un fichier CSV générique."""
        if not os.path.exists(file_path):
            return False

        # Effacer les données existantes si demandé
        if clear_existing:
            self.draws = []

        # Créer un ensemble pour suivre les tirages déjà importés (pour éviter les doublons)
        existing_draws = set()
        for draw in self.draws:
            # Utiliser l'ID et la date comme identifiant unique
            if draw.draw_id:
                existing_draws.add(draw.draw_id)
            else:
                # Si pas d'ID, utiliser la date et les numéros
                existing_draws.add((draw.draw_date.isoformat(), tuple(sorted(draw.draw_numbers))))

        # Initialiser les compteurs
        imported_count = 0
        skipped_count = 0

        try:
            # Détecter automatiquement le format et le délimiteur
            is_fdj_format = False
            try:
                with open(file_path, 'r', newline='', encoding=encoding) as f:
                    first_line = f.readline().strip()

                    # Détecter automatiquement le délimiteur
                    possible_delimiters = [';', ',', '\t', '|']
                    delimiter_counts = {}
                    for delim in possible_delimiters:
                        delimiter_counts[delim] = first_line.count(delim)

                    # Choisir le délimiteur le plus fréquent
                    max_count = max(delimiter_counts.values())
                    if max_count > 0:
                        detected_delimiter = max(delimiter_counts.items(), key=lambda x: x[1])[0]
                        if detected_delimiter != delimiter:
                            print(f"Délimiteur détecté: '{detected_delimiter}' (au lieu de '{delimiter}')")
                            delimiter = detected_delimiter

                    # Vérifier si c'est le format FDJ (en-tête avec annee_numero_de_tirage, date_de_tirage, etc.)
                    if 'annee_numero_de_tirage' in first_line and 'date_de_tirage' in first_line and 'heure_de_tirage' in first_line:
                        is_fdj_format = True
                        print(f"Format FDJ détecté pour le fichier {os.path.basename(file_path)}")
                        # Ajuster les paramètres pour le format FDJ
                        id_column = 0  # annee_numero_de_tirage
                        date_column = 1  # date_de_tirage
                        time_column = 2  # heure_de_tirage
                        numbers_start_column = 4  # boule1, boule2, etc.
                        french_date = True
            except Exception as e:
                print(f"Erreur lors de la détection du format: {e}")
                # Continuer avec les paramètres par défaut

            with open(file_path, 'r', newline='', encoding=encoding) as csvfile:
                reader = csv.reader(csvfile, delimiter=delimiter)

                # Skip header if needed
                if has_header:
                    next(reader)

                for row in reader:
                    try:
                        # Parse date
                        date_str = row[date_column]

                        # Handle French date format (DD/MM/YYYY)
                        if french_date and '/' in date_str:
                            day, month, year = date_str.split('/')
                            date_str = f"{year}-{month}-{day}"

                        # Traitement spécial pour le format FDJ Keno

                        if len(date_str.strip()) == 7 and date_str.strip().isdigit():
                            # Format YYYYDDD détecté
                            year_str = date_str.strip()[:4]  # Les 4 premiers chiffres = année
                            day_str = date_str.strip()[4:]   # Les 3 derniers chiffres = jour de l'année

                            try:
                                year = int(year_str)
                                day_of_year = int(day_str)

                                # Vérifier que l'année et le jour sont valides
                                if 1900 <= year <= 2100 and 1 <= day_of_year <= 366:
                                    # Convertir en date
                                    from datetime import timedelta
                                    base_date = datetime(year, 1, 1)  # 1er janvier de l'année
                                    draw_date = base_date + timedelta(days=day_of_year-1)  # -1 car le jour 001 = 1er janvier

                                    # Ajouter une heure par défaut (midi)
                                    from datetime import time
                                    draw_date = datetime.combine(draw_date.date(), time(12, 0, 0))

                                    print(f"Date convertie depuis le format YYYYDDD: {date_str} -> {draw_date}")

                                    # Créer les données du tirage et les ajouter à la liste
                                    draw_numbers = []
                                    for i in range(numbers_start_column, numbers_start_column + self.numbers_per_draw):
                                        if i < len(row):
                                            try:
                                                # Gérer les valeurs potentiellement vides ou non numériques
                                                num_str = row[i].strip()
                                                if num_str:
                                                    num = int(num_str)
                                                    # Vérifier que le numéro est dans la plage valide
                                                    if 1 <= num <= self.max_number:
                                                        draw_numbers.append(num)
                                                    else:
                                                        print(f"Numéro hors plage ignoré: {num} (doit être entre 1 et {self.max_number})")
                                            except ValueError:
                                                print(f"Valeur non numérique ignorée: {row[i]}")

                                    # Analyser l'ID si spécifié
                                    draw_id = None
                                    if id_column is not None and id_column < len(row):
                                        draw_id = row[id_column]

                                    # Créer les données du tirage et les ajouter à la liste
                                    if len(draw_numbers) == self.numbers_per_draw:
                                        # Vérifier si ce tirage existe déjà
                                        is_duplicate = False
                                        if draw_id and draw_id in existing_draws:
                                            is_duplicate = True
                                            skipped_count += 1
                                        elif (draw_date.isoformat(), tuple(sorted(draw_numbers))) in existing_draws:
                                            is_duplicate = True
                                            skipped_count += 1

                                        if not is_duplicate:
                                            # Ajouter le tirage à la liste
                                            draw = KenoDrawData(draw_date, draw_numbers, draw_id)
                                            self.draws.append(draw)

                                            # Ajouter à l'ensemble des tirages existants
                                            if draw_id:
                                                existing_draws.add(draw_id)
                                            else:
                                                existing_draws.add((draw_date.isoformat(), tuple(sorted(draw_numbers))))

                                            imported_count += 1

                                    continue  # Passer à la ligne suivante
                            except Exception as e:
                                print(f"Erreur lors de la conversion du format YYYYDDD: {e}")

                        # Si ce n'est pas au format YYYYDDD ou si la conversion a échoué, continuer avec le traitement normal
                        # Combine date and time if specified
                        if combine_date_time and time_column is not None and time_column < len(row):
                            time_str = row[time_column].lower().strip()

                            # Gestion automatique des formats d'heure français
                            if time_str == 'midi':
                                time_str = '12:00:00'
                            elif time_str == 'soir':
                                time_str = '19:00:00'
                            elif time_str == 'matin':
                                time_str = '09:00:00'
                            elif time_str == 'après-midi' or time_str == 'apres-midi' or time_str == 'aprem':
                                time_str = '15:00:00'
                            elif time_str == 'nuit' or time_str == 'soirée':
                                time_str = '21:00:00'
                            # Si l'heure est déjà au format HH:MM:SS ou HH:MM, on la garde telle quelle
                            elif ':' not in time_str:
                                # Si c'est juste un nombre, on suppose que c'est l'heure
                                try:
                                    hour = int(time_str)
                                    if 0 <= hour <= 23:
                                        time_str = f"{hour:02d}:00:00"
                                    else:
                                        time_str = '12:00:00'  # Valeur par défaut si l'heure est invalide
                                except ValueError:
                                    # Si ce n'est pas un nombre, on utilise une valeur par défaut
                                    time_str = '12:00:00'

                            date_str = f"{date_str} {time_str}"

                        # Traitement spécial pour le format FDJ Keno
                        # Vérifier si nous avons une colonne d'heure et une colonne de date
                        if time_column is not None and time_column < len(row):
                            # Obtenir l'heure (midi, soir, etc.)
                            time_str = row[time_column].lower().strip()

                            # Convertir l'heure textuelle en format horaire
                            if time_str == 'midi':
                                time_obj = datetime.strptime('12:00:00', '%H:%M:%S').time()
                            elif time_str == 'soir':
                                time_obj = datetime.strptime('19:00:00', '%H:%M:%S').time()
                            elif time_str == 'matin':
                                time_obj = datetime.strptime('09:00:00', '%H:%M:%S').time()
                            elif time_str == 'après-midi' or time_str == 'apres-midi' or time_str == 'aprem':
                                time_obj = datetime.strptime('15:00:00', '%H:%M:%S').time()
                            else:
                                # Valeur par défaut
                                time_obj = datetime.strptime('12:00:00', '%H:%M:%S').time()

                            try:
                                # Essayer de parser la date au format français DD/MM/YYYY
                                date_obj = datetime.strptime(date_str, '%d/%m/%Y').date()
                                # Combiner date et heure
                                draw_date = datetime.combine(date_obj, time_obj)
                                print(f"Date et heure combinées: {date_str} {time_str} -> {draw_date}")
                            except ValueError:
                                # Si le format de date échoue, essayer d'autres formats
                                try:
                                    # Essayer plusieurs formats de date courants
                                    formats_to_try = [
                                        "%d/%m/%Y",           # Format français DD/MM/YYYY
                                        "%Y-%m-%d",           # Format ISO
                                        "%Y-%m-%d %H:%M:%S",  # Format ISO avec heure
                                        "%Y-%m-%d %H:%M",     # Format ISO avec heure sans secondes
                                    ]

                                    for fmt in formats_to_try:
                                        try:
                                            # Essayer de parser la date avec le format actuel
                                            date_obj = datetime.strptime(date_str, fmt).date()
                                            draw_date = datetime.combine(date_obj, time_obj)
                                            print(f"Date parsée avec succès en utilisant le format: {fmt}")
                                            break
                                        except ValueError:
                                            # Essayer de nettoyer la date et réessayer
                                            try:
                                                # Supprimer les caractères non numériques sauf les séparateurs courants
                                                cleaned_date_str = ''.join(c for c in date_str if c.isdigit() or c in '/-: ')
                                                # Essayer de parser la date nettoyée
                                                date_obj = datetime.strptime(cleaned_date_str, fmt).date()
                                                draw_date = datetime.combine(date_obj, time_obj)
                                                print(f"Date parsée avec succès après nettoyage en utilisant le format: {fmt}")
                                                break
                                            except ValueError:
                                                continue
                                    else:  # Si aucun format n'a fonctionné
                                        raise ValueError(f"Impossible de parser la date: {date_str}")
                                except ValueError as e:
                                    print(f"Erreur lors du parsing de la date: {e}")
                                    continue
                        else:
                            # Si pas de colonne d'heure, essayer de parser la date directement
                            try:
                                draw_date = datetime.strptime(date_str, date_format)
                            except ValueError:
                                # Si le format standard échoue, essayer d'autres formats
                                try:
                                    # Essayer plusieurs formats de date courants
                                    formats_to_try = [
                                        "%d/%m/%Y",           # Format français DD/MM/YYYY
                                        "%d/%m/%Y %H:%M:%S",  # Format français avec heure
                                        "%d/%m/%Y %H:%M",     # Format français avec heure sans secondes
                                        "%Y-%m-%d",           # Format ISO
                                        "%Y-%m-%d %H:%M:%S",  # Format ISO avec heure
                                        "%Y-%m-%d %H:%M",     # Format ISO avec heure sans secondes
                                        "%Y%m%d",             # Format compact YYYYMMDD
                                        "%Y%m%d%H%M%S"        # Format compact YYYYMMDDhhmmss
                                    ]

                                    for fmt in formats_to_try:
                                        try:
                                            # Essayer de parser la date avec le format actuel
                                            draw_date = datetime.strptime(date_str, fmt)
                                            print(f"Date parsée avec succès en utilisant le format: {fmt}")
                                            break
                                        except ValueError:
                                            # Essayer de nettoyer la date et réessayer
                                            try:
                                                # Supprimer les caractères non numériques sauf les séparateurs courants
                                                cleaned_date_str = ''.join(c for c in date_str if c.isdigit() or c in '/-: ')
                                                # Essayer de parser la date nettoyée
                                                draw_date = datetime.strptime(cleaned_date_str, fmt)
                                                print(f"Date parsée avec succès après nettoyage en utilisant le format: {fmt}")
                                                break
                                            except ValueError:
                                                continue
                                    else:  # Si aucun format n'a fonctionné
                                        # Vérifier si la première colonne est au format YYYYNNN (7 chiffres)
                                        id_str = row[0].strip() if len(row) > 0 else ""
                                        if len(id_str) == 7 and id_str.isdigit():
                                            year_str = id_str[:4]  # Les 4 premiers chiffres = année
                                            num_str = id_str[4:]   # Les 3 derniers chiffres = numéro de tirage

                                            try:
                                                year = int(year_str)
                                                num = int(num_str)

                                                # Vérifier que l'année est valide
                                                if 1900 <= year <= 2100:
                                                    # Utiliser la date du fichier comme approximation
                                                    # Nous utilisons le numéro comme jour de l'année (approximatif)
                                                    from datetime import timedelta
                                                    base_date = datetime(year, 1, 1)  # 1er janvier de l'année

                                                    # Ajuster en fonction du numéro de tirage
                                                    # Supposons 2 tirages par jour, donc diviser par 2
                                                    day_of_year = (num + 1) // 2  # +1 pour éviter le jour 0
                                                    if day_of_year > 366:  # Limiter au nombre de jours dans l'année
                                                        day_of_year = 366

                                                    draw_date = base_date + timedelta(days=day_of_year-1)

                                                    # Ajouter l'heure en fonction du numéro de tirage (pair = soir, impair = midi)
                                                    hour = 19 if num % 2 == 0 else 12  # 19h pour soir, 12h pour midi
                                                    from datetime import time
                                                    draw_date = datetime.combine(draw_date.date(), time(hour, 0, 0))

                                                    print(f"Date convertie depuis l'ID YYYYNNN: {id_str} -> {draw_date}")
                                                    break
                                            except ValueError:
                                                pass

                                        # Si toutes les tentatives échouent
                                        raise ValueError(f"Impossible de parser la date: {date_str}")
                                except ValueError as e:
                                    print(f"Erreur lors du parsing de la date: {e}")
                                    continue

                        # Parse numbers
                        draw_numbers = []
                        for i in range(numbers_start_column, numbers_start_column + self.numbers_per_draw):
                            if i < len(row):
                                try:
                                    # Handle potential empty or non-numeric values
                                    num_str = row[i].strip()
                                    if num_str:
                                        num = int(num_str)
                                        # Vérifier que le numéro est dans la plage valide
                                        if 1 <= num <= self.max_number:
                                            draw_numbers.append(num)
                                        else:
                                            print(f"Numéro hors plage ignoré: {num} (doit être entre 1 et {self.max_number})")
                                except ValueError:
                                    print(f"Valeur non numérique ignorée: {row[i]}")

                        # Parse ID if specified
                        draw_id = None
                        if id_column is not None and id_column < len(row):
                            draw_id = row[id_column]

                        # Create draw data and add to list
                        if len(draw_numbers) == self.numbers_per_draw:
                            # Vérifier si ce tirage existe déjà
                            is_duplicate = False
                            if draw_id and draw_id in existing_draws:
                                is_duplicate = True
                                skipped_count += 1
                            elif (draw_date.isoformat(), tuple(sorted(draw_numbers))) in existing_draws:
                                is_duplicate = True
                                skipped_count += 1

                            if not is_duplicate:
                                # Ajouter le tirage à la liste
                                draw = KenoDrawData(draw_date, draw_numbers, draw_id)
                                self.draws.append(draw)

                                # Ajouter à l'ensemble des tirages existants
                                if draw_id:
                                    existing_draws.add(draw_id)
                                else:
                                    existing_draws.add((draw_date.isoformat(), tuple(sorted(draw_numbers))))

                                imported_count += 1
                    except (ValueError, IndexError) as e:
                        print(f"Erreur lors du traitement d'une ligne: {e}")
                        continue

            # Sort draws by date
            self.draws.sort(key=lambda x: x.draw_date)

            # Marquer comme modifié
            self._modified = True

            # Si aucun tirage n'a été importé mais qu'il n'y a pas eu d'erreur, retourner 0 au lieu de False
            # pour indiquer que l'importation s'est bien déroulée mais qu'aucun tirage n'a été importé
            return imported_count if imported_count > 0 else 0

        except Exception as e:
            print(f"Erreur lors du chargement du fichier CSV: {e}")
            return False

    def load_fdj_keno_csv(self, file_path, clear_existing=True, progress_callback=None):
        # Documentation string removed
        if not os.path.exists(file_path):
            return 0

        # Effacer les données existantes si demandé
        if clear_existing:
            self.draws = []

        # Créer un ensemble pour suivre les tirages déjà importés (pour éviter les doublons)
        existing_draws = set()
        for draw in self.draws:
            # Utiliser l'ID et la date comme identifiant unique
            if draw.draw_id:
                existing_draws.add(draw.draw_id)
            else:
                # Si pas d'ID, utiliser la date et les numéros
                existing_draws.add((draw.draw_date.isoformat(), tuple(sorted(draw.draw_numbers))))

        # Compteur pour les statistiques
        skipped_count = 0
        imported_count = 0

        # Compter le nombre total de lignes pour la barre de progression
        total_lines = 0
        try:
            with open(file_path, 'r', newline='', encoding='utf-8') as f:
                total_lines = sum(1 for _ in f)
                if progress_callback:
                    progress_callback(0, total_lines, "Comptage des lignes...")
        except Exception as e:
            print(f"Erreur lors du comptage des lignes: {e}")
            total_lines = 1000  # Valeur par défaut

        try:
            with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.reader(csvfile, delimiter=';')

                # Skip header
                header = next(reader)
                print(f"En-tête détectée: {header}")

                # Vérifier que c'est bien un fichier Keno FDJ
                if not header[0].startswith("annee_numero") or not "boule1" in header:
                    print("Ce n'est pas un fichier Keno FDJ standard. Utilisation de la méthode générique.")
                    return False

                # Trouver les index des colonnes importantes
                id_index = 0  # annee_numero_de_tirage
                date_index = 1  # date_de_tirage
                time_index = 2  # heure_de_tirage

                # Trouver l'index de la première boule
                numbers_start_index = -1
                for i, col in enumerate(header):
                    if col == "boule1":
                        numbers_start_index = i
                        break

                if numbers_start_index == -1:
                    print("Impossible de trouver les colonnes des numéros. Utilisation de la méthode générique.")
                    return False

                print(f"Structure détectée: ID={id_index}, Date={date_index}, Heure={time_index}, Numéros={numbers_start_index}")

                # Lire les données
                line_count = 0
                batch_size = 100  # Traiter les données par lots de 100 lignes
                batch = []

                for row in reader:
                    line_count += 1

                    # Mettre à jour la progression tous les 100 lignes
                    if progress_callback and line_count % 100 == 0:
                        progress_callback(line_count, total_lines, f"Import en cours... {len(self.draws)} importés, {skipped_count} ignorés")

                    batch.append(row)

                    # Traiter le lot lorsqu'il atteint la taille définie
                    if len(batch) >= batch_size:
                        result = self._process_batch(batch, id_index, date_index, time_index, numbers_start_index, existing_draws, file_path)
                        if isinstance(result, int):
                            imported_count += result
                        batch = []  # Réinitialiser le lot

                # Traiter le dernier lot s'il reste des lignes
                if batch:
                    imported_batch = self._process_batch(batch, id_index, date_index, time_index, numbers_start_index, existing_draws, file_path)
                    if isinstance(imported_batch, int):
                        imported_count += imported_batch

                # Trier les tirages par date
                self.draws.sort(key=lambda x: x.draw_date)
                print(f"Importé avec succès: {imported_count} tirages ({skipped_count} doublons ignorés)")

                # Mise à jour finale de la progression
                if progress_callback:
                    progress_callback(total_lines, total_lines, f"Import terminé. {imported_count} tirages importés.")

                return imported_count

        except Exception as e:
            print(f"Erreur lors du chargement du fichier CSV: {e}")
            return False

    def get_draws_count(self):
        # Documentation string removed
        return len(self.draws)

    def get_source_files(self):
        # Documentation string removed
        sources = set()
        for draw in self.draws:
            if draw.source_file:
                sources.add(draw.source_file)
        return sorted(list(sources))

    def get_last_draws(self, count=10):
        # Documentation string removed
        return self.draws[-count:] if self.draws else []

    def save_database(self, file_path):
        # Documentation string removed
        try:
            # Créer un dictionnaire avec les données à sauvegarder
            data = {
                'max_number': self.max_number,
                'numbers_per_draw': self.numbers_per_draw,
                'draws': []
            }

            # Convertir chaque tirage en dictionnaire
            for draw in self.draws:
                draw_dict = {
                    'draw_date': draw.draw_date.isoformat(),
                    'draw_numbers': draw.draw_numbers,
                    'draw_id': draw.draw_id,
                    'source_file': draw.source_file
                }
                data['draws'].append(draw_dict)

            # Déterminer le format de fichier en fonction de l'extension
            _, ext = os.path.splitext(file_path)

            if ext.lower() == '.json':
                # Sauvegarder au format JSON
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2)
            else:
                # Sauvegarder au format pickle par défaut
                with open(file_path, 'wb') as f:
                    pickle.dump(data, f)

            # Réinitialiser le flag de modification
            self._modified = False
            self._last_save_time = time.time()

            return True
        except Exception as e:
            print("Erreur lors de la sauvegarde de la base de données: {}".format(e))
            return False

    def load_database(self, file_path):
        # Documentation string removed
        if not os.path.exists(file_path):
            return False

        try:
            # Déterminer le format de fichier en fonction de l'extension
            _, ext = os.path.splitext(file_path)

            if ext.lower() == '.json':
                # Charger depuis le format JSON
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                # Charger depuis le format pickle par défaut
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)

            # Mettre à jour les paramètres
            self.max_number = data.get('max_number', 70)
            self.numbers_per_draw = data.get('numbers_per_draw', 20)

            # Effacer les données existantes
            self.draws = []

            # Recréer les objets KenoDrawData
            for draw_dict in data.get('draws', []):
                try:
                    draw_date = datetime.fromisoformat(draw_dict['draw_date'])
                    draw_numbers = draw_dict['draw_numbers']
                    draw_id = draw_dict.get('draw_id')
                    source_file = draw_dict.get('source_file')

                    draw = KenoDrawData(draw_date, draw_numbers, draw_id, source_file)
                    self.draws.append(draw)
                except Exception as e:
                    print("Erreur lors de la conversion d'un tirage: {}".format(e))

            # Trier les tirages par date
            self.draws.sort(key=lambda x: x.draw_date)

            # Réinitialiser le flag de modification
            self._modified = False
            self._last_save_time = time.time()

            return True
        except Exception as e:
            print("Erreur lors du chargement de la base de données: {}".format(e))
            return False

    def download_keno_data(self, url):
        """Télécharge les données Keno depuis une URL"""
        # Vérifier si requests est disponible
        if not requests_available:
            print("La bibliothèque 'requests' n'est pas installée. Impossible de télécharger les données.")
            print("Installez-la avec: pip install requests")
            return False

        try:
            # Vérifier si c'est l'URL de l'API FDJ spécifique
            if "sto.api.fdj.fr" in url.lower() and "service-draw-info" in url.lower():
                # Utiliser la fonction spécifique pour l'API FDJ
                return self.download_from_fdj_api(url)

            # Vérifier si c'est l'URL du site de la FDJ
            if "fdj.fr" in url.lower() and "keno" in url.lower() and not url.lower().endswith('.zip'):
                # Utiliser la fonction de scraping spécifique pour la FDJ
                return self.scrape_fdj_keno_results()

            # Pour les autres URLs, utiliser la méthode standard
            # Vérifier que l'URL est valide
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                print("URL invalide")
                return False

            # Télécharger le fichier
            print(f"Téléchargement depuis l'URL: {url}")
            response = requests.get(url, stream=True, timeout=30)
            if response.status_code != 200:
                print(f"Erreur lors du téléchargement: {response.status_code}")
                return False

            # Déterminer le nom du fichier
            filename = os.path.basename(parsed_url.path)
            if not filename:
                filename = "keno_data_downloaded.csv"

            # Sauvegarder le fichier téléchargé
            file_path = os.path.join(os.getcwd(), filename)
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            print(f"Fichier téléchargé et sauvegardé sous: {file_path}")

            # Si c'est un fichier ZIP, le décompresser et traiter les fichiers CSV
            if filename.lower().endswith('.zip'):
                return self.process_zip_file(file_path)

            # Essayer d'importer le fichier téléchargé
            success = self.load_fdj_keno_csv(file_path, clear_existing=False)
            if not success:
                # Essayer avec la méthode générique
                success = self.load_csv(file_path, clear_existing=False)

            return success
        except Exception as e:
            print(f"Erreur lors du téléchargement des données: {e}")
            import traceback
            traceback.print_exc()
            return False

    def download_from_fdj_api(self, url):
        """Télécharge les données Keno depuis l'API FDJ spécifique"""
        import json
        import tempfile
        import shutil
        import zipfile

        print(f"Téléchargement depuis l'API FDJ: {url}")

        try:
            # Créer un dossier temporaire pour stocker les fichiers
            temp_dir = tempfile.mkdtemp()
            print(f"Dossier temporaire créé: {temp_dir}")

            # Faire une requête à l'API
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7',
                'Origin': 'https://www.fdj.fr',
                'Referer': 'https://www.fdj.fr/'
            }

            response = requests.get(url, headers=headers, timeout=30)
            print(f"Code de réponse: {response.status_code}")

            if response.status_code != 200:
                print(f"Erreur lors de la requête à l'API: {response.status_code}")
                print(f"Contenu de la réponse: {response.text[:500]}...")
                return False

            # Essayer de parser la réponse JSON
            try:
                data = response.json()
                print(f"Réponse JSON reçue: {json.dumps(data)[:500]}...")
            except Exception as e:
                print(f"Erreur lors du parsing JSON: {e}")
                print(f"Contenu de la réponse: {response.text[:500]}...")

                # Si ce n'est pas du JSON, essayer de sauvegarder comme un fichier binaire
                if response.headers.get('content-type', '').startswith('application/octet-stream') or \
                   response.headers.get('content-type', '').startswith('application/zip'):
                    # C'est probablement un fichier ZIP
                    zip_path = os.path.join(temp_dir, "keno_data.zip")
                    with open(zip_path, 'wb') as f:
                        f.write(response.content)
                    print(f"Fichier binaire sauvegardé: {zip_path}")

                    # Vérifier si c'est un fichier ZIP valide
                    if zipfile.is_zipfile(zip_path):
                        print("Le fichier est un ZIP valide, traitement...")
                        return self.process_zip_file(zip_path)
                    else:
                        print("Le fichier n'est pas un ZIP valide")
                        return False
                else:
                    # Sauvegarder la réponse dans un fichier pour inspection
                    response_path = os.path.join(temp_dir, "api_response.txt")
                    with open(response_path, 'wb') as f:
                        f.write(response.content)
                    print(f"Réponse sauvegardée dans: {response_path}")
                    return False

            # Traiter les données JSON
            # Chercher les URLs de téléchargement dans la réponse
            download_urls = []

            # Parcourir la structure JSON pour trouver les URLs
            def find_download_urls(obj):
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        if key in ['url', 'downloadUrl', 'download_url'] and isinstance(value, str) and ('.zip' in value.lower() or '.csv' in value.lower()):
                            download_urls.append(value)
                        elif isinstance(value, (dict, list)):
                            find_download_urls(value)
                elif isinstance(obj, list):
                    for item in obj:
                        find_download_urls(item)

            find_download_urls(data)
            print(f"URLs de téléchargement trouvées: {download_urls}")

            if not download_urls:
                print("Aucune URL de téléchargement trouvée dans la réponse JSON")
                return False

            # Télécharger chaque fichier
            total_imported = 0
            for download_url in download_urls:
                # Normaliser l'URL si nécessaire
                if not download_url.startswith('http'):
                    if download_url.startswith('/'):
                        # URL relative au domaine
                        parsed_api_url = urlparse(url)
                        base_url = f"{parsed_api_url.scheme}://{parsed_api_url.netloc}"
                        download_url = base_url + download_url
                    else:
                        # URL relative au chemin
                        download_url = url.rsplit('/', 1)[0] + '/' + download_url

                print(f"Téléchargement depuis: {download_url}")

                # Télécharger le fichier
                try:
                    file_response = requests.get(download_url, headers=headers, timeout=30)
                    if file_response.status_code != 200:
                        print(f"Erreur lors du téléchargement du fichier: {file_response.status_code}")
                        continue

                    # Déterminer le nom du fichier
                    filename = os.path.basename(urlparse(download_url).path)
                    if not filename:
                        if '.zip' in download_url.lower():
                            filename = "keno_data.zip"
                        else:
                            filename = "keno_data.csv"

                    # Sauvegarder le fichier
                    file_path = os.path.join(temp_dir, filename)
                    with open(file_path, 'wb') as f:
                        f.write(file_response.content)
                    print(f"Fichier sauvegardé: {file_path}")

                    # Traiter le fichier selon son type
                    if filename.lower().endswith('.zip'):
                        imported = self.process_zip_file(file_path)
                        if isinstance(imported, bool) and imported:
                            total_imported += 1
                        elif isinstance(imported, int):
                            total_imported += imported
                    else:
                        # Essayer d'importer le fichier CSV
                        imported = self.load_fdj_keno_csv(file_path, clear_existing=False)
                        if not imported:
                            imported = self.load_csv(file_path, clear_existing=False)

                        if isinstance(imported, int) and imported > 0:
                            total_imported += imported
                except Exception as e:
                    print(f"Erreur lors du traitement de l'URL {download_url}: {e}")
                    import traceback
                    traceback.print_exc()

            return total_imported > 0

        except Exception as e:
            print(f"Erreur lors du téléchargement depuis l'API FDJ: {e}")
            import traceback
            traceback.print_exc()
            return False

        finally:
            # Nettoyer les fichiers temporaires
            try:
                shutil.rmtree(temp_dir)
                print(f"Nettoyage des fichiers temporaires: {temp_dir}")
            except Exception as e:
                print(f"Erreur lors du nettoyage des fichiers temporaires: {str(e)}")

    def process_zip_file(self, zip_path):
        """Traite un fichier ZIP contenant des données Keno"""
        import zipfile
        import tempfile
        import shutil

        print(f"Traitement du fichier ZIP: {zip_path}")

        # Créer un dossier temporaire pour extraire les fichiers
        temp_dir = tempfile.mkdtemp()
        try:
            # Vérifier que le fichier ZIP est valide
            if not zipfile.is_zipfile(zip_path):
                print("Le fichier n'est pas un fichier ZIP valide")
                return False

            # Extraire les fichiers
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # Lister le contenu du ZIP
                file_list = zip_ref.namelist()
                print(f"Contenu du ZIP: {file_list}")

                # Extraire tous les fichiers
                zip_ref.extractall(temp_dir)
                print(f"Fichiers extraits dans: {temp_dir}")

            # Trouver les fichiers CSV dans le dossier extrait
            csv_files = []
            for root, _, files in os.walk(temp_dir):
                for file in files:
                    if file.lower().endswith('.csv'):
                        csv_files.append(os.path.join(root, file))

            print(f"Fichiers CSV trouvés: {len(csv_files)}")
            for csv_file in csv_files:
                print(f"  - {csv_file}")

            if not csv_files:
                print("Aucun fichier CSV trouvé dans l'archive.")
                return False

            # Traiter chaque fichier CSV
            total_imported = 0
            for i, csv_file in enumerate(csv_files):
                print(f"Traitement du fichier {i+1}/{len(csv_files)}: {os.path.basename(csv_file)}")

                # Essayer d'abord avec le format FDJ Keno
                try:
                    print("Tentative d'importation avec load_fdj_keno_csv...")
                    imported = self.load_fdj_keno_csv(csv_file, clear_existing=False)
                    print(f"Résultat de load_fdj_keno_csv: {imported}")
                except Exception as e:
                    print(f"Erreur avec load_fdj_keno_csv: {str(e)}")
                    imported = False

                # Si cela échoue, essayer avec la méthode générique
                if not imported:
                    try:
                        print("Tentative d'importation avec load_csv...")
                        imported = self.load_csv(csv_file, clear_existing=False)
                        print(f"Résultat de load_csv: {imported}")
                    except Exception as e:
                        print(f"Erreur avec load_csv: {str(e)}")
                        imported = 0

                if isinstance(imported, int) and imported > 0:
                    total_imported += imported
                    print(f"Importé {imported} tirages depuis {csv_file}")
                else:
                    print(f"Aucun tirage importé depuis {csv_file}")

            print(f"Importation terminée. {total_imported} tirages importés.")
            return total_imported > 0

        except Exception as e:
            print(f"Erreur lors du traitement du fichier ZIP: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

        finally:
            # Nettoyer les fichiers temporaires
            try:
                shutil.rmtree(temp_dir)
                print(f"Nettoyage des fichiers temporaires: {temp_dir}")
            except Exception as e:
                print(f"Erreur lors du nettoyage des fichiers temporaires: {str(e)}")

    def scrape_fdj_keno_results(self, num_days=30):
        # Documentation string removed
        # Vérifier si les bibliothèques nécessaires sont disponibles
        if not requests_available:
            print("La bibliothèque 'requests' n'est pas installée. Impossible de récupérer les données.")
            print("Installez-la avec: pip install requests")
            return False

        if not bs4_available:
            print("La bibliothèque 'BeautifulSoup' n'est pas installée. Impossible de parser les données.")
            print("Installez-la avec: pip install beautifulsoup4")
            return False

        try:
            print("Début du scraping des résultats Keno depuis le site de la FDJ...")

            # URL de base pour les résultats Keno
            base_url = "https://www.fdj.fr/jeux-de-tirage/keno-gagnant-a-vie/resultats"

            # Créer un fichier CSV temporaire pour stocker les résultats
            temp_csv_path = os.path.join(os.getcwd(), "fdj_keno_scraped.csv")

            with open(temp_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile, delimiter=';')

                # Écrire l'en-tête
                # Créer les en-têtes des colonnes
                boules = ["boule{}".format(i) for i in range(1, 21)]
                emplacements = ["emplacement{}".format(i) for i in range(1, 71)]

                writer.writerow(["annee_numero_de_tirage", "date_de_tirage", "heure_de_tirage", "date_de_forclusion"] +
                               boules + emplacements +
                               ["multiplicateur", "numero_jokerplus", "devise"])

                # Récupérer les résultats pour chaque jour
                total_draws = 0
                skipped_count = 0

                # Accéder à la page principale des résultats
                response = requests.get(base_url)
                if response.status_code != 200:
                    print("Erreur lors de l'accès au site de la FDJ: {}".format(response.status_code))
                    return False

                soup = BeautifulSoup(response.text, 'html.parser')

                # Trouver les liens vers les résultats des différents jours
                result_links = []

                # Chercher les liens dans la page principale
                for link in soup.find_all('a', href=True):
                    href = link['href']
                    if '/resultats/' in href and 'keno-gagnant-a-vie' in href:
                        result_links.append('https://www.fdj.fr' + href)

                # Limiter le nombre de jours à scraper
                result_links = result_links[:min(num_days, len(result_links))]

                print("Trouvé {} jours de résultats à extraire".format(len(result_links)))

                # Parcourir chaque lien de résultat
                for day_url in result_links:
                    try:
                        print("Extraction des résultats depuis: {}".format(day_url))

                        # Ajouter un délai pour éviter de surcharger le serveur
                        time.sleep(1)

                        # Accéder à la page des résultats du jour
                        day_response = requests.get(day_url)
                        if day_response.status_code != 200:
                            print("Erreur lors de l'accès aux résultats du jour: {}".format(day_response.status_code))
                            continue

                        day_soup = BeautifulSoup(day_response.text, 'html.parser')

                        # Extraire la date du tirage
                        date_match = re.search(r'Résultats du (\d{2}/\d{2}/\d{4})', day_soup.text)
                        if date_match:
                            draw_date = date_match.group(1)
                        else:
                            # Essayer d'extraire la date de l'URL
                            date_match = re.search(r'/(\d{2}-\d{2}-\d{4})/', day_url)
                            if date_match:
                                date_parts = date_match.group(1).split('-')
                                draw_date = f"{date_parts[0]}/{date_parts[1]}/{date_parts[2]}"
                            else:
                                # Utiliser la date du jour si aucune date n'est trouvée
                                draw_date = datetime.now().strftime("%d/%m/%Y")

                        # Trouver les sections de tirage (midi et soir)
                        draw_sections = day_soup.find_all('div', class_=lambda c: c and 'results-item' in c)

                        for section in draw_sections:
                            try:
                                # Déterminer s'il s'agit du tirage du midi ou du soir
                                time_text = section.find('h3')
                                if time_text:
                                    time_text = time_text.text.strip().lower()
                                    if 'midi' in time_text:
                                        draw_time = "midi"
                                    elif 'soir' in time_text:
                                        draw_time = "soir"
                                    else:
                                        draw_time = "midi"  # Valeur par défaut
                                else:
                                    draw_time = "midi"  # Valeur par défaut

                                # Extraire les numéros tirés
                                numbers = []
                                number_elements = section.find_all('span', class_=lambda c: c and 'number' in c)

                                for elem in number_elements:
                                    try:
                                        num = int(elem.text.strip())
                                        numbers.append(num)
                                    except ValueError:
                                        continue

                                # Vérifier que nous avons bien 20 numéros
                                if len(numbers) != 20:
                                    print(f"Nombre incorrect de numéros: {len(numbers)} (attendu: 20)")
                                    continue

                                # Extraire le multiplicateur
                                multiplier = "1"  # Valeur par défaut
                                multiplier_elem = section.find('span', class_=lambda c: c and 'multiplier' in c)
                                if multiplier_elem:
                                    multiplier_text = multiplier_elem.text.strip()
                                    multiplier_match = re.search(r'x(\d+)', multiplier_text)
                                    if multiplier_match:
                                        multiplier = multiplier_match.group(1)

                                # Générer un ID de tirage (format YYYYNNN)
                                # Convertir la date en objet datetime
                                draw_date_obj = datetime.strptime(draw_date, "%d/%m/%Y")
                                year = draw_date_obj.year

                                # Générer un numéro de tirage basé sur la date et l'heure
                                day_of_year = draw_date_obj.timetuple().tm_yday
                                draw_num = day_of_year * 2
                                if draw_time == "soir":
                                    draw_num -= 1

                                draw_id = "{}{:03d}".format(year, draw_num)

                                # Date de forclusion (date limite pour réclamer les gains, par défaut 60 jours après le tirage)
                                from datetime import timedelta
                                forclusion_date = (draw_date_obj + timedelta(days=60)).strftime("%d/%m/%Y")

                                # Préparer la ligne de données
                                row_data = [draw_id, draw_date, draw_time, forclusion_date] + numbers

                                # Ajouter les emplacements (vides)
                                row_data.extend(["" for _ in range(70)])

                                # Ajouter le multiplicateur et autres informations
                                row_data.extend([multiplier, "Jokerplus indisponible", "eur"])

                                # Écrire la ligne dans le fichier CSV
                                writer.writerow(row_data)

                                total_draws += 1

                                # Créer un objet KenoDrawData et l'ajouter à la liste des tirages
                                # Convertir la date et l'heure en objet datetime
                                if draw_time == "midi":
                                    time_obj = datetime.strptime("12:00:00", "%H:%M:%S").time()
                                else:  # soir
                                    time_obj = datetime.strptime("19:00:00", "%H:%M:%S").time()

                                draw_datetime = datetime.combine(draw_date_obj, time_obj)

                                # Vérifier si ce tirage existe déjà dans la base de données
                                is_duplicate = False
                                for existing_draw in self.draws:
                                    if (existing_draw.draw_id and existing_draw.draw_id == draw_id) or \
                                       (existing_draw.draw_date == draw_datetime and sorted(existing_draw.draw_numbers) == sorted(numbers)):
                                        is_duplicate = True
                                        skipped_count += 1
                                        break

                                if not is_duplicate:
                                    # Créer l'objet KenoDrawData avec la source "FDJ Web"
                                    draw = KenoDrawData(draw_datetime, numbers, draw_id, "FDJ Web")
                                    self.draws.append(draw)
                                    # Incrémenter le compteur de tirages importés
                                    # Note: total_draws est déjà incrémenté à la ligne 852

                            except Exception as e:
                                print("Erreur lors de l'extraction d'un tirage: {}".format(e))

                    except Exception as e:
                        print("Erreur lors de l'extraction des résultats du jour: {}".format(e))

                # Trier les tirages par date
                self.draws.sort(key=lambda x: x.draw_date)

                print("Extraction terminée. {} tirages extraits et ajoutés à la base de données. {} doublons ignorés.".format(total_draws, skipped_count))

                return total_draws > 0

        except Exception as e:
            print("Erreur lors du scraping des résultats Keno: {}".format(e))
            return False

    def get_number_frequency(self):
        # Documentation string removed
        frequency = {i: 0 for i in range(1, self.max_number + 1)}
        total_draws = len(self.draws)

        if total_draws == 0:
            return frequency

        for draw in self.draws:
            for number in draw.draw_numbers:
                if 1 <= number <= self.max_number:
                    frequency[number] += 1

        # Convert to percentage
        for num in frequency:
            frequency[num] = (frequency[num] / total_draws) * 100

        return frequency

    def get_hot_numbers(self, count=10):
        # Documentation string removed
        frequency = self.get_number_frequency()
        sorted_numbers = sorted(frequency.items(), key=lambda x: x[1], reverse=True)
        return sorted_numbers[:count]

    def get_cold_numbers(self, count=10):
        # Documentation string removed
        frequency = self.get_number_frequency()
        sorted_numbers = sorted(frequency.items(), key=lambda x: x[1])
        return sorted_numbers[:count]

    def get_due_numbers(self, count=10):
        # Documentation string removed
        last_seen = {i: len(self.draws) for i in range(1, self.max_number + 1)}

        for i, draw in enumerate(self.draws):
            for number in draw.draw_numbers:
                if 1 <= number <= self.max_number:
                    last_seen[number] = i

        # Calculate draws since last appearance
        draws_since = {num: len(self.draws) - last_pos for num, last_pos in last_seen.items()}
        sorted_numbers = sorted(draws_since.items(), key=lambda x: x[1], reverse=True)

        return sorted_numbers[:count]

    def get_number_recency(self):
        # Documentation string removede# Documentation string removed
        return self._modified

    def add_draw(self, draw_date, draw_numbers, draw_id=None, source_file=None):
        # Documentation string removed
        # Vérifier que la date est valide
        if not isinstance(draw_date, datetime):
            print("Erreur: La date doit être un objet datetime, pas {}".format(type(draw_date)))
            return False

        # Vérifier que les numéros sont valides
        if not isinstance(draw_numbers, list) or len(draw_numbers) != self.numbers_per_draw:
            print("Erreur: Les numéros doivent être une liste de {} numéros".format(self.numbers_per_draw))
            return False

        # Vérifier que les numéros sont dans la plage valide
        for num in draw_numbers:
            if not isinstance(num, int) or num < 1 or num > self.max_number:
                print("Erreur: Les numéros doivent être des entiers entre 1 et {}".format(self.max_number))
                return False

        # Ajouter le tirage
        self.draws.append(KenoDrawData(draw_date, draw_numbers, draw_id, source_file))

        # Marquer comme modifié
        self._modified = True

        return True

    def clear_data(self):
        # Documentation string removed
        self.draws = []
        self._modified = True
