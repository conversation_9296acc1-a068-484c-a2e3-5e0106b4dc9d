"""
Module d'apprentissage par renforcement pour l'application Keno
Ce module implémente un agent d'apprentissage par renforcement pour améliorer
les prédictions de numéros Keno en apprenant des résultats réels.
"""

import os
import numpy as np
import random
import json
from collections import deque
import pickle
import datetime

# Vérifier si TensorFlow est disponible
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, load_model
    from tensorflow.keras.layers import Dense, Dropout
    from tensorflow.keras.optimizers import Adam
    tensorflow_available = True
    
    # Supprimer les avertissements TensorFlow
    tf.compat.v1.logging.set_verbosity(tf.compat.v1.logging.ERROR)
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
except ImportError:
    tensorflow_available = False
    print("TensorFlow n'est pas disponible. L'agent RL utilisera une approche simplifiée.")

class KenoRLAgent:
    """Agent d'apprentissage par renforcement pour les prédictions Keno"""
    
    def __init__(self, state_size=100, action_size=70, max_number=70, use_neural_network=True):
        """Initialise l'agent RL
        
        Args:
            state_size (int): Taille du vecteur d'état (caractéristiques)
            action_size (int): Nombre d'actions possibles (numéros Keno)
            max_number (int): Numéro maximum dans le jeu Keno
            use_neural_network (bool): Utiliser un réseau neuronal ou une table Q
        """
        self.state_size = state_size
        self.action_size = action_size
        self.max_number = max_number
        self.use_neural_network = use_neural_network and tensorflow_available
        
        # Paramètres d'apprentissage
        self.memory = deque(maxlen=2000)  # Mémoire d'expérience
        self.gamma = 0.95                 # Facteur d'actualisation
        self.epsilon = 1.0                # Taux d'exploration initial
        self.epsilon_min = 0.01           # Taux d'exploration minimum
        self.epsilon_decay = 0.995        # Décroissance du taux d'exploration
        self.learning_rate = 0.001        # Taux d'apprentissage
        
        # Statistiques
        self.training_count = 0
        self.last_training_date = None
        self.rewards_history = []
        self.prediction_accuracy = []
        
        # Table Q pour l'approche sans réseau neuronal
        self.q_table = {}
        
        # Modèle de réseau neuronal
        self.model = self._build_model() if self.use_neural_network else None
        
        # Fichier de sauvegarde
        self.save_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'keno_rl_agent.pkl')
        
        # Charger le modèle s'il existe
        self.load()
    
    def _build_model(self):
        """Construit le modèle de réseau neuronal pour l'apprentissage par renforcement
        
        Returns:
            model: Modèle Keras
        """
        if not tensorflow_available:
            print("TensorFlow n'est pas disponible. Impossible de construire le modèle.")
            return None
        
        try:
            model = Sequential()
            model.add(Dense(128, input_dim=self.state_size, activation='relu'))
            model.add(Dropout(0.2))
            model.add(Dense(128, activation='relu'))
            model.add(Dropout(0.2))
            model.add(Dense(self.action_size, activation='linear'))
            model.compile(loss='mse', optimizer=Adam(learning_rate=self.learning_rate))
            return model
        except Exception as e:
            print(f"Erreur lors de la construction du modèle: {e}")
            return None
    
    def _get_state_key(self, state):
        """Convertit un état en clé pour la table Q
        
        Args:
            state: État à convertir
        
        Returns:
            str: Clé pour la table Q
        """
        # Simplifier l'état pour la table Q (utiliser seulement les 10 premiers éléments)
        simplified_state = tuple(int(x * 100) for x in state[:10])
        return str(simplified_state)
    
    def remember(self, state, action, reward, next_state, done):
        """Stocke une expérience dans la mémoire
        
        Args:
            state: État actuel
            action: Action effectuée
            reward: Récompense reçue
            next_state: État suivant
            done: Indique si l'épisode est terminé
        """
        self.memory.append((state, action, reward, next_state, done))
        
        # Limiter la taille de la mémoire
        if len(self.memory) > 2000:
            self.memory.popleft()
    
    def act(self, state, num_to_select=7, exploration=True):
        """Sélectionne une action (ensemble de numéros) en fonction de l'état
        
        Args:
            state: État actuel
            num_to_select (int): Nombre de numéros à sélectionner
            exploration (bool): Autoriser l'exploration
        
        Returns:
            list: Liste des numéros sélectionnés
        """
        # Vérifier que l'état a la bonne dimension
        if len(state) != self.state_size:
            # Ajuster la taille de l'état si nécessaire
            if len(state) < self.state_size:
                state = np.pad(state, (0, self.state_size - len(state)), 'constant')
            else:
                state = state[:self.state_size]
        
        # Convertir l'état en tableau numpy
        state_array = np.array(state).reshape(1, self.state_size)
        
        # Exploration: sélection aléatoire avec probabilité epsilon
        if exploration and np.random.rand() <= self.epsilon:
            return np.random.choice(range(1, self.max_number + 1), num_to_select, replace=False).tolist()
        
        # Exploitation: utiliser le modèle ou la table Q
        if self.use_neural_network and self.model is not None:
            try:
                # Prédire les valeurs Q pour tous les numéros
                act_values = self.model.predict(state_array, verbose=0)
                
                # Sélectionner les numéros avec les valeurs Q les plus élevées
                # Ajouter 1 car les numéros Keno commencent à 1, pas à 0
                return (np.argsort(act_values[0])[-num_to_select:] + 1).tolist()
            except Exception as e:
                print(f"Erreur lors de la prédiction: {e}")
                # Fallback: sélection aléatoire
                return np.random.choice(range(1, self.max_number + 1), num_to_select, replace=False).tolist()
        else:
            # Utiliser la table Q
            state_key = self._get_state_key(state)
            if state_key in self.q_table:
                q_values = self.q_table[state_key]
                # Convertir en tableau numpy pour faciliter le tri
                q_array = np.zeros(self.max_number)
                for i, val in q_values.items():
                    if 1 <= int(i) <= self.max_number:
                        q_array[int(i)-1] = val
                
                # Sélectionner les numéros avec les valeurs Q les plus élevées
                return (np.argsort(q_array)[-num_to_select:] + 1).tolist()
            else:
                # Si l'état n'est pas dans la table Q, sélection aléatoire
                return np.random.choice(range(1, self.max_number + 1), num_to_select, replace=False).tolist()
    
    def train(self, batch_size=32):
        """Entraîne l'agent sur un batch d'expériences
        
        Args:
            batch_size (int): Taille du batch d'entraînement
        
        Returns:
            float: Perte moyenne
        """
        if len(self.memory) < batch_size:
            return 0  # Pas assez d'expériences pour l'entraînement
        
        # Mettre à jour les statistiques
        self.training_count += 1
        self.last_training_date = datetime.datetime.now().isoformat()
        
        # Sélectionner un batch aléatoire
        minibatch = random.sample(self.memory, batch_size)
        
        if self.use_neural_network and self.model is not None:
            # Entraînement avec réseau neuronal
            try:
                losses = []
                for state, action, reward, next_state, done in minibatch:
                    # Vérifier et ajuster la taille des états
                    if len(state) != self.state_size:
                        state = np.pad(state, (0, self.state_size - len(state)), 'constant') if len(state) < self.state_size else state[:self.state_size]
                    if len(next_state) != self.state_size:
                        next_state = np.pad(next_state, (0, self.state_size - len(next_state)), 'constant') if len(next_state) < self.state_size else next_state[:self.state_size]
                    
                    # Convertir en tableaux numpy
                    state = np.array(state).reshape(1, self.state_size)
                    next_state = np.array(next_state).reshape(1, self.state_size)
                    
                    # Calculer la cible
                    target = reward
                    if not done:
                        target = reward + self.gamma * np.amax(self.model.predict(next_state, verbose=0)[0])
                    
                    # Obtenir les prédictions actuelles
                    target_f = self.model.predict(state, verbose=0)
                    
                    # Mettre à jour la valeur Q pour l'action effectuée
                    # Soustraire 1 car les numéros Keno commencent à 1, pas à 0
                    target_f[0][action-1] = target
                    
                    # Entraîner le modèle
                    history = self.model.fit(state, target_f, epochs=1, verbose=0)
                    losses.append(history.history['loss'][0])
                
                # Réduire l'exploration
                if self.epsilon > self.epsilon_min:
                    self.epsilon *= self.epsilon_decay
                
                # Enregistrer la récompense moyenne
                if minibatch:
                    avg_reward = sum(x[2] for x in minibatch) / len(minibatch)
                    self.rewards_history.append(avg_reward)
                
                return np.mean(losses) if losses else 0
            
            except Exception as e:
                print(f"Erreur lors de l'entraînement du modèle: {e}")
                return 0
        else:
            # Entraînement avec table Q
            for state, action, reward, next_state, done in minibatch:
                state_key = self._get_state_key(state)
                next_state_key = self._get_state_key(next_state)
                
                # Initialiser l'état dans la table Q s'il n'existe pas
                if state_key not in self.q_table:
                    self.q_table[state_key] = {str(i): 0 for i in range(1, self.max_number + 1)}
                
                # Calculer la cible
                if done:
                    target = reward
                else:
                    # Initialiser l'état suivant s'il n'existe pas
                    if next_state_key not in self.q_table:
                        self.q_table[next_state_key] = {str(i): 0 for i in range(1, self.max_number + 1)}
                    
                    # Trouver la valeur Q maximale pour l'état suivant
                    max_next_q = max(self.q_table[next_state_key].values())
                    target = reward + self.gamma * max_next_q
                
                # Mettre à jour la valeur Q
                action_key = str(action)
                if action_key not in self.q_table[state_key]:
                    self.q_table[state_key][action_key] = 0
                
                # Formule de mise à jour Q-learning
                self.q_table[state_key][action_key] += self.learning_rate * (target - self.q_table[state_key][action_key])
            
            # Réduire l'exploration
            if self.epsilon > self.epsilon_min:
                self.epsilon *= self.epsilon_decay
            
            # Enregistrer la récompense moyenne
            if minibatch:
                avg_reward = sum(x[2] for x in minibatch) / len(minibatch)
                self.rewards_history.append(avg_reward)
            
            return 0
    
    def calculate_reward(self, predicted_numbers, actual_numbers):
        """Calcule la récompense en fonction des numéros prédits et réels
        
        Args:
            predicted_numbers (list): Numéros prédits
            actual_numbers (list): Numéros réels
        
        Returns:
            float: Récompense
        """
        # Convertir en ensembles pour faciliter l'intersection
        pred_set = set(predicted_numbers)
        actual_set = set(actual_numbers)
        
        # Nombre de correspondances
        matches = len(pred_set.intersection(actual_set))
        
        # Calculer le pourcentage de précision
        accuracy = matches / len(predicted_numbers) if predicted_numbers else 0
        self.prediction_accuracy.append(accuracy)
        
        # Récompense non-linéaire (valorise davantage les scores élevés)
        if matches == 0:
            return -1  # Pénalité pour aucune correspondance
        elif matches <= 2:
            return matches
        else:
            # Récompense exponentielle pour 3+ correspondances
            return 2**(matches-2)  # Ex: 3 matches = 2, 4 matches = 4, 5 matches = 8, etc.
    
    def save(self):
        """Sauvegarde l'agent dans un fichier"""
        try:
            # Créer le répertoire si nécessaire
            os.makedirs(os.path.dirname(self.save_file), exist_ok=True)
            
            # Préparer les données à sauvegarder
            save_data = {
                'state_size': self.state_size,
                'action_size': self.action_size,
                'max_number': self.max_number,
                'use_neural_network': self.use_neural_network,
                'epsilon': self.epsilon,
                'memory': list(self.memory),
                'training_count': self.training_count,
                'last_training_date': self.last_training_date,
                'rewards_history': self.rewards_history,
                'prediction_accuracy': self.prediction_accuracy,
                'q_table': self.q_table
            }
            
            # Sauvegarder les données
            with open(self.save_file, 'wb') as f:
                pickle.dump(save_data, f)
            
            # Sauvegarder le modèle séparément si disponible
            if self.use_neural_network and self.model is not None:
                model_file = self.save_file.replace('.pkl', '_model.h5')
                self.model.save(model_file)
            
            print(f"Agent RL sauvegardé dans {self.save_file}")
            return True
        except Exception as e:
            print(f"Erreur lors de la sauvegarde de l'agent RL: {e}")
            return False
    
    def load(self):
        """Charge l'agent depuis un fichier
        
        Returns:
            bool: True si le chargement a réussi, False sinon
        """
        try:
            # Vérifier si le fichier existe
            if not os.path.exists(self.save_file):
                print(f"Aucun fichier d'agent RL trouvé à {self.save_file}")
                return False
            
            # Charger les données
            with open(self.save_file, 'rb') as f:
                save_data = pickle.load(f)
            
            # Restaurer les attributs
            self.state_size = save_data.get('state_size', self.state_size)
            self.action_size = save_data.get('action_size', self.action_size)
            self.max_number = save_data.get('max_number', self.max_number)
            self.use_neural_network = save_data.get('use_neural_network', self.use_neural_network)
            self.epsilon = save_data.get('epsilon', self.epsilon)
            self.memory = deque(save_data.get('memory', []), maxlen=2000)
            self.training_count = save_data.get('training_count', 0)
            self.last_training_date = save_data.get('last_training_date', None)
            self.rewards_history = save_data.get('rewards_history', [])
            self.prediction_accuracy = save_data.get('prediction_accuracy', [])
            self.q_table = save_data.get('q_table', {})
            
            # Charger le modèle séparément si disponible
            if self.use_neural_network and tensorflow_available:
                model_file = self.save_file.replace('.pkl', '_model.h5')
                if os.path.exists(model_file):
                    try:
                        self.model = load_model(model_file)
                        print(f"Modèle RL chargé depuis {model_file}")
                    except Exception as e:
                        print(f"Erreur lors du chargement du modèle RL: {e}")
                        self.model = self._build_model()
                else:
                    print(f"Aucun fichier de modèle RL trouvé à {model_file}")
                    self.model = self._build_model()
            
            print(f"Agent RL chargé depuis {self.save_file}")
            return True
        except Exception as e:
            print(f"Erreur lors du chargement de l'agent RL: {e}")
            return False
    
    def get_state_from_history(self, draw_history, frequency_data=None):
        """Génère un vecteur d'état à partir de l'historique des tirages
        
        Args:
            draw_history (list): Historique des tirages
            frequency_data (dict, optional): Données de fréquence des numéros
        
        Returns:
            list: Vecteur d'état
        """
        state = []
        
        # Limiter l'historique aux 10 derniers tirages
        recent_draws = draw_history[-10:] if len(draw_history) > 10 else draw_history
        
        # Aplatir les numéros des tirages récents (1 si présent, 0 sinon)
        for draw in recent_draws:
            # Créer un vecteur one-hot pour chaque tirage
            draw_vector = [1 if i+1 in draw else 0 for i in range(self.max_number)]
            state.extend(draw_vector)
        
        # Ajouter des zéros si l'historique est trop court
        if len(recent_draws) < 10:
            padding = (10 - len(recent_draws)) * self.max_number
            state.extend([0] * padding)
        
        # Ajouter les fréquences si disponibles
        if frequency_data:
            # Normaliser les fréquences
            max_freq = max(frequency_data.values()) if frequency_data else 1
            freq_vector = [frequency_data.get(i+1, 0) / max_freq for i in range(self.max_number)]
            state.extend(freq_vector)
        else:
            # Ajouter des zéros si les fréquences ne sont pas disponibles
            state.extend([0] * self.max_number)
        
        # Limiter la taille de l'état
        if len(state) > self.state_size:
            state = state[:self.state_size]
        elif len(state) < self.state_size:
            # Ajouter des zéros si l'état est trop court
            state.extend([0] * (self.state_size - len(state)))
        
        return state
    
    def get_performance_stats(self):
        """Récupère les statistiques de performance de l'agent
        
        Returns:
            dict: Statistiques de performance
        """
        stats = {
            'training_count': self.training_count,
            'last_training_date': self.last_training_date,
            'memory_size': len(self.memory),
            'epsilon': self.epsilon,
            'q_table_size': len(self.q_table),
            'average_reward': sum(self.rewards_history[-100:]) / max(1, len(self.rewards_history[-100:])) if self.rewards_history else 0,
            'average_accuracy': sum(self.prediction_accuracy[-100:]) / max(1, len(self.prediction_accuracy[-100:])) if self.prediction_accuracy else 0,
            'use_neural_network': self.use_neural_network
        }
        return stats
