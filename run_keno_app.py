#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script principal pour lancer l'application Keno avec toutes les améliorations
Ce script applique toutes les améliorations de prédiction et lance l'application.
"""

import os
import sys
import time
import json
import argparse
import subprocess
import threading

# Ajouter le répertoire courant au chemin de recherche Python
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, BASE_DIR)

# Supprimer les avertissements
import warnings
warnings.filterwarnings('ignore')

# Supprimer les avertissements de TensorFlow
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR

def check_dependencies():
    """Vérifie que toutes les dépendances sont installées"""
    print("Vérification des dépendances...")

    dependencies = {
        'numpy': False,
        'pandas': False,
        'matplotlib': False,
        'scikit-learn': False,
        'tensorflow': False,
        'xgboost': False,
        'lightgbm': False,
        'joblib': False,
        'tqdm': False,
        'requests': False
    }

    # Vérifier chaque dépendance
    try:
        import numpy
        dependencies['numpy'] = True
    except ImportError:
        pass

    try:
        import pandas
        dependencies['pandas'] = True
    except ImportError:
        pass

    try:
        import matplotlib
        dependencies['matplotlib'] = True
    except ImportError:
        pass

    try:
        import sklearn
        dependencies['scikit-learn'] = True
    except ImportError:
        pass

    try:
        import tensorflow
        dependencies['tensorflow'] = True
    except ImportError:
        pass

    try:
        import xgboost
        dependencies['xgboost'] = True
    except ImportError:
        pass

    try:
        import lightgbm
        dependencies['lightgbm'] = True
    except ImportError:
        pass

    try:
        import joblib
        dependencies['joblib'] = True
    except ImportError:
        pass

    try:
        import tqdm
        dependencies['tqdm'] = True
    except ImportError:
        pass

    try:
        import requests
        dependencies['requests'] = True
    except ImportError:
        pass

    # Afficher les résultats
    print("\nStatut des dépendances:")
    for dep, installed in dependencies.items():
        status = "✓ Installé" if installed else "✗ Manquant"
        print(f"  {dep}: {status}")

    # Vérifier si toutes les dépendances sont installées
    all_installed = all(dependencies.values())
    if not all_installed:
        print("\nCertaines dépendances sont manquantes. Installation automatique...")
        install_dependencies([dep for dep, installed in dependencies.items() if not installed])

    return all_installed

def install_dependencies(missing_deps):
    """Installe les dépendances manquantes"""
    print(f"Installation des dépendances manquantes: {', '.join(missing_deps)}")

    # Installer chaque dépendance
    for dep in missing_deps:
        print(f"Installation de {dep}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"  {dep} installé avec succès")
        except subprocess.CalledProcessError:
            print(f"  Erreur lors de l'installation de {dep}")

def download_data():
    """Télécharge les données Keno depuis l'API FDJ"""
    print("Téléchargement des données Keno...")

    try:
        from download_keno_data import update_keno_data
        update_keno_data()
    except ImportError:
        print("Module download_keno_data non disponible")
    except Exception as e:
        print(f"Erreur lors du téléchargement des données: {e}")

def optimize_weights():
    """Optimise les poids des numéros Keno"""
    print("Optimisation des poids des numéros Keno...")

    try:
        from optimize_number_weights import calculate_combined_weights, optimize_scale_pos_weight_with_weights
        calculate_combined_weights()
        optimize_scale_pos_weight_with_weights()
    except ImportError:
        print("Module optimize_number_weights non disponible")
    except Exception as e:
        print(f"Erreur lors de l'optimisation des poids: {e}")

def analyze_patterns():
    """Analyse les patterns sur 48 heures"""
    print("Analyse des patterns sur 48 heures...")

    try:
        from analyze_48h_patterns import analyze_48h_patterns
        analyze_48h_patterns()
    except ImportError:
        print("Module analyze_48h_patterns non disponible")
    except Exception as e:
        print(f"Erreur lors de l'analyse des patterns: {e}")

def run_auto_improve(timeout=7200, fast_mode=False, ultra_fast=False):
    """Exécute l'auto-amélioration"""
    print("Exécution de l'auto-amélioration...")

    try:
        from improve_predictions import run_auto_improve
        run_auto_improve(timeout=timeout, fast_mode=fast_mode, ultra_fast=ultra_fast)
    except ImportError:
        print("Module improve_predictions non disponible")
    except Exception as e:
        print(f"Erreur lors de l'auto-amélioration: {e}")

def launch_app():
    """Lance l'application Keno"""
    print("Lancement de l'application Keno...")

    try:
        # Importer le module principal
        from main import main

        # Lancer l'application
        main()
    except ImportError:
        print("Module main non disponible")
    except Exception as e:
        print(f"Erreur lors du lancement de l'application: {e}")

def parse_arguments():
    """Parse les arguments de la ligne de commande"""
    parser = argparse.ArgumentParser(description="Lance l'application Keno avec des améliorations de prédiction")

    parser.add_argument('--download', action='store_true', help="Télécharger les données Keno")
    parser.add_argument('--fix-format', action='store_true', help="Corriger le format des données")
    parser.add_argument('--optimize', action='store_true', help="Optimiser les poids des numéros")
    parser.add_argument('--patterns', action='store_true', help="Analyser les patterns sur 48 heures")
    parser.add_argument('--auto-improve', action='store_true', help="Exécuter l'auto-amélioration")
    parser.add_argument('--advanced-prediction', action='store_true', help="Utiliser la prédiction avancée")
    parser.add_argument('--predict', type=int, default=0, help="Prédire N numéros pour le prochain tirage")
    parser.add_argument('--all', action='store_true', help="Appliquer toutes les améliorations")
    parser.add_argument('--timeout', type=int, default=7200, help="Timeout pour l'auto-amélioration (en secondes)")
    parser.add_argument('--fast', action='store_true', help="Utiliser le mode rapide pour l'auto-amélioration")
    parser.add_argument('--ultra-fast', action='store_true', help="Utiliser le mode ultra-rapide pour l'auto-amélioration")

    args = parser.parse_args()

    # Si --all est spécifié, activer toutes les améliorations
    if args.all:
        args.download = True
        args.fix_format = True
        args.optimize = True
        args.patterns = True
        args.auto_improve = True
        args.advanced_prediction = True
        args.predict = 10  # Prédire 10 numéros par défaut

    return args

def fix_data_format():
    """Corrige le format des données Keno"""
    print("Correction du format des données Keno...")

    try:
        from fix_data_format import fix_data_format, convert_to_csv

        # Corriger le format du fichier de données
        data_file = os.path.join(BASE_DIR, "data", "datafull.keno")
        if os.path.exists(data_file):
            # Corriger le format
            fixed_file = data_file + '.fixed'
            if fix_data_format(data_file, fixed_file):
                try:
                    # Renommer le fichier corrigé
                    if os.path.exists(fixed_file):
                        # Supprimer le fichier original s'il existe
                        if os.path.exists(data_file):
                            os.remove(data_file)
                        # Renommer le fichier corrigé
                        os.rename(fixed_file, data_file)
                        print(f"Format du fichier {data_file} corrigé avec succès")
                except Exception as e:
                    print(f"Erreur lors du renommage du fichier: {e}")
                    # Utiliser le fichier corrigé tel quel
                    data_file = fixed_file

            # Convertir au format CSV
            csv_file = os.path.join(BASE_DIR, "data", "datafull.csv")
            if convert_to_csv(data_file, csv_file):
                print(f"Fichier converti en CSV: {csv_file}")
        else:
            print(f"Fichier de données {data_file} introuvable")

            # Créer un fichier CSV directement
            csv_file = os.path.join(BASE_DIR, "data", "datafull.csv")
            if convert_to_csv(data_file, csv_file):
                print(f"Fichier CSV créé: {csv_file}")

    except ImportError:
        print("Module fix_data_format non disponible")
    except Exception as e:
        print(f"Erreur lors de la correction du format des données: {e}")

def run_advanced_prediction(num_predictions=10):
    """
    Exécute la prédiction avancée

    Args:
        num_predictions (int): Nombre de numéros à prédire
    """
    print(f"Exécution de la prédiction avancée pour {num_predictions} numéros...")

    try:
        from advanced_prediction import AdvancedPrediction

        # Créer l'objet AdvancedPrediction
        predictor = AdvancedPrediction()

        # Entraîner les modèles d'ensemble pour tous les numéros
        for num in range(1, predictor.data_manager.max_number + 1):
            predictor.train_ensemble_model(num)

        # Prédire les numéros du prochain tirage
        predicted_numbers = predictor.predict_next_draw(num_predictions=num_predictions)

        print("\n" + "="*50)
        print(f"PRÉDICTION POUR LE PROCHAIN TIRAGE: {predicted_numbers}")
        print("="*50 + "\n")

        return predicted_numbers

    except ImportError:
        print("Module advanced_prediction non disponible")
    except Exception as e:
        print(f"Erreur lors de la prédiction avancée: {e}")
        return None

def main():
    """Fonction principale"""
    # Parser les arguments
    args = parse_arguments()

    # Vérifier les dépendances
    check_dependencies()

    # Créer le répertoire de données si nécessaire
    os.makedirs(os.path.join(BASE_DIR, "data"), exist_ok=True)

    # Télécharger les données
    if args.download:
        download_data()

    # Corriger le format des données
    if args.fix_format or args.all:
        fix_data_format()

    # Optimiser les poids
    if args.optimize:
        optimize_weights()

    # Analyser les patterns
    if args.patterns:
        analyze_patterns()

    # Exécuter l'auto-amélioration
    if args.auto_improve:
        run_auto_improve(timeout=args.timeout, fast_mode=args.fast, ultra_fast=args.ultra_fast)

    # Exécuter la prédiction avancée
    if args.advanced_prediction or args.predict > 0:
        num_predictions = args.predict if args.predict > 0 else 10
        run_advanced_prediction(num_predictions=num_predictions)

    # Lancer l'application
    launch_app()

if __name__ == "__main__":
    main()
