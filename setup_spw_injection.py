"""
Script d'installation pour configurer l'injection de scale_pos_weight.
"""

import os
import sys
import json
import random
import shutil
from datetime import datetime

def generate_random_weights():
    """
    Génère des valeurs aléatoires de scale_pos_weight pour chaque numéro
    
    Returns:
        dict: Dictionnaire des valeurs par numéro
    """
    # Générer des valeurs aléatoires autour de 2.57
    values = {}
    for num in range(1, 71):
        # Générer une valeur entre 1.5 et 3.5
        value = 1.5 + random.random() * 2.0
        values[num] = round(value, 4)
    
    print("Valeurs de scale_pos_weight générées:")
    for num in range(1, 11):  # Afficher les 10 premières valeurs
        print(f"  Numéro {num}: {values[num]}")
    
    return values

def save_weights_to_json(weights):
    """
    Sauvegarde les poids dans un fichier JSON
    
    Args:
        weights: Dictionnaire des poids par numéro
        
    Returns:
        str: Chemin du fichier créé
    """
    # <PERSON><PERSON><PERSON> le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Chemin du fichier
    file_path = os.path.join('data', 'simple_spw_values.json')
    
    # Convertir les clés en chaînes pour la sérialisation JSON
    weights_str = {str(k): v for k, v in weights.items()}
    
    # Sauvegarder les poids
    with open(file_path, 'w') as f:
        json.dump(weights_str, f, indent=2)
    
    print(f"Poids sauvegardés dans {file_path}")
    
    return file_path

def create_readme():
    """
    Crée un fichier README avec des instructions
    
    Returns:
        str: Chemin du fichier créé
    """
    readme_content = """# Injection de scale_pos_weight pour Keno

Ce package contient des scripts pour injecter des valeurs différentes de `scale_pos_weight` pour chaque numéro Keno.

## Fichiers inclus

- `simple_spw_injector.py` : Module d'injection qui force les valeurs
- `launch_with_spw.py` : Script de lancement qui injecte le module
- `data/simple_spw_values.json` : Valeurs de `scale_pos_weight` pour chaque numéro

## Comment utiliser cette solution

### Option 1 : Importer le module d'injection dans votre code

Ajoutez cette ligne au début de votre script principal :

```python
import simple_spw_injector
```

Ensuite, continuez avec votre code normal. Les valeurs de `scale_pos_weight` seront automatiquement injectées.

### Option 2 : Utiliser le script de lancement

Exécutez le script :

```bash
python launch_with_spw.py
```

### Option 3 : Utiliser XGBClassifier avec un numéro spécifique

```python
from xgboost import XGBClassifier

# Créer un modèle avec un numéro spécifique
model = XGBClassifier(
    n_estimators=100,
    max_depth=5,
    learning_rate=0.1,
    num=7  # Spécifier le numéro Keno ici
)

# La valeur de scale_pos_weight sera automatiquement injectée
print(f"scale_pos_weight: {model.scale_pos_weight}")
```

## Comment vérifier que la solution fonctionne

Après avoir intégré la solution, vous devriez voir des messages comme :

```
XGBClassifier: Numéro 7 - scale_pos_weight forcé à 2.1234
```

Cela indique que la valeur de `scale_pos_weight` a été forcée pour ce numéro.

## Comment modifier les valeurs

Si vous souhaitez modifier les valeurs de `scale_pos_weight`, éditez le fichier `data/simple_spw_values.json`.
"""
    
    # Sauvegarder le README
    readme_file = 'README_SPW_INJECTION.md'
    
    with open(readme_file, 'w') as f:
        f.write(readme_content)
    
    print(f"README sauvegardé dans {readme_file}")
    
    return readme_file

def main():
    """Fonction principale"""
    print("Installation de l'injection de scale_pos_weight")
    
    # Vérifier si les fichiers existent déjà
    if os.path.exists('simple_spw_injector.py') and os.path.exists('launch_with_spw.py'):
        print("Les fichiers d'injection existent déjà")
    else:
        print("Erreur: Les fichiers d'injection n'existent pas")
        print("Veuillez exécuter ce script dans le même répertoire que les fichiers d'injection")
        return
    
    # Générer des valeurs aléatoires
    weights = generate_random_weights()
    
    # Sauvegarder les poids dans un fichier JSON
    weights_file = save_weights_to_json(weights)
    
    # Créer le README
    readme_file = create_readme()
    
    print("\nInstallation terminée!")
    print("L'injection de scale_pos_weight est maintenant configurée.")
    print("Pour l'utiliser, suivez les instructions dans le fichier README_SPW_INJECTION.md")
    
    # Demander à l'utilisateur s'il souhaite lancer l'application
    print("\nSouhaitez-vous lancer l'application maintenant? (o/n)")
    choice = input().lower()
    
    if choice == 'o':
        print("Lancement de l'application...")
        os.system('python launch_with_spw.py')

if __name__ == "__main__":
    main()
