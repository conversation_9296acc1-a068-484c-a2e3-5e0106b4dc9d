"""
Script pour corriger le problème de scale_pos_weight identique pour tous les numéros Keno
Ce script modifie la façon dont les données sont préparées et dont scale_pos_weight est calculé.
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime

# Essayer d'importer le gestionnaire de données Keno
try:
    from keno_data import KenoDataManager
    print("Module de gestion des données Keno importé avec succès")
except ImportError:
    print("Erreur: Module de gestion des données Keno non disponible")
    sys.exit(1)

def prepare_data_for_number(data_manager, num):
    """
    Prépare les données spécifiques à un numéro
    
    Args:
        data_manager: Gestionnaire de données Keno
        num: Numéro à analyser
        
    Returns:
        tuple: (X, y) caractéristiques et étiquettes
    """
    # Convertir les tirages en DataFrame
    data = []
    for draw in data_manager.draws:
        # Vérifier si le tirage est valide
        if not hasattr(draw, 'draw_numbers') or not draw.draw_numbers:
            continue
        if not hasattr(draw, 'draw_date') or not draw.draw_date:
            continue
            
        # Créer une ligne pour ce tirage
        row = {
            'date': draw.draw_date,
            'day_of_week': draw.draw_date.weekday(),
            'hour': draw.draw_date.hour,
            'is_weekend': 1 if draw.draw_date.weekday() >= 5 else 0,
            'is_afternoon': 1 if draw.draw_date.hour >= 12 else 0,
            'day': draw.draw_date.day,
            'month': draw.draw_date.month,
            'year': draw.draw_date.year
        }
        
        # Ajouter un indicateur pour ce numéro spécifique
        row[f'has_{num}'] = 1 if num in draw.draw_numbers else 0
        
        data.append(row)
    
    # Créer le DataFrame
    df = pd.DataFrame(data)
    if df.empty:
        print(f"Aucune donnée valide pour le numéro {num}")
        return None, None
    
    # Trier par date
    df = df.sort_values('date')
    
    # Ajouter des caractéristiques de lag pour ce numéro spécifique
    for i in range(1, 6):
        df[f'has_{num}_lag_{i}'] = df[f'has_{num}'].shift(i).fillna(0)
    
    # Supprimer les lignes avec des valeurs manquantes
    df = df.dropna()
    
    if len(df) < 100:
        print(f"Pas assez de données pour le numéro {num} après nettoyage. Minimum 100 tirages requis.")
        return None, None
    
    # Définir les caractéristiques et la cible
    feature_cols = [f'has_{num}_lag_{i}' for i in range(1, 6)]
    feature_cols += ['day_of_week', 'hour', 'is_weekend', 'is_afternoon', 'day', 'month', 'year']
    
    X = df[feature_cols]
    y = df[f'has_{num}']
    
    return X, y

def calculate_scale_pos_weight(y):
    """
    Calcule scale_pos_weight basé sur la distribution des classes
    
    Args:
        y: Étiquettes (0 ou 1)
        
    Returns:
        float: Valeur de scale_pos_weight
    """
    # Compter les exemples positifs et négatifs
    neg_count = np.sum(y == 0)
    pos_count = np.sum(y == 1)
    
    # Calculer le ratio
    if pos_count > 0:
        return neg_count / pos_count
    else:
        return 1.0  # Valeur par défaut

def analyze_all_numbers():
    """
    Analyse tous les numéros et calcule leur scale_pos_weight spécifique
    """
    # Créer le gestionnaire de données
    data_manager = KenoDataManager()
    
    # Charger les données
    print("Chargement des données Keno...")
    data_loaded = data_manager.load_data()
    
    if not data_loaded or not data_manager.draws:
        print("Erreur: Impossible de charger les données Keno")
        return
    
    print(f"Données chargées: {len(data_manager.draws)} tirages")
    
    # Analyser chaque numéro
    results = []
    for num in range(1, data_manager.max_number + 1):
        print(f"Analyse du numéro {num}...")
        
        # Préparer les données pour ce numéro spécifique
        X, y = prepare_data_for_number(data_manager, num)
        
        if X is None or y is None:
            print(f"Pas assez de données pour le numéro {num}")
            continue
        
        # Calculer scale_pos_weight pour ce numéro
        spw = calculate_scale_pos_weight(y)
        
        # Calculer les statistiques
        neg_count = np.sum(y == 0)
        pos_count = np.sum(y == 1)
        pos_ratio = pos_count / len(y)
        
        print(f"  Numéro {num}: Distribution des classes: [{neg_count} {pos_count}]")
        print(f"  Numéro {num}: Ratio positif: {pos_ratio:.4f}")
        print(f"  Numéro {num}: scale_pos_weight = {spw:.4f}")
        
        # Stocker les résultats
        results.append({
            'number': num,
            'neg_count': neg_count,
            'pos_count': pos_count,
            'pos_ratio': pos_ratio,
            'scale_pos_weight': spw,
            'total_samples': len(y)
        })
    
    # Créer un DataFrame avec les résultats
    results_df = pd.DataFrame(results)
    
    # Afficher les statistiques
    print("\nStatistiques de scale_pos_weight:")
    print(f"Valeur moyenne: {results_df['scale_pos_weight'].mean():.4f}")
    print(f"Écart-type: {results_df['scale_pos_weight'].std():.4f}")
    print(f"Valeur minimum: {results_df['scale_pos_weight'].min():.4f} (numéro {results_df.loc[results_df['scale_pos_weight'].idxmin()]['number']:.0f})")
    print(f"Valeur maximum: {results_df['scale_pos_weight'].max():.4f} (numéro {results_df.loc[results_df['scale_pos_weight'].idxmax()]['number']:.0f})")
    
    # Sauvegarder les résultats dans un fichier CSV
    output_file = 'keno_scale_pos_weight_analysis.csv'
    results_df.to_csv(output_file, index=False)
    print(f"\nRésultats sauvegardés dans {output_file}")
    
    # Créer un fichier JSON avec les valeurs de scale_pos_weight
    import json
    
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Créer un dictionnaire avec les valeurs de scale_pos_weight
    spw_dict = {int(row['number']): float(row['scale_pos_weight']) for _, row in results_df.iterrows()}
    
    # Sauvegarder dans un fichier JSON
    json_file = os.path.join('data', 'scale_pos_weight_values.json')
    with open(json_file, 'w') as f:
        json.dump(spw_dict, f, indent=2)
    
    print(f"Valeurs de scale_pos_weight sauvegardées dans {json_file}")
    
    return results_df

def create_xgboost_params_file(results_df):
    """
    Crée un fichier JSON avec les paramètres XGBoost optimisés pour chaque numéro
    
    Args:
        results_df: DataFrame avec les résultats de l'analyse
    """
    import json
    
    # Créer un dictionnaire avec les paramètres XGBoost
    params = {}
    
    for _, row in results_df.iterrows():
        num = int(row['number'])
        spw = float(row['scale_pos_weight'])
        
        # Paramètres de base pour XGBoost
        params[str(num)] = {
            'n_estimators': 200,
            'max_depth': 6,
            'learning_rate': 0.05,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'min_child_weight': 3,
            'gamma': 0.1,
            'scale_pos_weight': spw,
            'objective': 'binary:logistic',
            'eval_metric': 'auc',
            'verbosity': 0,
            'tree_method': 'hist',
            'predictor': 'cpu_predictor'
        }
    
    # Sauvegarder dans un fichier JSON
    output_file = 'xgboost_optimal_params.json'
    
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Sauvegarder dans le répertoire data
    output_path = os.path.join('data', output_file)
    
    with open(output_path, 'w') as f:
        json.dump(params, f, indent=2)
    
    print(f"Paramètres XGBoost optimisés sauvegardés dans {output_path}")

def create_integration_module():
    """
    Crée un module Python pour intégrer les valeurs de scale_pos_weight dans l'optimiseur XGBoost
    """
    module_content = """\"\"\"
Module pour intégrer les valeurs de scale_pos_weight spécifiques à chaque numéro dans l'optimiseur XGBoost
\"\"\"

import os
import json
import numpy as np

# Chemin du fichier de valeurs de scale_pos_weight
SPW_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'scale_pos_weight_values.json')

def load_scale_pos_weight_values():
    \"\"\"
    Charge les valeurs de scale_pos_weight depuis le fichier JSON
    
    Returns:
        dict: Valeurs de scale_pos_weight par numéro
    \"\"\"
    try:
        if os.path.exists(SPW_FILE):
            with open(SPW_FILE, 'r') as f:
                values = json.load(f)
            
            # Convertir les clés en entiers
            return {int(k): v for k, v in values.items()}
        else:
            print(f"Le fichier {SPW_FILE} n'existe pas")
            return {}
    except Exception as e:
        print(f"Erreur lors du chargement des valeurs de scale_pos_weight: {e}")
        return {}

def get_scale_pos_weight(num, y=None):
    \"\"\"
    Récupère la valeur de scale_pos_weight pour un numéro spécifique
    
    Args:
        num: Numéro Keno
        y: Étiquettes (optionnel, pour calculer si la valeur n'est pas disponible)
        
    Returns:
        float: Valeur de scale_pos_weight
    \"\"\"
    # Charger les valeurs
    values = load_scale_pos_weight_values()
    
    # Récupérer la valeur pour ce numéro
    if num in values:
        return values[num]
    
    # Calculer si les données sont disponibles
    if y is not None:
        neg_count = np.sum(y == 0)
        pos_count = np.sum(y == 1)
        
        if pos_count > 0:
            return neg_count / pos_count
    
    # Valeur par défaut
    return 2.57  # Valeur moyenne observée

def enhance_xgboost_optimizer(optimizer):
    \"\"\"
    Améliore l'optimiseur XGBoost en utilisant des valeurs de scale_pos_weight spécifiques
    
    Args:
        optimizer: Optimiseur XGBoost
        
    Returns:
        optimizer: Optimiseur amélioré
    \"\"\"
    # Sauvegarder la méthode originale
    original_calculate_spw = optimizer.calculate_optimal_scale_pos_weight
    
    # Définir la nouvelle méthode
    def enhanced_calculate_spw(y_train, num=None):
        # Si le numéro est spécifié, utiliser la valeur spécifique
        if num is not None:
            spw = get_scale_pos_weight(num, y_train)
            
            if optimizer.verbose > 0:
                neg_count = np.sum(y_train == 0)
                pos_count = np.sum(y_train == 1)
                print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
                print(f"  Numéro {num}: scale_pos_weight spécifique = {spw:.4f}")
            
            return spw
        
        # Sinon, utiliser la méthode originale
        return original_calculate_spw(y_train)
    
    # Remplacer la méthode
    optimizer.calculate_optimal_scale_pos_weight = enhanced_calculate_spw
    
    # Ajouter un attribut pour indiquer que l'optimiseur a été amélioré
    optimizer.enhanced_spw = True
    
    return optimizer
"""
    
    # Sauvegarder le module
    module_file = 'keno_spw_enhancer.py'
    
    with open(module_file, 'w') as f:
        f.write(module_content)
    
    print(f"Module d'intégration sauvegardé dans {module_file}")

def create_example_usage():
    """
    Crée un exemple d'utilisation du module d'intégration
    """
    example_content = """\"\"\"
Exemple d'utilisation du module d'intégration des valeurs de scale_pos_weight
\"\"\"

import numpy as np
import pandas as pd
from keno_data import KenoDataManager
from keno_xgboost_optimizer_simple import XGBoostOptimizer
from keno_spw_enhancer import enhance_xgboost_optimizer

def main():
    \"\"\"Fonction principale\"\"\"
    # Créer le gestionnaire de données
    data_manager = KenoDataManager()
    
    # Charger les données
    print("Chargement des données Keno...")
    data_loaded = data_manager.load_data()
    
    if not data_loaded or not data_manager.draws:
        print("Erreur: Impossible de charger les données Keno")
        return
    
    print(f"Données chargées: {len(data_manager.draws)} tirages")
    
    # Créer l'optimiseur XGBoost
    optimizer = XGBoostOptimizer(use_gpu=False, n_jobs=-1, verbose=1)
    
    # Améliorer l'optimiseur avec les valeurs de scale_pos_weight spécifiques
    enhanced_optimizer = enhance_xgboost_optimizer(optimizer)
    
    # Préparer les données pour un numéro spécifique
    num = 7  # Exemple avec le numéro 7
    
    # Créer un DataFrame avec les données historiques
    data = []
    for draw in data_manager.draws:
        if not hasattr(draw, 'draw_numbers') or not draw.draw_numbers:
            continue
        if not hasattr(draw, 'draw_date') or not draw.draw_date:
            continue
            
        row = {
            'date': draw.draw_date,
            'has_{}'.format(num): 1 if num in draw.draw_numbers else 0
        }
        data.append(row)
    
    df = pd.DataFrame(data)
    df = df.sort_values('date')
    
    # Ajouter des caractéristiques de lag
    for i in range(1, 6):
        df['has_{}_lag_{}'.format(num, i)] = df['has_{}'.format(num)].shift(i).fillna(0)
    
    # Supprimer les lignes avec des valeurs manquantes
    df = df.dropna()
    
    # Définir les caractéristiques et la cible
    X = df[['has_{}_lag_{}'.format(num, i) for i in range(1, 6)]]
    y = df['has_{}'.format(num)]
    
    # Entraîner le modèle avec l'optimiseur amélioré
    print(f"\\nEntraînement du modèle pour le numéro {num} avec l'optimiseur amélioré...")
    result = enhanced_optimizer.train_model(X, y, num, optimize=True, fast_mode=True)
    
    # Afficher les résultats
    if result:
        print(f"\\nRésultats pour le numéro {num}:")
        print(f"Précision: {result['accuracy']:.4f}")
        print(f"F1-score: {result['f1']:.4f}")
        print(f"Précision: {result['precision']:.4f}")
        print(f"Rappel: {result['recall']:.4f}")

if __name__ == "__main__":
    main()
"""
    
    # Sauvegarder l'exemple
    example_file = 'example_enhanced_spw.py'
    
    with open(example_file, 'w') as f:
        f.write(example_content)
    
    print(f"Exemple d'utilisation sauvegardé dans {example_file}")

def main():
    """Fonction principale"""
    print("Correction du problème de scale_pos_weight identique pour tous les numéros Keno")
    
    # Analyser tous les numéros
    results_df = analyze_all_numbers()
    
    if results_df is not None and len(results_df) > 0:
        # Créer le fichier de paramètres XGBoost
        create_xgboost_params_file(results_df)
        
        # Créer le module d'intégration
        create_integration_module()
        
        # Créer l'exemple d'utilisation
        create_example_usage()
        
        print("\nCorrection terminée!")
        print("Pour utiliser les valeurs de scale_pos_weight spécifiques à chaque numéro:")
        print("1. Importez le module d'intégration:")
        print("   from keno_spw_enhancer import enhance_xgboost_optimizer")
        print("2. Améliorez votre optimiseur:")
        print("   enhanced_optimizer = enhance_xgboost_optimizer(optimizer)")
        print("3. Utilisez l'optimiseur amélioré pour entraîner vos modèles")
        print("4. Consultez l'exemple dans example_enhanced_spw.py")
    else:
        print("\nErreur lors de l'analyse des numéros")

if __name__ == "__main__":
    main()
