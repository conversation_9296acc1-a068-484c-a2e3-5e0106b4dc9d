#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script pour patcher l'interface Keno avec le système d'auto-amélioration optimisé
"""

import os
import shutil
from datetime import datetime

def backup_original_files():
    """Sauvegarde les fichiers originaux"""
    
    files_to_backup = ['keno_gui.py', 'keno_analyzer.py']
    backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        
        for file_name in files_to_backup:
            if os.path.exists(file_name):
                shutil.copy2(file_name, os.path.join(backup_dir, file_name))
                print(f"✓ Sauvegardé: {file_name} -> {backup_dir}/{file_name}")
        
        print(f"✓ Sauvegarde créée dans: {backup_dir}")
        return backup_dir
        
    except Exception as e:
        print(f"✗ Erreur lors de la sauvegarde: {e}")
        return None

def add_enhanced_auto_improve_to_gui():
    """Ajoute le bouton d'auto-amélioration optimisée à l'interface"""
    
    gui_file = 'keno_gui.py'
    
    if not os.path.exists(gui_file):
        print(f"✗ Fichier {gui_file} non trouvé")
        return False
    
    try:
        # Lire le fichier
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifier si déjà patché
        if 'enhanced_auto_improve' in content:
            print("✓ Interface déjà patchée avec le système optimisé")
            return True
        
        # Ajouter l'import du système optimisé
        import_section = """
# Import du système d'auto-amélioration optimisé
try:
    from keno_enhanced_auto_improve import KenoEnhancedAutoImprove, integrate_enhanced_auto_improve
    enhanced_system_available = True
except ImportError:
    enhanced_system_available = False
"""
        
        # Trouver où insérer l'import
        import_pos = content.find("from keno_data_manager import KenoDataManager")
        if import_pos != -1:
            content = content[:import_pos] + import_section + "\n" + content[import_pos:]
        
        # Ajouter l'initialisation du système optimisé dans __init__
        init_section = """
        # Initialiser le système d'auto-amélioration optimisé
        self.enhanced_improver = None
        if enhanced_system_available:
            try:
                self.enhanced_improver = integrate_enhanced_auto_improve(self.analyzer)
                print("✓ Système d'auto-amélioration optimisé initialisé")
            except Exception as e:
                print(f"Erreur lors de l'initialisation du système optimisé: {e}")
"""
        
        # Trouver la fin de __init__
        init_pos = content.find("self.create_interface()")
        if init_pos != -1:
            content = content[:init_pos] + init_section + "\n        " + content[init_pos:]
        
        # Ajouter le bouton dans l'interface
        button_section = """
        # Bouton d'auto-amélioration optimisée
        if enhanced_system_available:
            enhanced_improve_button = ttk.Button(
                buttons_frame,
                text="Auto-Amélioration OPTIMISÉE",
                command=self.start_enhanced_auto_improve,
                style="Action.TButton"
            )
            enhanced_improve_button.pack(side=tk.LEFT, padx=5)
"""
        
        # Trouver où ajouter le bouton (après le bouton d'auto-amélioration normal)
        button_pos = content.find('text="Auto-Amélioration"')
        if button_pos != -1:
            # Trouver la fin de cette section de bouton
            end_pos = content.find('.pack(side=tk.LEFT, padx=5)', button_pos)
            if end_pos != -1:
                end_pos = content.find('\n', end_pos)
                content = content[:end_pos] + "\n" + button_section + content[end_pos:]
        
        # Ajouter les méthodes pour l'auto-amélioration optimisée
        methods_section = '''
    def start_enhanced_auto_improve(self):
        """Lance l'auto-amélioration optimisée"""
        if not self.enhanced_improver:
            messagebox.showerror("Erreur", "Système d'auto-amélioration optimisé non disponible")
            return
        
        # Vérifier qu'il y a des données
        if self.data_manager.get_draws_count() < 100:
            messagebox.showerror("Erreur", "Pas assez de données pour l'auto-amélioration optimisée (minimum 100 tirages).")
            return
        
        # Demander le mode d'entraînement
        mode_dialog = tk.Toplevel(self)
        mode_dialog.title("Mode d'auto-amélioration optimisée")
        mode_dialog.geometry("400x300")
        mode_dialog.transient(self)
        mode_dialog.grab_set()
        
        # Centrer la fenêtre
        mode_dialog.update_idletasks()
        x = (self.winfo_width() // 2) - (400 // 2) + self.winfo_x()
        y = (self.winfo_height() // 2) - (300 // 2) + self.winfo_y()
        mode_dialog.geometry(f"400x300+{x}+{y}")
        
        # Titre
        title_label = tk.Label(mode_dialog, text="Auto-Amélioration OPTIMISÉE", 
                              font=("Helvetica", 14, "bold"))
        title_label.pack(pady=10)
        
        # Description
        desc_text = """Ce système utilise des algorithmes avancés de machine learning
avec feature engineering optimisé, ensemble learning et
sélection automatique des meilleures caractéristiques.

Choisissez le mode d'entraînement:"""
        
        desc_label = tk.Label(mode_dialog, text=desc_text, wraplength=350, justify=tk.LEFT)
        desc_label.pack(pady=10)
        
        # Variable pour le mode
        mode_var = tk.StringVar(value="fast")
        
        # Options de mode
        modes_frame = tk.Frame(mode_dialog)
        modes_frame.pack(pady=10)
        
        tk.Radiobutton(modes_frame, text="Rapide (10 numéros, ~5-10 min)", 
                      variable=mode_var, value="fast").pack(anchor=tk.W)
        tk.Radiobutton(modes_frame, text="Moyen (35 numéros, ~20-30 min)", 
                      variable=mode_var, value="medium").pack(anchor=tk.W)
        tk.Radiobutton(modes_frame, text="Complet (70 numéros, ~1-2 heures)", 
                      variable=mode_var, value="complete").pack(anchor=tk.W)
        
        # Boutons
        buttons_frame = tk.Frame(mode_dialog)
        buttons_frame.pack(pady=20)
        
        def start_training():
            mode = mode_var.get()
            mode_dialog.destroy()
            self._run_enhanced_auto_improve(mode)
        
        def cancel():
            mode_dialog.destroy()
        
        ttk.Button(buttons_frame, text="Démarrer", command=start_training).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Annuler", command=cancel).pack(side=tk.LEFT, padx=5)
    
    def _run_enhanced_auto_improve(self, mode):
        """Exécute l'auto-amélioration optimisée"""
        try:
            # Déterminer les numéros à entraîner selon le mode
            if mode == 'fast':
                numbers_to_train = [1, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70]
            elif mode == 'medium':
                numbers_to_train = list(range(1, 71, 2))  # Numéros impairs
            else:  # complete
                numbers_to_train = None
            
            # Créer une fenêtre de progression
            progress_window = tk.Toplevel(self)
            progress_window.title("Auto-amélioration optimisée en cours")
            progress_window.geometry("600x400")
            progress_window.transient(self)
            progress_window.grab_set()
            
            # Interface de progression
            title_label = tk.Label(progress_window, text="Auto-Amélioration OPTIMISÉE", 
                                  font=("Helvetica", 14, "bold"))
            title_label.pack(pady=10)
            
            status_var = tk.StringVar(value="Initialisation...")
            status_label = tk.Label(progress_window, textvariable=status_var)
            status_label.pack(pady=5)
            
            # Zone de texte pour les détails
            details_frame = tk.Frame(progress_window)
            details_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            scrollbar = tk.Scrollbar(details_frame)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            details_text = tk.Text(details_frame, yscrollcommand=scrollbar.set)
            details_text.pack(fill=tk.BOTH, expand=True)
            scrollbar.config(command=details_text.yview)
            
            def update_details(message):
                details_text.insert(tk.END, message + "\\n")
                details_text.see(tk.END)
                progress_window.update()
            
            # Lancer l'entraînement dans un thread
            import threading
            
            def run_training():
                try:
                    update_details("Démarrage de l'auto-amélioration optimisée...")
                    status_var.set("Entraînement en cours...")
                    
                    results = self.enhanced_improver.run_enhanced_auto_improve(numbers_to_train)
                    
                    if results and results['trained_numbers']:
                        update_details(f"✓ Entraînement terminé avec succès!")
                        update_details(f"  Numéros entraînés: {len(results['trained_numbers'])}")
                        
                        if results['performance_summary']:
                            import numpy as np
                            avg_f1 = np.mean([p['f1_score'] for p in results['performance_summary'].values()])
                            update_details(f"  F1-score moyen: {avg_f1:.4f}")
                        
                        status_var.set("Entraînement terminé avec succès!")
                        
                        # Tester les prédictions
                        update_details("\\nTest des prédictions optimisées...")
                        predictions = self.enhanced_improver.predict_next_draw_enhanced(10)
                        
                        if predictions:
                            update_details(f"✓ Prédictions générées: {predictions}")
                            status_var.set("Système optimisé prêt!")
                        
                    else:
                        update_details("✗ Échec de l'entraînement")
                        status_var.set("Échec de l'entraînement")
                        
                except Exception as e:
                    update_details(f"✗ Erreur: {e}")
                    status_var.set("Erreur lors de l'entraînement")
                
                # Ajouter un bouton de fermeture
                def close_window():
                    progress_window.destroy()
                
                close_button = ttk.Button(progress_window, text="Fermer", command=close_window)
                close_button.pack(pady=10)
            
            # Démarrer le thread
            training_thread = threading.Thread(target=run_training)
            training_thread.daemon = True
            training_thread.start()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'auto-amélioration optimisée: {e}")
'''
        
        # Ajouter les méthodes à la fin de la classe
        class_end = content.rfind("if __name__ == \"__main__\":")
        if class_end != -1:
            content = content[:class_end] + methods_section + "\n\n" + content[class_end:]
        
        # Écrire le fichier modifié
        with open(gui_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✓ Interface patchée avec le système optimisé")
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors du patch de l'interface: {e}")
        return False

def main():
    """Fonction principale"""
    
    print("=== Patch de l'interface Keno avec le système optimisé ===")
    
    # 1. Sauvegarder les fichiers originaux
    backup_dir = backup_original_files()
    if not backup_dir:
        print("Échec de la sauvegarde. Arrêt du patch.")
        return 1
    
    # 2. Patcher l'interface
    success = add_enhanced_auto_improve_to_gui()
    
    if success:
        print("✓ Patch appliqué avec succès!")
        print(f"✓ Fichiers originaux sauvegardés dans: {backup_dir}")
        print("\nVotre application Keno dispose maintenant du système d'auto-amélioration optimisé!")
        print("Lancez votre application et cherchez le bouton 'Auto-Amélioration OPTIMISÉE'")
        return 0
    else:
        print("✗ Échec du patch")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
