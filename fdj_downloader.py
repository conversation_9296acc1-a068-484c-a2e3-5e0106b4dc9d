import os
import requests
import zipfile
import tempfile
import shutil
from tkinter import messagebox
import threading
import time
import csv
from datetime import datetime

def download_and_process_fdj_data(url, data_manager, progress_callback=None, status_callback=None):
    """
    Télécharge, décompresse et traite les données Keno depuis l'URL fournie.

    Args:
        url (str): URL du fichier ZIP à télécharger
        data_manager (KenoDataManager): Gestionnaire de données Keno
        progress_callback (function): Fonction de rappel pour mettre à jour la progression
        status_callback (function): Fonction de rappel pour mettre à jour le statut

    Returns:
        bool: True si le téléchargement et le traitement ont réussi, False sinon
    """
    # Mettre à jour le statut
    if status_callback:
        status_callback("Initialisation du téléchargement...")

    if progress_callback:
        progress_callback(5)

    # Créer un dossier temporaire pour stocker les fichiers
    temp_dir = tempfile.mkdtemp()
    zip_path = os.path.join(temp_dir, "keno_data.zip")

    try:
        # Télécharger le fichier ZIP
        if status_callback:
            status_callback("Téléchargement du fichier ZIP...")

        if progress_callback:
            progress_callback(10)

        print(f"Téléchargement depuis l'URL: {url}")
        try:
            response = requests.get(url, stream=True, timeout=30)
            print(f"Code de réponse: {response.status_code}")
            print(f"En-têtes: {response.headers}")

            if response.status_code != 200:
                error_msg = f"Erreur lors du téléchargement: {response.status_code} - {response.text[:200]}"
                print(error_msg)
                if status_callback:
                    status_callback(error_msg)
                return False
        except Exception as e:
            error_msg = f"Exception lors de la requête: {str(e)}"
            print(error_msg)
            if status_callback:
                status_callback(error_msg)
            return False

        # Enregistrer le fichier ZIP
        try:
            with open(zip_path, 'wb') as f:
                total_size = int(response.headers.get('content-length', 0))
                print(f"Taille totale du fichier: {total_size} octets")
                downloaded = 0
                chunk_size = 8192

                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)

                        # Mettre à jour la progression du téléchargement
                        if total_size > 0 and progress_callback:
                            progress = 10 + int((downloaded / total_size) * 30)
                            progress_callback(min(progress, 40))

            print(f"Fichier ZIP téléchargé avec succès: {os.path.getsize(zip_path)} octets")
        except Exception as e:
            error_msg = f"Erreur lors de l'enregistrement du fichier ZIP: {str(e)}"
            print(error_msg)
            if status_callback:
                status_callback(error_msg)
            return False

        # Vérifier que le fichier ZIP est valide
        try:
            if not zipfile.is_zipfile(zip_path):
                error_msg = "Le fichier téléchargé n'est pas un fichier ZIP valide"
                print(error_msg)
                if status_callback:
                    status_callback(error_msg)
                return False
        except Exception as e:
            error_msg = f"Erreur lors de la vérification du fichier ZIP: {str(e)}"
            print(error_msg)
            if status_callback:
                status_callback(error_msg)
            return False

        # Décompresser le fichier ZIP
        if status_callback:
            status_callback("Décompression du fichier ZIP...")

        if progress_callback:
            progress_callback(40)

        extract_dir = os.path.join(temp_dir, "extracted")
        os.makedirs(extract_dir, exist_ok=True)

        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # Lister le contenu du ZIP
                file_list = zip_ref.namelist()
                print(f"Contenu du ZIP: {file_list}")

                # Extraire tous les fichiers
                zip_ref.extractall(extract_dir)
                print(f"Fichiers extraits dans: {extract_dir}")
        except Exception as e:
            error_msg = f"Erreur lors de la décompression: {str(e)}"
            print(error_msg)
            if status_callback:
                status_callback(error_msg)
            return False

        if progress_callback:
            progress_callback(50)

        # Trouver les fichiers CSV dans le dossier extrait
        csv_files = []
        for root, _, files in os.walk(extract_dir):
            for file in files:
                if file.lower().endswith('.csv'):
                    csv_files.append(os.path.join(root, file))

        print(f"Fichiers CSV trouvés: {len(csv_files)}")
        for csv_file in csv_files:
            print(f"  - {csv_file}")

        if not csv_files:
            error_msg = "Aucun fichier CSV trouvé dans l'archive."
            print(error_msg)
            if status_callback:
                status_callback(error_msg)
            return False

        # Traiter chaque fichier CSV
        total_imported = 0
        for i, csv_file in enumerate(csv_files):
            if status_callback:
                status_callback(f"Traitement du fichier {i+1}/{len(csv_files)}: {os.path.basename(csv_file)}...")

            if progress_callback:
                progress = 50 + int((i / len(csv_files)) * 40)
                progress_callback(min(progress, 90))

            print(f"Traitement du fichier: {csv_file}")

            # Essayer d'abord avec le format FDJ Keno
            try:
                print("Tentative d'importation avec load_fdj_keno_csv...")
                imported = data_manager.load_fdj_keno_csv(csv_file, clear_existing=False)
                print(f"Résultat de load_fdj_keno_csv: {imported}")
            except Exception as e:
                print(f"Erreur avec load_fdj_keno_csv: {str(e)}")
                imported = False

            # Si cela échoue, essayer avec la méthode générique
            if not imported:
                try:
                    print("Tentative d'importation avec load_csv...")
                    imported = data_manager.load_csv(csv_file, clear_existing=False)
                    print(f"Résultat de load_csv: {imported}")
                except Exception as e:
                    print(f"Erreur avec load_csv: {str(e)}")
                    imported = 0

            if isinstance(imported, int) and imported > 0:
                total_imported += imported
                print(f"Importé {imported} tirages depuis {csv_file}")
            else:
                print(f"Aucun tirage importé depuis {csv_file}")

        # Mettre à jour le statut final
        final_msg = f"Importation terminée. {total_imported} tirages importés."
        print(final_msg)
        if status_callback:
            status_callback(final_msg)

        if progress_callback:
            progress_callback(100)

        return total_imported > 0

    except Exception as e:
        error_msg = f"Erreur lors du traitement: {str(e)}"
        print(error_msg)
        import traceback
        traceback.print_exc()
        if status_callback:
            status_callback(error_msg)
        return False

    finally:
        # Nettoyer les fichiers temporaires
        try:
            shutil.rmtree(temp_dir)
            print(f"Nettoyage des fichiers temporaires: {temp_dir}")
        except Exception as e:
            print(f"Erreur lors du nettoyage des fichiers temporaires: {str(e)}")

def download_fdj_data_threaded(gui, url, progress_var=None, status_var=None, progress_window=None):
    """
    Lance le téléchargement et le traitement des données FDJ dans un thread séparé.

    Args:
        gui: Instance de KenoGUI
        url (str): URL du fichier ZIP à télécharger
        progress_var: Variable Tkinter pour la progression
        status_var: Variable Tkinter pour le statut
        progress_window: Fenêtre de progression
    """
    def update_progress(value):
        if progress_var:
            progress_var.set(value)
            if progress_window:
                progress_window.update_idletasks()

    def update_status(text):
        if status_var:
            status_var.set(text)
            if progress_window:
                progress_window.update_idletasks()

    def run_download():
        with gui.thread_lock:
            success = download_and_process_fdj_data(
                url,
                gui.data_manager,
                progress_callback=update_progress,
                status_callback=update_status
            )

            # Mettre à jour l'interface utilisateur dans le thread principal
            # La classe KenoGUI hérite de tk.Tk, donc gui est la fenêtre racine
            gui.after(100, lambda: handle_download_completion(gui, success, progress_window))

    # Démarrer le thread de téléchargement
    download_thread = threading.Thread(target=run_download)
    download_thread.daemon = True
    download_thread.start()

def handle_download_completion(gui, success, progress_window=None):
    """
    Gère la fin du téléchargement et met à jour l'interface utilisateur.

    Args:
        gui: Instance de KenoGUI
        success (bool): Indique si le téléchargement a réussi
        progress_window: Fenêtre de progression à fermer
    """
    if progress_window:
        progress_window.destroy()

    # Utiliser la méthode _handle_download_result de la classe KenoGUI
    if hasattr(gui, '_handle_download_result'):
        gui._handle_download_result(success)
    else:
        # Fallback si la méthode n'existe pas
        if success:
            messagebox.showinfo(
                "Téléchargement terminé",
                f"Les données ont été téléchargées et importées avec succès.\n\n"
                f"Nombre de tirages disponibles: {gui.data_manager.get_draws_count()}"
            )

            # Mettre à jour les statistiques
            if hasattr(gui, 'update_stats_safely'):
                gui.update_stats_safely()
        else:
            messagebox.showerror(
                "Erreur",
                "Une erreur s'est produite lors du téléchargement ou de l'importation des données."
            )
