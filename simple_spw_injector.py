"""
Module simple pour injecter des valeurs différentes de scale_pos_weight pour chaque numéro Keno.
Ce module utilise une approche non invasive qui ne modifie pas le code source existant.
"""

import os
import json
import random
import numpy as np
from functools import wraps

# Générer des valeurs aléatoires de scale_pos_weight pour chaque numéro
def generate_random_weights():
    """
    Génère des valeurs aléatoires de scale_pos_weight pour chaque numéro
    
    Returns:
        dict: Dictionnaire des valeurs par numéro
    """
    # Générer des valeurs aléatoires autour de 2.57
    values = {}
    for num in range(1, 71):
        # Générer une valeur entre 1.5 et 3.5
        value = 1.5 + random.random() * 2.0
        values[num] = round(value, 4)
    
    print("Valeurs de scale_pos_weight générées:")
    for num in range(1, 11):  # Afficher les 10 premières valeurs
        print(f"  Numéro {num}: {values[num]}")
    
    return values

# Sauvegarder les poids dans un fichier JSON
def save_weights_to_json(weights):
    """
    Sauvegarde les poids dans un fichier JSON
    
    Args:
        weights: Dictionnaire des poids par numéro
        
    Returns:
        str: Chemin du fichier créé
    """
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Chemin du fichier
    file_path = os.path.join('data', 'simple_spw_values.json')
    
    # Convertir les clés en chaînes pour la sérialisation JSON
    weights_str = {str(k): v for k, v in weights.items()}
    
    # Sauvegarder les poids
    with open(file_path, 'w') as f:
        json.dump(weights_str, f, indent=2)
    
    print(f"Poids sauvegardés dans {file_path}")
    
    return file_path

# Chemin du fichier de valeurs de scale_pos_weight
SPW_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'simple_spw_values.json')

# Vérifier si le fichier existe, sinon le créer
if not os.path.exists(SPW_FILE):
    print(f"Le fichier {SPW_FILE} n'existe pas, création...")
    weights = generate_random_weights()
    save_weights_to_json(weights)

# Charger les valeurs
try:
    with open(SPW_FILE, 'r') as f:
        SPW_VALUES = json.load(f)
    print(f"Valeurs de scale_pos_weight chargées depuis {SPW_FILE}")
except Exception as e:
    print(f"Erreur lors du chargement des valeurs de scale_pos_weight: {e}")
    SPW_VALUES = {}

# Fonction pour obtenir la valeur forcée
def get_forced_scale_pos_weight(num):
    """
    Récupère la valeur forcée de scale_pos_weight pour un numéro
    
    Args:
        num: Numéro Keno
        
    Returns:
        float: Valeur de scale_pos_weight
    """
    # Convertir en entier si nécessaire
    try:
        num = int(num)
    except:
        pass
    
    # Récupérer la valeur
    if isinstance(num, int) and 1 <= num <= 70 and str(num) in SPW_VALUES:
        return SPW_VALUES[str(num)]
    
    # Valeur par défaut
    return 2.57

# Monkey patch pour XGBClassifier
try:
    from xgboost import XGBClassifier
    
    # Sauvegarder la méthode originale
    original_init = XGBClassifier.__init__
    
    @wraps(original_init)
    def patched_init(self, *args, **kwargs):
        # Récupérer le numéro si disponible
        num = kwargs.pop('num', None)
        
        # Récupérer scale_pos_weight s'il est déjà défini
        scale_pos_weight = kwargs.get('scale_pos_weight', None)
        
        # Appeler la méthode originale
        original_init(self, *args, **kwargs)
        
        # Forcer scale_pos_weight si le numéro est disponible et scale_pos_weight n'est pas déjà défini
        if num is not None and scale_pos_weight is None:
            forced_spw = get_forced_scale_pos_weight(num)
            self.scale_pos_weight = forced_spw
            print(f"XGBClassifier: Numéro {num} - scale_pos_weight forcé à {forced_spw}")
    
    # Remplacer la méthode
    XGBClassifier.__init__ = patched_init
    
    print("XGBClassifier patché avec succès")
except Exception as e:
    print(f"Erreur lors du patch de XGBClassifier: {e}")

print("Module d'injection de scale_pos_weight chargé avec succès")
