{"n_jobs": -1, "max_workers": null, "hardware_acceleration": "npu", "model_params": {"deep_learning": {"epochs": 20, "batch_size": 64, "neurons": 128, "dropout_rate": 0.3, "use_simple": false, "use_deep": false, "use_cnn": false, "use_ensemble": false}, "random_forest": {"use_model": false, "n_estimators": 1000, "max_depth": 25}, "xgboost": {"use_model": true, "n_estimators": 1000, "max_depth": 12, "learning_rate": 0.01}}}