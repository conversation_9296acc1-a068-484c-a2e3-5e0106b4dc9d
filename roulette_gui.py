import tkinter as tk
from tkinter import ttk
from datetime import datetime
from machine_roulette_analyzer import MachineRouletteAnalyzer
import json

class RouletteGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Analyseur de Roulette")
        self.root.geometry("1000x800")  # Résolution plus adaptée

        # Configuration pour permettre à la fenêtre de s'étendre
        self.root.minsize(800, 600)  # Taille minimale
        self.root.columnconfigure(0, weight=1)  # Permet à la colonne de s'étendre
        self.root.rowconfigure(0, weight=1)  # Permet à la ligne de s'étendre

        self.analyzer = MachineRouletteAnalyzer()

        # Style
        self.root.configure(bg='#2c3e50')
        style = ttk.Style()
        style.configure('TButton', padding=6, font=('Helvetica', 11))
        style.configure('TLabel', background='#2c3e50', foreground='white', font=('Helvetica', 11))
        style.configure('TLabelframe', background='#2c3e50', foreground='white', font=('Helvetica', 12))
        style.configure('TLabelframe.Label', background='#2c3e50', foreground='white', font=('Helvetica', 12, 'bold'))
        style.configure('Action.TButton', padding=6, font=('Helvetica', 11, 'bold'))

        self.create_widgets()

    def create_widgets(self):
        # Frame principale
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configurer main_frame pour qu'il s'adapte à la taille de la fenêtre
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        for i in range(5):  # 5 lignes dans main_frame
            main_frame.rowconfigure(i, weight=1)

        # Section entrée numéro
        input_frame = ttk.LabelFrame(main_frame, text="Entrée des numéros", padding="5")
        input_frame.grid(row=0, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Création des boutons de numéros
        self.number_buttons = {}
        self.create_number_buttons(input_frame)

        # Zone d'affichage des résultats
        results_frame = ttk.LabelFrame(main_frame, text="Résultats d'analyse", padding="5")
        results_frame.grid(row=1, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configurer results_frame pour s'adapter
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

        self.result_text = tk.Text(results_frame, height=12, width=80, font=('Helvetica', 11))
        self.result_text.grid(row=0, column=0, padx=10, pady=10, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Section de prédiction
        prediction_frame = ttk.LabelFrame(main_frame, text="Prédiction du prochain numéro", padding="5")
        prediction_frame.grid(row=2, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configurer prediction_frame pour s'adapter
        prediction_frame.columnconfigure(0, weight=1)
        prediction_frame.rowconfigure(0, weight=1)

        self.prediction_label = ttk.Label(prediction_frame, text="Aucune prédiction disponible", font=("Helvetica", 12, "bold"))
        self.prediction_label.grid(row=0, column=0, padx=10, pady=10, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Historique
        history_frame = ttk.LabelFrame(main_frame, text="Historique", padding="5")
        history_frame.grid(row=3, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configurer history_frame pour s'adapter
        history_frame.columnconfigure(0, weight=1)
        history_frame.rowconfigure(0, weight=1)

        self.history_text = tk.Text(history_frame, height=6, width=80, font=('Helvetica', 11))
        self.history_text.grid(row=0, column=0, padx=10, pady=10, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Boutons d'action
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=4, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configurer action_frame pour centrer les boutons
        action_frame.columnconfigure(0, weight=1)
        action_frame.columnconfigure(1, weight=1)
        action_frame.columnconfigure(2, weight=1)

        # Les boutons d'action utilisent le style déjà défini dans __init__

        ttk.Button(action_frame, text="Effacer l'historique", style='Action.TButton',
                  command=self.clear_history).grid(row=0, column=0, padx=8, pady=8)
        ttk.Button(action_frame, text="Sauvegarder", style='Action.TButton',
                  command=self.save_history).grid(row=0, column=1, padx=8, pady=8)
        ttk.Button(action_frame, text="Charger", style='Action.TButton',
                  command=self.load_history).grid(row=0, column=2, padx=8, pady=8)

    def create_number_buttons(self, parent):
        numbers_frame = ttk.Frame(parent)
        numbers_frame.grid(row=0, column=0, padx=5, pady=5, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configurer parent pour s'adapter
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(0, weight=1)

        # Style pour les boutons de numéros
        style = ttk.Style()
        style.configure('Normal.TButton', padding=5, font=('Helvetica', 10))
        style.configure('Predicted.TButton', padding=5, font=('Helvetica', 10, 'bold'), background='#ffcc00')

        # Création de la grille de boutons
        row = 0
        col = 0
        for i in range(37):  # 0 à 36
            btn = ttk.Button(numbers_frame, text=str(i), style='Normal.TButton',
                           command=lambda x=i: self.number_clicked(x))
            btn.grid(row=row, column=col, padx=2, pady=2)
            self.number_buttons[i] = btn
            col += 1
            if col > 9:  # 10 boutons par ligne
                col = 0
                row += 1

    def number_clicked(self, number):
        # Ajoute le numéro à l'analyseur
        self.analyzer.add_spin(number, datetime.now())

        # Met à jour l'historique
        self.update_history()

        # Analyse et affiche les résultats
        self.update_analysis()

    def update_history(self):
        self.history_text.delete(1.0, tk.END)
        history = list(self.analyzer.history)
        history_text = "Derniers numéros: "
        if history:
            history_text += " → ".join([str(spin.number) for spin in history])
        self.history_text.insert(tk.END, history_text)

    def update_analysis(self):
        self.result_text.delete(1.0, tk.END)

        # Analyse de la force
        force_pattern = self.analyzer.analyze_force_pattern()

        # Prédiction de la zone
        likely_zone, _ = self.analyzer.predict_landing_zone()  # On ignore la confiance de la zone

        # Nouvelle prédiction du prochain numéro
        predicted_number, prediction_confidence = self.analyzer.predict_next_number()

        # Obtenir les top prédictions (limité à 4)
        top_predictions = self.analyzer.get_top_predictions(4)

        # Statistiques
        stats = self.analyzer.get_force_statistics()

        # Réinitialiser tous les boutons à leur style normal
        for num, btn in self.number_buttons.items():
            btn.configure(style='Normal.TButton')

        # Mettre en évidence les numéros prédits
        if top_predictions:
            for num, prob in top_predictions:
                if num in self.number_buttons:
                    self.number_buttons[num].configure(style='Predicted.TButton')

        # Mettre à jour le label de prédiction
        if predicted_number is not None:
            prediction_text = f"Prochain numéro prédit: {predicted_number} ({prediction_confidence:.2f}%)"
            self.prediction_label.configure(text=prediction_text)
        else:
            self.prediction_label.configure(text="Aucune prédiction disponible")

        # Affichage des résultats
        results = f"Force actuelle: {force_pattern['force']}\n"
        results += f"Confiance: {force_pattern['confidence']:.2f}%\n\n"

        # Affichage des prédictions
        results += "PRÉDICTIONS DES PROCHAINS NUMÉROS:\n"
        results += "--------------------------------\n"

        if predicted_number is not None:
            results += f"Meilleure prédiction: {predicted_number} (confiance: {prediction_confidence:.2f}%)\n\n"

        if top_predictions:
            results += "TOP 4 DES NUMÉROS PRÉDITS:\n"
            for i, (num, prob) in enumerate(top_predictions, 1):
                results += f"{i}. Numéro {num}: {prob:.2f}% de chance\n"
            results += "\n"

        results += f"Zone probable: {sorted(likely_zone)}\n\n"
        results += "Statistiques:\n"
        if stats:
            results += f"- Distance moyenne: {stats['avg_distance']:.2f}\n"
            results += f"- Écart-type distance: {stats['std_distance']:.2f}\n"
            results += f"- Temps moyen entre spins: {stats['avg_time']:.2f}s\n"

        self.result_text.insert(tk.END, results)

    def clear_history(self):
        self.analyzer = MachineRouletteAnalyzer()

        # Réinitialiser tous les boutons à leur style normal
        for num, btn in self.number_buttons.items():
            btn.configure(style='Normal.TButton')

        # Réinitialiser le label de prédiction
        self.prediction_label.configure(text="Aucune prédiction disponible")

        self.update_history()
        self.update_analysis()

    def save_history(self):
        history_data = []
        for spin in self.analyzer.history:
            history_data.append({
                'number': spin.number,
                'timestamp': spin.timestamp.isoformat(),
                'distance_from_prev': spin.distance_from_prev,
                'spin_time': spin.spin_time
            })

        with open('roulette_history.json', 'w') as f:
            json.dump(history_data, f)

    def load_history(self):
        try:
            with open('roulette_history.json', 'r') as f:
                history_data = json.load(f)

            self.analyzer = MachineRouletteAnalyzer()
            for spin in history_data:
                self.analyzer.add_spin(
                    spin['number'],
                    datetime.fromisoformat(spin['timestamp'])
                )

            self.update_history()
            self.update_analysis()
        except FileNotFoundError:
            pass

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = RouletteGUI()
    app.run()
