"""
Module d'optimisation XGBoost pour le projet Keno
Ce module fournit des fonctions pour optimiser les modèles XGBoost
pour la prédiction des numéros Keno.
"""

import os
import json
import numpy as np
import pandas as pd

# Charger les poids scale_pos_weight spécifiques à chaque numéro
DIRECT_SPW_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'direct_scale_pos_weight.json')
try:
    with open(DIRECT_SPW_FILE, 'r') as f:
        DIRECT_SPW_VALUES = json.load(f)
    print(f"Valeurs de scale_pos_weight chargées depuis {DIRECT_SPW_FILE}")
except Exception as e:
    print(f"Erreur lors du chargement des valeurs de scale_pos_weight: {e}")
    DIRECT_SPW_VALUES = {}
from sklearn.model_selection import train_test_split, GridSearchCV, RandomizedSearchCV
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, roc_auc_score, f1_score
from sklearn.preprocessing import StandardScaler
import warnings

# Essayer d'importer XGBoost
try:
    from xgboost import XGBClassifier
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("XGBoost n'est pas disponible. Veuillez l'installer avec 'pip install xgboost'.")

# Chemin du fichier de cache pour les paramètres optimaux
PARAMS_CACHE_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'xgboost_optimal_params.json')

class XGBoostOptimizer:
    """Classe pour optimiser les modèles XGBoost pour la prédiction Keno"""
    
    def __init__(self, use_gpu=False, n_jobs=-1, verbose=1):
        """
        Initialise l'optimiseur XGBoost
        
        Args:
            use_gpu (bool): Utiliser le GPU si disponible
            n_jobs (int): Nombre de jobs parallèles (-1 pour utiliser tous les cœurs)
            verbose (int): Niveau de verbosité (0-3)
        """
        self.use_gpu = use_gpu
        self.n_jobs = n_jobs
        self.verbose = verbose
        self.optimal_params = self._load_cached_params()
        
        # Vérifier si XGBoost est disponible
        if not XGBOOST_AVAILABLE:
            raise ImportError("XGBoost n'est pas disponible. Veuillez l'installer avec 'pip install xgboost'.")
    
    def _load_cached_params(self):
        """Charge les paramètres optimaux depuis le cache
        
        Returns:
            dict: Paramètres optimaux par numéro
        """
        try:
            if os.path.exists(PARAMS_CACHE_FILE):
                with open(PARAMS_CACHE_FILE, 'r') as f:
                    params = json.load(f)
                
                # Convertir les clés en entiers
                return {int(k): v for k, v in params.items()}
            else:
                return {}
        except Exception as e:
            print(f"Erreur lors du chargement des paramètres optimaux: {e}")
            return {}
    
    def _save_cached_params(self):
        """Sauvegarde les paramètres optimaux dans le cache"""
        try:
            # Créer le répertoire si nécessaire
            os.makedirs(os.path.dirname(PARAMS_CACHE_FILE), exist_ok=True)
            
            with open(PARAMS_CACHE_FILE, 'w') as f:
                json.dump(self.optimal_params, f, indent=2)
            
            if self.verbose > 0:
                print(f"Paramètres optimaux sauvegardés pour {len(self.optimal_params)} numéros")
        except Exception as e:
            print(f"Erreur lors de la sauvegarde des paramètres optimaux: {e}")
    
    def calculate_optimal_scale_pos_weight(self, y_train, num=None):
        """
        Calcule la valeur optimale de scale_pos_weight basée sur la distribution des classes
        
        Args:
            y_train: Étiquettes d'entraînement
            num: Numéro Keno (pour utiliser une valeur spécifique)
            
        Returns:
            float: Valeur optimale de scale_pos_weight
        """
        # Si un numéro est spécifié, utiliser la valeur spécifique
        if num is not None and str(num) in DIRECT_SPW_VALUES:
            specific_value = DIRECT_SPW_VALUES[str(num)]
            
            if self.verbose > 0:
                print(f"  Numéro {num}: Utilisation de scale_pos_weight spécifique = {specific_value}")
            
            return specific_value
        
        # Sinon, calculer normalement
        # Compter les exemples positifs et négatifs
        neg_count = np.sum(y_train == 0)
        pos_count = np.sum(y_train == 1)
        
        # Éviter la division par zéro
        if pos_count > 0:
            # Calculer le ratio
            ratio = neg_count / pos_count
            
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
                print(f"  Numéro {num}: Ratio calculé = {ratio:.4f}")
            
            return ratio
        else:
            # Valeur par défaut si aucun exemple positif
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Aucun exemple positif trouvé, utilisation de scale_pos_weight=1.0")
            
            return 1.0
    
    def optimize_scale_pos_weight(self, X_train, y_train, X_val, y_val, base_config, num):
        """
        Trouve la valeur optimale de scale_pos_weight pour un numéro spécifique
        
        Args:
            X_train: Données d'entraînement
            y_train: Étiquettes d'entraînement
            X_val: Données de validation
            y_val: Étiquettes de validation
            base_config: Configuration de base pour XGBoost
            num: Numéro Keno à optimiser
            
        Returns:
            float: Valeur optimale de scale_pos_weight
        """
        # Calculer le ratio de classe de base
        neg_count = np.sum(y_train == 0)
        pos_count = np.sum(y_train == 1)
        base_ratio = neg_count / max(1, pos_count)
        
        if self.verbose > 0:
            print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
            print(f"  Numéro {num}: Ratio de classe de base = {base_ratio:.4f}")
        
        # Créer une liste de valeurs à tester
        test_values = [
            0.5, 0.75, 1.0, 1.5, 2.0, 
            base_ratio * 0.5, base_ratio * 0.75, base_ratio, 
            base_ratio * 1.25, base_ratio * 1.5, base_ratio * 2.0
        ]
        # Éliminer les doublons et trier
        test_values = sorted(list(set([round(v, 2) for v in test_values])))
        
        if self.verbose > 0:
            print(f"  Numéro {num}: Optimisation de scale_pos_weight. Valeurs à tester: {test_values}")
        
        best_f1 = 0
        best_value = base_ratio
        results = []
        
        # Tester chaque valeur
        for spw in test_values:
            try:
                # Créer une copie de la configuration
                config = base_config.copy()
                config['scale_pos_weight'] = spw
                
                # Entraîner un modèle avec cette valeur
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    model = XGBClassifier(**config)
                    model.fit(X_train, y_train)
                
                # Évaluer sur l'ensemble de validation
                y_pred = model.predict(X_val)
                
                # Calculer les métriques
                precision, recall, f1, _ = precision_recall_fscore_support(
                    y_val, y_pred, average='binary', zero_division=0
                )
                
                results.append((spw, f1, precision, recall))
                
                if f1 > best_f1:
                    best_f1 = f1
                    best_value = spw
            except Exception as e:
                if self.verbose > 1:
                    print(f"  Erreur avec scale_pos_weight={spw}: {e}")
        
        # Trier et afficher les résultats
        results.sort(key=lambda x: x[1], reverse=True)
        
        if self.verbose > 0:
            print(f"  Numéro {num}: Résultats de l'optimisation de scale_pos_weight:")
            for spw, f1, precision, recall in results[:3]:
                print(f"    - scale_pos_weight={spw}: F1={f1:.4f}, Précision={precision:.4f}, Rappel={recall:.4f}")
            
            print(f"  Numéro {num}: Meilleure valeur de scale_pos_weight: {best_value} (F1={best_f1:.4f})")
        
        return best_value
    
    def optimize_hyperparameters(self, X, y, num, random_state=42, fast_mode=True):
        """
        Optimise les hyperparamètres XGBoost pour un numéro spécifique
        
        Args:
            X: Caractéristiques
            y: Étiquettes
            num: Numéro Keno
            random_state: Graine aléatoire
            fast_mode: Si True, utilise une recherche plus rapide
            
        Returns:
            dict: Paramètres optimaux
        """
        # Vérifier si nous avons déjà des paramètres optimaux pour ce numéro
        if num in self.optimal_params:
            if self.verbose > 0:
                print(f"  Numéro {num}: Utilisation des paramètres optimaux en cache")
            return self.optimal_params[num]
        
        # Diviser les données
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=random_state
        )
        
        # Calculer scale_pos_weight optimal
        scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train, num=num)
        
        if self.verbose > 0:
            print(f"  Numéro {num}: Optimisation des hyperparamètres XGBoost")
            print(f"  Numéro {num}: scale_pos_weight calculé = {scale_pos_weight:.4f}")
        
        # Paramètres de base
        base_params = {
            'objective': 'binary:logistic',
            'eval_metric': 'auc',
            'use_label_encoder': False,
            'verbosity': 0,
            'random_state': random_state,
            'n_jobs': self.n_jobs,
            'scale_pos_weight': scale_pos_weight
        }
        
        # Configurer l'accélération matérielle
        if self.use_gpu:
            try:
                base_params.update({
                    'tree_method': 'gpu_hist',
                    'gpu_id': 0,
                    'predictor': 'gpu_predictor'
                })
                if self.verbose > 0:
                    print("  Utilisation de l'accélération GPU pour XGBoost")
            except Exception as e:
                if self.verbose > 0:
                    print(f"  Erreur lors de la configuration GPU: {e}")
                base_params.update({
                    'tree_method': 'hist',
                    'predictor': 'cpu_predictor'
                })
        else:
            base_params.update({
                'tree_method': 'hist',
                'predictor': 'cpu_predictor'
            })
        
        # Définir l'espace de recherche
        if fast_mode:
            # Mode rapide: moins de paramètres à tester
            param_grid = {
                'n_estimators': [100, 200],
                'max_depth': [3, 5, 7],
                'learning_rate': [0.05, 0.1],
                'subsample': [0.7, 0.9],
                'colsample_bytree': [0.7, 0.9],
                'min_child_weight': [1, 3]
            }
            n_iter = 10
        else:
            # Mode complet: recherche plus exhaustive
            param_grid = {
                'n_estimators': [100, 200, 300, 500],
                'max_depth': [3, 5, 7, 9],
                'learning_rate': [0.01, 0.05, 0.1],
                'subsample': [0.6, 0.7, 0.8, 0.9],
                'colsample_bytree': [0.6, 0.7, 0.8, 0.9],
                'min_child_weight': [1, 3, 5],
                'gamma': [0, 0.1, 0.2]
            }
            n_iter = 20
        
        # Créer le modèle de base
        model = XGBClassifier(**base_params)
        
        # Utiliser RandomizedSearchCV pour trouver les meilleurs paramètres
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            
            search = RandomizedSearchCV(
                model, param_grid, n_iter=n_iter,
                scoring='f1', cv=3, random_state=random_state,
                n_jobs=1  # Utiliser 1 job car XGBoost utilise déjà le parallélisme
            )
            
            search.fit(X_train, y_train)
        
        # Récupérer les meilleurs paramètres
        best_params = search.best_params_
        
        # Ajouter les paramètres de base
        for key, value in base_params.items():
            if key not in best_params:
                best_params[key] = value
        
        if self.verbose > 0:
            print(f"  Numéro {num}: Meilleurs paramètres trouvés: {best_params}")
            print(f"  Numéro {num}: Meilleur score: {search.best_score_:.4f}")
        
        # Évaluer sur l'ensemble de test
        best_model = search.best_estimator_
        y_pred = best_model.predict(X_test)
        
        # Calculer les métriques
        accuracy = accuracy_score(y_test, y_pred)
        precision, recall, f1, _ = precision_recall_fscore_support(
            y_test, y_pred, average='binary', zero_division=0
        )
        
        if self.verbose > 0:
            print(f"  Numéro {num}: Performance sur l'ensemble de test:")
            print(f"    - Précision: {accuracy:.4f}")
            print(f"    - F1-score: {f1:.4f}")
            print(f"    - Précision: {precision:.4f}")
            print(f"    - Rappel: {recall:.4f}")
        
        # Stocker les paramètres optimaux
        self.optimal_params[num] = best_params
        
        # Sauvegarder les paramètres
        self._save_cached_params()
        
        return best_params
    
    def train_model(self, X, y, num, params=None, random_state=42, optimize=True, fast_mode=True):
        """
        Entraîne un modèle XGBoost pour un numéro spécifique
        
        Args:
            X: Caractéristiques
            y: Étiquettes
            num: Numéro Keno
            params: Paramètres XGBoost (si None, utilise les paramètres optimaux)
            random_state: Graine aléatoire
            optimize: Si True, optimise les paramètres
            fast_mode: Si True, utilise une optimisation plus rapide
            
        Returns:
            dict: Informations sur le modèle entraîné
        """
        # Diviser les données
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=random_state
        )
        
        # Obtenir les paramètres
        if params is None:
            if optimize:
                # Optimiser les paramètres
                params = self.optimize_hyperparameters(X, y, num, random_state, fast_mode)
            elif num in self.optimal_params:
                # Utiliser les paramètres en cache
                params = self.optimal_params[num]
            else:
                # Utiliser des paramètres par défaut
                scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train, num=num)
                
                params = {
                    'n_estimators': 200,
                    'max_depth': 6,
                    'learning_rate': 0.05,
                    'subsample': 0.8,
                    'colsample_bytree': 0.8,
                    'min_child_weight': 3,
                    'gamma': 0.1,
                    'scale_pos_weight': scale_pos_weight,
                    'objective': 'binary:logistic',
                    'eval_metric': 'auc',
                    'use_label_encoder': False,
                    'verbosity': 0,
                    'random_state': random_state,
                    'n_jobs': self.n_jobs
                }
                
                # Configurer l'accélération matérielle
                if self.use_gpu:
                    params.update({
                        'tree_method': 'gpu_hist',
                        'gpu_id': 0,
                        'predictor': 'gpu_predictor'
                    })
                else:
                    params.update({
                        'tree_method': 'hist',
                        'predictor': 'cpu_predictor'
                    })
        
        if self.verbose > 0:
            print(f"  Numéro {num}: Entraînement du modèle XGBoost avec les paramètres:")
            for key, value in params.items():
                print(f"    - {key}: {value}")
        
        # Entraîner le modèle
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            model = XGBClassifier(**params)
            model.fit(X_train, y_train)
        
        # Évaluer le modèle
        y_pred = model.predict(X_test)
        y_proba = model.predict_proba(X_test)[:, 1]
        
        # Calculer les métriques
        accuracy = accuracy_score(y_test, y_pred)
        precision, recall, f1, _ = precision_recall_fscore_support(
            y_test, y_pred, average='binary', zero_division=0
        )
        
        try:
            auc = roc_auc_score(y_test, y_proba)
        except:
            auc = 0.5
        
        # Analyser les caractéristiques importantes
        feature_importance = None
        if hasattr(model, 'feature_importances_'):
            feature_importance = model.feature_importances_
        
        # Créer le dictionnaire de résultats
        result = {
            'model': model,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'auc': auc,
            'params': params,
            'feature_importance': feature_importance
        }
        
        if self.verbose > 0:
            print(f"  Numéro {num}: Performance du modèle XGBoost:")
            print(f"    - Précision: {accuracy:.4f}")
            print(f"    - F1-score: {f1:.4f}")
            print(f"    - Précision: {precision:.4f}")
            print(f"    - Rappel: {recall:.4f}")
            print(f"    - AUC: {auc:.4f}")
        
        return result
    
    def analyze_feature_importance(self, model, feature_names):
        """
        Analyse l'importance des caractéristiques d'un modèle XGBoost
        
        Args:
            model: Modèle XGBoost
            feature_names: Noms des caractéristiques
            
        Returns:
            list: Liste des caractéristiques triées par importance
        """
        if not hasattr(model, 'feature_importances_'):
            return None
        
        # Obtenir l'importance des caractéristiques
        importances = model.feature_importances_
        
        # Créer un dictionnaire des importances
        feature_importance = dict(zip(feature_names, importances))
        
        # Trier par importance décroissante
        sorted_importance = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
        
        return sorted_importance
    
    def predict_with_model(self, model, X, threshold=0.5):
        """
        Fait une prédiction avec un modèle XGBoost
        
        Args:
            model: Modèle XGBoost
            X: Caractéristiques
            threshold: Seuil de décision
            
        Returns:
            tuple: (prédiction binaire, probabilité)
        """
        # Faire la prédiction
        proba = model.predict_proba(X)[:, 1]
        
        # Appliquer le seuil
        prediction = (proba >= threshold).astype(int)
        
        return prediction, proba
    
    def find_optimal_threshold(self, model, X_val, y_val):
        """
        Trouve le seuil optimal pour un modèle XGBoost
        
        Args:
            model: Modèle XGBoost
            X_val: Caractéristiques de validation
            y_val: Étiquettes de validation
            
        Returns:
            float: Seuil optimal
        """
        # Obtenir les probabilités
        y_proba = model.predict_proba(X_val)[:, 1]
        
        # Tester différents seuils
        thresholds = np.arange(0.1, 0.9, 0.05)
        best_f1 = 0
        best_threshold = 0.5
        
        for threshold in thresholds:
            # Appliquer le seuil
            y_pred = (y_proba >= threshold).astype(int)
            
            # Calculer le F1-score
            f1 = f1_score(y_val, y_pred, zero_division=0)
            
            if f1 > best_f1:
                best_f1 = f1
                best_threshold = threshold
        
        return best_threshold
