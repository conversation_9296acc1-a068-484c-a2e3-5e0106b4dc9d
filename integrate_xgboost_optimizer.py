"""
Script pour intégrer l'optimiseur XGBoost dans le projet Keno
Ce script modifie les fichiers existants pour intégrer l'optimiseur XGBoost.
"""

import os
import sys
import shutil
import re

def backup_file(file_path):
    """Crée une sauvegarde du fichier"""
    backup_path = file_path + '.bak'
    if os.path.exists(file_path):
        shutil.copy2(file_path, backup_path)
        print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def integrate_imports(file_path):
    """Intègre les imports de l'optimiseur XGBoost"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Chercher la section d'import de LightGBM
    lightgbm_import_pattern = r"# Essayer d'importer LightGBM.*?print\(.*?LightGBM.*?\)"
    lightgbm_import_match = re.search(lightgbm_import_pattern, content, re.DOTALL)
    
    if lightgbm_import_match:
        # Lire le contenu du fichier d'imports
        with open('keno_advanced_analyzer_imports.py', 'r', encoding='utf-8') as f:
            new_imports = f.read()
        
        # Remplacer la section d'import de LightGBM
        new_content = content.replace(lightgbm_import_match.group(0), new_imports)
        
        # Écrire le contenu modifié
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"Imports intégrés dans {file_path}")
        return True
    else:
        print(f"Section d'import de LightGBM non trouvée dans {file_path}")
        return False

def integrate_init(file_path):
    """Intègre l'initialisation de l'optimiseur XGBoost"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Chercher la section d'initialisation de LightGBM
    lightgbm_init_pattern = r"# Initialiser LightGBM si disponible\s+self\.lightgbm_available = LIGHTGBM_AVAILABLE"
    lightgbm_init_match = re.search(lightgbm_init_pattern, content)
    
    if lightgbm_init_match:
        # Lire le contenu du fichier d'initialisation
        with open('keno_advanced_analyzer_init.py', 'r', encoding='utf-8') as f:
            new_init = f.read()
        
        # Remplacer la section d'initialisation de LightGBM
        new_content = content.replace(lightgbm_init_match.group(0), new_init)
        
        # Écrire le contenu modifié
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"Initialisation intégrée dans {file_path}")
        return True
    else:
        print(f"Section d'initialisation de LightGBM non trouvée dans {file_path}")
        return False

def add_train_xgboost_method(file_path):
    """Ajoute la méthode train_xgboost_model"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Chercher la méthode set_hardware_acceleration
    set_hardware_pattern = r"def set_hardware_acceleration.*?return result"
    set_hardware_match = re.search(set_hardware_pattern, content, re.DOTALL)
    
    if set_hardware_match:
        # Lire le contenu du fichier de méthode
        with open('keno_advanced_analyzer_train_xgboost.py', 'r', encoding='utf-8') as f:
            new_method = f.read()
        
        # Trouver la fin de la méthode set_hardware_acceleration
        end_pos = set_hardware_match.end()
        
        # Insérer la nouvelle méthode après set_hardware_acceleration
        new_content = content[:end_pos] + "\n\n" + new_method + content[end_pos:]
        
        # Écrire le contenu modifié
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"Méthode train_xgboost_model ajoutée dans {file_path}")
        return True
    else:
        print(f"Méthode set_hardware_acceleration non trouvée dans {file_path}")
        return False

def replace_train_models(file_path):
    """Remplace la méthode train_models"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Chercher la méthode train_models
    train_models_pattern = r"def train_models\(self, num_range=None, force_retrain=False, check_stop_requested=None, fast_mode=False\):.*?return True"
    train_models_match = re.search(train_models_pattern, content, re.DOTALL)
    
    if train_models_match:
        # Lire le contenu du fichier de méthode
        with open('keno_advanced_analyzer_train_models.py', 'r', encoding='utf-8') as f:
            new_method = f.read()
        
        # Remplacer la méthode train_models
        new_content = content.replace(train_models_match.group(0), new_method)
        
        # Écrire le contenu modifié
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"Méthode train_models remplacée dans {file_path}")
        return True
    else:
        print(f"Méthode train_models non trouvée dans {file_path}")
        return False

def main():
    """Fonction principale"""
    # Fichier à modifier
    file_path = 'keno_advanced_analyzer.py'
    
    # Créer une sauvegarde
    backup_file(file_path)
    
    # Intégrer les modifications
    imports_ok = integrate_imports(file_path)
    init_ok = integrate_init(file_path)
    method_ok = add_train_xgboost_method(file_path)
    train_models_ok = replace_train_models(file_path)
    
    # Vérifier si toutes les modifications ont été appliquées
    if imports_ok and init_ok and method_ok and train_models_ok:
        print("\nToutes les modifications ont été appliquées avec succès!")
        print("L'optimiseur XGBoost est maintenant intégré dans le projet Keno.")
    else:
        print("\nCertaines modifications n'ont pas pu être appliquées.")
        print("Veuillez vérifier les messages d'erreur ci-dessus.")

if __name__ == "__main__":
    main()
