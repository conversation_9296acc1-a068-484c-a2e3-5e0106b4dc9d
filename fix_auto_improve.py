"""
Script pour modifier directement le code d'auto-amélioration et forcer des valeurs différentes de scale_pos_weight.
Cette solution cible spécifiquement le code qui s'exécute pendant l'auto-amélioration.
"""

import os
import sys
import re
import json
import shutil
import random
from datetime import datetime

def backup_file(file_path):
    """
    Crée une sauvegarde du fichier
    
    Args:
        file_path: Chemin du fichier à sauvegarder
        
    Returns:
        str: <PERSON>em<PERSON> de la sauvegarde
    """
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def find_auto_improve_files():
    """
    Trouve les fichiers liés à l'auto-amélioration
    
    Returns:
        list: Liste des fichiers trouvés
    """
    potential_files = [
        'keno_advanced_analyzer.py',
        'keno_auto_improve.py',
        'auto_improve.py',
        'keno_ml_trainer.py'
    ]
    
    found_files = []
    for file in potential_files:
        if os.path.exists(file):
            found_files.append(file)
    
    # Rechercher d'autres fichiers potentiels
    for file in os.listdir('.'):
        if file.endswith('.py') and 'auto' in file.lower() and 'improve' in file.lower() and file not in found_files:
            found_files.append(file)
    
    print(f"Fichiers liés à l'auto-amélioration trouvés: {found_files}")
    return found_files

def analyze_file_content(file_path):
    """
    Analyse le contenu d'un fichier pour trouver les sections pertinentes
    
    Args:
        file_path: Chemin du fichier à analyser
        
    Returns:
        dict: Informations sur le contenu du fichier
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Rechercher des patterns spécifiques
    patterns = {
        'auto_improve': r'auto[_-]?improve|amélioration\s+automatique',
        'scale_pos_weight': r'scale_pos_weight|class_weight|pos_weight',
        'xgboost': r'XGBoost|XGBClassifier|xgboost',
        'train_models': r'train_models|train_model|train_xgboost',
        'distribution': r'distribution|class_distribution|class_counts|class_weight'
    }
    
    results = {}
    for key, pattern in patterns.items():
        matches = re.findall(pattern, content, re.IGNORECASE)
        results[key] = len(matches)
    
    # Déterminer si le fichier est pertinent
    relevance_score = sum(results.values())
    results['relevance_score'] = relevance_score
    results['file_path'] = file_path
    
    print(f"Analyse de {file_path}: Score de pertinence = {relevance_score}")
    for key, value in results.items():
        if key != 'file_path' and key != 'relevance_score':
            print(f"  - {key}: {value} occurrences")
    
    return results

def find_most_relevant_file(files):
    """
    Trouve le fichier le plus pertinent pour la modification
    
    Args:
        files: Liste des fichiers à analyser
        
    Returns:
        str: Chemin du fichier le plus pertinent
    """
    if not files:
        return None
    
    analyses = [analyze_file_content(file) for file in files]
    analyses.sort(key=lambda x: x['relevance_score'], reverse=True)
    
    most_relevant = analyses[0]['file_path']
    print(f"Fichier le plus pertinent: {most_relevant} (score: {analyses[0]['relevance_score']})")
    
    return most_relevant

def generate_random_weights():
    """
    Génère des valeurs aléatoires de scale_pos_weight pour chaque numéro
    
    Returns:
        dict: Dictionnaire des valeurs par numéro
    """
    # Générer des valeurs aléatoires autour de 2.57
    values = {}
    for num in range(1, 71):
        # Générer une valeur entre 1.5 et 3.5
        value = 1.5 + random.random() * 2.0
        values[num] = round(value, 4)
    
    print("Valeurs de scale_pos_weight générées:")
    for num in range(1, 11):  # Afficher les 10 premières valeurs
        print(f"  Numéro {num}: {values[num]}")
    
    return values

def save_weights_to_json(weights):
    """
    Sauvegarde les poids dans un fichier JSON
    
    Args:
        weights: Dictionnaire des poids par numéro
        
    Returns:
        str: Chemin du fichier créé
    """
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Chemin du fichier
    file_path = os.path.join('data', 'auto_improve_spw.json')
    
    # Convertir les clés en chaînes pour la sérialisation JSON
    weights_str = {str(k): v for k, v in weights.items()}
    
    # Sauvegarder les poids
    with open(file_path, 'w') as f:
        json.dump(weights_str, f, indent=2)
    
    print(f"Poids sauvegardés dans {file_path}")
    
    return file_path

def inject_code_into_file(file_path, weights_file):
    """
    Injecte du code dans le fichier pour forcer des valeurs différentes de scale_pos_weight
    
    Args:
        file_path: Chemin du fichier à modifier
        weights_file: Chemin du fichier de poids
        
    Returns:
        bool: True si l'injection a réussi, False sinon
    """
    if not os.path.exists(file_path):
        print(f"Le fichier {file_path} n'existe pas")
        return False
    
    # Créer une sauvegarde
    backup_file(file_path)
    
    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Vérifier si le code a déjà été injecté
    if "# Code injecté pour forcer des valeurs différentes de scale_pos_weight" in content:
        print("Le code a déjà été injecté dans ce fichier")
        return True
    
    # Ajouter l'import du fichier de poids au début du fichier
    import_code = """import os
import json

# Code injecté pour forcer des valeurs différentes de scale_pos_weight
AUTO_IMPROVE_SPW_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'auto_improve_spw.json')
try:
    with open(AUTO_IMPROVE_SPW_FILE, 'r') as f:
        AUTO_IMPROVE_SPW_VALUES = json.load(f)
    print(f"Valeurs de scale_pos_weight chargées depuis {AUTO_IMPROVE_SPW_FILE}")
except Exception as e:
    print(f"Erreur lors du chargement des valeurs de scale_pos_weight: {e}")
    AUTO_IMPROVE_SPW_VALUES = {}

# Fonction pour obtenir la valeur de scale_pos_weight pour un numéro
def get_forced_scale_pos_weight(num):
    \"\"\"
    Récupère la valeur forcée de scale_pos_weight pour un numéro
    
    Args:
        num: Numéro Keno
        
    Returns:
        float: Valeur de scale_pos_weight
    \"\"\"
    # Convertir en chaîne
    num_str = str(num)
    
    # Récupérer la valeur
    if num_str in AUTO_IMPROVE_SPW_VALUES:
        return float(AUTO_IMPROVE_SPW_VALUES[num_str])
    
    # Valeur par défaut
    return 2.57

# Monkey patch pour XGBClassifier
try:
    from xgboost import XGBClassifier
    
    # Sauvegarder la méthode originale
    original_init = XGBClassifier.__init__
    
    def patched_init(self, *args, **kwargs):
        # Récupérer le numéro si disponible
        num = kwargs.pop('num', None)
        
        # Appeler la méthode originale
        original_init(self, *args, **kwargs)
        
        # Forcer scale_pos_weight si le numéro est disponible
        if num is not None:
            forced_spw = get_forced_scale_pos_weight(num)
            self.scale_pos_weight = forced_spw
            print(f"XGBClassifier: Numéro {num} - scale_pos_weight forcé à {forced_spw}")
    
    # Remplacer la méthode
    XGBClassifier.__init__ = patched_init
    
    print("XGBClassifier patché avec succès")
except Exception as e:
    print(f"Erreur lors du patch de XGBClassifier: {e}")

"""
    
    # Insérer le code au début du fichier, après les imports existants
    import_section_end = re.search(r'import.*?\n\n', content, re.DOTALL)
    if import_section_end:
        new_content = content[:import_section_end.end()] + import_code + content[import_section_end.end():]
    else:
        new_content = import_code + content
    
    # Rechercher les méthodes qui créent des modèles XGBoost
    xgboost_patterns = [
        r'(XGBClassifier\([^)]*\))',
        r'(XGBoost\([^)]*\))',
        r'(train_xgboost[^(]*\([^)]*\))'
    ]
    
    for pattern in xgboost_patterns:
        matches = re.findall(pattern, new_content)
        for match in matches:
            # Vérifier si le match contient déjà num=
            if 'num=' not in match and 'num =' not in match:
                # Ajouter num=current_num au modèle
                replacement = match.replace(')', ', num=current_num)')
                new_content = new_content.replace(match, replacement)
    
    # Rechercher les méthodes qui traitent un numéro spécifique
    num_patterns = [
        r'(def\s+train_models[^(]*\([^)]*\):)',
        r'(def\s+train_model[^(]*\([^)]*\):)',
        r'(def\s+train_xgboost[^(]*\([^)]*\):)',
        r'(def\s+auto_improve[^(]*\([^)]*\):)',
        r'(def\s+process_number[^(]*\([^)]*\):)'
    ]
    
    for pattern in num_patterns:
        matches = re.findall(pattern, new_content)
        for match in matches:
            # Ajouter une ligne pour stocker le numéro courant
            method_body = match + '\n        # Stocker le numéro courant pour le patch XGBClassifier\n        current_num = num'
            new_content = new_content.replace(match, method_body)
    
    # Écrire le contenu modifié
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Code injecté dans {file_path} avec succès")
    return True

def main():
    """Fonction principale"""
    print("Modification du code d'auto-amélioration pour forcer des valeurs différentes de scale_pos_weight")
    
    # Trouver les fichiers liés à l'auto-amélioration
    files = find_auto_improve_files()
    
    if not files:
        print("Aucun fichier lié à l'auto-amélioration trouvé")
        return
    
    # Trouver le fichier le plus pertinent
    most_relevant = find_most_relevant_file(files)
    
    if not most_relevant:
        print("Aucun fichier pertinent trouvé")
        return
    
    # Générer des valeurs aléatoires
    weights = generate_random_weights()
    
    # Sauvegarder les poids dans un fichier JSON
    weights_file = save_weights_to_json(weights)
    
    # Injecter du code dans le fichier
    success = inject_code_into_file(most_relevant, weights_file)
    
    if success:
        print("\nModification terminée!")
        print("Le code d'auto-amélioration a été modifié pour forcer des valeurs différentes de scale_pos_weight.")
        print("Veuillez redémarrer votre application pour que les modifications prennent effet.")
    else:
        print("\nÉchec de la modification")
        print("Veuillez vérifier manuellement les fichiers et appliquer les modifications nécessaires.")

if __name__ == "__main__":
    main()
