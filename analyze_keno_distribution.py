"""
Script pour analyser la distribution des numéros Keno dans les données
Ce script calcule la fréquence d'apparition de chaque numéro et le ratio négatif/positif.
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter

# Essayer d'importer le gestionnaire de données Keno
try:
    from keno_data import KenoDataManager
    print("Module de gestion des données Keno importé avec succès")
except ImportError:
    print("Erreur: Module de gestion des données Keno non disponible")
    sys.exit(1)

def analyze_distribution():
    """Analyse la distribution des numéros dans les données Keno"""
    # Créer le gestionnaire de données
    data_manager = KenoDataManager()
    
    # Charger les données
    print("Chargement des données Keno...")
    data_loaded = data_manager.load_data()
    
    if not data_loaded or not data_manager.draws:
        print("Erreur: Impossible de charger les données Keno")
        return
    
    print(f"Données chargées: {len(data_manager.draws)} tirages")
    
    # Analyser la distribution des numéros
    print("Analyse de la distribution des numéros...")
    
    # Compter les occurrences de chaque numéro
    number_counts = Counter()
    total_draws = len(data_manager.draws)
    
    for draw in data_manager.draws:
        if hasattr(draw, 'draw_numbers') and draw.draw_numbers:
            for num in draw.draw_numbers:
                number_counts[num] += 1
    
    # Calculer les statistiques pour chaque numéro
    stats = []
    for num in range(1, data_manager.max_number + 1):
        occurrences = number_counts.get(num, 0)
        frequency = occurrences / total_draws
        pos_count = occurrences
        neg_count = total_draws - occurrences
        ratio = neg_count / max(1, pos_count)  # Éviter division par zéro
        
        stats.append({
            'number': num,
            'occurrences': occurrences,
            'frequency': frequency,
            'pos_count': pos_count,
            'neg_count': neg_count,
            'neg_pos_ratio': ratio,
            'scale_pos_weight': ratio
        })
    
    # Créer un DataFrame avec les statistiques
    stats_df = pd.DataFrame(stats)
    
    # Afficher les statistiques
    print("\nStatistiques de distribution des numéros Keno:")
    print(f"Nombre total de tirages: {total_draws}")
    print(f"Fréquence moyenne d'apparition: {stats_df['frequency'].mean():.4f}")
    print(f"Ratio négatif/positif moyen: {stats_df['neg_pos_ratio'].mean():.4f}")
    print(f"Écart-type du ratio: {stats_df['neg_pos_ratio'].std():.4f}")
    print(f"Ratio minimum: {stats_df['neg_pos_ratio'].min():.4f} (numéro {stats_df.loc[stats_df['neg_pos_ratio'].idxmin()]['number']:.0f})")
    print(f"Ratio maximum: {stats_df['neg_pos_ratio'].max():.4f} (numéro {stats_df.loc[stats_df['neg_pos_ratio'].idxmax()]['number']:.0f})")
    
    # Vérifier si tous les ratios sont identiques
    if stats_df['neg_pos_ratio'].std() < 0.01:
        print("\nATTENTION: Tous les numéros ont pratiquement le même ratio négatif/positif!")
        print("Cela suggère un problème dans les données ou dans leur prétraitement.")
    
    # Sauvegarder les statistiques dans un fichier CSV
    output_file = 'keno_number_distribution.csv'
    stats_df.to_csv(output_file, index=False)
    print(f"\nStatistiques sauvegardées dans {output_file}")
    
    # Créer un graphique de la distribution
    plt.figure(figsize=(12, 6))
    
    # Graphique des fréquences
    plt.subplot(1, 2, 1)
    plt.bar(stats_df['number'], stats_df['frequency'])
    plt.axhline(y=stats_df['frequency'].mean(), color='r', linestyle='-', label=f'Moyenne ({stats_df["frequency"].mean():.4f})')
    plt.xlabel('Numéro Keno')
    plt.ylabel('Fréquence d\'apparition')
    plt.title('Fréquence d\'apparition de chaque numéro Keno')
    plt.legend()
    
    # Graphique des ratios négatif/positif
    plt.subplot(1, 2, 2)
    plt.bar(stats_df['number'], stats_df['neg_pos_ratio'])
    plt.axhline(y=stats_df['neg_pos_ratio'].mean(), color='r', linestyle='-', label=f'Moyenne ({stats_df["neg_pos_ratio"].mean():.4f})')
    plt.xlabel('Numéro Keno')
    plt.ylabel('Ratio négatif/positif')
    plt.title('Ratio négatif/positif pour chaque numéro Keno')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig('keno_distribution.png')
    print("Graphique sauvegardé dans keno_distribution.png")
    
    return stats_df

if __name__ == "__main__":
    analyze_distribution()
