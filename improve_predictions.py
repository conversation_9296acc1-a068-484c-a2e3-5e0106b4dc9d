#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module pour améliorer les prédictions Keno
Ce module optimise les modèles XGBoost et ajoute des fonctionnalités d'apprentissage profond
pour améliorer la précision des prédictions.
"""

import os
import sys
import numpy as np
import pandas as pd
import json
import time
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, roc_auc_score

# Ajouter le répertoire courant au chemin de recherche Python
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, BASE_DIR)

# Importer les modules nécessaires
from keno_data import KenoDataManager
from keno_advanced_analyzer import KenoAdvancedAnalyzer
from keno_xgboost_optimizer import XGBoostOptimizer

# Supprimer les avertissements
import warnings
warnings.filterwarnings('ignore')

# Supprimer les avertissements de TensorFlow
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR

# Essayer d'importer TensorFlow
try:
    import tensorflow as tf
    # Désactiver les messages de log verbeux de TensorFlow
    tf.get_logger().setLevel('ERROR')
    # Désactiver les opérations oneDNN personnalisées
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("TensorFlow n'est pas disponible. Les fonctionnalités de deep learning seront désactivées.")

# Essayer d'importer XGBoost
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("XGBoost n'est pas disponible. Les fonctionnalités XGBoost seront désactivées.")

# Essayer d'importer LightGBM
try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("LightGBM n'est pas disponible. Les fonctionnalités LightGBM seront désactivées.")

def optimize_xgboost_params():
    """Optimise les paramètres XGBoost pour tous les numéros"""
    print("Optimisation des paramètres XGBoost pour tous les numéros...")

    # Charger les données
    data_manager = KenoDataManager()
    data_file = os.path.join(BASE_DIR, "data", "datafull.keno")

    # Vérifier si le fichier existe
    if not os.path.exists(data_file):
        print(f"Fichier de données {data_file} introuvable.")
        return None

    # Charger les données avec la méthode load_csv
    data_manager.load_csv(data_file, clear_existing=True)

    # Créer l'analyseur avancé
    analyzer = KenoAdvancedAnalyzer(data_manager)

    # Préparer les données
    analyzer.prepare_data()

    # Créer l'optimiseur XGBoost
    use_gpu = True  # Utiliser le GPU si disponible
    optimizer = XGBoostOptimizer(use_gpu=use_gpu, n_jobs=-1, verbose=1)

    # Optimiser les paramètres pour chaque numéro
    for num in range(1, data_manager.max_number + 1):
        print(f"\nOptimisation des paramètres pour le numéro {num}...")

        # Préparer les données pour ce numéro
        X, y = analyzer.prepare_features_for_number(num)

        # Optimiser les paramètres
        params = optimizer.optimize_hyperparameters(X, y, num, fast_mode=False)

        print(f"Paramètres optimaux pour le numéro {num}: {params}")

    print("\nOptimisation des paramètres XGBoost terminée.")

def train_deep_learning_models():
    """Entraîne des modèles d'apprentissage profond pour tous les numéros"""
    if not TENSORFLOW_AVAILABLE:
        print("TensorFlow n'est pas disponible. Impossible d'entraîner des modèles d'apprentissage profond.")
        return

    print("Entraînement des modèles d'apprentissage profond pour tous les numéros...")

    # Charger les données
    data_manager = KenoDataManager()
    data_file = os.path.join(BASE_DIR, "data", "datafull.keno")

    # Vérifier si le fichier existe
    if not os.path.exists(data_file):
        print(f"Fichier de données {data_file} introuvable.")
        return None

    # Charger les données avec la méthode load_csv
    data_manager.load_csv(data_file, clear_existing=True)

    # Créer l'analyseur avancé
    analyzer = KenoAdvancedAnalyzer(data_manager)

    # Préparer les données
    analyzer.prepare_data()

    # Entraîner les modèles pour chaque numéro
    for num in range(1, data_manager.max_number + 1):
        print(f"\nEntraînement du modèle d'apprentissage profond pour le numéro {num}...")

        # Entraîner le modèle
        analyzer.train_deep_learning_model(num)

    print("\nEntraînement des modèles d'apprentissage profond terminé.")

def run_auto_improve(timeout=7200, fast_mode=False, ultra_fast=False, max_numbers=None, resume=True):
    """
    Exécute l'auto-amélioration avec des paramètres optimisés

    Args:
        timeout (int): Temps maximum en secondes pour l'entraînement (défaut: 2 heures)
        fast_mode (bool): Si True, utilise le mode rapide pour l'entraînement
        ultra_fast (bool): Si True, utilise le mode ultra-rapide pour l'entraînement
        max_numbers (int): Nombre maximum de numéros à entraîner
        resume (bool): Si True, reprend l'analyse en ignorant les numéros déjà traités
    """
    print(f"Exécution de l'auto-amélioration avec timeout={timeout}s, fast_mode={fast_mode}, ultra_fast={ultra_fast}...")

    # Charger les données
    data_manager = KenoDataManager()

    # Essayer d'abord le fichier CSV
    csv_file = os.path.join(BASE_DIR, "data", "datafull.csv")
    if os.path.exists(csv_file):
        print(f"Chargement des données depuis le fichier CSV: {csv_file}")
        try:
            data_manager.load_csv(csv_file, clear_existing=True)
        except Exception as e:
            print(f"Erreur lors du chargement du fichier CSV: {e}")
            # Essayer le fichier .keno
            data_file = os.path.join(BASE_DIR, "data", "datafull.keno")
            if os.path.exists(data_file):
                print(f"Chargement des données depuis le fichier .keno: {data_file}")
                try:
                    data_manager.load_csv(data_file, clear_existing=True)
                except Exception as e:
                    print(f"Erreur lors du chargement du fichier .keno: {e}")
                    print("Impossible de charger les données. Création de données factices...")
                    create_dummy_data(data_manager)
            else:
                print("Fichier .keno introuvable. Création de données factices...")
                create_dummy_data(data_manager)
    else:
        # Essayer le fichier .keno
        data_file = os.path.join(BASE_DIR, "data", "datafull.keno")
        if os.path.exists(data_file):
            print(f"Chargement des données depuis le fichier .keno: {data_file}")
            try:
                data_manager.load_csv(data_file, clear_existing=True)
            except Exception as e:
                print(f"Erreur lors du chargement du fichier .keno: {e}")
                print("Impossible de charger les données. Création de données factices...")
                create_dummy_data(data_manager)
        else:
            print("Aucun fichier de données trouvé. Création de données factices...")
            create_dummy_data(data_manager)

    # Vérifier qu'il y a suffisamment de données
    if len(data_manager.draws) < 50:
        print(f"Pas assez de données pour l'amélioration automatique (minimum 50 tirages)")
        return {'success': False, 'message': "Pas assez de données pour l'amélioration automatique (minimum 50 tirages)"}

    # Créer l'analyseur avancé
    analyzer = KenoAdvancedAnalyzer(data_manager)

    # Définir le nombre de cœurs CPU à utiliser
    analyzer.n_jobs = -1  # Utiliser tous les cœurs disponibles

    # Activer l'auto-amélioration
    analyzer.auto_improve_enabled = True

    # Configurer l'accélération matérielle
    try:
        # Vérifier si CUDA est disponible
        import xgboost as xgb
        try:
            # Essayer de créer un DMatrix avec GPU
            test_data = xgb.DMatrix(np.random.rand(10, 10), label=np.random.rand(10))
            test_param = {'tree_method': 'gpu_hist'}
            test_bst = xgb.train(test_param, test_data, num_boost_round=1)
            analyzer.cuda_available = True
            analyzer.hardware_acceleration = 'gpu'
            print("Accélération GPU activée pour XGBoost")
        except Exception as e:
            print(f"GPU non disponible pour XGBoost: {e}")
            analyzer.cuda_available = False
            analyzer.hardware_acceleration = 'cpu'
    except ImportError:
        print("XGBoost non disponible")
        analyzer.cuda_available = False
        analyzer.hardware_acceleration = 'cpu'

    # Exécuter l'auto-amélioration
    result = analyzer.auto_improve(
        callback=None,
        fast_mode=fast_mode,
        timeout=timeout,
        ultra_fast=ultra_fast,
        max_numbers=max_numbers,
        resume=resume
    )

    print(f"\nRésultat de l'auto-amélioration: {result}")

    return result

def create_dummy_data(data_manager):
    """
    Crée des données factices pour permettre à l'application de démarrer

    Args:
        data_manager (KenoDataManager): Gestionnaire de données Keno
    """
    print("Création de données factices pour permettre à l'application de démarrer...")

    import random
    import datetime

    # Générer 100 tirages aléatoires
    for i in range(1, 101):
        # Générer une date aléatoire dans les 6 derniers mois
        days_ago = random.randint(1, 180)
        date = (datetime.datetime.now() - datetime.timedelta(days=days_ago)).strftime('%Y-%m-%d %H:%M:%S')

        # Générer un numéro de tirage
        draw_number = 2023000 + i

        # Générer 20 numéros aléatoires entre 1 et 70
        numbers = [random.randint(1, 70) for _ in range(20)]

        # Générer un multiplicateur aléatoire
        multiplier = random.choice([1, 2, 3, 5, 10])

        # Créer le tirage
        draw = {
            'date': date,
            'draw_number': draw_number,
            'numbers': numbers,
            'multiplier': multiplier
        }

        # Ajouter le tirage
        data_manager.add_draw(draw)

    print(f"Données factices créées: {len(data_manager.draws)} tirages")

    # Sauvegarder les données
    csv_file = os.path.join(BASE_DIR, "data", "datafull.csv")
    data_manager.save_csv(csv_file)
    print(f"Données factices sauvegardées dans {csv_file}")

    print("ATTENTION: Ces données sont générées aléatoirement et ne reflètent pas les tirages réels.")

if __name__ == "__main__":
    # Exécuter l'auto-amélioration avec des paramètres optimisés
    run_auto_improve(timeout=7200, fast_mode=False, ultra_fast=False, max_numbers=None, resume=True)
