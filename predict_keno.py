#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script pour faire des prédictions Keno avec les modèles entraînés
"""

import os
import sys
import argparse
import joblib
import json
from datetime import datetime
from keno_data import KenoDataManager
from keno_ml_trainer import KenoAdvancedAnalyzer

def parse_arguments():
    """Parse les arguments de ligne de commande"""
    parser = argparse.ArgumentParser(description="Fait des prédictions Keno avec les modèles entraînés")
    
    parser.add_argument("database", help="Chemin vers la base de données Keno (.keno)")
    parser.add_argument("--model", default="ml_models/best_model.pkl", help="Chemin vers le modèle à utiliser (défaut: ml_models/best_model.pkl)")
    parser.add_argument("--count", type=int, default=10, help="Nombre de numéros à prédire (défaut: 10)")
    parser.add_argument("--output", help="Fichier de sortie pour les prédictions (défaut: prediction_YYYYMMDD_HHMMSS.txt)")
    
    return parser.parse_args()

def main():
    """Fonction principale"""
    args = parse_arguments()
    
    # Vérifier que les fichiers existent
    if not os.path.exists(args.database):
        print(f"Erreur: Le fichier de base de données {args.database} n'existe pas")
        return 1
        
    if not os.path.exists(args.model):
        print(f"Erreur: Le fichier de modèle {args.model} n'existe pas")
        return 1
    
    # Définir le fichier de sortie
    if not args.output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output = f"prediction_{timestamp}.txt"
    
    print(f"=== Prédiction Keno ===")
    print(f"Base de données: {args.database}")
    print(f"Modèle: {args.model}")
    print(f"Nombre de numéros: {args.count}")
    print(f"Fichier de sortie: {args.output}")
    print("=" * 30)
    
    # Charger la base de données
    data_manager = KenoDataManager()
    success = data_manager.load_database(args.database)
    
    if not success:
        print(f"Erreur lors du chargement de la base de données")
        return 1
    
    print(f"Base de données chargée: {len(data_manager.draws)} tirages")
    
    # Créer l'analyseur
    analyzer = KenoAdvancedAnalyzer(data_manager)
    analyzer.prepare_data()
    
    # Charger le modèle
    success = analyzer.load_models(args.model)
    
    if not success:
        print(f"Erreur lors du chargement du modèle")
        return 1
    
    print(f"Modèle chargé avec succès")
    
    # Faire une prédiction
    print("\nCalcul des prédictions...")
    
    # Simuler une prédiction ML
    predictions = []
    for num in range(1, data_manager.max_number + 1):
        if num not in analyzer.models['targets']:
            continue
            
        target_info = analyzer.models['targets'][num]
        feature_info = analyzer.models['features'][num]
        model = target_info['model']
        scaler = feature_info['scaler']
        feature_cols = feature_info['feature_cols']
        
        # Préparer les caractéristiques pour ce numéro
        last_row = analyzer.df.iloc[-1].copy()
        features = {}
        for col in feature_cols:
            if col in last_row:
                features[col] = last_row[col]
            else:
                features[col] = 0  # Valeur par défaut si la colonne n'existe pas
        
        # Convertir en DataFrame
        import pandas as pd
        X = pd.DataFrame([features])
        
        # S'assurer que toutes les colonnes nécessaires sont présentes
        for col in feature_cols:
            if col not in X.columns:
                X[col] = 0
        
        # Réorganiser les colonnes pour correspondre à l'ordre attendu par le scaler
        X = X[feature_cols]
        
        # Normaliser
        X_scaled = scaler.transform(X)
        
        # Prédire
        prob = model.predict_proba(X_scaled)[0][1]  # Probabilité de la classe 1 (numéro présent)
        
        predictions.append({
            'number': num,
            'probability': prob,
            'model_accuracy': target_info['accuracy'],
            'model_type': target_info['type']
        })
    
    # Trier par probabilité décroissante
    predictions.sort(key=lambda x: x['probability'], reverse=True)
    
    # Sélectionner les N premiers numéros
    selected = predictions[:args.count]
    
    # Afficher les prédictions
    print(f"\nPrédiction des {args.count} prochains numéros:")
    for i, pred in enumerate(selected):
        print(f"  {i+1}. Numéro {pred['number']} - Probabilité: {pred['probability']:.4f} - Précision du modèle: {pred['model_accuracy']:.4f}")
    
    # Sauvegarder les prédictions
    with open(args.output, 'w') as f:
        f.write(f"Prédiction Keno - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Base de données: {args.database}\n")
        f.write(f"Modèle: {args.model}\n")
        f.write(f"Dernier tirage: {data_manager.draws[-1].draw_date.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write(f"Prédiction des {args.count} prochains numéros:\n")
        for i, pred in enumerate(selected):
            f.write(f"{i+1}. Numéro {pred['number']} - Probabilité: {pred['probability']:.4f} - Précision: {pred['model_accuracy']:.4f}\n")
        
        # Ajouter la liste simple des numéros
        f.write("\nListe des numéros prédits:\n")
        f.write(", ".join(str(pred['number']) for pred in selected))
    
    print(f"\nPrédictions sauvegardées dans {args.output}")
    return 0

if __name__ == "__main__":
    sys.exit(main())
