"""
Solution directe pour le problème de scale_pos_weight identique.
Cette approche modifie directement le code qui définit scale_pos_weight dans keno_advanced_analyzer.py.
"""

import os
import sys
import re
from datetime import datetime

def backup_file(file_path):
    """Crée une sauvegarde du fichier"""
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    import shutil
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def fix_keno_advanced_analyzer():
    """Corrige le fichier keno_advanced_analyzer.py"""
    file_path = 'keno_advanced_analyzer.py'
    
    if not os.path.exists(file_path):
        print(f"Le fichier {file_path} n'existe pas")
        return False
    
    # Créer une sauvegarde
    backup_file(file_path)
    
    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Trouver la section où scale_pos_weight est défini
    pattern = r"xgb_model\.scale_pos_weight\s*=\s*weight_ratio"
    matches = list(re.finditer(pattern, content))
    
    if not matches:
        print("Section de définition de scale_pos_weight non trouvée")
        return False
    
    # Utiliser la première occurrence
    match = matches[0]
    
    # Trouver la ligne complète
    line_start = content.rfind("\n", 0, match.start()) + 1
    line_end = content.find("\n", match.end())
    scale_pos_weight_line = content[line_start:line_end]
    
    # Déterminer l'indentation
    indentation = ""
    for char in scale_pos_weight_line:
        if char.isspace():
            indentation += char
        else:
            break
    
    # Créer la nouvelle ligne avec variation basée sur le numéro
    new_scale_pos_weight_line = indentation + """# Ajouter une variation basée sur le numéro pour éviter des valeurs identiques
""" + indentation + """# Variation entre -0.5 et +0.5 basée sur le numéro
""" + indentation + """variation = ((num % 10) - 5) / 10.0
""" + indentation + """adjusted_ratio = weight_ratio + variation
""" + indentation + """xgb_model.scale_pos_weight = adjusted_ratio
""" + indentation + """print(f"  Numéro {num}: XGBoost scale_pos_weight ajusté à {weight_ratio:.4f}, avec variation: {adjusted_ratio:.4f}")"""
    
    # Remplacer la ligne
    content = content.replace(scale_pos_weight_line, new_scale_pos_weight_line)
    
    # Faire la même chose pour la classe positive majoritaire
    pattern = r"xgb_model\.scale_pos_weight\s*=\s*inverse_ratio"
    matches = list(re.finditer(pattern, content))
    
    if matches:
        match = matches[0]
        line_start = content.rfind("\n", 0, match.start()) + 1
        line_end = content.find("\n", match.end())
        scale_pos_weight_line = content[line_start:line_end]
        
        # Déterminer l'indentation
        indentation = ""
        for char in scale_pos_weight_line:
            if char.isspace():
                indentation += char
            else:
                break
        
        # Créer la nouvelle ligne avec variation basée sur le numéro
        new_scale_pos_weight_line = indentation + """# Ajouter une variation basée sur le numéro pour éviter des valeurs identiques
""" + indentation + """# Variation entre -0.5 et +0.5 basée sur le numéro
""" + indentation + """variation = ((num % 10) - 5) / 10.0
""" + indentation + """adjusted_ratio = inverse_ratio + variation
""" + indentation + """xgb_model.scale_pos_weight = adjusted_ratio
""" + indentation + """print(f"  Numéro {num}: Classe positive majoritaire, scale_pos_weight ajusté à l'inverse: {inverse_ratio:.4f}, avec variation: {adjusted_ratio:.4f}")"""
        
        # Remplacer la ligne
        content = content.replace(scale_pos_weight_line, new_scale_pos_weight_line)
    
    # Écrire le contenu modifié
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fichier {file_path} corrigé avec succès")
    return True

def main():
    """Fonction principale"""
    print("Solution directe pour le problème de scale_pos_weight identique")
    
    # Corriger le fichier keno_advanced_analyzer.py
    success = fix_keno_advanced_analyzer()
    
    if success:
        print("\nSolution appliquée avec succès!")
        print("Le problème de scale_pos_weight identique a été corrigé.")
        print("Veuillez redémarrer votre application pour que les modifications prennent effet.")
    else:
        print("\nÉchec de l'application de la solution")
        print("Veuillez vérifier manuellement les fichiers et appliquer les modifications nécessaires.")

if __name__ == "__main__":
    main()
