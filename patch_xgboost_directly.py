"""
Script pour modifier directement le fichier XGBoost dans l'environnement Python.
Cette solution est radicale mais devrait fonctionner même si toutes les autres ont échoué.
"""

import os
import sys
import re
import json
import shutil
import random
from datetime import datetime

def find_xgboost_file():
    """
    Trouve le fichier XGBoost dans l'environnement Python
    
    Returns:
        str: Chemin du fichier XGBoost ou None si non trouvé
    """
    # Obtenir le chemin de l'environnement Python
    python_path = sys.executable
    python_dir = os.path.dirname(python_path)
    
    # Chemins possibles pour XGBoost
    possible_paths = [
        os.path.join(python_dir, 'Lib', 'site-packages', 'xgboost'),
        os.path.join(python_dir, 'Lib', 'site-packages', 'xgboost', 'sklearn.py'),
        os.path.join(python_dir, '..', 'Lib', 'site-packages', 'xgboost'),
        os.path.join(python_dir, '..', 'Lib', 'site-packages', 'xgboost', 'sklearn.py')
    ]
    
    # Vérifier chaque chemin
    for path in possible_paths:
        if os.path.exists(path):
            if os.path.isdir(path):
                # Si c'est un répertoire, chercher le fichier sklearn.py
                sklearn_path = os.path.join(path, 'sklearn.py')
                if os.path.exists(sklearn_path):
                    return sklearn_path
            else:
                return path
    
    # Chercher dans sys.path
    for path in sys.path:
        xgboost_dir = os.path.join(path, 'xgboost')
        if os.path.isdir(xgboost_dir):
            sklearn_path = os.path.join(xgboost_dir, 'sklearn.py')
            if os.path.exists(sklearn_path):
                return sklearn_path
    
    return None

def backup_file(file_path):
    """
    Crée une sauvegarde du fichier
    
    Args:
        file_path: Chemin du fichier à sauvegarder
        
    Returns:
        str: Chemin de la sauvegarde
    """
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def generate_random_weights():
    """
    Génère des valeurs aléatoires de scale_pos_weight pour chaque numéro
    
    Returns:
        dict: Dictionnaire des valeurs par numéro
    """
    # Générer des valeurs aléatoires autour de 2.57
    values = {}
    for num in range(1, 71):
        # Générer une valeur entre 1.5 et 3.5
        value = 1.5 + random.random() * 2.0
        values[num] = round(value, 4)
    
    print("Valeurs de scale_pos_weight générées:")
    for num in range(1, 11):  # Afficher les 10 premières valeurs
        print(f"  Numéro {num}: {values[num]}")
    
    return values

def save_weights_to_json(weights):
    """
    Sauvegarde les poids dans un fichier JSON
    
    Args:
        weights: Dictionnaire des poids par numéro
        
    Returns:
        str: Chemin du fichier créé
    """
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Chemin du fichier
    file_path = os.path.join('data', 'xgboost_patch_spw.json')
    
    # Convertir les clés en chaînes pour la sérialisation JSON
    weights_str = {str(k): v for k, v in weights.items()}
    
    # Sauvegarder les poids
    with open(file_path, 'w') as f:
        json.dump(weights_str, f, indent=2)
    
    print(f"Poids sauvegardés dans {file_path}")
    
    return file_path

def patch_xgboost_file(file_path, weights_file):
    """
    Modifie le fichier XGBoost pour forcer des valeurs différentes de scale_pos_weight
    
    Args:
        file_path: Chemin du fichier XGBoost
        weights_file: Chemin du fichier de poids
        
    Returns:
        bool: True si la modification a réussi, False sinon
    """
    if not os.path.exists(file_path):
        print(f"Le fichier {file_path} n'existe pas")
        return False
    
    # Créer une sauvegarde
    backup_file(file_path)
    
    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Vérifier si le fichier a déjà été patché
    if "# Patched for Keno scale_pos_weight" in content:
        print("Le fichier a déjà été patché")
        return True
    
    # Trouver la classe XGBClassifier
    xgb_class_match = re.search(r'class\s+XGBClassifier\([^)]*\):', content)
    if not xgb_class_match:
        print("Classe XGBClassifier non trouvée")
        return False
    
    # Trouver la méthode __init__
    init_method_match = re.search(r'def\s+__init__\s*\([^)]*\):', content[xgb_class_match.start():])
    if not init_method_match:
        print("Méthode __init__ non trouvée")
        return False
    
    # Position de la méthode __init__
    init_pos = xgb_class_match.start() + init_method_match.start()
    
    # Trouver la fin de la méthode __init__
    next_method_match = re.search(r'\n\s+def\s+', content[init_pos:])
    if not next_method_match:
        print("Fin de la méthode __init__ non trouvée")
        return False
    
    # Position de la fin de la méthode __init__
    init_end_pos = init_pos + next_method_match.start()
    
    # Extraire la méthode __init__
    init_method = content[init_pos:init_end_pos]
    
    # Trouver l'indentation
    indent_match = re.search(r'\n(\s+)', init_method)
    if not indent_match:
        print("Indentation non trouvée")
        return False
    
    indent = indent_match.group(1)
    
    # Créer le code à injecter
    inject_code = f"""
{indent}# Patched for Keno scale_pos_weight
{indent}import os
{indent}import json
{indent}
{indent}# Charger les valeurs de scale_pos_weight
{indent}SPW_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', '..', '..', 'data', 'xgboost_patch_spw.json')
{indent}try:
{indent}    with open(SPW_FILE, 'r') as f:
{indent}        SPW_VALUES = json.load(f)
{indent}    print(f"XGBoost: Valeurs de scale_pos_weight chargées depuis {{SPW_FILE}}")
{indent}except Exception as e:
{indent}    print(f"XGBoost: Erreur lors du chargement des valeurs de scale_pos_weight: {{e}}")
{indent}    SPW_VALUES = {{}}
{indent}
{indent}# Récupérer le numéro Keno s'il est disponible
{indent}num = kwargs.pop('num', None)
{indent}if num is not None and str(num) in SPW_VALUES:
{indent}    # Forcer scale_pos_weight pour ce numéro
{indent}    kwargs['scale_pos_weight'] = float(SPW_VALUES[str(num)])
{indent}    print(f"XGBoost: Numéro {{num}} - scale_pos_weight forcé à {{kwargs['scale_pos_weight']}}")
"""
    
    # Trouver où injecter le code
    # Chercher la ligne après les paramètres
    params_end_match = re.search(r'(\n\s+)(?:self\.|super\()', init_method)
    if not params_end_match:
        print("Fin des paramètres non trouvée")
        return False
    
    # Position où injecter le code
    inject_pos = init_pos + params_end_match.start()
    
    # Injecter le code
    new_content = content[:inject_pos] + inject_code + content[inject_pos:]
    
    # Écrire le contenu modifié
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Fichier {file_path} patché avec succès")
    return True

def main():
    """Fonction principale"""
    print("Modification directe du fichier XGBoost")
    
    # Trouver le fichier XGBoost
    xgboost_file = find_xgboost_file()
    
    if not xgboost_file:
        print("Fichier XGBoost non trouvé")
        return
    
    print(f"Fichier XGBoost trouvé: {xgboost_file}")
    
    # Générer des valeurs aléatoires
    weights = generate_random_weights()
    
    # Sauvegarder les poids dans un fichier JSON
    weights_file = save_weights_to_json(weights)
    
    # Patcher le fichier XGBoost
    success = patch_xgboost_file(xgboost_file, weights_file)
    
    if success:
        print("\nModification terminée!")
        print("Le fichier XGBoost a été modifié pour forcer des valeurs différentes de scale_pos_weight.")
        print("Cette modification s'appliquera à toutes les applications qui utilisent XGBoost.")
        print("Pour revenir à la version originale, restaurez la sauvegarde.")
    else:
        print("\nÉchec de la modification")
        print("Veuillez vérifier manuellement le fichier XGBoost et appliquer les modifications nécessaires.")

if __name__ == "__main__":
    main()
