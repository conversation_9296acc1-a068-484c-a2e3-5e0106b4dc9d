    def train_models(self, num_range=None, force_retrain=False, check_stop_requested=None, fast_mode=False):
        """Entraîne des modèles de prédiction pour chaque numéro
        
        Args:
            num_range (list, optional): Liste des numéros à traiter. Si None, traite tous les numéros.
            force_retrain (bool): Si True, force le réentraînement même si un modèle existe déjà
            check_stop_requested (callable, optional): Fonction pour vérifier si l'arrêt a été demandé
            fast_mode (bool): Si True, utilise des paramètres plus rapides pour l'entraînement
            
        Returns:
            bool: True si l'entraînement a réussi
        """
        if self.df is None:
            self.prepare_data()
            
        if self.df is None or self.df.empty:
            print("Aucune donnée disponible pour l'entraînement")
            return False
            
        # Fonction par défaut pour vérifier l'arrêt
        if check_stop_requested is None:
            check_stop_requested = lambda: False
            
        # Déterminer la plage de numéros à traiter
        if num_range is None:
            num_range = range(1, self.max_number + 1)
        
        # Vérifier si les numéros sont valides
        valid_nums = [num for num in num_range if 1 <= num <= self.max_number]
        if not valid_nums:
            print(f"Aucun numéro valide dans la plage spécifiée. Les numéros doivent être entre 1 et {self.max_number}.")
            return False
            
        print(f"Entraînement des modèles pour les numéros: {valid_nums}")
        
        # Caractéristiques temporelles à utiliser
        temporal_features = ['day_of_week', 'hour', 'is_weekend', 'is_afternoon', 'day', 'month', 'year']
        
        # Configurer le nombre de jobs pour scikit-learn
        from sklearn.utils import parallel_backend
        
        # Traiter chaque numéro
        for num in valid_nums:
            # Vérifier si l'arrêt a été demandé
            if check_stop_requested():
                print(f"Arrêt demandé après le numéro {num-1}")
                return False
                
            print(f"\nTraitement du numéro {num}...")
            
            # Vérifier si un modèle existe déjà pour ce numéro
            if not force_retrain and num in self.models:
                print(f"Un modèle existe déjà pour le numéro {num}. Utilisation du modèle existant.")
                continue
                
            # Préparer les données pour ce numéro
            num_data = self.df.copy()
            
            # Ajouter des caractéristiques de lag (décalage temporel)
            for i in range(1, 6):  # Regarder les 5 derniers tirages
                num_data[f'has_{num}_lag_{i}'] = num_data[f'has_{num}'].shift(i).fillna(0)
                
            # Ajouter des caractéristiques pour les numéros voisins
            for offset in [-2, -1, 1, 2]:
                neighbor = num + offset
                if 1 <= neighbor <= self.max_number:
                    num_data[f'has_neighbor_{neighbor}'] = num_data[f'has_{neighbor}']
                    
            # Ajouter des caractéristiques de fréquence mobile
            window_sizes = [5, 10, 20]
            for window in window_sizes:
                num_data[f'freq_{num}_{window}'] = num_data[f'has_{num}'].rolling(window=window, min_periods=1).mean()
                
            # Supprimer les lignes avec des valeurs manquantes
            num_data = num_data.dropna()
            
            if len(num_data) < 100:
                print(f"Pas assez de données pour le numéro {num} après nettoyage. Minimum 100 tirages requis.")
                continue
                
            # Définir les caractéristiques et la cible
            feature_cols = [f'has_{num}_lag_{i}' for i in range(1, 6)]
            feature_cols += [f'has_neighbor_{num+offset}' for offset in [-2, -1, 1, 2] 
                            if 1 <= num+offset <= self.max_number]
            feature_cols += [f'freq_{num}_{window}' for window in window_sizes]
            feature_cols += temporal_features
            
            # Ajouter des caractéristiques cycliques pour le jour de la semaine et le mois
            num_data['day_of_week_sin'] = np.sin(2 * np.pi * num_data['day_of_week'] / 7)
            num_data['day_of_week_cos'] = np.cos(2 * np.pi * num_data['day_of_week'] / 7)
            num_data['month_sin'] = np.sin(2 * np.pi * num_data['month'] / 12)
            num_data['month_cos'] = np.cos(2 * np.pi * num_data['month'] / 12)
            
            feature_cols += ['day_of_week_sin', 'day_of_week_cos', 'month_sin', 'month_cos']
            
            # Ajouter des caractéristiques de cycle
            try:
                # Trouver les cycles potentiels
                from scipy import signal
                
                # Calculer l'autocorrélation
                autocorr = pd.Series(num_data[f'has_{num}']).autocorr(lag=range(1, min(50, len(num_data) // 2)))
                
                # Trouver les pics dans l'autocorrélation
                peaks, _ = signal.find_peaks(autocorr, height=0.05)
                
                if len(peaks) > 0:
                    # Ajouter des caractéristiques basées sur les cycles détectés
                    for i, peak in enumerate(peaks[:3]):  # Utiliser les 3 premiers pics
                        cycle_length = peak + 1  # +1 car les indices commencent à 0
                        
                        # Calculer le modulo par rapport à la longueur du cycle
                        num_data[f'cycle_{num}_{cycle_length}'] = np.arange(len(num_data)) % cycle_length
                        
                        # Convertir en caractéristiques sinusoïdales
                        num_data[f'cycle_{num}_{cycle_length}_sin'] = np.sin(2 * np.pi * num_data[f'cycle_{num}_{cycle_length}'] / cycle_length)
                        num_data[f'cycle_{num}_{cycle_length}_cos'] = np.cos(2 * np.pi * num_data[f'cycle_{num}_{cycle_length}'] / cycle_length)
                        
                        feature_cols += [f'cycle_{num}_{cycle_length}_sin', f'cycle_{num}_{cycle_length}_cos']
                        
                    print(f"Cycles détectés pour le numéro {num}: {[p+1 for p in peaks[:3]]}")
            except Exception as e:
                print(f"Erreur lors de la détection des cycles pour le numéro {num}: {e}")
                
            # Ajouter des caractéristiques de résidus
            try:
                # Calculer les résidus (reste de la division)
                residuals = []
                residual_counts = np.zeros(20)  # Compter les résidus de 0 à 19
                
                for i in range(len(num_data)):
                    for mod in range(2, 20):
                        residual = i % mod
                        if num_data.iloc[i][f'has_{num}'] == 1:
                            residual_counts[residual] += 1
                            
                # Normaliser les comptages
                total_appearances = residual_counts.sum()
                if total_appearances > 0:
                    residual_counts = residual_counts / total_appearances
                    
                # Trouver les 3 résidus les plus fréquents pour des prédictions plus robustes
                top_residuals = np.argsort(residual_counts)[-3:]
                
                # Ajouter des caractéristiques de résidus
                for mod in range(2, 10):
                    residual = np.arange(len(num_data)) % mod
                    for top_r in top_residuals:
                        num_data[f'residual_{num}_{mod}_{top_r}'] = (residual == top_r).astype(int)
                        feature_cols.append(f'residual_{num}_{mod}_{top_r}')
                        
                print(f"Résidus les plus fréquents pour le numéro {num}: {top_residuals}")
            except Exception as e:
                print(f"Erreur lors du calcul des résidus pour le numéro {num}: {e}")
                
            # Préparer les données d'entraînement
            X = num_data[feature_cols]
            y = num_data[f'has_{num}']
            
            # Vérifier s'il y a suffisamment d'exemples positifs
            positive_count = y.sum()
            if positive_count < 10:
                print(f"Pas assez d'exemples positifs pour le numéro {num} ({positive_count}). Minimum 10 requis.")
                continue
                
            print(f"Entraînement avec {len(X)} exemples, dont {positive_count} positifs ({positive_count/len(X)*100:.2f}%)")
            print(f"Nombre de caractéristiques: {len(feature_cols)}")
            
            # Diviser les données en ensembles d'entraînement et de test
            from sklearn.model_selection import train_test_split
            
            # Utiliser une graine aléatoire fixe pour la reproductibilité
            random_state = 42
            
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=random_state, stratify=y
            )
            
            # Normaliser les caractéristiques
            from sklearn.preprocessing import StandardScaler
            
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Remplacer les valeurs NaN ou infinies par 0
            X_train_scaled_clean = np.nan_to_num(X_train_scaled, nan=0.0, posinf=0.0, neginf=0.0)
            X_test_scaled_clean = np.nan_to_num(X_test_scaled, nan=0.0, posinf=0.0, neginf=0.0)
            
            # Initialiser le dictionnaire pour stocker les modèles et les métriques
            models = {}
            
            # Récupérer les paramètres d'activation des modèles
            rf_params = self.model_params.get('random_forest', {})
            xgb_params = self.model_params.get('xgboost', {})
            use_rf = rf_params.get('use_model', True)
            use_xgb = xgb_params.get('use_model', True)
            
            # Entraîner un modèle Random Forest si activé
            if use_rf:
                try:
                    print(f"Entraînement du modèle Random Forest pour le numéro {num}...")
                    
                    # Configurer Random Forest
                    from sklearn.ensemble import RandomForestClassifier
                    
                    # Récupérer les paramètres
                    n_estimators = rf_params.get('n_estimators', 300)
                    max_depth = rf_params.get('max_depth', 15)
                    
                    # Créer et entraîner le modèle
                    with parallel_backend('threading', n_jobs=self.n_jobs):
                        rf_model = RandomForestClassifier(
                            n_estimators=n_estimators,
                            max_depth=max_depth,
                            random_state=random_state,
                            class_weight='balanced'
                        )
                        rf_model.fit(X_train_scaled_clean, y_train)
                    
                    # Évaluer le modèle
                    rf_pred = rf_model.predict(X_test_scaled_clean)
                    rf_accuracy = accuracy_score(y_test, rf_pred)
                    
                    print(f"  Précision du modèle Random Forest: {rf_accuracy:.4f}")
                    
                    # Stocker le modèle et les métriques
                    models['random_forest'] = {
                        'model': rf_model,
                        'scaler': scaler,
                        'accuracy': rf_accuracy,
                        'feature_cols': feature_cols
                    }
                    
                    # Analyser l'importance des caractéristiques
                    if hasattr(rf_model, 'feature_importances_'):
                        importances = rf_model.feature_importances_
                        if len(importances) == len(feature_cols):
                            # Créer un dictionnaire des importances
                            feature_importance = dict(zip(feature_cols, importances))
                            # Trier par importance décroissante
                            sorted_importance = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
                            # Prendre les 5 plus importantes
                            top_features = sorted_importance[:5]
                            
                            print(f"  Caractéristiques les plus importantes (Random Forest):")
                            for feature, importance in top_features:
                                print(f"    - {feature}: {importance:.4f}")
                                
                            # Stocker l'importance des caractéristiques
                            models['random_forest']['feature_importance'] = sorted_importance
                except Exception as e:
                    print(f"Erreur lors de l'entraînement du modèle Random Forest pour le numéro {num}: {e}")
            
            # Entraîner un modèle XGBoost optimisé si activé et disponible
            if use_xgb:
                # Vérifier si l'optimiseur XGBoost est disponible
                if self.xgboost_optimizer_available and self.xgboost_optimizer:
                    try:
                        print(f"Entraînement du modèle XGBoost optimisé pour le numéro {num}...")
                        
                        # Entraîner le modèle avec l'optimiseur
                        xgb_result = self.train_xgboost_model(
                            num, X_train_scaled_clean, y_train, feature_cols, 
                            random_state=random_state,
                            optimize=not fast_mode,  # Optimiser seulement si pas en mode rapide
                            fast_mode=fast_mode
                        )
                        
                        if xgb_result:
                            # Évaluer le modèle
                            xgb_model = xgb_result['model']
                            xgb_pred = xgb_model.predict(X_test_scaled_clean)
                            xgb_accuracy = accuracy_score(y_test, xgb_pred)
                            
                            print(f"  Précision du modèle XGBoost optimisé: {xgb_accuracy:.4f}")
                            
                            # Stocker le modèle et les métriques
                            models['xgboost'] = {
                                'model': xgb_model,
                                'scaler': scaler,
                                'accuracy': xgb_accuracy,
                                'feature_cols': feature_cols,
                                'params': xgb_result['params'],
                                'feature_importance': xgb_result.get('feature_importance_details', [])
                            }
                    except Exception as e:
                        print(f"Erreur lors de l'entraînement du modèle XGBoost optimisé pour le numéro {num}: {e}")
                        # Fallback au code XGBoost standard si l'optimiseur échoue
                        print("Utilisation du code XGBoost standard comme fallback...")
                        use_standard_xgb = True
                else:
                    # Utiliser le code XGBoost standard si l'optimiseur n'est pas disponible
                    use_standard_xgb = True
                    
                # Code XGBoost standard (utilisé comme fallback ou si l'optimiseur n'est pas disponible)
                if not self.xgboost_optimizer_available or 'xgboost' not in models:
                    try:
                        print(f"Entraînement du modèle XGBoost standard pour le numéro {num}...")
                        
                        # Importer XGBoost
                        from xgboost import XGBClassifier
                        
                        # Récupérer les paramètres
                        n_estimators = xgb_params.get('n_estimators', 300)
                        max_depth = xgb_params.get('max_depth', 8)
                        learning_rate = xgb_params.get('learning_rate', 0.03)
                        
                        # Configuration de base pour XGBoost
                        xgb_config = {
                            'n_estimators': n_estimators,
                            'max_depth': max_depth,
                            'learning_rate': learning_rate,
                            'min_child_weight': 3,
                            'gamma': 0.1,
                            'subsample': 0.7,
                            'colsample_bytree': 0.7,
                            'objective': 'binary:logistic',
                            'eval_metric': 'auc',
                            'use_label_encoder': False,
                            'verbosity': 0,
                            'random_state': random_state
                        }
                        
                        # Configurer l'accélération matérielle
                        if self.hardware_acceleration == 'gpu' and self.cuda_available:
                            xgb_config.update({
                                'tree_method': 'gpu_hist',
                                'gpu_id': 0,
                                'predictor': 'gpu_predictor'
                            })
                            print("  Utilisation du GPU pour XGBoost")
                        else:
                            xgb_config.update({
                                'tree_method': 'hist',
                                'device': 'cpu',
                                'predictor': 'cpu_predictor'
                            })
                            print("  Utilisation du CPU pour XGBoost")
                        
                        # Configurer le nombre de threads
                        xgb_config['n_jobs'] = self.n_jobs
                        
                        # Calculer scale_pos_weight spécifique à ce numéro
                        raw_class_counts = np.bincount(y_train.astype(int).values, minlength=2)
                        neg_count = raw_class_counts[0]
                        pos_count = raw_class_counts[1]
                        
                        # Éviter la division par zéro
                        if pos_count > 0:
                            # Calculer le ratio spécifique à ce numéro
                            num_specific_scale_pos_weight = neg_count / pos_count
                            print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
                            print(f"  Numéro {num}: scale_pos_weight spécifique = {num_specific_scale_pos_weight:.4f}")
                        else:
                            # Valeur par défaut si aucun exemple positif
                            num_specific_scale_pos_weight = 1.0
                            print(f"  Numéro {num}: Aucun exemple positif trouvé, utilisation de scale_pos_weight=1.0")
                        
                        # Mettre à jour la configuration XGBoost avec le scale_pos_weight spécifique
                        xgb_config['scale_pos_weight'] = num_specific_scale_pos_weight
                        
                        # Créer et entraîner le modèle
                        xgb_model = XGBClassifier(**xgb_config)
                        
                        # Vérifier si l'arrêt a été demandé avant l'entraînement
                        if check_stop_requested():
                            return False
                        
                        # Entraîner le modèle
                        xgb_model.fit(X_train_scaled_clean, y_train)
                        
                        # Évaluer le modèle
                        xgb_pred = xgb_model.predict(X_test_scaled_clean)
                        xgb_accuracy = accuracy_score(y_test, xgb_pred)
                        
                        print(f"  Précision du modèle XGBoost standard: {xgb_accuracy:.4f}")
                        
                        # Stocker le modèle et les métriques
                        models['xgboost'] = {
                            'model': xgb_model,
                            'scaler': scaler,
                            'accuracy': xgb_accuracy,
                            'feature_cols': feature_cols,
                            'scale_pos_weight': num_specific_scale_pos_weight
                        }
                        
                        # Analyser l'importance des caractéristiques
                        if hasattr(xgb_model, 'feature_importances_'):
                            importances = xgb_model.feature_importances_
                            if len(importances) == len(feature_cols):
                                # Créer un dictionnaire des importances
                                feature_importance = dict(zip(feature_cols, importances))
                                # Trier par importance décroissante
                                sorted_importance = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
                                # Prendre les 5 plus importantes
                                top_features = sorted_importance[:5]
                                
                                print(f"  Caractéristiques les plus importantes (XGBoost):")
                                for feature, importance in top_features:
                                    print(f"    - {feature}: {importance:.4f}")
                                    
                                # Stocker l'importance des caractéristiques
                                models['xgboost']['feature_importance'] = sorted_importance
                    except Exception as e:
                        print(f"Erreur lors de l'entraînement du modèle XGBoost standard pour le numéro {num}: {e}")
            
            # Stocker les modèles pour ce numéro
            if models:
                self.models[num] = models
                print(f"Modèles entraînés avec succès pour le numéro {num}")
            else:
                print(f"Aucun modèle entraîné pour le numéro {num}")
        
        print(f"\nEntraînement terminé pour {len(self.models)} numéros")
        return True
