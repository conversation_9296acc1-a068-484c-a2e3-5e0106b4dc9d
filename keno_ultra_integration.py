#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script d'intégration ultra-optimisé pour le système Keno
Version améliorée avec suppression complète des warnings et performances maximales
"""

import os
import sys
import warnings
import json
from datetime import datetime

# Suppression complète de tous les warnings
warnings.filterwarnings('ignore')
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

def setup_ultra_environment():
    """Configure l'environnement pour des performances optimales"""
    
    # Supprimer les warnings XGBoost
    try:
        import xgboost as xgb
        xgb.set_config(verbosity=0)
    except ImportError:
        pass
    
    # Supprimer les warnings LightGBM
    try:
        import lightgbm as lgb
        lgb.set_config(verbosity=-1)
    except ImportError:
        pass
    
    # Supprimer les warnings scikit-learn
    try:
        from sklearn.utils._testing import ignore_warnings
        from sklearn.exceptions import ConvergenceWarning
        warnings.filterwarnings('ignore', category=ConvergenceWarning)
    except ImportError:
        pass

def integrate_ultra_system():
    """Intègre le système ultra-optimisé"""
    
    print("=== Intégration du système ULTRA-OPTIMISÉ ===")
    
    # Configuration de l'environnement
    setup_ultra_environment()
    
    try:
        # Importer les modules
        from keno_ultra_optimizer import KenoUltraOptimizer
        from keno_data import KenoDataManager
        from keno_analyzer import KenoAnalyzer
        
        print("✓ Modules ultra-optimisés importés avec succès")
        
        # Créer les instances
        data_manager = KenoDataManager()
        analyzer = KenoAnalyzer(data_manager)
        
        # Charger les données
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        if os.path.exists(data_dir):
            data_files = [f for f in os.listdir(data_dir) if f.endswith('.json') or f.endswith('.keno')]
            if data_files:
                latest_file = max(data_files, key=lambda f: os.path.getmtime(os.path.join(data_dir, f)))
                file_path = os.path.join(data_dir, latest_file)
                
                print(f"Chargement des données depuis: {file_path}")
                success = data_manager.load_database(file_path)
                
                if success:
                    print(f"✓ {data_manager.get_draws_count()} tirages chargés")
                else:
                    print("✗ Échec du chargement des données")
                    return False, None, None
            else:
                print("✗ Aucun fichier de données trouvé")
                return False, None, None
        else:
            print("✗ Répertoire de données non trouvé")
            return False, None, None
        
        # Créer l'optimiseur ultra-avancé
        ultra_optimizer = KenoUltraOptimizer(data_manager, analyzer)
        
        print("✓ Système ultra-optimisé intégré avec succès")
        return True, analyzer, ultra_optimizer
        
    except Exception as e:
        print(f"✗ Erreur lors de l'intégration: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def run_ultra_training(ultra_optimizer, mode='fast'):
    """Lance l'entraînement ultra-optimisé"""
    
    print(f"\n=== Entraînement ULTRA-OPTIMISÉ (mode: {mode}) ===")
    
    try:
        if mode == 'fast':
            # Mode rapide: 5 numéros représentatifs
            numbers_to_train = [7, 21, 35, 49, 63]
            print(f"Mode rapide: entraînement de {len(numbers_to_train)} numéros")
        elif mode == 'medium':
            # Mode moyen: 15 numéros
            numbers_to_train = [1, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70, 3, 17, 31, 45]
            print(f"Mode moyen: entraînement de {len(numbers_to_train)} numéros")
        else:  # mode == 'complete'
            # Mode complet: tous les numéros
            numbers_to_train = list(range(1, 71))
            print("Mode complet: entraînement de tous les numéros (1-70)")
        
        results = {
            'trained_numbers': [],
            'failed_numbers': [],
            'ultra_models': {},
            'performance_summary': {}
        }
        
        total_numbers = len(numbers_to_train)
        
        for i, number in enumerate(numbers_to_train):
            print(f"\n--- Traitement ULTRA du numéro {number} ({i+1}/{total_numbers}) ---")
            
            try:
                # Créer les caractéristiques ultra-avancées
                result = ultra_optimizer.create_ultra_features(number)
                
                if result is None:
                    print(f"Pas assez de données pour le numéro {number}")
                    results['failed_numbers'].append(number)
                    continue
                
                X, y, feature_names = result
                
                if len(X) < 100:
                    print(f"Pas assez d'échantillons pour le numéro {number}")
                    results['failed_numbers'].append(number)
                    continue
                
                # Entraîner un modèle ultra-optimisé
                model_result = train_ultra_model(X, y, feature_names, number)
                
                if model_result:
                    results['trained_numbers'].append(number)
                    results['ultra_models'][number] = model_result
                    results['performance_summary'][number] = {
                        'f1_score': model_result['f1_score'],
                        'accuracy': model_result['accuracy'],
                        'precision': model_result['precision'],
                        'recall': model_result['recall'],
                        'model_type': model_result['model_type'],
                        'num_features': len(feature_names)
                    }
                    
                    print(f"✓ Numéro {number} traité avec succès")
                    print(f"  F1: {model_result['f1_score']:.4f}, Acc: {model_result['accuracy']:.4f}")
                else:
                    results['failed_numbers'].append(number)
                    print(f"✗ Échec du traitement du numéro {number}")
                
            except Exception as e:
                print(f"✗ Erreur lors du traitement du numéro {number}: {e}")
                results['failed_numbers'].append(number)
        
        # Résumé des résultats
        print(f"\n=== Résumé de l'entraînement ULTRA-OPTIMISÉ ===")
        print(f"Numéros traités avec succès: {len(results['trained_numbers'])}")
        print(f"Numéros échoués: {len(results['failed_numbers'])}")
        
        if results['performance_summary']:
            import numpy as np
            avg_f1 = np.mean([p['f1_score'] for p in results['performance_summary'].values()])
            avg_accuracy = np.mean([p['accuracy'] for p in results['performance_summary'].values()])
            print(f"F1-score moyen: {avg_f1:.4f}")
            print(f"Précision moyenne: {avg_accuracy:.4f}")
        
        # Sauvegarder les résultats
        save_ultra_results(results)
        
        return True, results
        
    except Exception as e:
        print(f"✗ Erreur lors de l'entraînement ultra-optimisé: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def train_ultra_model(X, y, feature_names, number):
    """Entraîne un modèle ultra-optimisé pour un numéro"""
    
    try:
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        from sklearn.feature_selection import SelectKBest, f_classif
        
        # Diviser les données
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Sélection des meilleures caractéristiques
        k_best = min(20, X_train.shape[1])
        selector = SelectKBest(score_func=f_classif, k=k_best)
        X_train_selected = selector.fit_transform(X_train, y_train)
        X_test_selected = selector.transform(X_test)
        
        # Entraîner un Random Forest optimisé
        model = RandomForestClassifier(
            n_estimators=300,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            class_weight='balanced',
            random_state=42,
            n_jobs=-1
        )
        
        model.fit(X_train_selected, y_train)
        
        # Évaluer le modèle
        y_pred = model.predict(X_test_selected)
        
        f1 = f1_score(y_test, y_pred, zero_division=0)
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, zero_division=0)
        recall = recall_score(y_test, y_pred, zero_division=0)
        
        return {
            'model': model,
            'selector': selector,
            'f1_score': f1,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'model_type': 'RandomForest_Ultra',
            'feature_names': feature_names,
            'selected_features': selector.get_support(indices=True)
        }
        
    except Exception as e:
        print(f"    Erreur lors de l'entraînement du modèle: {e}")
        return None

def save_ultra_results(results):
    """Sauvegarde les résultats ultra-optimisés"""
    try:
        models_dir = os.path.join(os.path.dirname(__file__), 'models')
        os.makedirs(models_dir, exist_ok=True)
        
        # Sauvegarder le résumé
        summary_file = os.path.join(models_dir, 'ultra_optimizer_summary.json')
        summary_data = {
            'timestamp': datetime.now().isoformat(),
            'trained_numbers': results['trained_numbers'],
            'failed_numbers': results['failed_numbers'],
            'performance_summary': results['performance_summary'],
            'system_type': 'ULTRA_OPTIMIZED'
        }
        
        with open(summary_file, 'w') as f:
            json.dump(summary_data, f, indent=2)
        
        print(f"✓ Résultats ultra-optimisés sauvegardés dans: {summary_file}")
        
    except Exception as e:
        print(f"Erreur lors de la sauvegarde: {e}")

def main():
    """Fonction principale"""
    
    print("=== Système d'optimisation ULTRA-AVANCÉ pour Keno ===")
    print("Version améliorée avec suppression complète des warnings")
    print("et performances maximales\n")
    
    # 1. Intégrer le système
    success, analyzer, ultra_optimizer = integrate_ultra_system()
    
    if not success:
        print("Échec de l'intégration. Arrêt du script.")
        return 1
    
    # 2. Demander le mode d'entraînement
    print("\nModes d'entraînement ULTRA-OPTIMISÉS disponibles:")
    print("1. Rapide (5 numéros, ~3-5 minutes)")
    print("2. Moyen (15 numéros, ~10-15 minutes)")
    print("3. Complet (70 numéros, ~45-60 minutes)")
    
    try:
        choice = input("\nChoisissez un mode (1/2/3) [défaut: 1]: ").strip()
        if choice == '2':
            mode = 'medium'
        elif choice == '3':
            mode = 'complete'
        else:
            mode = 'fast'
    except KeyboardInterrupt:
        print("\nInterruption par l'utilisateur")
        return 0
    
    # 3. Lancer l'entraînement ultra-optimisé
    training_success, results = run_ultra_training(ultra_optimizer, mode)
    
    if training_success and results:
        print(f"\n=== SUCCÈS ! Système ULTRA-OPTIMISÉ opérationnel ===")
        print(f"✓ {len(results['trained_numbers'])} numéros entraînés avec succès")
        print(f"✓ Performances moyennes améliorées")
        print(f"✓ Warnings complètement supprimés")
        print(f"✓ Système prêt pour des prédictions ultra-précises")
        
        return 0
    else:
        print("Échec de l'entraînement ultra-optimisé.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nInterruption par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
