#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module pour la détection d'anomalies dans les tirages Keno
Utilise plusieurs techniques pour identifier les tirages inhabituels
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import IsolationForest
from sklearn.neighbors import LocalOutlierFactor
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
import os
import joblib
from datetime import datetime

class KenoAnomalyDetector:
    """
    Classe pour la détection d'anomalies dans les tirages Keno
    """
    
    def __init__(self, data_manager=None):
        """
        Initialise le détecteur d'anomalies
        
        Args:
            data_manager: Gestionnaire de données Keno
        """
        self.data_manager = data_manager
        self.models = {}
        self.df = None
        self.features = None
        self.anomalies = None
        self.max_number = data_manager.max_number if data_manager else 70
        self.numbers_per_draw = data_manager.numbers_per_draw if data_manager else 20
    
    def prepare_data(self, df=None):
        """
        Prépare les données pour la détection d'anomalies
        
        Args:
            df: DataFrame pandas contenant les données (si None, utilise les données du data_manager)
            
        Returns:
            DataFrame: DataFrame contenant les caractéristiques pour la détection d'anomalies
        """
        if df is not None:
            self.df = df
        elif self.data_manager is not None and self.data_manager.draws:
            # Convertir les tirages en DataFrame pandas
            data = []
            for draw in self.data_manager.draws:
                row = {
                    'date': draw.draw_date,
                    'id': draw.draw_id,
                    'day_of_week': draw.draw_date.weekday(),
                    'hour': draw.draw_date.hour
                }
                
                # Ajouter les numéros tirés
                for i, num in enumerate(sorted(draw.draw_numbers)):
                    row[f'num_{i+1}'] = num
                
                # Ajouter des indicateurs pour chaque numéro possible
                for i in range(1, self.max_number + 1):
                    row[f'has_{i}'] = 1 if i in draw.draw_numbers else 0
                
                data.append(row)
            
            # Créer le DataFrame
            self.df = pd.DataFrame(data)
            self.df = self.df.sort_values('date')
        else:
            print("Aucune donnée disponible pour la détection d'anomalies")
            return None
        
        # Extraire les caractéristiques pour la détection d'anomalies
        features = []
        
        # 1. Caractéristiques basées sur les numéros
        # Somme des numéros
        self.df['sum_numbers'] = self.df[[f'num_{i+1}' for i in range(self.numbers_per_draw) if f'num_{i+1}' in self.df.columns]].sum(axis=1)
        features.append('sum_numbers')
        
        # Écart-type des numéros
        self.df['std_numbers'] = self.df[[f'num_{i+1}' for i in range(self.numbers_per_draw) if f'num_{i+1}' in self.df.columns]].std(axis=1)
        features.append('std_numbers')
        
        # Nombre de numéros pairs
        self.df['even_count'] = self.df[[f'num_{i+1}' for i in range(self.numbers_per_draw) if f'num_{i+1}' in self.df.columns]].apply(lambda x: sum(1 for num in x if num % 2 == 0), axis=1)
        features.append('even_count')
        
        # Nombre de numéros impairs
        self.df['odd_count'] = self.numbers_per_draw - self.df['even_count']
        features.append('odd_count')
        
        # Nombre de numéros bas (1-35) et hauts (36-70)
        self.df['low_count'] = self.df[[f'num_{i+1}' for i in range(self.numbers_per_draw) if f'num_{i+1}' in self.df.columns]].apply(lambda x: sum(1 for num in x if num <= self.max_number/2), axis=1)
        features.append('low_count')
        
        self.df['high_count'] = self.numbers_per_draw - self.df['low_count']
        features.append('high_count')
        
        # 2. Caractéristiques basées sur les séquences
        # Nombre de séquences consécutives
        def count_consecutive_sequences(numbers):
            numbers = sorted(numbers)
            sequences = 0
            for i in range(len(numbers) - 1):
                if numbers[i+1] == numbers[i] + 1:
                    sequences += 1
            return sequences
        
        self.df['consecutive_sequences'] = self.df[[f'num_{i+1}' for i in range(self.numbers_per_draw) if f'num_{i+1}' in self.df.columns]].apply(count_consecutive_sequences, axis=1)
        features.append('consecutive_sequences')
        
        # 3. Caractéristiques basées sur la distribution
        # Écart entre le plus petit et le plus grand numéro
        self.df['range'] = self.df[[f'num_{i+1}' for i in range(self.numbers_per_draw) if f'num_{i+1}' in self.df.columns]].max(axis=1) - self.df[[f'num_{i+1}' for i in range(self.numbers_per_draw) if f'num_{i+1}' in self.df.columns]].min(axis=1)
        features.append('range')
        
        # 4. Caractéristiques basées sur les motifs
        # Nombre de numéros dans chaque dizaine (1-10, 11-20, etc.)
        for i in range(7):  # 7 dizaines pour les numéros 1-70
            start = i * 10 + 1
            end = min((i + 1) * 10, self.max_number)
            self.df[f'decade_{i+1}'] = self.df[[f'has_{j}' for j in range(start, end + 1) if f'has_{j}' in self.df.columns]].sum(axis=1)
            features.append(f'decade_{i+1}')
        
        # 5. Caractéristiques temporelles
        # Jour de la semaine (one-hot encoding)
        for day in range(7):
            self.df[f'day_{day}'] = (self.df['day_of_week'] == day).astype(int)
            features.append(f'day_{day}')
        
        # Heure de la journée (matin, après-midi, soir, nuit)
        self.df['morning'] = ((self.df['hour'] >= 6) & (self.df['hour'] < 12)).astype(int)
        features.append('morning')
        
        self.df['afternoon'] = ((self.df['hour'] >= 12) & (self.df['hour'] < 18)).astype(int)
        features.append('afternoon')
        
        self.df['evening'] = ((self.df['hour'] >= 18) & (self.df['hour'] < 22)).astype(int)
        features.append('evening')
        
        self.df['night'] = ((self.df['hour'] >= 22) | (self.df['hour'] < 6)).astype(int)
        features.append('night')
        
        # Stocker les caractéristiques
        self.features = features
        
        return self.df[features]
    
    def train_models(self, contamination=0.05):
        """
        Entraîne plusieurs modèles de détection d'anomalies
        
        Args:
            contamination: Proportion attendue d'anomalies dans les données
            
        Returns:
            dict: Dictionnaire contenant les modèles entraînés
        """
        if self.df is None or self.features is None:
            self.prepare_data()
        
        if self.df is None or self.features is None:
            print("Aucune donnée disponible pour l'entraînement des modèles")
            return None
        
        print(f"Entraînement des modèles de détection d'anomalies...")
        
        # Extraire les caractéristiques
        X = self.df[self.features].values
        
        # Normaliser les données
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 1. Isolation Forest
        print("Entraînement du modèle Isolation Forest...")
        iso_forest = IsolationForest(
            n_estimators=100,
            max_samples='auto',
            contamination=contamination,
            random_state=42,
            n_jobs=-1
        )
        iso_forest.fit(X_scaled)
        
        # 2. Local Outlier Factor
        print("Entraînement du modèle Local Outlier Factor...")
        lof = LocalOutlierFactor(
            n_neighbors=20,
            contamination=contamination,
            n_jobs=-1
        )
        # LOF ne nécessite pas d'entraînement explicite, il sera utilisé directement pour la prédiction
        
        # 3. PCA pour la détection d'anomalies
        print("Entraînement du modèle PCA...")
        pca = PCA(n_components=0.95)  # Conserver 95% de la variance
        pca.fit(X_scaled)
        
        # Calculer l'erreur de reconstruction pour chaque exemple
        X_pca = pca.transform(X_scaled)
        X_reconstructed = pca.inverse_transform(X_pca)
        reconstruction_error = np.mean(np.square(X_scaled - X_reconstructed), axis=1)
        
        # Déterminer le seuil pour les anomalies (basé sur la distribution des erreurs)
        threshold = np.percentile(reconstruction_error, (1 - contamination) * 100)
        
        # Stocker les modèles
        self.models = {
            'isolation_forest': iso_forest,
            'local_outlier_factor': lof,
            'pca': {
                'model': pca,
                'threshold': threshold
            },
            'scaler': scaler
        }
        
        print("Modèles de détection d'anomalies entraînés avec succès")
        
        return self.models
    
    def detect_anomalies(self, new_data=None):
        """
        Détecte les anomalies dans les données
        
        Args:
            new_data: Nouvelles données à analyser (si None, utilise les données d'entraînement)
            
        Returns:
            DataFrame: DataFrame contenant les anomalies détectées
        """
        if self.models is None or len(self.models) == 0:
            self.train_models()
        
        if self.models is None or len(self.models) == 0:
            print("Aucun modèle disponible pour la détection d'anomalies")
            return None
        
        # Préparer les données
        if new_data is not None:
            # Préparer les nouvelles données
            X = new_data[self.features].values
        else:
            # Utiliser les données d'entraînement
            X = self.df[self.features].values
        
        # Normaliser les données
        scaler = self.models['scaler']
        X_scaled = scaler.transform(X)
        
        print(f"Détection d'anomalies sur {len(X)} tirages...")
        
        # 1. Isolation Forest
        iso_forest = self.models['isolation_forest']
        iso_forest_scores = iso_forest.decision_function(X_scaled)
        iso_forest_predictions = iso_forest.predict(X_scaled)
        
        # 2. Local Outlier Factor
        lof = self.models['local_outlier_factor']
        lof_predictions = lof.fit_predict(X_scaled)
        lof_scores = lof.negative_outlier_factor_
        
        # 3. PCA
        pca = self.models['pca']['model']
        threshold = self.models['pca']['threshold']
        
        X_pca = pca.transform(X_scaled)
        X_reconstructed = pca.inverse_transform(X_pca)
        reconstruction_error = np.mean(np.square(X_scaled - X_reconstructed), axis=1)
        pca_predictions = np.where(reconstruction_error > threshold, -1, 1)
        pca_scores = -reconstruction_error  # Négatif pour que les scores plus élevés soient normaux
        
        # Combiner les prédictions
        # Une anomalie est détectée si au moins 2 modèles sur 3 la détectent
        combined_predictions = np.zeros(len(X))
        for i in range(len(X)):
            votes = 0
            if iso_forest_predictions[i] == -1:
                votes += 1
            if lof_predictions[i] == -1:
                votes += 1
            if pca_predictions[i] == -1:
                votes += 1
            
            combined_predictions[i] = -1 if votes >= 2 else 1
        
        # Calculer un score d'anomalie combiné
        # Normaliser les scores entre 0 et 1 (0 = normal, 1 = anomalie)
        iso_forest_scores_norm = (iso_forest_scores - np.min(iso_forest_scores)) / (np.max(iso_forest_scores) - np.min(iso_forest_scores))
        iso_forest_scores_norm = 1 - iso_forest_scores_norm  # Inverser pour que 1 = anomalie
        
        lof_scores_norm = (lof_scores - np.min(lof_scores)) / (np.max(lof_scores) - np.min(lof_scores))
        lof_scores_norm = 1 - lof_scores_norm  # Inverser pour que 1 = anomalie
        
        pca_scores_norm = (pca_scores - np.min(pca_scores)) / (np.max(pca_scores) - np.min(pca_scores))
        pca_scores_norm = 1 - pca_scores_norm  # Inverser pour que 1 = anomalie
        
        # Score combiné (moyenne pondérée)
        combined_scores = 0.4 * iso_forest_scores_norm + 0.3 * lof_scores_norm + 0.3 * pca_scores_norm
        
        # Créer un DataFrame avec les résultats
        if new_data is not None:
            results_df = new_data.copy()
        else:
            results_df = self.df.copy()
        
        results_df['anomaly'] = combined_predictions == -1
        results_df['anomaly_score'] = combined_scores
        results_df['isolation_forest_anomaly'] = iso_forest_predictions == -1
        results_df['isolation_forest_score'] = iso_forest_scores_norm
        results_df['lof_anomaly'] = lof_predictions == -1
        results_df['lof_score'] = lof_scores_norm
        results_df['pca_anomaly'] = pca_predictions == -1
        results_df['pca_score'] = pca_scores_norm
        
        # Stocker les anomalies
        self.anomalies = results_df[results_df['anomaly']]
        
        print(f"Détection terminée. {len(self.anomalies)} anomalies détectées sur {len(results_df)} tirages.")
        
        return self.anomalies
    
    def visualize_anomalies(self, top_n=10):
        """
        Visualise les anomalies détectées
        
        Args:
            top_n: Nombre d'anomalies à visualiser
            
        Returns:
            str: Chemin du fichier image généré
        """
        if self.anomalies is None or len(self.anomalies) == 0:
            print("Aucune anomalie à visualiser")
            return None
        
        print(f"Visualisation des {min(top_n, len(self.anomalies))} anomalies les plus importantes...")
        
        # Créer le dossier de visualisations s'il n'existe pas
        os.makedirs('visualisations', exist_ok=True)
        
        # Sélectionner les top_n anomalies les plus importantes
        top_anomalies = self.anomalies.sort_values('anomaly_score', ascending=False).head(top_n)
        
        # Créer la figure
        plt.figure(figsize=(15, 10))
        
        # 1. Graphique des scores d'anomalie
        plt.subplot(2, 2, 1)
        sns.histplot(self.df['anomaly_score'] if 'anomaly_score' in self.df.columns else self.anomalies['anomaly_score'], bins=30, kde=True)
        plt.axvline(x=self.anomalies['anomaly_score'].min(), color='r', linestyle='--', label='Seuil d\'anomalie')
        plt.title('Distribution des scores d\'anomalie')
        plt.xlabel('Score d\'anomalie')
        plt.ylabel('Fréquence')
        plt.legend()
        
        # 2. Graphique des caractéristiques des anomalies
        plt.subplot(2, 2, 2)
        
        # Sélectionner les caractéristiques les plus discriminantes
        feature_importance = {}
        for feature in self.features:
            if feature in self.df.columns:
                # Calculer la différence entre la moyenne des anomalies et la moyenne globale
                anomaly_mean = self.anomalies[feature].mean()
                global_mean = self.df[feature].mean()
                # Normaliser par l'écart-type
                global_std = self.df[feature].std()
                if global_std > 0:
                    importance = abs(anomaly_mean - global_mean) / global_std
                    feature_importance[feature] = importance
        
        # Sélectionner les 10 caractéristiques les plus importantes
        top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]
        
        # Créer un graphique en barres
        features = [f[0] for f in top_features]
        importances = [f[1] for f in top_features]
        
        sns.barplot(x=importances, y=features)
        plt.title('Caractéristiques les plus discriminantes pour les anomalies')
        plt.xlabel('Importance relative')
        
        # 3. Tableau des top anomalies
        plt.subplot(2, 1, 2)
        plt.axis('off')
        
        # Créer un tableau avec les informations sur les top anomalies
        table_data = []
        headers = ['Date', 'Score', 'Numéros tirés', 'Caractéristiques anormales']
        
        for _, row in top_anomalies.iterrows():
            date = row['date'].strftime('%Y-%m-%d %H:%M') if 'date' in row else 'N/A'
            score = f"{row['anomaly_score']:.4f}"
            
            # Extraire les numéros tirés
            numbers = []
            for i in range(self.numbers_per_draw):
                if f'num_{i+1}' in row and not pd.isna(row[f'num_{i+1}']):
                    numbers.append(int(row[f'num_{i+1}']))
            numbers_str = ', '.join(map(str, sorted(numbers)))
            
            # Identifier les caractéristiques anormales
            abnormal_features = []
            for feature, importance in top_features:
                if feature in row:
                    feature_value = row[feature]
                    feature_mean = self.df[feature].mean()
                    feature_std = self.df[feature].std()
                    
                    # Une caractéristique est anormale si elle est à plus de 2 écarts-types de la moyenne
                    if abs(feature_value - feature_mean) > 2 * feature_std:
                        direction = 'élevé' if feature_value > feature_mean else 'bas'
                        abnormal_features.append(f"{feature} ({direction})")
            
            abnormal_features_str = ', '.join(abnormal_features[:3])  # Limiter à 3 caractéristiques
            
            table_data.append([date, score, numbers_str, abnormal_features_str])
        
        # Créer le tableau
        table = plt.table(
            cellText=table_data,
            colLabels=headers,
            loc='center',
            cellLoc='center',
            colWidths=[0.15, 0.1, 0.5, 0.25]
        )
        
        # Ajuster la taille du texte dans le tableau
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1, 1.5)
        
        plt.title('Top anomalies détectées', y=1.1)
        
        # Ajouter un titre global
        plt.suptitle('Analyse des anomalies dans les tirages Keno', fontsize=16)
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.9)
        
        # Sauvegarder la figure
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'visualisations/anomalies_{timestamp}.png'
        plt.savefig(filename)
        print(f"Visualisation sauvegardée dans {filename}")
        
        # Fermer la figure pour libérer la mémoire
        plt.close()
        
        return filename
    
    def save_models(self, filepath='keno_anomaly_models.pkl'):
        """
        Sauvegarde les modèles de détection d'anomalies
        
        Args:
            filepath: Chemin du fichier où sauvegarder les modèles
            
        Returns:
            bool: True si la sauvegarde a réussi, False sinon
        """
        if self.models is None or len(self.models) == 0:
            print("Aucun modèle à sauvegarder")
            return False
        
        try:
            # Créer le répertoire parent si nécessaire
            os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)
            
            # Sauvegarder les modèles
            joblib.dump(self.models, filepath)
            
            print(f"Modèles de détection d'anomalies sauvegardés dans {filepath}")
            return True
        except Exception as e:
            print(f"Erreur lors de la sauvegarde des modèles: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def load_models(self, filepath='keno_anomaly_models.pkl'):
        """
        Charge les modèles de détection d'anomalies
        
        Args:
            filepath: Chemin du fichier contenant les modèles
            
        Returns:
            bool: True si le chargement a réussi, False sinon
        """
        try:
            # Vérifier que le fichier existe
            if not os.path.exists(filepath):
                print(f"Fichier {filepath} introuvable")
                return False
            
            # Charger les modèles
            self.models = joblib.load(filepath)
            
            print(f"Modèles de détection d'anomalies chargés depuis {filepath}")
            return True
        except Exception as e:
            print(f"Erreur lors du chargement des modèles: {e}")
            import traceback
            traceback.print_exc()
            return False
