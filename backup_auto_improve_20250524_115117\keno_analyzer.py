import numpy as np
from collections import Counter
import random
import os
from keno_data import KenoDataManager

# Définir la variable pour l'analyse avancée
ADVANCED_ANALYSIS_AVAILABLE = False

class KenoAnalyzer:
    """Classe d'analyse et de prédiction pour le Keno"""

    def __init__(self, data_manager=None):
        """
        Initialise l'analyseur Ken<PERSON>

        Args:
            data_manager (KenoDataManager, optional): Gestionnaire de données Keno
        """
        self.data_manager = data_manager if data_manager else KenoDataManager()
        self.max_number = self.data_manager.max_number
        self.numbers_per_draw = self.data_manager.numbers_per_draw

        # Attribut pour contrôler l'arrêt de l'auto-amélioration
        self.stop_auto_improve = False

        # Initialiser l'analyseur avancé si disponible
        self.advanced_analyzer = None

        # Essayer d'importer et d'initialiser l'analyseur avancé de manière différée
        try:
            # Importer la classe d'analyse avancée
            from keno_advanced_analyzer import KenoAdvancedAnalyzer
            # Définir la variable globale
            global ADVANCED_ANALYSIS_AVAILABLE
            ADVANCED_ANALYSIS_AVAILABLE = True
            # Initialiser l'analyseur avancé
            self.advanced_analyzer = KenoAdvancedAnalyzer(self.data_manager)

            # Charger les données d'apprentissage si disponibles, mais ne pas lancer l'auto-amélioration
            try:
                from save_learning_data import load_learning_data
                if os.path.exists('keno_learning_data.json'):
                    print("Chargement des données d'apprentissage...")
                    # Désactiver temporairement l'auto-amélioration pendant le chargement
                    auto_improve_enabled = False
                    if hasattr(self.advanced_analyzer, 'auto_improve_enabled'):
                        auto_improve_enabled = self.advanced_analyzer.auto_improve_enabled
                        self.advanced_analyzer.auto_improve_enabled = False

                    # Charger les données
                    load_success = load_learning_data(self, 'keno_learning_data.json')

                    # Restaurer l'état de l'auto-amélioration
                    if hasattr(self.advanced_analyzer, 'auto_improve_enabled'):
                        self.advanced_analyzer.auto_improve_enabled = auto_improve_enabled

                    if load_success:
                        print("Données d'apprentissage chargées avec succès")
            except ImportError:
                print("Module load_learning_data non disponible")
            except Exception as e:
                print(f"Erreur lors du chargement des données d'apprentissage: {e}")
        except ImportError:
            print("Module d'analyse avancée non disponible")
        except Exception as e:
            print(f"Erreur lors de l'initialisation de l'analyseur avancé: {e}")

    def update_parameters(self):
        """Met à jour les paramètres depuis le gestionnaire de données"""
        self.max_number = self.data_manager.max_number
        self.numbers_per_draw = self.data_manager.numbers_per_draw

    def _ensure_unique_numbers(self, numbers, num_predictions):
        """
        S'assure que les numéros prédits sont uniques

        Args:
            numbers (list): Liste des numéros prédits (peut contenir des doublons)
            num_predictions (int): Nombre de numéros à prédire

        Returns:
            list: Liste des numéros uniques
        """
        # Convertir en ensemble pour éliminer les doublons
        unique_numbers = list(set(numbers))

        # Si on a perdu des numéros à cause des doublons, en ajouter d'autres
        while len(unique_numbers) < num_predictions:
            # Générer un numéro aléatoire qui n'est pas déjà dans la liste
            new_num = random.randint(1, self.max_number)
            if new_num not in unique_numbers:
                unique_numbers.append(new_num)

        # Si on a trop de numéros, garder seulement les premiers
        if len(unique_numbers) > num_predictions:
            unique_numbers = unique_numbers[:num_predictions]

        return unique_numbers

    def predict_next_draw(self, num_predictions=10, method="combined"):
        """
        Prédit les numéros pour le prochain tirage

        Args:
            num_predictions (int): Nombre de numéros à prédire
            method (str): Méthode de prédiction ('frequency', 'pattern', 'random', 'combined', 'ml', 'patterns', 'series', 'advanced', 'reinforcement_learning')

        Returns:
            list: Liste des numéros prédits
            float: Score de confiance (0-100)
        """
        try:
            # Vérifier qu'il y a des données
            if not self.data_manager.draws:
                print("Aucune donnée disponible, génération de nombres aléatoires")
                return list(range(1, num_predictions + 1)), 0.0

            # Mettre à jour les paramètres
            self.update_parameters()

            # Afficher des informations de débogage
            print(f"Méthode de prédiction: {method}")
            print(f"Nombre de tirages disponibles: {len(self.data_manager.draws)}")
            print(f"Analyse avancée disponible: {ADVANCED_ANALYSIS_AVAILABLE}")
            print(f"Analyseur avancé initialisé: {self.advanced_analyzer is not None}")

            # Vérifier si la méthode est reinforcement_learning
            if method == "reinforcement_learning":
                print("Tentative de prédiction avec apprentissage par renforcement")
                try:
                    # Vérifier si le module de feedback est disponible
                    from keno_feedback import KenoFeedback

                    # Créer une instance temporaire du gestionnaire de feedback
                    feedback_manager = KenoFeedback(self.data_manager, self)

                    # Vérifier si l'agent RL est disponible
                    if hasattr(feedback_manager, 'rl_agent') and feedback_manager.rl_agent:
                        # Prédire avec l'agent RL
                        predicted_numbers = feedback_manager.predict_with_rl(num_predictions)
                        if predicted_numbers:
                            unique_numbers = self._ensure_unique_numbers(predicted_numbers, num_predictions)
                            return unique_numbers, 90.0
                    else:
                        print("Agent RL non disponible, utilisation d'une autre méthode")
                except ImportError:
                    print("Module de feedback non disponible, utilisation d'une autre méthode")
                except Exception as e:
                    print(f"Erreur lors de la prédiction par apprentissage par renforcement: {e}")

            # Méthodes d'analyse avancée
            if ADVANCED_ANALYSIS_AVAILABLE and self.advanced_analyzer:
                try:
                    if method == "ml":
                        print("Tentative de prédiction avec ML")
                        predictions = self.advanced_analyzer.predict_with_ml(num_predictions * 2)  # Demander plus de numéros pour éviter les doublons
                        numbers = [p['number'] for p in predictions]
                        unique_numbers = self._ensure_unique_numbers(numbers, num_predictions)
                        return unique_numbers, 85.0
                    elif method == "patterns":
                        print("Tentative de prédiction avec patterns")
                        predictions = self.advanced_analyzer.predict_with_patterns(num_predictions * 2)
                        numbers = [p['number'] for p in predictions]
                        unique_numbers = self._ensure_unique_numbers(numbers, num_predictions)
                        return unique_numbers, 80.0
                    elif method == "series":
                        print("Tentative de prédiction avec series")
                        predictions = self.advanced_analyzer.predict_with_series(num_predictions * 2)
                        numbers = [p['number'] for p in predictions]
                        unique_numbers = self._ensure_unique_numbers(numbers, num_predictions)
                        return unique_numbers, 80.0
                    elif method == "advanced":
                        print("Tentative de prédiction avec advanced")
                        predictions = self.advanced_analyzer.predict_combined_advanced(num_predictions * 2)
                        numbers = [p['number'] for p in predictions]
                        unique_numbers = self._ensure_unique_numbers(numbers, num_predictions)
                        return unique_numbers, 90.0
                except Exception as e:
                    print(f"Erreur lors de la prédiction avancée avec la méthode {method}: {e}")
                    # Continuer avec les méthodes classiques

            # Méthodes classiques
            print(f"Utilisation de la méthode classique: {method}")
            if method == "frequency":
                numbers, confidence = self._predict_by_frequency(num_predictions)
                unique_numbers = self._ensure_unique_numbers(numbers, num_predictions)
                return unique_numbers, confidence
            elif method == "pattern":
                numbers, confidence = self._predict_by_pattern(num_predictions)
                unique_numbers = self._ensure_unique_numbers(numbers, num_predictions)
                return unique_numbers, confidence
            elif method == "random":
                numbers, confidence = self._predict_random(num_predictions)
                unique_numbers = self._ensure_unique_numbers(numbers, num_predictions)
                return unique_numbers, confidence
            elif method == "ml":
                # Si l'analyse avancée n'est pas disponible, utiliser la méthode interne
                try:
                    numbers, confidence = self._predict_by_ml(num_predictions)
                    unique_numbers = self._ensure_unique_numbers(numbers, num_predictions)
                    return unique_numbers, confidence
                except Exception as e:
                    print(f"Erreur lors de la prédiction par ML: {e}")
                    numbers, confidence = self._predict_by_frequency(num_predictions)
                    unique_numbers = self._ensure_unique_numbers(numbers, num_predictions)
                    return unique_numbers, confidence
            else:  # combined (default)
                # Utiliser directement la méthode combinée classique pour éviter de déclencher l'auto-amélioration
                # Note: Nous évitons d'utiliser predict_combined_advanced qui peut déclencher l'auto-amélioration

                try:
                    numbers, confidence = self._predict_combined(num_predictions)
                    unique_numbers = self._ensure_unique_numbers(numbers, num_predictions)
                    return unique_numbers, confidence
                except Exception as e:
                    print(f"Erreur lors de la prédiction combinée: {e}")
                    numbers, confidence = self._predict_by_frequency(num_predictions)
                    unique_numbers = self._ensure_unique_numbers(numbers, num_predictions)
                    return unique_numbers, confidence
        except Exception as e:
            print(f"Erreur générale lors de la prédiction: {e}")
            # En cas d'erreur, utiliser la méthode de fréquence comme secours
            try:
                numbers, confidence = self._predict_by_frequency(num_predictions)
                unique_numbers = self._ensure_unique_numbers(numbers, num_predictions)
                return unique_numbers, confidence
            except Exception as e2:
                print(f"Erreur lors de la prédiction de secours: {e2}")
                # En dernier recours, retourner des nombres aléatoires
                numbers, confidence = self._predict_random(num_predictions)
                unique_numbers = self._ensure_unique_numbers(numbers, num_predictions)
                return unique_numbers, confidence

    def _predict_by_frequency(self, num_predictions):
        """
        Prédit en se basant sur la fréquence d'apparition des numéros

        Args:
            num_predictions (int): Nombre de numéros à prédire

        Returns:
            list: Liste des numéros prédits
            float: Score de confiance (0-100)
        """
        # Obtenir les fréquences
        frequencies = self.data_manager.get_number_frequency()

        # Trier par fréquence décroissante
        sorted_numbers = sorted(frequencies.items(), key=lambda x: x[1], reverse=True)

        # Sélectionner les numéros les plus fréquents
        predicted_numbers = [num for num, _ in sorted_numbers[:num_predictions]]

        # Calculer la confiance (moyenne des fréquences des numéros prédits)
        confidence = sum(frequencies[num] for num in predicted_numbers) / num_predictions

        return predicted_numbers, min(95.0, confidence)

    def _predict_by_pattern(self, num_predictions):
        """
        Prédit en cherchant des patterns dans les tirages précédents

        Args:
            num_predictions (int): Nombre de numéros à prédire

        Returns:
            list: Liste des numéros prédits
            float: Score de confiance (0-100)
        """
        if len(self.data_manager.draws) < 5:
            return self._predict_random(num_predictions)

        # Récupérer les 5 derniers tirages
        last_draws = self.data_manager.get_last_draws(5)
        last_numbers = [draw.draw_numbers for draw in last_draws]

        # Compter les occurrences de chaque numéro dans les derniers tirages
        counter = Counter()
        for draw in last_numbers:
            counter.update(draw)

        # Trouver les numéros qui apparaissent dans plusieurs tirages récents
        recurring_numbers = [num for num, count in counter.items() if count > 1]

        # Compléter avec des numéros fréquents si nécessaire
        if len(recurring_numbers) < num_predictions:
            freq_numbers, _ = self._predict_by_frequency(num_predictions - len(recurring_numbers))
            # Ajouter uniquement les numéros qui ne sont pas déjà dans recurring_numbers
            for num in freq_numbers:
                if num not in recurring_numbers:
                    recurring_numbers.append(num)
                    if len(recurring_numbers) >= num_predictions:
                        break

        # Si on a trop de numéros, garder seulement les plus fréquents
        if len(recurring_numbers) > num_predictions:
            recurring_numbers = sorted(recurring_numbers,
                                      key=lambda x: counter[x],
                                      reverse=True)[:num_predictions]

        # Si on n'a toujours pas assez de numéros, compléter avec des numéros aléatoires
        while len(recurring_numbers) < num_predictions:
            random_num = random.randint(1, self.max_number)
            if random_num not in recurring_numbers:
                recurring_numbers.append(random_num)

        # Calculer la confiance basée sur la fréquence d'apparition dans les derniers tirages
        confidence = sum(counter[num] / 5 * 100 for num in recurring_numbers) / num_predictions

        return recurring_numbers, min(90.0, confidence)

    def _predict_random(self, num_predictions):
        """
        Génère des prédictions aléatoires

        Args:
            num_predictions (int): Nombre de numéros à prédire

        Returns:
            list: Liste des numéros prédits
            float: Score de confiance (0-100)
        """
        # Générer des numéros aléatoires uniques
        predicted_numbers = random.sample(range(1, self.max_number + 1), num_predictions)

        # Confiance faible pour les prédictions aléatoires
        confidence = 10.0

        return predicted_numbers, confidence

    def get_detailed_prediction(self, num_predictions=10, method="combined"):
        """
        Obtient une prédiction détaillée avec des informations supplémentaires

        Args:
            num_predictions (int): Nombre de numéros à prédire
            method (str): Méthode de prédiction

        Returns:
            dict: Dictionnaire contenant les numéros prédits et des informations supplémentaires
        """
        # Pour les méthodes avancées, utiliser directement les résultats détaillés
        if ADVANCED_ANALYSIS_AVAILABLE and self.advanced_analyzer:
            if method == "ml":
                predictions = self.advanced_analyzer.predict_with_ml(num_predictions)
                predicted_numbers = [p['number'] for p in predictions]
                return {
                    'method': "Apprentissage automatique",
                    'numbers': predicted_numbers,
                    'confidence': 85.0,
                    'details': predictions
                }
            elif method == "patterns":
                predictions = self.advanced_analyzer.predict_with_patterns(num_predictions)
                predicted_numbers = [p['number'] for p in predictions]
                return {
                    'method': "Analyse des motifs",
                    'numbers': predicted_numbers,
                    'confidence': 80.0,
                    'details': predictions
                }
            elif method == "series":
                predictions = self.advanced_analyzer.predict_with_series(num_predictions)
                predicted_numbers = [p['number'] for p in predictions]
                return {
                    'method': "Analyse des séries",
                    'numbers': predicted_numbers,
                    'confidence': 80.0,
                    'details': predictions
                }
            elif method == "advanced":
                # Utiliser la méthode combinée classique pour éviter de déclencher l'auto-amélioration
                predicted_numbers, confidence = self._predict_combined(num_predictions)
                # Créer des détails simplifiés pour chaque numéro
                details = [{'number': num, 'score': 90.0} for num in predicted_numbers]
                return {
                    'method': "Analyse avancée combinée",
                    'numbers': predicted_numbers,
                    'confidence': 90.0,
                    'details': details
                }

        # Pour les méthodes classiques
        predicted_numbers, confidence = self.predict_next_draw(num_predictions, method)

        # Obtenir des statistiques pour chaque numéro prédit
        details = []

        for num in predicted_numbers:
            # Calculer la fréquence d'apparition
            frequency = self.data_manager.get_number_frequency().get(num, 0)
            frequency_percent = frequency / len(self.data_manager.draws) * 100 if self.data_manager.draws else 0

            # Calculer la récence (nombre de tirages depuis la dernière apparition)
            try:
                recency = self.data_manager.get_number_recency().get(num, float('inf'))
            except AttributeError:
                # Si la méthode get_number_recency n'existe pas
                recency = 0

            # Ajouter les détails
            details.append({
                'number': num,
                'frequency': frequency,
                'frequency_percent': frequency_percent,
                'recency': recency
            })

        # Traduire le nom de la méthode
        method_names = {
            'frequency': 'Fréquence',
            'pattern': 'Motifs',
            'random': 'Aléatoire',
            'combined': 'Combinée'
        }

        return {
            'method': method_names.get(method, method),
            'numbers': predicted_numbers,
            'confidence': confidence,
            'details': details
        }

    def _predict_by_ml(self, num_predictions):
        """
        Prédit en utilisant l'apprentissage automatique

        Args:
            num_predictions (int): Nombre de numéros à prédire

        Returns:
            list: Liste des numéros prédits
            float: Score de confiance (0-100)
        """
        # Import modules nécessaires
        import numpy as np
        import sys
        import os
        print("Début de la prédiction par ML")

        # Vérifier si l'analyse avancée est disponible
        if not ADVANCED_ANALYSIS_AVAILABLE or not self.advanced_analyzer:
            print("Analyse avancée non disponible, utilisation de la méthode par fréquence")
            return self._predict_by_frequency(num_predictions)

        try:
            # Vérifier si la méthode predict_with_ml existe
            if not hasattr(self.advanced_analyzer, 'predict_with_ml'):
                print("Méthode predict_with_ml non disponible, utilisation de la méthode par fréquence")
                return self._predict_by_frequency(num_predictions)

            # Utiliser la méthode predict_with_ml de l'analyseur avancé
            print("Appel de la méthode predict_with_ml")
            predictions = self.advanced_analyzer.predict_with_ml(num_predictions)
            print(f"Résultat de predict_with_ml: {predictions}")

            # Extraire les numéros des prédictions
            if isinstance(predictions, list) and predictions:
                if isinstance(predictions[0], dict) and 'number' in predictions[0]:
                    # Si les prédictions sont au format [{'number': X, ...}, ...]
                    predicted_numbers = [pred['number'] for pred in predictions]
                    print(f"Numéros prédits (format dict): {predicted_numbers}")
                    return predicted_numbers, 85.0
                elif isinstance(predictions[0], (int, float)):
                    # Si les prédictions sont au format [X, Y, Z, ...]
                    print(f"Numéros prédits (format liste): {predictions}")
                    return predictions, 85.0

            # Si le format n'est pas reconnu, utiliser la prédiction par fréquence
            print("Format de prédiction non reconnu, utilisation de la méthode par fréquence")
            return self._predict_by_frequency(num_predictions)
        except Exception as e:
            print(f"Erreur lors de la prédiction par apprentissage automatique: {e}")
            # En cas d'erreur, utiliser la méthode par fréquence
            return self._predict_by_frequency(num_predictions)

    def _predict_combined(self, num_predictions):
        """
        Combine plusieurs méthodes de prédiction

        Args:
            num_predictions (int): Nombre de numéros à prédire

        Returns:
            list: Liste des numéros prédits
            float: Score de confiance (0-100)
        """
        import time
        start_time = time.time()
        print("Début de la prédiction combinée...")

        # Créer un cache pour les prédictions
        # Si le cache existe déjà et est récent (moins de 5 minutes), l'utiliser
        if hasattr(self, '_combined_cache') and hasattr(self, '_combined_cache_time'):
            cache_age = time.time() - self._combined_cache_time
            if cache_age < 300 and self._combined_cache.get('num_predictions') == num_predictions:  # 5 minutes
                print(f"Utilisation du cache de prédiction (age: {cache_age:.1f} secondes)")
                return self._combined_cache['numbers'], self._combined_cache['confidence']

        # Obtenir les prédictions de chaque méthode
        print("Obtention des prédictions par fréquence...")
        freq_numbers, freq_confidence = self._predict_by_frequency(num_predictions)

        print("Obtention des prédictions par motifs...")
        pattern_numbers, pattern_confidence = self._predict_by_pattern(num_predictions)

        # S'assurer que les numéros sont uniques
        freq_numbers = list(set(freq_numbers))
        pattern_numbers = list(set(pattern_numbers))

        # Ajouter les prédictions des méthodes avancées si disponibles
        ml_numbers = []
        pattern_adv_numbers = []
        series_numbers = []
        ml_conf = 0.0
        pattern_adv_conf = 0.0
        series_conf = 0.0

        # Utiliser un thread pour chaque méthode avancée pour accélérer le processus
        if ADVANCED_ANALYSIS_AVAILABLE and self.advanced_analyzer:
            import threading
            import queue

            # File pour stocker les résultats des threads
            results_queue = queue.Queue()

            # Fonction pour exécuter une méthode de prédiction dans un thread
            def run_prediction(method_name, num_preds):
                try:
                    # Import tous les modules nécessaires
                    import numpy as np
                    import sys
                    import os
                    import time
                    import threading

                    if method_name == 'ml':
                        print("Obtention des prédictions par apprentissage automatique...")
                        ml_preds = self.advanced_analyzer.predict_with_ml(num_preds)
                        numbers = [p['number'] for p in ml_preds]
                        results_queue.put(('ml', list(set(numbers)), 85.0))
                    elif method_name == 'patterns':
                        print("Obtention des prédictions par motifs avancés...")
                        pattern_preds = self.advanced_analyzer.predict_with_patterns(num_preds)
                        numbers = [p['number'] for p in pattern_preds]
                        results_queue.put(('patterns', list(set(numbers)), 80.0))
                    elif method_name == 'series':
                        print("Obtention des prédictions par séries...")
                        series_preds = self.advanced_analyzer.predict_with_series(num_preds)
                        numbers = [p['number'] for p in series_preds]
                        results_queue.put(('series', list(set(numbers)), 80.0))
                except Exception as e:
                    print(f"Erreur lors de la prédiction {method_name}: {e}")
                    results_queue.put((method_name, [], 0.0))

            # Créer et démarrer les threads
            threads = []
            for method in ['ml', 'patterns', 'series']:
                thread = threading.Thread(target=run_prediction, args=(method, num_predictions * 2))
                thread.daemon = True  # Le thread s'arrête quand le programme principal s'arrête
                thread.start()
                threads.append(thread)

            # Attendre que tous les threads terminent (avec timeout)
            timeout = 10  # secondes
            for thread in threads:
                thread.join(timeout)

            # Récupérer les résultats
            while not results_queue.empty():
                method, numbers, conf = results_queue.get()
                if method == 'ml':
                    ml_numbers = numbers
                    ml_conf = conf
                elif method == 'patterns':
                    pattern_adv_numbers = numbers
                    pattern_adv_conf = conf
                elif method == 'series':
                    series_numbers = numbers
                    series_conf = conf

        # Créer un dictionnaire pour stocker les scores de chaque numéro
        scores = {i: 0.0 for i in range(1, self.max_number + 1)}

        # Ajouter des points pour les numéros fréquents
        for i, num in enumerate(freq_numbers):
            # Plus le numéro est haut dans la liste, plus il reçoit de points
            scores[num] += freq_confidence * (1.0 - i/len(freq_numbers))

        # Ajouter des points pour les numéros avec pattern
        for i, num in enumerate(pattern_numbers):
            # Plus le numéro est haut dans la liste, plus il reçoit de points
            scores[num] += pattern_confidence * (1.0 - i/num_predictions)

        # Ajouter des points pour les méthodes avancées
        if ml_numbers:
            for i, num in enumerate(ml_numbers):
                scores[num] += ml_conf * (1.0 - i/num_predictions) * 0.8

        if pattern_adv_numbers:
            for i, num in enumerate(pattern_adv_numbers):
                scores[num] += pattern_adv_conf * (1.0 - i/num_predictions) * 0.6

        if series_numbers:
            for i, num in enumerate(series_numbers):
                scores[num] += series_conf * (1.0 - i/num_predictions) * 0.6

        # Ajouter un bonus pour les numéros qui apparaissent dans plusieurs méthodes
        for num in set(freq_numbers) & set(pattern_numbers):
            scores[num] += 20.0

        if ml_numbers:
            for num in set(freq_numbers) & set(ml_numbers):
                scores[num] += 20.0
            for num in set(pattern_numbers) & set(ml_numbers):
                scores[num] += 20.0

        if pattern_adv_numbers:
            for num in set(freq_numbers) & set(pattern_adv_numbers):
                scores[num] += 15.0

        if series_numbers:
            for num in set(freq_numbers) & set(series_numbers):
                scores[num] += 15.0

        # Trier les numéros par score décroissant
        sorted_numbers = sorted(scores.items(), key=lambda x: x[1], reverse=True)

        # Sélectionner les numéros avec les scores les plus élevés (prendre plus de numéros pour éviter les doublons)
        predicted_numbers = [num for num, _ in sorted_numbers[:num_predictions*2]]

        # S'assurer que les numéros sont uniques
        unique_numbers = self._ensure_unique_numbers(predicted_numbers, num_predictions)

        # Calculer la confiance (moyenne des scores normalisés)
        max_score = sorted_numbers[0][1] if sorted_numbers else 1.0
        confidence = sum(scores[num] for num in unique_numbers) / (num_predictions * max_score) * 100

        # Ajuster la confiance en fonction des méthodes disponibles
        if ADVANCED_ANALYSIS_AVAILABLE and self.advanced_analyzer:
            if ml_numbers or pattern_adv_numbers or series_numbers:
                confidence = min(98.0, confidence * 1.2)  # Augmenter la confiance avec les méthodes avancées

        # Stocker les résultats dans le cache
        self._combined_cache = {
            'numbers': unique_numbers,
            'confidence': min(95.0, confidence),
            'num_predictions': num_predictions,
            'time': time.time()
        }
        self._combined_cache_time = time.time()

        # Afficher le temps d'exécution
        end_time = time.time()
        print(f"Prédiction combinée terminée en {end_time - start_time:.2f} secondes")

        return unique_numbers, min(95.0, confidence)

    def get_prediction_stats(self, num_predictions=10):
        """
        Génère des statistiques sur les prédictions sans déclencher l'auto-amélioration

        Args:
            num_predictions (int): Nombre de numéros à prédire

        Returns:
            dict: Dictionnaire contenant les statistiques
        """
        if not self.data_manager.draws:
            return {
                "total_draws": 0,
                "hot_numbers": [],
                "cold_numbers": [],
                "due_numbers": [],
                "predictions": {
                    "frequency": ([], 0.0),
                    "pattern": ([], 0.0),
                    "combined": ([], 0.0)
                }
            }

        # Obtenir les statistiques de base
        hot_numbers = self.data_manager.get_hot_numbers(num_predictions)
        cold_numbers = self.data_manager.get_cold_numbers(num_predictions)
        due_numbers = self.data_manager.get_due_numbers(num_predictions)

        # Obtenir les prédictions avec les méthodes de base uniquement
        # pour éviter de déclencher l'auto-amélioration
        freq_pred, freq_conf = self._predict_by_frequency(num_predictions)
        pattern_pred, pattern_conf = self._predict_by_pattern(num_predictions)

        # Au lieu d'utiliser _predict_combined qui peut déclencher l'auto-amélioration,
        # créer une prédiction combinée simplifiée
        # Combiner les prédictions des méthodes de base
        combined_numbers = list(set(freq_pred + pattern_pred))
        # S'assurer que nous avons le bon nombre de prédictions
        if len(combined_numbers) > num_predictions:
            combined_numbers = combined_numbers[:num_predictions]
        elif len(combined_numbers) < num_predictions:
            # Ajouter des numéros aléatoires si nécessaire
            while len(combined_numbers) < num_predictions:
                new_num = random.randint(1, self.max_number)
                if new_num not in combined_numbers:
                    combined_numbers.append(new_num)

        # Calculer une confiance combinée simplifiée
        combined_conf = (freq_conf + pattern_conf) / 2

        return {
            "total_draws": self.data_manager.get_draws_count(),
            "hot_numbers": hot_numbers,
            "cold_numbers": cold_numbers,
            "due_numbers": due_numbers,
            "predictions": {
                "frequency": (freq_pred, freq_conf),
                "pattern": (pattern_pred, pattern_conf),
                "combined": (combined_numbers, combined_conf)
            }
        }

    def auto_improve(self, callback=None, fast_mode=False, timeout=1800, ultra_fast=False, max_numbers=None, resume=True):
        """
        Améliore automatiquement les modèles de prédiction

        Args:
            callback (function, optional): Fonction de rappel pour mettre à jour la progression
                                         Signature: callback(step, total_steps, message)
            fast_mode (bool, optional): Si True, utilise le mode rapide pour l'entraînement
                                      (plus rapide mais moins précis)
            timeout (int, optional): Temps maximum en secondes pour l'entraînement
            ultra_fast (bool, optional): Si True, utilise le mode ultra-rapide pour l'entraînement
                                       (très rapide mais moins précis)
            max_numbers (int, optional): Nombre maximum de numéros à entraîner

        Returns:
            dict: Dictionnaire contenant les résultats de l'amélioration
                 {'success': bool, 'message': str, 'stats': dict}
        """
        # Import tous les modules nécessaires
        import numpy as np
        import sys
        import os
        import time
        import threading
        import queue
        # Initialiser les résultats
        results = {
            'success': False,
            'message': "",
            'stats': {}
        }

        # Vérifier s'il y a suffisamment de données
        if len(self.data_manager.draws) < 50:
            message = "Pas assez de données pour améliorer les modèles (minimum 50 tirages requis)"
            print(message)
            results['message'] = message
            return results

        # Définir le nombre total d'étapes
        total_steps = 10
        current_step = 0

        # Fonction pour mettre à jour la progression
        def update_progress(step_increment, message):
            nonlocal current_step
            current_step += step_increment
            print(message)
            if callback:
                # Appeler la fonction de rappel et vérifier son retour
                result = callback(current_step, total_steps, message)
                # Si la fonction de rappel retourne False, arrêter le processus
                if result is False:
                    print("Arrêt demandé par l'utilisateur")
                    # Mettre à jour les résultats pour indiquer l'arrêt
                    results['message'] = "Processus arrêté par l'utilisateur"
                    results['success'] = False
                    results['interrupted'] = True
                    # Retourner False pour indiquer l'arrêt
                    return False
            # Continuer le processus
            return True

        # Si l'analyseur avancé est disponible, utiliser ses méthodes
        if ADVANCED_ANALYSIS_AVAILABLE and self.advanced_analyzer:
            try:
                # Étape 1: Préparer les données
                if not update_progress(1, "Préparation des données..."):
                    return results
                self.advanced_analyzer.prepare_data()

                # Étape 2: Analyser les motifs
                if not update_progress(1, "Analyse des motifs..."):
                    return results
                patterns = self.advanced_analyzer.analyze_patterns()
                results['stats']['patterns'] = len(patterns) if isinstance(patterns, dict) else 0

                # Étape 3: Analyser les corrélations
                if not update_progress(1, "Analyse des corrélations..."):
                    return results
                correlations = self.advanced_analyzer.analyze_correlations()
                results['stats']['correlations'] = len(correlations) if isinstance(correlations, dict) else 0

                # Étape 4: Analyser les séries
                if not update_progress(1, "Analyse des séries..."):
                    return results
                series = self.advanced_analyzer.analyze_series()
                results['stats']['series'] = len(series) if isinstance(series, dict) else 0

                # Étape 5-6: Entraîner les modèles avec une configuration optimisée
                if not update_progress(2, "Entraînement des modèles (phase 1/2)..."):
                    return results
                # Créer un objet pour stocker le drapeau d'arrêt
                class StopFlag:
                    def __init__(self):
                        self.value = False
                stop_flag = StopFlag()

                # Mettre à jour le drapeau d'arrêt lorsque stop_auto_improve est True
                def update_stop_flag():
                    if hasattr(self, 'stop_auto_improve') and self.stop_auto_improve:
                        stop_flag.value = True
                        print("\nDrapeau d'arrêt activé - Interruption demandée par l'utilisateur")
                        return True
                    return False

                # Vérifier périodiquement si l'arrêt a été demandé
                import threading
                def check_stop_periodically():
                    while not stop_flag.value:
                        # Vérifier toutes les 0.5 secondes
                        time.sleep(0.5)
                        update_stop_flag()
                        if stop_flag.value:
                            print("\nInterruption détectée par le thread de surveillance")
                            break

                # Démarrer un thread de surveillance pour vérifier périodiquement si l'arrêt a été demandé
                stop_monitor = threading.Thread(target=check_stop_periodically)
                stop_monitor.daemon = True
                stop_monitor.start()

                # Utiliser un seul entraînement avec des paramètres adaptés au mode choisi
                # test_size=0.2 est un bon compromis entre vitesse et précision
                if ultra_fast:
                    print("Utilisation du mode ULTRA-RAPIDE pour l'entraînement (phase 1/2)")
                elif fast_mode:
                    print("Utilisation du mode rapide pour l'entraînement (phase 1/2)")
                else:
                    print("Utilisation du mode complet pour l'entraînement (phase 1/2)")
                success1 = self.advanced_analyzer.train_models(test_size=0.2, random_state=42, stop_flag=stop_flag,
                                                            fast_mode=fast_mode, timeout=timeout,
                                                            ultra_fast=ultra_fast, max_numbers=max_numbers,
                                                            resume=resume)
                update_stop_flag()
                if self.stop_auto_improve:
                    return results

                # Si on est en mode ultra-rapide, sauter la deuxième phase d'entraînement
                if ultra_fast:
                    success2 = True  # Simuler un succès pour la phase 2
                    if not update_progress(2, "Phase 2 ignorée en mode ULTRA-RAPIDE"):
                        return results
                else:
                    if not update_progress(2, "Entraînement des modèles (phase 2/2)..."):
                        return results
                    # Utiliser un deuxième entraînement avec des paramètres différents pour améliorer la robustesse
                    if fast_mode:
                        print("Utilisation du mode rapide pour l'entraînement (phase 2/2)")
                    else:
                        print("Utilisation du mode complet pour l'entraînement (phase 2/2)")
                    # Phase 2 : ne pas utiliser la reprise pour forcer l'entraînement de tous les modèles
                    print("Phase 2 : Ignorer les fichiers sauvegardés pour forcer l'entraînement de tous les modèles")
                    success2 = self.advanced_analyzer.train_models(test_size=0.25, random_state=123, stop_flag=stop_flag,
                                                                fast_mode=fast_mode, timeout=timeout,
                                                                ultra_fast=ultra_fast, max_numbers=max_numbers,
                                                                resume=False)
                update_stop_flag()
                if self.stop_auto_improve:
                    return results

                # Nous n'effectuons que 2 entraînements au lieu de 4 pour accélérer le processus
                success3 = True  # Simulé pour maintenir la compatibilité
                success4 = True  # Simulé pour maintenir la compatibilité

                # Étape 9: Évaluer les modèles
                if not update_progress(1, "Validation des modèles..."):
                    return results

                # Compter le nombre de modèles entraînés
                num_models = 0
                if hasattr(self.advanced_analyzer, 'models') and self.advanced_analyzer.models:
                    if 'targets' in self.advanced_analyzer.models:
                        num_models = len(self.advanced_analyzer.models['targets'])

                results['stats']['models'] = num_models
                results['stats']['success_rate'] = sum([success1, success2, success3, success4]) / 4 * 100
                results['stats']['fast_mode'] = fast_mode
                results['stats']['ultra_fast'] = ultra_fast
                if max_numbers is not None:
                    results['stats']['max_numbers'] = max_numbers

                # Étape 10: Sauvegarder les modèles et les données d'apprentissage
                if not update_progress(1, "Sauvegarde des modèles et des données d'apprentissage..."):
                    return results
                save_success = False

                # Sauvegarder les modèles
                if hasattr(self.advanced_analyzer, 'save_models'):
                    save_success = self.advanced_analyzer.save_models('keno_ml_models.pkl')

                # Sauvegarder les données d'apprentissage
                try:
                    from save_learning_data import save_learning_data
                    save_learning_success = save_learning_data(self, 'keno_learning_data.json')
                    if save_learning_success:
                        print("Données d'apprentissage sauvegardées avec succès")
                        save_success = save_success or save_learning_success
                except ImportError:
                    print("Module save_learning_data non disponible")

                # Déterminer le succès global
                success = num_models > 0 and (success1 or success2 or success3 or success4)
                results['success'] = success

                if success:
                    message = f"Amélioration réussie! {num_models} modèles entraînés."
                    if save_success:
                        message += " Modèles sauvegardés."
                else:
                    message = "L'amélioration a échoué ou n'a produit aucun modèle valide."

                print(message)
                results['message'] = message
                return results

            except Exception as e:
                message = f"Erreur lors de l'amélioration des modèles avancés: {e}"
                print(message)
                results['message'] = message
                return results
        else:
            # Si l'analyseur avancé n'est pas disponible, améliorer les méthodes de base
            try:
                # Étape 1-5: Évaluer les méthodes actuelles avec différentes tailles de test
                if not update_progress(2, "Analyse des données..."):
                    return results

                # Évaluer avec différentes tailles de test
                if not update_progress(2, "Évaluation des méthodes de prédiction (phase 1/3)..."):
                    return results
                accuracy_stats1 = self.evaluate_prediction_accuracy(test_size=10)

                if not update_progress(2, "Évaluation des méthodes de prédiction (phase 2/3)..."):
                    return results
                accuracy_stats2 = self.evaluate_prediction_accuracy(test_size=20)

                if not update_progress(2, "Évaluation des méthodes de prédiction (phase 3/3)..."):
                    return results
                accuracy_stats3 = self.evaluate_prediction_accuracy(test_size=30)

                # Calculer la précision moyenne
                avg_accuracy = (accuracy_stats1['accuracy'] + accuracy_stats2['accuracy'] + accuracy_stats3['accuracy']) / 3

                # Trouver la meilleure méthode
                method_accuracies = {}
                for method in ['frequency', 'pattern', 'combined']:
                    method_accuracies[method] = (accuracy_stats1['method_accuracy'][method] +
                                               accuracy_stats2['method_accuracy'][method] +
                                               accuracy_stats3['method_accuracy'][method]) / 3

                best_method = max(method_accuracies, key=method_accuracies.get)

                # Mettre à jour les résultats
                results['stats']['accuracy'] = avg_accuracy
                results['stats']['method_accuracies'] = method_accuracies
                results['stats']['best_method'] = best_method
                results['success'] = True

                message = f"Précision moyenne: {avg_accuracy:.2f}%\n"
                message += f"Meilleure méthode: {best_method} ({method_accuracies[best_method]:.2f}%)"
                print(message)
                results['message'] = message

                if not update_progress(2, "Optimisation terminée!"):
                    return results
                return results

            except Exception as e:
                message = f"Erreur lors de l'amélioration des modèles de base: {e}"
                print(message)
                results['message'] = message
                return results

    def evaluate_prediction_accuracy(self, test_size=10):
        """
        Évalue la précision des prédictions sur les derniers tirages

        Args:
            test_size (int): Nombre de tirages à utiliser pour l'évaluation

        Returns:
            dict: Dictionnaire contenant les statistiques de précision
        """
        if len(self.data_manager.draws) <= test_size:
            return {
                "accuracy": 0.0,
                "method_accuracy": {
                    "frequency": 0.0,
                    "pattern": 0.0,
                    "combined": 0.0
                }
            }

        # Sauvegarder les tirages actuels
        original_draws = self.data_manager.draws.copy()

        # Initialiser les compteurs de précision
        method_hits = {
            "frequency": 0,
            "pattern": 0,
            "combined": 0
        }

        # Pour chaque tirage de test
        for i in range(test_size):
            # Définir les données d'entraînement (tous les tirages sauf le dernier)
            test_draw = original_draws[-(i+1)]
            self.data_manager.draws = original_draws[:-(i+1)]

            # Faire des prédictions avec chaque méthode (sans déclencher l'auto-amélioration)
            for method in ["frequency", "pattern", "combined"]:
                # Utiliser directement les méthodes internes pour éviter de déclencher l'auto-amélioration
                if method == "frequency":
                    predicted_numbers, _ = self._predict_by_frequency(self.numbers_per_draw)
                elif method == "pattern":
                    predicted_numbers, _ = self._predict_by_pattern(self.numbers_per_draw)
                elif method == "combined":
                    predicted_numbers, _ = self._predict_combined(self.numbers_per_draw)

                # Compter les hits (numéros correctement prédits)
                hits = len(set(predicted_numbers) & set(test_draw.draw_numbers))
                method_hits[method] += hits

        # Restaurer les tirages originaux
        self.data_manager.draws = original_draws

        # Calculer les précisions
        total_possible_hits = test_size * self.numbers_per_draw
        method_accuracy = {
            method: (hits / total_possible_hits) * 100
            for method, hits in method_hits.items()
        }

        # Calculer la précision globale (moyenne des méthodes)
        overall_accuracy = sum(method_accuracy.values()) / len(method_accuracy)

        return {
            "accuracy": overall_accuracy,
            "method_accuracy": method_accuracy
        }
