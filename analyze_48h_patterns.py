#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module pour analyser les patterns sur 48 heures dans les tirages Keno
Ce module identifie les similitudes entre les tirages consécutifs sur une période de 48 heures
et utilise ces informations pour améliorer les prédictions.
"""

import os
import sys
import numpy as np
import pandas as pd
import json
import datetime
import matplotlib.pyplot as plt
from collections import Counter, defaultdict
from sklearn.metrics import accuracy_score

# Ajouter le répertoire courant au chemin de recherche Python
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, BASE_DIR)

# Importer les modules nécessaires
from keno_data import KenoDataManager
from keno_advanced_analyzer import KenoAdvancedAnalyzer

# Supprimer les avertissements
import warnings
warnings.filterwarnings('ignore')

def analyze_48h_patterns():
    """Analyse les patterns sur 48 heures dans les tirages Keno"""
    print("Analyse des patterns sur 48 heures dans les tirages Keno...")

    # Charger les données
    data_manager = KenoDataManager()
    data_file = os.path.join(BASE_DIR, "data", "datafull.keno")

    # Vérifier si le fichier existe
    if not os.path.exists(data_file):
        print(f"Fichier de données {data_file} introuvable.")
        return None

    # Charger les données avec la méthode load_csv
    data_manager.load_csv(data_file, clear_existing=True)

    # Obtenir les tirages
    draws = data_manager.draws

    # Vérifier qu'il y a suffisamment de données
    if len(draws) < 10:
        print("Pas assez de données pour l'analyse (minimum 10 tirages)")
        return None

    # Convertir les dates en objets datetime
    for draw in draws:
        if 'date' in draw and isinstance(draw['date'], str):
            try:
                draw['datetime'] = datetime.datetime.strptime(draw['date'], '%Y-%m-%d %H:%M:%S')
            except ValueError:
                try:
                    draw['datetime'] = datetime.datetime.strptime(draw['date'], '%Y-%m-%d')
                except ValueError:
                    draw['datetime'] = None

    # Trier les tirages par date
    sorted_draws = sorted(draws, key=lambda x: x.get('datetime', datetime.datetime.min))

    # Analyser les patterns sur 48 heures
    patterns_48h = analyze_consecutive_draws(sorted_draws)

    # Sauvegarder les résultats
    save_patterns(patterns_48h)

    # Afficher les résultats
    print_patterns_summary(patterns_48h)

    return patterns_48h

def analyze_consecutive_draws(draws):
    """
    Analyse les patterns entre tirages consécutifs

    Args:
        draws (list): Liste des tirages triés par date

    Returns:
        dict: Dictionnaire des patterns identifiés
    """
    patterns = {
        'common_numbers': defaultdict(int),  # Nombre de numéros communs entre tirages consécutifs
        'position_patterns': defaultdict(int),  # Patterns de position des numéros communs
        'evening_noon_patterns': defaultdict(int),  # Patterns entre tirages du soir et du midi
        'noon_evening_patterns': defaultdict(int),  # Patterns entre tirages du midi et du soir
        'evening_evening_patterns': defaultdict(int),  # Patterns entre tirages du soir consécutifs
        'noon_noon_patterns': defaultdict(int),  # Patterns entre tirages du midi consécutifs
        '48h_patterns': defaultdict(int),  # Patterns sur 48 heures
    }

    # Analyser les tirages consécutifs
    for i in range(1, len(draws)):
        prev_draw = draws[i-1]
        curr_draw = draws[i]

        # Vérifier que les deux tirages ont des numéros
        if 'numbers' not in prev_draw or 'numbers' not in curr_draw:
            continue

        # Obtenir les numéros des tirages
        prev_numbers = set(prev_draw['numbers'])
        curr_numbers = set(curr_draw['numbers'])

        # Calculer les numéros communs
        common = prev_numbers.intersection(curr_numbers)
        patterns['common_numbers'][len(common)] += 1

        # Analyser les patterns de position
        for num in common:
            prev_pos = prev_draw['numbers'].index(num)
            curr_pos = curr_draw['numbers'].index(num)
            pos_diff = curr_pos - prev_pos
            patterns['position_patterns'][pos_diff] += 1

        # Analyser les patterns selon l'heure du tirage
        if 'datetime' in prev_draw and 'datetime' in curr_draw:
            prev_time = prev_draw['datetime'].time()
            curr_time = curr_draw['datetime'].time()

            # Déterminer si c'est un tirage du midi ou du soir
            prev_is_evening = prev_time.hour >= 18
            curr_is_evening = curr_time.hour >= 18

            # Patterns entre tirages du soir et du midi
            if prev_is_evening and not curr_is_evening:
                patterns['evening_noon_patterns'][len(common)] += 1

            # Patterns entre tirages du midi et du soir
            elif not prev_is_evening and curr_is_evening:
                patterns['noon_evening_patterns'][len(common)] += 1

            # Patterns entre tirages du soir consécutifs
            elif prev_is_evening and curr_is_evening:
                patterns['evening_evening_patterns'][len(common)] += 1

            # Patterns entre tirages du midi consécutifs
            elif not prev_is_evening and not curr_is_evening:
                patterns['noon_noon_patterns'][len(common)] += 1

    # Analyser les patterns sur 48 heures
    for i in range(2, len(draws)):
        draw_48h_ago = draws[i-2]
        curr_draw = draws[i]

        # Vérifier que les deux tirages ont des numéros
        if 'numbers' not in draw_48h_ago or 'numbers' not in curr_draw:
            continue

        # Obtenir les numéros des tirages
        prev_numbers = set(draw_48h_ago['numbers'])
        curr_numbers = set(curr_draw['numbers'])

        # Calculer les numéros communs
        common = prev_numbers.intersection(curr_numbers)
        patterns['48h_patterns'][len(common)] += 1

    return patterns

def save_patterns(patterns):
    """
    Sauvegarde les patterns identifiés dans un fichier JSON

    Args:
        patterns (dict): Dictionnaire des patterns identifiés
    """
    # Convertir les defaultdict en dictionnaires normaux
    patterns_json = {}
    for key, value in patterns.items():
        patterns_json[key] = dict(value)

    # Sauvegarder les patterns
    output_file = os.path.join(BASE_DIR, "data", "keno_48h_patterns.json")
    with open(output_file, 'w') as f:
        json.dump(patterns_json, f, indent=2)

    print(f"Patterns sauvegardés dans {output_file}")

def print_patterns_summary(patterns):
    """
    Affiche un résumé des patterns identifiés

    Args:
        patterns (dict): Dictionnaire des patterns identifiés
    """
    print("\nRésumé des patterns identifiés:")

    # Nombre de numéros communs entre tirages consécutifs
    print("\nNombre de numéros communs entre tirages consécutifs:")
    for count, freq in sorted(patterns['common_numbers'].items()):
        print(f"  {count} numéros communs: {freq} occurrences")

    # Patterns sur 48 heures
    print("\nNombre de numéros communs sur 48 heures:")
    for count, freq in sorted(patterns['48h_patterns'].items()):
        print(f"  {count} numéros communs: {freq} occurrences")

    # Patterns entre tirages du soir et du midi
    print("\nNombre de numéros communs entre tirages du soir et du midi:")
    for count, freq in sorted(patterns['evening_noon_patterns'].items()):
        print(f"  {count} numéros communs: {freq} occurrences")

    # Patterns entre tirages du midi et du soir
    print("\nNombre de numéros communs entre tirages du midi et du soir:")
    for count, freq in sorted(patterns['noon_evening_patterns'].items()):
        print(f"  {count} numéros communs: {freq} occurrences")

def improve_predictions_with_48h_patterns():
    """
    Améliore les prédictions en utilisant les patterns sur 48 heures
    """
    print("Amélioration des prédictions en utilisant les patterns sur 48 heures...")

    # Charger les données
    data_manager = KenoDataManager()
    data_file = os.path.join(BASE_DIR, "data", "datafull.keno")

    # Vérifier si le fichier existe
    if not os.path.exists(data_file):
        print(f"Fichier de données {data_file} introuvable.")
        return None

    # Charger les données avec la méthode load_csv
    data_manager.load_csv(data_file, clear_existing=True)

    # Créer l'analyseur avancé
    analyzer = KenoAdvancedAnalyzer(data_manager)

    # Préparer les données
    analyzer.prepare_data()

    # Charger les patterns sur 48 heures
    patterns_file = os.path.join(BASE_DIR, "data", "keno_48h_patterns.json")
    if not os.path.exists(patterns_file):
        print(f"Fichier de patterns {patterns_file} introuvable. Analyse des patterns...")
        patterns = analyze_48h_patterns()
    else:
        with open(patterns_file, 'r') as f:
            patterns = json.load(f)

    # Utiliser les patterns pour améliorer les prédictions
    # Cette fonction sera implémentée dans l'analyseur avancé

    print("Amélioration des prédictions terminée.")

if __name__ == "__main__":
    # Analyser les patterns sur 48 heures
    patterns = analyze_48h_patterns()

    # Améliorer les prédictions en utilisant les patterns
    improve_predictions_with_48h_patterns()
