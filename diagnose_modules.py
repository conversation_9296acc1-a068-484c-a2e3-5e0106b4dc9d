#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de diagnostic pour identifier les modules Keno disponibles
"""

import os
import sys
import importlib.util

def find_python_files():
    """Trouve tous les fichiers Python dans le répertoire"""
    python_files = []
    for file in os.listdir('.'):
        if file.endswith('.py'):
            python_files.append(file)
    return sorted(python_files)

def check_module_imports():
    """Vérifie quels modules Keno peuvent être importés"""
    
    # Modules Keno potentiels
    potential_modules = [
        'keno_data_manager',
        'keno_analyzer', 
        'keno_gui',
        'keno_advanced_analyzer',
        'keno_ml_trainer',
        'keno_feedback',
        'keno_settings'
    ]
    
    available_modules = []
    missing_modules = []
    
    for module_name in potential_modules:
        try:
            # Essayer d'importer le module
            module = __import__(module_name)
            available_modules.append(module_name)
            print(f"✓ {module_name} - Disponible")
            
            # Essayer de voir les classes principales
            if hasattr(module, 'KenoDataManager'):
                print(f"  └─ Contient KenoDataManager")
            if hasattr(module, 'KenoAnalyzer'):
                print(f"  └─ Contient KenoAnalyzer")
            if hasattr(module, 'KenoGUI'):
                print(f"  └─ Contient KenoGUI")
                
        except ImportError as e:
            missing_modules.append(module_name)
            print(f"✗ {module_name} - Non disponible: {e}")
        except Exception as e:
            print(f"⚠ {module_name} - Erreur: {e}")
    
    return available_modules, missing_modules

def check_file_contents():
    """Vérifie le contenu des fichiers pour identifier les classes"""
    
    print("\n=== Analyse du contenu des fichiers ===")
    
    python_files = find_python_files()
    
    for file in python_files:
        if file.startswith('keno') or file == 'main.py':
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"\n📄 {file}:")
                
                # Chercher les classes importantes
                if 'class KenoDataManager' in content:
                    print("  └─ Contient KenoDataManager")
                if 'class KenoAnalyzer' in content:
                    print("  └─ Contient KenoAnalyzer")
                if 'class KenoGUI' in content:
                    print("  └─ Contient KenoGUI")
                if 'class KenoAdvancedAnalyzer' in content:
                    print("  └─ Contient KenoAdvancedAnalyzer")
                
                # Chercher les imports
                imports = []
                for line in content.split('\n'):
                    if line.strip().startswith('from ') and 'keno' in line:
                        imports.append(line.strip())
                    elif line.strip().startswith('import ') and 'keno' in line:
                        imports.append(line.strip())
                
                if imports:
                    print("  └─ Imports Keno:")
                    for imp in imports[:3]:  # Limiter à 3 pour éviter le spam
                        print(f"    • {imp}")
                        
            except Exception as e:
                print(f"  └─ Erreur lors de la lecture: {e}")

def suggest_fixes():
    """Suggère des corrections basées sur l'analyse"""
    
    print("\n=== Suggestions de correction ===")
    
    # Vérifier si les fichiers existent
    required_files = {
        'keno_data_manager.py': 'KenoDataManager',
        'keno_analyzer.py': 'KenoAnalyzer', 
        'keno_gui.py': 'KenoGUI'
    }
    
    missing_files = []
    
    for file, class_name in required_files.items():
        if not os.path.exists(file):
            missing_files.append((file, class_name))
            print(f"✗ Fichier manquant: {file} (devrait contenir {class_name})")
    
    if missing_files:
        print("\n💡 Solutions possibles:")
        print("1. Vérifiez que vous êtes dans le bon répertoire")
        print("2. Les fichiers ont peut-être des noms différents")
        print("3. Cherchez les classes dans d'autres fichiers")
        
        # Chercher les classes dans d'autres fichiers
        print("\n🔍 Recherche des classes dans d'autres fichiers...")
        python_files = find_python_files()
        
        for target_file, target_class in missing_files:
            print(f"\nRecherche de {target_class}:")
            found = False
            
            for file in python_files:
                try:
                    with open(file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if f'class {target_class}' in content:
                        print(f"  ✓ Trouvé dans {file}")
                        found = True
                        
                except Exception:
                    pass
            
            if not found:
                print(f"  ✗ {target_class} non trouvé")

def main():
    """Fonction principale"""
    
    print("=== Diagnostic des modules Keno ===")
    print(f"Répertoire: {os.getcwd()}")
    print(f"Python: {sys.version}")
    
    # 1. Lister les fichiers Python
    print("\n=== Fichiers Python trouvés ===")
    python_files = find_python_files()
    for file in python_files:
        print(f"📄 {file}")
    
    # 2. Tester les imports
    print("\n=== Test des imports ===")
    available, missing = check_module_imports()
    
    # 3. Analyser le contenu
    check_file_contents()
    
    # 4. Suggérer des corrections
    suggest_fixes()
    
    # 5. Résumé
    print(f"\n=== Résumé ===")
    print(f"Fichiers Python trouvés: {len(python_files)}")
    print(f"Modules Keno disponibles: {len(available)}")
    print(f"Modules Keno manquants: {len(missing)}")
    
    if available:
        print(f"Modules disponibles: {', '.join(available)}")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
