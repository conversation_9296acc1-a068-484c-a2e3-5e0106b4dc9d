#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de correction pour l'intégration du système ULTRA-OPTIMISÉ
Diagnostique et corrige automatiquement les problèmes d'intégration
"""

import os
import sys

def diagnose_integration():
    """Diagnostique l'état de l'intégration"""
    
    print("=== DIAGNOSTIC DE L'INTÉGRATION ULTRA-OPTIMISÉE ===")
    
    # Vérifier les fichiers nécessaires
    required_files = [
        'keno_ultra_optimizer.py',
        'keno_ultra_integration.py',
        'keno_gui.py'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file} présent")
        else:
            print(f"✗ {file} manquant")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ Fichiers manquants: {missing_files}")
        return False
    
    # Vérifier le contenu de keno_gui.py
    print(f"\n=== Vérification de keno_gui.py ===")
    
    try:
        with open('keno_gui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = {
            'keno_ultra_optimizer': 'Import du système ultra-optimisé',
            'ultra_system_available': 'Variable de disponibilité',
            'start_ultra_optimization': 'Méthode de lancement',
            'ULTRA-OPTIMISÉ': 'Bouton ultra-optimisé'
        }
        
        integration_ok = True
        for check, description in checks.items():
            if check in content:
                print(f"✓ {description} trouvé")
            else:
                print(f"✗ {description} manquant")
                integration_ok = False
        
        return integration_ok
        
    except Exception as e:
        print(f"✗ Erreur lors de la lecture de keno_gui.py: {e}")
        return False

def test_imports():
    """Teste les imports du système ultra-optimisé"""
    
    print(f"\n=== TEST DES IMPORTS ===")
    
    try:
        from keno_ultra_optimizer import KenoUltraOptimizer
        print("✓ KenoUltraOptimizer importé avec succès")
    except ImportError as e:
        print(f"✗ Erreur import KenoUltraOptimizer: {e}")
        return False
    
    try:
        from keno_ultra_integration import integrate_ultra_system
        print("✓ integrate_ultra_system importé avec succès")
    except ImportError as e:
        print(f"✗ Erreur import integrate_ultra_system: {e}")
        return False
    
    return True

def force_integration():
    """Force l'intégration du système ultra-optimisé"""
    
    print(f"\n=== INTÉGRATION FORCÉE ===")
    
    gui_file = 'keno_gui.py'
    
    try:
        # Lire le fichier
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifier si déjà intégré
        if 'start_ultra_optimization' in content:
            print("✓ Système déjà intégré")
            return True
        
        # Ajouter l'import en haut du fichier
        import_section = '''
# === SYSTÈME ULTRA-OPTIMISÉ ===
try:
    from keno_ultra_optimizer import KenoUltraOptimizer
    from keno_ultra_integration import integrate_ultra_system, run_ultra_training
    ultra_system_available = True
    print("✓ Système ULTRA-OPTIMISÉ disponible")
except ImportError as e:
    ultra_system_available = False
    print(f"✗ Système ULTRA-OPTIMISÉ non disponible: {e}")
'''
        
        # Trouver la position après les imports existants
        import_pos = content.find("class KenoGUI")
        if import_pos != -1:
            content = content[:import_pos] + import_section + "\n" + content[import_pos:]
        
        # Ajouter le bouton dans create_interface
        button_code = '''
        
        # === BOUTON ULTRA-OPTIMISÉ ===
        if 'ultra_system_available' in globals() and ultra_system_available:
            ultra_frame = ttk.Frame(buttons_frame)
            ultra_frame.pack(side=tk.LEFT, padx=10)
            
            ultra_button = ttk.Button(
                ultra_frame,
                text="🚀 ULTRA-OPTIMISÉ 🚀",
                command=self.start_ultra_optimization
            )
            ultra_button.pack()
            
            # Style du bouton
            try:
                style = ttk.Style()
                style.configure("Ultra.TButton", 
                               foreground="red", 
                               font=("Helvetica", 10, "bold"))
                ultra_button.configure(style="Ultra.TButton")
            except:
                pass
'''
        
        # Trouver où ajouter le bouton (après les autres boutons)
        button_pos = content.find("# Créer les onglets")
        if button_pos == -1:
            button_pos = content.find("self.create_tabs()")
        
        if button_pos != -1:
            content = content[:button_pos] + button_code + "\n        " + content[button_pos:]
        
        # Ajouter les méthodes à la fin de la classe
        methods_code = '''
    
    def start_ultra_optimization(self):
        """Lance l'optimisation ULTRA-AVANCÉE"""
        try:
            # Vérifier les données
            if self.data_manager.get_draws_count() < 100:
                messagebox.showerror("Erreur", "Pas assez de données (minimum 100 tirages).")
                return
            
            # Créer l'optimiseur
            from keno_ultra_optimizer import KenoUltraOptimizer
            ultra_optimizer = KenoUltraOptimizer(self.data_manager, self.analyzer)
            
            # Dialogue simple
            mode = messagebox.askyesnocancel("Mode d'entraînement", 
                                           "Choisissez le mode:\\n\\n" +
                                           "OUI = Rapide (5 numéros)\\n" +
                                           "NON = Complet (70 numéros)\\n" +
                                           "ANNULER = Annuler")
            
            if mode is None:  # Annuler
                return
            elif mode:  # Oui = Rapide
                numbers = [7, 21, 35, 49, 63]
                mode_name = "rapide"
            else:  # Non = Complet
                numbers = list(range(1, 71))
                mode_name = "complet"
            
            # Lancer l'entraînement
            self._run_ultra_training(ultra_optimizer, numbers, mode_name)
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur système ultra-optimisé: {e}")
    
    def _run_ultra_training(self, ultra_optimizer, numbers, mode_name):
        """Exécute l'entraînement ultra-optimisé"""
        
        # Fenêtre de progression
        progress_window = tk.Toplevel(self)
        progress_window.title(f"🚀 ULTRA-OPTIMISÉ - Mode {mode_name}")
        progress_window.geometry("600x400")
        progress_window.transient(self)
        
        # Interface
        title_label = tk.Label(progress_window, 
                              text="🚀 SYSTÈME ULTRA-OPTIMISÉ 🚀", 
                              font=("Helvetica", 14, "bold"))
        title_label.pack(pady=10)
        
        status_var = tk.StringVar(value="Initialisation...")
        status_label = tk.Label(progress_window, textvariable=status_var)
        status_label.pack(pady=5)
        
        # Zone de texte
        text_frame = tk.Frame(progress_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        scrollbar = tk.Scrollbar(text_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        text_widget = tk.Text(text_frame, yscrollcommand=scrollbar.set)
        text_widget.pack(fill=tk.BOTH, expand=True)
        scrollbar.config(command=text_widget.yview)
        
        def update_text(message):
            text_widget.insert(tk.END, message + "\\n")
            text_widget.see(tk.END)
            progress_window.update()
        
        # Entraînement
        import threading
        
        def train():
            try:
                update_text(f"🚀 Démarrage mode {mode_name} ({len(numbers)} numéros)")
                status_var.set("Entraînement en cours...")
                
                trained = 0
                total_f1 = 0
                
                for i, number in enumerate(numbers):
                    update_text(f"Traitement numéro {number} ({i+1}/{len(numbers)})")
                    
                    # Créer les features
                    result = ultra_optimizer.create_ultra_features(number)
                    if result:
                        X, y, features = result
                        update_text(f"  ✓ {len(features)} caractéristiques créées")
                        
                        # Entraîner un modèle simple
                        from sklearn.ensemble import RandomForestClassifier
                        from sklearn.model_selection import train_test_split
                        from sklearn.metrics import f1_score
                        
                        if len(X) > 50:
                            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
                            model = RandomForestClassifier(n_estimators=100, random_state=42)
                            model.fit(X_train, y_train)
                            y_pred = model.predict(X_test)
                            f1 = f1_score(y_test, y_pred, zero_division=0)
                            
                            update_text(f"  ✓ F1-score: {f1:.4f}")
                            total_f1 += f1
                            trained += 1
                        else:
                            update_text(f"  ✗ Pas assez de données")
                    else:
                        update_text(f"  ✗ Échec création features")
                
                if trained > 0:
                    avg_f1 = total_f1 / trained
                    update_text(f"\\n🎯 SUCCÈS ! F1-score moyen: {avg_f1:.4f}")
                    status_var.set("✅ Entraînement terminé avec succès !")
                else:
                    update_text("\\n❌ Aucun numéro entraîné avec succès")
                    status_var.set("❌ Échec de l'entraînement")
                
            except Exception as e:
                update_text(f"\\n❌ Erreur: {e}")
                status_var.set("❌ Erreur")
            
            # Bouton fermer
            close_btn = tk.Button(progress_window, text="Fermer", 
                                 command=progress_window.destroy)
            close_btn.pack(pady=10)
        
        # Lancer dans un thread
        thread = threading.Thread(target=train)
        thread.daemon = True
        thread.start()
'''
        
        # Ajouter les méthodes avant la fin du fichier
        end_pos = content.rfind("if __name__ == \"__main__\":")
        if end_pos != -1:
            content = content[:end_pos] + methods_code + "\n\n" + content[end_pos:]
        
        # Sauvegarder
        with open(gui_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✓ Intégration forcée réussie")
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors de l'intégration forcée: {e}")
        return False

def main():
    """Fonction principale"""
    
    # 1. Diagnostic
    integration_ok = diagnose_integration()
    
    # 2. Test des imports
    imports_ok = test_imports()
    
    # 3. Correction si nécessaire
    if not integration_ok:
        print(f"\n🔧 CORRECTION AUTOMATIQUE EN COURS...")
        success = force_integration()
        
        if success:
            print(f"\n✅ CORRECTION RÉUSSIE !")
            print(f"Relancez votre application:")
            print(f"  python main.py")
            print(f"Le bouton 🚀 ULTRA-OPTIMISÉ 🚀 devrait maintenant être visible !")
            return 0
        else:
            print(f"\n❌ ÉCHEC DE LA CORRECTION")
            return 1
    else:
        print(f"\n✅ INTÉGRATION OK")
        print(f"Le bouton devrait être visible dans votre interface.")
        return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
