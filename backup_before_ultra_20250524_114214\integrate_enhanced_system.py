#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script d'intégration du système d'auto-amélioration optimisé
Ce script intègre le nouveau système dans l'application Keno existante
"""

import os
import sys
import importlib.util

def integrate_enhanced_auto_improve():
    """Intègre le système d'auto-amélioration optimisé dans l'application"""

    print("=== Intégration du système d'auto-amélioration optimisé ===")

    try:
        # Importer les modules nécessaires
        from keno_enhanced_auto_improve import KenoEnhancedAutoImprove, integrate_enhanced_auto_improve
        from keno_data import KenoDataManager  # Correction: utiliser keno_data au lieu de keno_data_manager
        from keno_analyzer import KenoAnalyzer

        print("✓ Modules importés avec succès")

        # Créer les instances
        data_manager = KenoDataManager()
        analyzer = KenoAnalyzer(data_manager)

        # Charger les données existantes
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        if os.path.exists(data_dir):
            # Chercher un fichier de données
            data_files = [f for f in os.listdir(data_dir) if f.endswith('.json') or f.endswith('.keno')]
            if data_files:
                latest_file = max(data_files, key=lambda f: os.path.getmtime(os.path.join(data_dir, f)))
                file_path = os.path.join(data_dir, latest_file)

                print(f"Chargement des données depuis: {file_path}")
                success = data_manager.load_database(file_path)

                if success:
                    print(f"✓ {data_manager.get_draws_count()} tirages chargés")
                else:
                    print("✗ Échec du chargement des données")
                    return False
            else:
                print("✗ Aucun fichier de données trouvé")
                return False
        else:
            print("✗ Répertoire de données non trouvé")
            return False

        # Intégrer le système optimisé
        enhanced_improver = integrate_enhanced_auto_improve(analyzer)

        if enhanced_improver:
            print("✓ Système d'auto-amélioration optimisé intégré")
            return True, analyzer, enhanced_improver
        else:
            print("✗ Échec de l'intégration")
            return False, None, None

    except Exception as e:
        print(f"✗ Erreur lors de l'intégration: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def run_enhanced_training(analyzer, enhanced_improver, mode='fast'):
    """Lance l'entraînement optimisé"""

    print(f"\n=== Lancement de l'entraînement optimisé (mode: {mode}) ===")

    try:
        if mode == 'fast':
            # Mode rapide: entraîner seulement 10 numéros représentatifs
            numbers_to_train = [1, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70]
            print(f"Mode rapide: entraînement de {len(numbers_to_train)} numéros")
        elif mode == 'medium':
            # Mode moyen: entraîner 30 numéros
            numbers_to_train = list(range(1, 71, 2))  # Numéros impairs + quelques pairs
            print(f"Mode moyen: entraînement de {len(numbers_to_train)} numéros")
        else:  # mode == 'complete'
            # Mode complet: tous les numéros
            numbers_to_train = None
            print("Mode complet: entraînement de tous les numéros (1-70)")

        # Lancer l'entraînement
        results = enhanced_improver.run_enhanced_auto_improve(numbers_to_train)

        if results and results['trained_numbers']:
            print(f"\n✓ Entraînement terminé avec succès!")
            print(f"  Numéros entraînés: {len(results['trained_numbers'])}")
            print(f"  Numéros échoués: {len(results['failed_numbers'])}")

            if results['performance_summary']:
                import numpy as np
                avg_f1 = np.mean([p['f1_score'] for p in results['performance_summary'].values()])
                avg_accuracy = np.mean([p['accuracy'] for p in results['performance_summary'].values()])
                print(f"  F1-score moyen: {avg_f1:.4f}")
                print(f"  Précision moyenne: {avg_accuracy:.4f}")

            return True, results
        else:
            print("✗ Échec de l'entraînement")
            return False, None

    except Exception as e:
        print(f"✗ Erreur lors de l'entraînement: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_enhanced_predictions(enhanced_improver, num_predictions=10):
    """Teste les prédictions optimisées"""

    print(f"\n=== Test des prédictions optimisées ===")

    try:
        # Générer des prédictions
        predictions = enhanced_improver.predict_next_draw_enhanced(num_predictions)

        if predictions:
            print(f"✓ Prédictions générées: {predictions}")
            print(f"  Nombre de prédictions: {len(predictions)}")
            print(f"  Numéros prédits: {sorted(predictions)}")
            return True, predictions
        else:
            print("✗ Aucune prédiction générée")
            return False, None

    except Exception as e:
        print(f"✗ Erreur lors de la génération de prédictions: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """Fonction principale"""

    print("=== Script d'intégration du système d'auto-amélioration optimisé ===")

    # 1. Intégrer le système
    success, analyzer, enhanced_improver = integrate_enhanced_auto_improve()

    if not success:
        print("Échec de l'intégration. Arrêt du script.")
        return 1

    # 2. Demander le mode d'entraînement
    print("\nModes d'entraînement disponibles:")
    print("1. Rapide (10 numéros, ~5-10 minutes)")
    print("2. Moyen (35 numéros, ~20-30 minutes)")
    print("3. Complet (70 numéros, ~1-2 heures)")

    try:
        choice = input("\nChoisissez un mode (1/2/3) [défaut: 1]: ").strip()
        if choice == '2':
            mode = 'medium'
        elif choice == '3':
            mode = 'complete'
        else:
            mode = 'fast'
    except KeyboardInterrupt:
        print("\nInterruption par l'utilisateur")
        return 0

    # 3. Lancer l'entraînement
    training_success, results = run_enhanced_training(analyzer, enhanced_improver, mode)

    if not training_success:
        print("Échec de l'entraînement. Arrêt du script.")
        return 1

    # 4. Tester les prédictions
    prediction_success, predictions = test_enhanced_predictions(enhanced_improver)

    if prediction_success:
        print(f"\n=== Résumé final ===")
        print(f"✓ Système intégré et testé avec succès")
        print(f"✓ Modèles entraînés pour {len(results['trained_numbers'])} numéros")
        print(f"✓ Prédictions générées: {predictions}")

        # Sauvegarder les prédictions
        try:
            import json
            from datetime import datetime

            prediction_file = f"predictions_enhanced_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            prediction_data = {
                'timestamp': datetime.now().isoformat(),
                'predictions': predictions,
                'training_results': {
                    'trained_numbers': results['trained_numbers'],
                    'performance_summary': results['performance_summary']
                }
            }

            with open(prediction_file, 'w') as f:
                json.dump(prediction_data, f, indent=2)

            print(f"✓ Prédictions sauvegardées dans: {prediction_file}")

        except Exception as e:
            print(f"Erreur lors de la sauvegarde: {e}")

        print("\nLe système d'auto-amélioration optimisé est maintenant prêt à être utilisé!")
        print("Vous pouvez l'utiliser dans votre application principale.")

        return 0
    else:
        print("Échec des tests de prédiction.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nInterruption par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
