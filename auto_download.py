#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module pour le téléchargement automatique des données Keno depuis le site de la FDJ
"""

import os
import time
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import json

# Constantes
FDJ_KENO_URL = "https://www.fdj.fr/jeux-de-tirage/keno/historique"
DEFAULT_DAYS = 7  # Nombre de jours par défaut à télécharger
CONFIG_FILE = "auto_download_config.json"

def get_config_path():
    """Retourne le chemin du fichier de configuration"""
    return os.path.join(os.path.dirname(os.path.abspath(__file__)), CONFIG_FILE)

def save_config(config):
    """Sauvegarde la configuration"""
    config_path = get_config_path()

    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)
        return True
    except Exception as e:
        print(f"Erreur lors de la sauvegarde de la configuration: {e}")
        return False

def load_config():
    """Charge la configuration"""
    config_path = get_config_path()

    if not os.path.exists(config_path):
        # Configuration par défaut
        config = {
            "auto_download": True,
            "days": DEFAULT_DAYS,
            "last_download": None,
            "download_frequency": "daily"  # daily, weekly, manual
        }
        save_config(config)
        return config

    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except Exception as e:
        print(f"Erreur lors du chargement de la configuration: {e}")
        # Configuration par défaut en cas d'erreur
        return {
            "auto_download": True,
            "days": DEFAULT_DAYS,
            "last_download": None,
            "download_frequency": "daily"
        }

def should_download():
    """Vérifie si un téléchargement automatique est nécessaire"""
    config = load_config()

    # Si le téléchargement automatique est désactivé
    if not config.get("auto_download", True):
        return False

    # Si aucun téléchargement n'a été effectué
    last_download = config.get("last_download")
    if not last_download:
        return True

    # Vérifier la fréquence de téléchargement
    frequency = config.get("download_frequency", "daily")
    current_time = time.time()

    if frequency == "daily":
        # Télécharger si le dernier téléchargement date de plus de 24 heures
        return (current_time - last_download) > 24 * 3600
    elif frequency == "weekly":
        # Télécharger si le dernier téléchargement date de plus de 7 jours
        return (current_time - last_download) > 7 * 24 * 3600
    elif frequency == "manual":
        # Ne jamais télécharger automatiquement
        return False

    # Par défaut, télécharger
    return True

def update_last_download():
    """Met à jour la date du dernier téléchargement"""
    config = load_config()
    config["last_download"] = time.time()
    save_config(config)

def auto_download_data(gui, show_confirmation=True):
    """
    Télécharge automatiquement les données depuis le site de la FDJ

    Args:
        gui: Instance de KenoGUI
        show_confirmation: Afficher une confirmation avant le téléchargement

    Returns:
        bool: True si le téléchargement a été effectué, False sinon
    """
    # Vérifier si un téléchargement est nécessaire
    if not should_download():
        return False

    # Charger la configuration
    config = load_config()
    days = config.get("days", DEFAULT_DAYS)

    # Demander confirmation si nécessaire
    if show_confirmation:
        if not messagebox.askyesno("Téléchargement automatique",
                                 f"Voulez-vous télécharger les résultats des {days} derniers jours depuis le site de la FDJ?"):
            return False

    # Afficher une fenêtre de progression
    progress_window = tk.Toplevel(gui.root)
    progress_window.title("Téléchargement des données")
    progress_window.geometry("400x150")
    progress_window.transient(gui.root)
    progress_window.grab_set()

    # Centrer la fenêtre
    progress_window.update_idletasks()
    width = progress_window.winfo_width()
    height = progress_window.winfo_height()
    x = (gui.root.winfo_width() // 2) - (width // 2) + gui.root.winfo_x()
    y = (gui.root.winfo_height() // 2) - (height // 2) + gui.root.winfo_y()
    progress_window.geometry(f"{width}x{height}+{x}+{y}")

    # Ajouter un label et une barre de progression
    ttk.Label(progress_window, text=f"Téléchargement des résultats des {days} derniers jours...").pack(pady=10)
    progress_var = tk.DoubleVar()
    progress_bar = ttk.Progressbar(progress_window, variable=progress_var, maximum=100)
    progress_bar.pack(fill=tk.X, padx=20, pady=10)

    status_var = tk.StringVar(value="Initialisation...")
    status_label = ttk.Label(progress_window, textvariable=status_var)
    status_label.pack(pady=5)

    # Lancer le téléchargement en arrière-plan
    threading.Thread(target=_perform_download,
                     args=(gui, progress_window, progress_var, status_var, days)).start()

    return True

def _perform_download(gui, progress_window, progress_var, status_var, days):
    """
    Effectue le téléchargement en arrière-plan

    Args:
        gui: Instance de KenoGUI
        progress_window: Fenêtre de progression
        progress_var: Variable de progression
        status_var: Variable de statut
        days: Nombre de jours à télécharger
    """
    try:
        # Acquérir le verrou pour éviter les conflits entre threads
        with gui.thread_lock:
            # Mettre à jour le statut
            status_var.set("Connexion au site de la FDJ...")
            progress_var.set(10)
            progress_window.update_idletasks()

            # Télécharger les données
            status_var.set("Téléchargement des résultats...")
            progress_var.set(30)
            progress_window.update_idletasks()

            # Appeler la méthode de scraping de la FDJ
            success = gui.data_manager.scrape_fdj_keno_results(num_days=days)

            # Mettre à jour la progression
            progress_var.set(90)
            status_var.set("Finalisation...")
            progress_window.update_idletasks()

            # Mettre à jour la date du dernier téléchargement
            if success:
                update_last_download()

            # Mettre à jour la progression
            progress_var.set(100)

            # Fermer la fenêtre de progression dans le thread principal
            gui.root.after(0, progress_window.destroy)

            # Afficher le résultat dans le thread principal
            if success:
                draws_count = gui.data_manager.get_draws_count()
                gui.root.after(0, lambda: messagebox.showinfo("Téléchargement terminé",
                                                           f"Les résultats des {days} derniers jours ont été téléchargés avec succès.\n\n"
                                                           f"Total de {draws_count} tirages dans la base de données."))

                # Mettre à jour les statistiques
                gui.root.after(0, gui.update_stats)
            else:
                gui.root.after(0, lambda: messagebox.showerror("Erreur",
                                                            "Erreur lors du téléchargement des résultats.\n\n"
                                                            "Vérifiez votre connexion Internet et réessayez."))

    except Exception as e:
        # Journaliser l'erreur pour le débogage
        print(f"Erreur lors du téléchargement automatique: {e}")
        import traceback
        traceback.print_exc()

        # Fermer la fenêtre de progression dans le thread principal
        gui.root.after(0, progress_window.destroy)

        # Afficher l'erreur dans le thread principal
        gui.root.after(0, lambda: messagebox.showerror("Erreur",
                                                    f"Une erreur est survenue lors du téléchargement automatique: {e}"))

def show_auto_download_settings(gui):
    """
    Affiche la fenêtre de configuration du téléchargement automatique

    Args:
        gui: Instance de KenoGUI
    """
    # Charger la configuration actuelle
    config = load_config()

    # Créer une fenêtre de dialogue
    dialog = tk.Toplevel(gui.root)
    dialog.title("Configuration du téléchargement automatique")
    dialog.geometry("500x300")
    dialog.transient(gui.root)
    dialog.grab_set()

    # Centrer la fenêtre
    dialog.update_idletasks()
    width = dialog.winfo_width()
    height = dialog.winfo_height()
    x = (gui.root.winfo_width() // 2) - (width // 2) + gui.root.winfo_x()
    y = (gui.root.winfo_height() // 2) - (height // 2) + gui.root.winfo_y()
    dialog.geometry(f"{width}x{height}+{x}+{y}")

    # Créer un cadre principal
    main_frame = ttk.Frame(dialog, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)

    # Titre
    ttk.Label(main_frame, text="Configuration du téléchargement automatique",
             font=("Helvetica", 12, "bold")).pack(pady=(0, 20))

    # Activer/désactiver le téléchargement automatique
    auto_download_var = tk.BooleanVar(value=config.get("auto_download", True))
    ttk.Checkbutton(main_frame, text="Activer le téléchargement automatique",
                   variable=auto_download_var).pack(anchor=tk.W, pady=5)

    # Fréquence de téléchargement
    freq_frame = ttk.Frame(main_frame)
    freq_frame.pack(fill=tk.X, pady=5)

    ttk.Label(freq_frame, text="Fréquence de téléchargement:").pack(side=tk.LEFT, padx=5)

    frequency_var = tk.StringVar(value=config.get("download_frequency", "daily"))
    frequency_combo = ttk.Combobox(freq_frame, textvariable=frequency_var, state="readonly", width=15)
    frequency_combo["values"] = ["daily", "weekly", "manual"]
    frequency_combo.pack(side=tk.LEFT, padx=5)

    # Nombre de jours à télécharger
    days_frame = ttk.Frame(main_frame)
    days_frame.pack(fill=tk.X, pady=5)

    ttk.Label(days_frame, text="Nombre de jours à télécharger:").pack(side=tk.LEFT, padx=5)

    days_var = tk.IntVar(value=config.get("days", DEFAULT_DAYS))
    days_spinbox = ttk.Spinbox(days_frame, from_=1, to=60, textvariable=days_var, width=5)
    days_spinbox.pack(side=tk.LEFT, padx=5)

    # Informations sur le dernier téléchargement
    last_download = config.get("last_download")
    if last_download:
        last_download_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(last_download))
        ttk.Label(main_frame, text=f"Dernier téléchargement: {last_download_str}").pack(anchor=tk.W, pady=10)
    else:
        ttk.Label(main_frame, text="Aucun téléchargement effectué").pack(anchor=tk.W, pady=10)

    # Boutons
    buttons_frame = ttk.Frame(main_frame)
    buttons_frame.pack(fill=tk.X, pady=20)

    def save_settings():
        # Sauvegarder les paramètres
        config["auto_download"] = auto_download_var.get()
        config["days"] = days_var.get()
        config["download_frequency"] = frequency_var.get()

        save_config(config)

        dialog.destroy()
        messagebox.showinfo("Configuration", "Configuration du téléchargement automatique sauvegardée avec succès.")

    def download_now():
        dialog.destroy()
        auto_download_data(gui, show_confirmation=False)

    ttk.Button(buttons_frame, text="Télécharger maintenant",
              command=download_now, style="Action.TButton").pack(side=tk.LEFT, padx=5)

    ttk.Button(buttons_frame, text="Annuler",
              command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

    ttk.Button(buttons_frame, text="Sauvegarder",
              command=save_settings, style="Action.TButton").pack(side=tk.RIGHT, padx=5)

def enhance_update_stats(gui):
    """
    Améliore la méthode update_stats pour télécharger automatiquement les données

    Args:
        gui: Instance de KenoGUI
    """
    # Sauvegarder la méthode originale
    original_update_stats = gui.update_stats

    def enhanced_update_stats(self):
        """Version améliorée de la méthode update_stats avec téléchargement automatique"""
        # Vérifier si un téléchargement automatique est nécessaire
        if should_download():
            # Télécharger les données
            auto_download_data(self, show_confirmation=True)
        else:
            # Utiliser la méthode originale
            original_update_stats()

    # Remplacer la méthode originale par la version améliorée
    import types
    gui.update_stats = types.MethodType(enhanced_update_stats, gui)

    # Ajouter une option dans le menu pour configurer le téléchargement automatique
    def add_menu_option():
        """Ajoute l'option de menu pour configurer le téléchargement automatique"""
        try:
            # Trouver le menu Outils
            for child in gui.root.winfo_children():
                if isinstance(child, tk.Menu):
                    menubar = child
                    # Parcourir les menus
                    for i in range(menubar.index('end') + 1 if menubar.index('end') is not None else 0):
                        try:
                            menu_label = menubar.entrycget(i, 'label')
                            if menu_label == "Outils":
                                # Obtenir le menu Outils
                                tools_menu = menubar.nametowidget(menubar.entrycget(i, 'menu'))

                                # Ajouter un séparateur et l'option de configuration
                                tools_menu.add_separator()
                                tools_menu.add_command(label="Configuration téléchargement auto",
                                                      command=lambda: show_auto_download_settings(gui))
                                return True
                        except Exception:
                            continue
            return False
        except Exception as e:
            print(f"Erreur lors de l'ajout de l'option de menu: {e}")
            return False

    # Essayer d'ajouter l'option de menu après un court délai
    gui.root.after(1000, add_menu_option)

    return True
