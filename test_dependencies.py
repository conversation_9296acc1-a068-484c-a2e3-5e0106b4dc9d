#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test des dépendances pour l'application Keno
Ce script vérifie que toutes les dépendances nécessaires sont disponibles
"""

import sys
import os

def test_basic_imports():
    """Test des imports de base"""
    print("=== Test des imports de base ===")
    
    try:
        import tkinter as tk
        print("✓ tkinter disponible")
    except ImportError as e:
        print(f"✗ tkinter non disponible: {e}")
        return False
    
    try:
        from tkinter import ttk, messagebox, filedialog
        print("✓ modules tkinter étendus disponibles")
    except ImportError as e:
        print(f"✗ modules tkinter étendus non disponibles: {e}")
        return False
    
    try:
        import json
        print("✓ json disponible")
    except ImportError as e:
        print(f"✗ json non disponible: {e}")
        return False
    
    try:
        import csv
        print("✓ csv disponible")
    except ImportError as e:
        print(f"✗ csv non disponible: {e}")
        return False
    
    try:
        import threading
        print("✓ threading disponible")
    except ImportError as e:
        print(f"✗ threading non disponible: {e}")
        return False
    
    try:
        import queue
        print("✓ queue disponible")
    except ImportError as e:
        print(f"✗ queue non disponible: {e}")
        return False
    
    return True

def test_optional_imports():
    """Test des imports optionnels"""
    print("\n=== Test des imports optionnels ===")
    
    try:
        import numpy
        print(f"✓ numpy disponible (version: {numpy.__version__})")
    except ImportError as e:
        print(f"✗ numpy non disponible: {e}")
    
    try:
        import pandas
        print(f"✓ pandas disponible (version: {pandas.__version__})")
    except ImportError as e:
        print(f"✗ pandas non disponible: {e}")
    
    try:
        import sklearn
        print(f"✓ scikit-learn disponible (version: {sklearn.__version__})")
    except ImportError as e:
        print(f"✗ scikit-learn non disponible: {e}")

def test_project_modules():
    """Test des modules du projet"""
    print("\n=== Test des modules du projet ===")
    
    # Ajouter le répertoire courant au chemin de recherche Python
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, BASE_DIR)
    
    modules_to_test = [
        'keno_data_manager',
        'keno_analyzer',
        'keno_gui'
    ]
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✓ {module_name} disponible")
        except ImportError as e:
            print(f"✗ {module_name} non disponible: {e}")
        except Exception as e:
            print(f"✗ {module_name} erreur lors de l'import: {e}")

def test_data_directory():
    """Test du répertoire de données"""
    print("\n=== Test du répertoire de données ===")
    
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(BASE_DIR, "data")
    
    print(f"Répertoire de base: {BASE_DIR}")
    print(f"Répertoire de données: {data_dir}")
    
    if os.path.exists(data_dir):
        print(f"✓ Répertoire de données existe: {data_dir}")
        
        # Lister les fichiers dans le répertoire
        try:
            files = os.listdir(data_dir)
            keno_files = [f for f in files if f.endswith('.keno') or f.endswith('.json')]
            print(f"  Fichiers trouvés: {len(files)}")
            print(f"  Fichiers Keno (.keno/.json): {len(keno_files)}")
            if keno_files:
                print(f"  Fichiers Keno: {keno_files}")
        except Exception as e:
            print(f"✗ Erreur lors de la lecture du répertoire: {e}")
    else:
        print(f"✗ Répertoire de données n'existe pas: {data_dir}")
        try:
            os.makedirs(data_dir, exist_ok=True)
            print(f"✓ Répertoire de données créé: {data_dir}")
        except Exception as e:
            print(f"✗ Impossible de créer le répertoire de données: {e}")

def test_tkinter_window():
    """Test de création d'une fenêtre tkinter simple"""
    print("\n=== Test de création d'une fenêtre tkinter ===")
    
    try:
        import tkinter as tk
        
        # Créer une fenêtre de test
        root = tk.Tk()
        root.title("Test Tkinter")
        root.geometry("300x200")
        
        # Ajouter un label
        label = tk.Label(root, text="Test réussi!", font=("Helvetica", 16))
        label.pack(pady=20)
        
        # Programmer la fermeture automatique après 2 secondes
        root.after(2000, root.destroy)
        
        print("✓ Fenêtre tkinter créée avec succès")
        print("  La fenêtre va se fermer automatiquement dans 2 secondes...")
        
        # Démarrer la boucle principale
        root.mainloop()
        
        print("✓ Boucle principale tkinter terminée avec succès")
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors de la création de la fenêtre tkinter: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("=== Test des dépendances de l'application Keno ===")
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    print()
    
    # Test des imports de base
    basic_ok = test_basic_imports()
    
    # Test des imports optionnels
    test_optional_imports()
    
    # Test des modules du projet
    test_project_modules()
    
    # Test du répertoire de données
    test_data_directory()
    
    # Test de tkinter
    if basic_ok:
        tkinter_ok = test_tkinter_window()
    else:
        tkinter_ok = False
    
    print("\n=== Résumé ===")
    if basic_ok and tkinter_ok:
        print("✓ Tous les tests de base sont passés avec succès")
        print("  L'application devrait pouvoir se lancer")
    else:
        print("✗ Certains tests ont échoué")
        print("  L'application pourrait avoir des problèmes au lancement")
    
    return 0 if (basic_ok and tkinter_ok) else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
