"""
Script pour prédire les numéros Keno en utilisant les motifs sur 48h
Ce script intègre l'analyse des motifs sur 48h pour améliorer les prédictions.
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter, defaultdict
from datetime import datetime, timedelta
import json

# Essayer d'importer le gestionnaire de données Keno
try:
    from keno_data import KenoDataManager, KenoDrawData
    print("Module de gestion des données Keno importé avec succès")
except ImportError:
    print("Erreur: Module de gestion des données Keno non disponible")
    sys.exit(1)

# Importer les fonctions des étapes précédentes
from analyze_keno_48h_patterns import group_draws_by_48h_periods
from analyze_keno_48h_patterns_step2 import analyze_number_specific_patterns, find_best_prediction_numbers

class Keno48hPredictor:
    """Classe pour prédire les numéros Keno en utilisant les motifs sur 48h"""
    
    def __init__(self, data_manager=None):
        """
        Initialise le prédicteur
        
        Args:
            data_manager: Gestionnaire de données Keno (optionnel)
        """
        if data_manager:
            self.data_manager = data_manager
        else:
            self.data_manager = KenoDataManager()
            self.data_manager.load_data()
        
        self.number_stats = None
        self.best_numbers = None
        self.recent_draws = []
        self.pattern_weights = {}
        self.number_weights = {}
    
    def analyze_historical_data(self):
        """
        Analyse les données historiques pour identifier les motifs sur 48h
        
        Returns:
            bool: True si l'analyse a réussi, False sinon
        """
        if not self.data_manager.draws:
            print("Aucun tirage disponible dans le gestionnaire de données")
            return False
        
        print(f"Analyse des données historiques ({len(self.data_manager.draws)} tirages)...")
        
        # Regrouper les tirages par périodes de 48h
        groups = group_draws_by_48h_periods(self.data_manager)
        
        if not groups:
            print("Aucun groupe de tirages trouvé")
            return False
        
        # Analyser les motifs spécifiques à chaque numéro
        self.number_stats = analyze_number_specific_patterns(groups)
        
        # Trouver les numéros avec les meilleurs potentiels de prédiction
        self.best_numbers = find_best_prediction_numbers(self.number_stats, groups)
        
        # Calculer les poids pour chaque motif
        self._calculate_pattern_weights()
        
        # Calculer les poids pour chaque numéro
        self._calculate_number_weights()
        
        print("Analyse des données historiques terminée")
        return True
    
    def _calculate_pattern_weights(self):
        """Calcule les poids pour chaque motif d'apparition"""
        # Collecter tous les motifs et leurs fréquences
        all_patterns = Counter()
        pattern_next_appearance = defaultdict(Counter)
        
        # Regrouper les tirages par périodes de 48h
        groups = group_draws_by_48h_periods(self.data_manager)
        
        # Pour chaque groupe, analyser les motifs et les tirages suivants
        for group_idx, group in enumerate(groups[:-1]):  # Ignorer le dernier groupe
            # Pour chaque numéro
            for num in range(1, 71):
                # Créer le motif d'apparition pour ce numéro dans ce groupe
                pattern = ""
                for draw in group:
                    if num in draw.draw_numbers:
                        pattern += "1"
                    else:
                        pattern += "0"
                
                # Enregistrer le motif
                all_patterns[pattern] += 1
                
                # Vérifier si le numéro apparaît dans le premier tirage du groupe suivant
                if group_idx + 1 < len(groups) and groups[group_idx + 1]:
                    next_draw = groups[group_idx + 1][0]
                    if num in next_draw.draw_numbers:
                        pattern_next_appearance[pattern][1] += 1
                    else:
                        pattern_next_appearance[pattern][0] += 1
        
        # Calculer les poids pour chaque motif
        for pattern in all_patterns:
            total_occurrences = sum(pattern_next_appearance[pattern].values())
            if total_occurrences > 0:
                # Probabilité que le numéro apparaisse dans le tirage suivant
                prob_next_appearance = pattern_next_appearance[pattern][1] / total_occurrences
                
                # Calculer le poids (ajuster selon les besoins)
                weight = prob_next_appearance * 2  # Multiplier par 2 pour amplifier l'effet
                
                # Stocker le poids
                self.pattern_weights[pattern] = {
                    'weight': weight,
                    'occurrences': all_patterns[pattern],
                    'next_appearance_prob': prob_next_appearance
                }
            else:
                self.pattern_weights[pattern] = {
                    'weight': 0.5,  # Valeur par défaut
                    'occurrences': all_patterns[pattern],
                    'next_appearance_prob': 0.5
                }
    
    def _calculate_number_weights(self):
        """Calcule les poids pour chaque numéro basés sur les statistiques historiques"""
        for num in range(1, 71):
            stats = self.number_stats[num]
            
            # Initialiser le poids
            weight = 1.0
            
            # Facteur 1: Fréquence d'apparition
            if stats['total_groups'] > 0:
                appearance_frequency = stats['total_appearances'] / (stats['total_groups'] * 6)  # 6 tirages max par groupe
                weight *= (0.5 + appearance_frequency)  # Ajuster selon les besoins
            
            # Facteur 2: Prévisibilité (motif le plus fréquent)
            if stats['total_groups'] > 0:
                predictability = stats['most_common_pattern_count'] / stats['total_groups']
                weight *= (0.8 + predictability)  # Ajuster selon les besoins
            
            # Facteur 3: Apparitions consécutives
            if stats['total_groups'] > 0:
                consecutive_ratio = stats['consecutive_appearances'] / stats['total_groups']
                weight *= (0.9 + consecutive_ratio)  # Ajuster selon les besoins
            
            # Stocker le poids
            self.number_weights[num] = weight
    
    def get_recent_draws(self, n=5):
        """
        Récupère les n tirages les plus récents
        
        Args:
            n: Nombre de tirages à récupérer
            
        Returns:
            list: Liste des n tirages les plus récents
        """
        if not self.data_manager.draws:
            return []
        
        # Trier les tirages par date
        sorted_draws = sorted(self.data_manager.draws, key=lambda x: x.draw_date)
        
        # Récupérer les n derniers tirages
        self.recent_draws = sorted_draws[-n:]
        
        return self.recent_draws
    
    def predict_next_draw(self, num_predictions=10):
        """
        Prédit les numéros du prochain tirage en utilisant les motifs sur 48h
        
        Args:
            num_predictions: Nombre de numéros à prédire
            
        Returns:
            list: Liste des numéros prédits
        """
        if not self.number_stats or not self.best_numbers:
            if not self.analyze_historical_data():
                return []
        
        # Récupérer les tirages récents
        recent_draws = self.get_recent_draws(5)  # Récupérer les 5 derniers tirages
        
        if len(recent_draws) < 3:
            print("Pas assez de tirages récents pour faire une prédiction")
            return []
        
        # Calculer les scores pour chaque numéro
        scores = {}
        
        for num in range(1, 71):
            # Initialiser le score avec le poids du numéro
            scores[num] = self.number_weights.get(num, 1.0)
            
            # Créer le motif d'apparition pour ce numéro dans les tirages récents
            pattern = ""
            for draw in recent_draws:
                if num in draw.draw_numbers:
                    pattern += "1"
                else:
                    pattern += "0"
            
            # Ajuster le score en fonction du motif
            if pattern in self.pattern_weights:
                pattern_weight = self.pattern_weights[pattern]['weight']
                next_appearance_prob = self.pattern_weights[pattern]['next_appearance_prob']
                
                # Ajuster le score
                scores[num] *= pattern_weight * 2  # Multiplier par 2 pour amplifier l'effet
                
                # Bonus pour les motifs avec une forte probabilité d'apparition
                if next_appearance_prob > 0.7:
                    scores[num] *= 1.5
                
                # Malus pour les motifs avec une faible probabilité d'apparition
                if next_appearance_prob < 0.3:
                    scores[num] *= 0.7
            
            # Bonus pour les numéros qui sont apparus récemment
            recent_appearances = sum(1 for draw in recent_draws if num in draw.draw_numbers)
            if recent_appearances > 0:
                # Bonus plus important si le numéro est apparu dans le dernier tirage
                if num in recent_draws[-1].draw_numbers:
                    scores[num] *= 1.2
                
                # Bonus pour les numéros qui sont apparus plusieurs fois
                if recent_appearances > 1:
                    scores[num] *= 1.1 * recent_appearances
            
            # Malus pour les numéros qui n'ont pas été vus récemment
            else:
                scores[num] *= 0.9
        
        # Trier les numéros par score décroissant
        sorted_numbers = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        # Sélectionner les numéros avec les meilleurs scores
        predicted_numbers = [num for num, _ in sorted_numbers[:num_predictions]]
        
        return predicted_numbers
    
    def evaluate_prediction(self, predicted_numbers, actual_draw):
        """
        Évalue la qualité d'une prédiction
        
        Args:
            predicted_numbers: Liste des numéros prédits
            actual_draw: Tirage réel
            
        Returns:
            dict: Statistiques d'évaluation
        """
        if not predicted_numbers or not actual_draw or not hasattr(actual_draw, 'draw_numbers'):
            return {
                'correct': 0,
                'total': 0,
                'accuracy': 0,
                'correct_numbers': []
            }
        
        # Convertir en ensembles pour faciliter la comparaison
        predicted_set = set(predicted_numbers)
        actual_set = set(actual_draw.draw_numbers)
        
        # Calculer les statistiques
        correct_numbers = predicted_set.intersection(actual_set)
        correct_count = len(correct_numbers)
        total_count = len(predicted_numbers)
        accuracy = correct_count / total_count if total_count > 0 else 0
        
        return {
            'correct': correct_count,
            'total': total_count,
            'accuracy': accuracy,
            'correct_numbers': list(correct_numbers)
        }
    
    def save_model(self, file_path='keno_48h_predictor_model.json'):
        """
        Sauvegarde le modèle de prédiction
        
        Args:
            file_path: Chemin du fichier de sauvegarde
            
        Returns:
            bool: True si la sauvegarde a réussi, False sinon
        """
        if not self.number_stats or not self.best_numbers:
            print("Aucun modèle à sauvegarder")
            return False
        
        # Créer le dictionnaire à sauvegarder
        model_data = {
            'pattern_weights': self.pattern_weights,
            'number_weights': self.number_weights,
            'best_numbers': [num_data for num_data in self.best_numbers[:50]]  # Sauvegarder les 50 meilleurs numéros
        }
        
        try:
            with open(file_path, 'w') as f:
                json.dump(model_data, f, indent=2)
            
            print(f"Modèle sauvegardé dans {file_path}")
            return True
        except Exception as e:
            print(f"Erreur lors de la sauvegarde du modèle: {e}")
            return False
    
    def load_model(self, file_path='keno_48h_predictor_model.json'):
        """
        Charge un modèle de prédiction
        
        Args:
            file_path: Chemin du fichier de modèle
            
        Returns:
            bool: True si le chargement a réussi, False sinon
        """
        if not os.path.exists(file_path):
            print(f"Le fichier {file_path} n'existe pas")
            return False
        
        try:
            with open(file_path, 'r') as f:
                model_data = json.load(f)
            
            self.pattern_weights = model_data.get('pattern_weights', {})
            self.number_weights = model_data.get('number_weights', {})
            self.best_numbers = model_data.get('best_numbers', [])
            
            # Convertir les clés en entiers pour number_weights
            self.number_weights = {int(k): v for k, v in self.number_weights.items()}
            
            print(f"Modèle chargé depuis {file_path}")
            return True
        except Exception as e:
            print(f"Erreur lors du chargement du modèle: {e}")
            return False

def main():
    """Fonction principale"""
    # Créer le prédicteur
    predictor = Keno48hPredictor()
    
    # Analyser les données historiques
    if not predictor.analyze_historical_data():
        print("Erreur lors de l'analyse des données historiques")
        return
    
    # Sauvegarder le modèle
    predictor.save_model()
    
    # Récupérer les tirages récents
    recent_draws = predictor.get_recent_draws(5)
    
    if not recent_draws:
        print("Aucun tirage récent trouvé")
        return
    
    # Afficher les tirages récents
    print("\nTirages récents:")
    for i, draw in enumerate(recent_draws):
        print(f"Tirage {i+1} ({draw.draw_date}): {sorted(draw.draw_numbers)}")
    
    # Prédire les numéros du prochain tirage
    print("\nPrédiction pour le prochain tirage:")
    
    # Prédire 7 numéros (pour un 7/7)
    predicted_7 = predictor.predict_next_draw(7)
    print(f"Prédiction 7/7: {sorted(predicted_7)}")
    
    # Prédire 10 numéros (pour un 10/10)
    predicted_10 = predictor.predict_next_draw(10)
    print(f"Prédiction 10/10: {sorted(predicted_10)}")
    
    # Prédire 20 numéros (pour plus de chances)
    predicted_20 = predictor.predict_next_draw(20)
    print(f"Prédiction 20 numéros: {sorted(predicted_20)}")
    
    # Évaluer les prédictions sur le dernier tirage connu
    if len(recent_draws) >= 2:
        last_draw = recent_draws[-1]
        previous_draws = recent_draws[:-1]
        
        # Créer un prédicteur temporaire pour évaluer sur les données historiques
        temp_predictor = Keno48hPredictor()
        temp_predictor.data_manager.draws = predictor.data_manager.draws[:-1]  # Exclure le dernier tirage
        temp_predictor.analyze_historical_data()
        temp_predictor.recent_draws = previous_draws
        
        # Faire des prédictions
        historical_pred_7 = temp_predictor.predict_next_draw(7)
        historical_pred_10 = temp_predictor.predict_next_draw(10)
        
        # Évaluer les prédictions
        eval_7 = temp_predictor.evaluate_prediction(historical_pred_7, last_draw)
        eval_10 = temp_predictor.evaluate_prediction(historical_pred_10, last_draw)
        
        print("\nÉvaluation sur le dernier tirage connu:")
        print(f"Dernier tirage ({last_draw.draw_date}): {sorted(last_draw.draw_numbers)}")
        print(f"Prédiction 7/7: {sorted(historical_pred_7)}")
        print(f"Résultat: {eval_7['correct']}/{eval_7['total']} ({eval_7['accuracy']*100:.2f}%)")
        print(f"Numéros corrects: {sorted(eval_7['correct_numbers'])}")
        
        print(f"Prédiction 10/10: {sorted(historical_pred_10)}")
        print(f"Résultat: {eval_10['correct']}/{eval_10['total']} ({eval_10['accuracy']*100:.2f}%)")
        print(f"Numéros corrects: {sorted(eval_10['correct_numbers'])}")

if __name__ == "__main__":
    main()
