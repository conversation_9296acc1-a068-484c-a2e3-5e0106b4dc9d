#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour vérifier la correction du système d'auto-amélioration optimisé
"""

import os
import sys

def test_single_number():
    """Test sur un seul numéro pour vérifier que la correction fonctionne"""
    
    print("=== Test de correction du système optimisé ===")
    
    try:
        # Importer les modules
        from keno_enhanced_auto_improve import KenoEnhancedAutoImprove, integrate_enhanced_auto_improve
        from keno_data import KenoDataManager
        from keno_analyzer import KenoAnalyzer
        
        print("✓ Modules importés avec succès")
        
        # Créer les instances
        data_manager = KenoDataManager()
        analyzer = KenoAnalyzer(data_manager)
        
        # Charger les données
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        if os.path.exists(data_dir):
            data_files = [f for f in os.listdir(data_dir) if f.endswith('.json') or f.endswith('.keno')]
            if data_files:
                latest_file = max(data_files, key=lambda f: os.path.getmtime(os.path.join(data_dir, f)))
                file_path = os.path.join(data_dir, latest_file)
                
                print(f"Chargement des données depuis: {file_path}")
                success = data_manager.load_database(file_path)
                
                if success:
                    print(f"✓ {data_manager.get_draws_count()} tirages chargés")
                else:
                    print("✗ Échec du chargement des données")
                    return False
            else:
                print("✗ Aucun fichier de données trouvé")
                return False
        else:
            print("✗ Répertoire de données non trouvé")
            return False
        
        # Intégrer le système optimisé
        enhanced_improver = integrate_enhanced_auto_improve(analyzer)
        
        if not enhanced_improver:
            print("✗ Échec de l'intégration")
            return False
        
        print("✓ Système intégré avec succès")
        
        # Test sur un seul numéro (numéro 7)
        print("\n=== Test de création de caractéristiques pour le numéro 7 ===")
        
        try:
            result = enhanced_improver.create_enhanced_features(7)
            
            if result is None:
                print("✗ Aucun résultat retourné")
                return False
            
            X, y, feature_names = result
            
            if X is not None and len(X) > 0:
                print(f"✓ Caractéristiques créées avec succès!")
                print(f"  Nombre d'échantillons: {len(X)}")
                print(f"  Nombre de caractéristiques: {len(feature_names) if feature_names else 'N/A'}")
                print(f"  Nombre de targets: {len(y)}")
                
                # Afficher quelques statistiques
                if len(y) > 0:
                    positive_rate = sum(y) / len(y)
                    print(f"  Taux de présence du numéro 7: {positive_rate:.3f}")
                
                return True
            else:
                print("✗ Données vides retournées")
                return False
                
        except Exception as e:
            print(f"✗ Erreur lors de la création des caractéristiques: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"✗ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_quick_training():
    """Test d'entraînement rapide sur 3 numéros"""
    
    print("\n=== Test d'entraînement rapide (3 numéros) ===")
    
    try:
        # Importer les modules
        from keno_enhanced_auto_improve import KenoEnhancedAutoImprove, integrate_enhanced_auto_improve
        from keno_data import KenoDataManager
        from keno_analyzer import KenoAnalyzer
        
        # Créer les instances
        data_manager = KenoDataManager()
        analyzer = KenoAnalyzer(data_manager)
        
        # Charger les données
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        data_files = [f for f in os.listdir(data_dir) if f.endswith('.json') or f.endswith('.keno')]
        latest_file = max(data_files, key=lambda f: os.path.getmtime(os.path.join(data_dir, f)))
        file_path = os.path.join(data_dir, latest_file)
        
        data_manager.load_database(file_path)
        
        # Intégrer le système optimisé
        enhanced_improver = integrate_enhanced_auto_improve(analyzer)
        
        # Test d'entraînement sur 3 numéros seulement
        test_numbers = [7, 21, 42]
        print(f"Test d'entraînement sur les numéros: {test_numbers}")
        
        results = enhanced_improver.run_enhanced_auto_improve(test_numbers)
        
        if results and results['trained_numbers']:
            print(f"✓ Entraînement réussi!")
            print(f"  Numéros entraînés: {results['trained_numbers']}")
            print(f"  Numéros échoués: {results['failed_numbers']}")
            
            if results['performance_summary']:
                import numpy as np
                avg_f1 = np.mean([p['f1_score'] for p in results['performance_summary'].values()])
                print(f"  F1-score moyen: {avg_f1:.4f}")
            
            # Test de prédiction
            print("\n=== Test de prédiction ===")
            predictions = enhanced_improver.predict_next_draw_enhanced(5)
            
            if predictions:
                print(f"✓ Prédictions générées: {predictions}")
                return True
            else:
                print("✗ Aucune prédiction générée")
                return False
        else:
            print("✗ Échec de l'entraînement")
            return False
            
    except Exception as e:
        print(f"✗ Erreur lors du test d'entraînement: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    
    print("=== Test de correction du système d'auto-amélioration optimisé ===")
    
    # Test 1: Création de caractéristiques
    test1_success = test_single_number()
    
    if not test1_success:
        print("\n✗ Le test de base a échoué. Arrêt des tests.")
        return 1
    
    # Test 2: Entraînement rapide
    test2_success = test_quick_training()
    
    if test2_success:
        print("\n✓ Tous les tests ont réussi!")
        print("Le système d'auto-amélioration optimisé fonctionne correctement.")
        print("\nVous pouvez maintenant utiliser:")
        print("  python integrate_enhanced_system.py")
        return 0
    else:
        print("\n✗ Le test d'entraînement a échoué.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nInterruption par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
