"""
Module pour l'interface de saisie des résultats de prédiction Keno
"""

import tkinter as tk
from tkinter import ttk, messagebox
import datetime

class KenoFeedbackDialog:
    """Classe pour l'interface de saisie des résultats de prédiction"""

    def __init__(self, parent, feedback_manager, prediction_data=None):
        """Initialise la fenêtre de saisie des résultats

        Args:
            parent: Fenêtre parente
            feedback_manager: Gestionnaire de feedback
            prediction_data (dict, optional): Données de prédiction (méthode, numéros prédits)
        """
        self.parent = parent
        self.feedback_manager = feedback_manager
        self.prediction_data = prediction_data or {}

        # Créer la fenêtre
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Saisie des résultats de prédiction")
        self.dialog.geometry("600x500")
        self.dialog.transient(parent)  # Rendre la fenêtre modale
        self.dialog.grab_set()

        # Centrer la fenêtre
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # Variables
        self.method_var = tk.StringVar(value=self.prediction_data.get('method', 'advanced'))
        self.predicted_numbers_var = tk.StringVar(value=self._format_numbers(self.prediction_data.get('numbers', [])))
        self.actual_numbers_var = tk.StringVar()
        self.score_correct_var = tk.IntVar(value=0)
        self.score_total_var = tk.IntVar(value=len(self.prediction_data.get('numbers', [])) or 7)

        # Créer les widgets
        self._create_widgets()

    def _format_numbers(self, numbers):
        """Formate une liste de numéros en chaîne de caractères

        Args:
            numbers (list): Liste de numéros

        Returns:
            str: Chaîne de caractères formatée
        """
        return ', '.join(str(num) for num in numbers)

    def _parse_numbers(self, numbers_str):
        """Parse une chaîne de caractères en liste de numéros

        Args:
            numbers_str (str): Chaîne de caractères à parser

        Returns:
            list: Liste de numéros
        """
        try:
            # Supprimer les espaces et séparer par virgules
            numbers = [int(num.strip()) for num in numbers_str.split(',') if num.strip()]
            return numbers
        except ValueError:
            messagebox.showerror("Erreur", "Format de numéros invalide. Utilisez des nombres séparés par des virgules.")
            return []

    def _create_widgets(self):
        """Crée les widgets de l'interface"""
        # Style
        style = ttk.Style()
        style.configure('TLabel', font=('Helvetica', 12))
        style.configure('TEntry', font=('Helvetica', 12))
        style.configure('TButton', font=('Helvetica', 12))

        # Frame principal
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Titre
        title_label = ttk.Label(main_frame, text="Saisie des résultats de prédiction", font=('Helvetica', 16, 'bold'))
        title_label.pack(pady=(0, 20))

        # Méthode de prédiction
        method_frame = ttk.Frame(main_frame)
        method_frame.pack(fill=tk.X, pady=10)

        method_label = ttk.Label(method_frame, text="Méthode de prédiction:")
        method_label.pack(side=tk.LEFT, padx=5)

        methods = ["ml", "advanced", "patterns", "series", "combined", "frequency"]
        method_menu = ttk.Combobox(method_frame, textvariable=self.method_var, values=methods, width=15)
        method_menu.pack(side=tk.LEFT, padx=5)

        # Numéros prédits
        predicted_frame = ttk.Frame(main_frame)
        predicted_frame.pack(fill=tk.X, pady=10)

        predicted_label = ttk.Label(predicted_frame, text="Numéros prédits:")
        predicted_label.pack(side=tk.LEFT, padx=5)

        predicted_entry = ttk.Entry(predicted_frame, textvariable=self.predicted_numbers_var, width=40)
        predicted_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Numéros réels
        actual_frame = ttk.Frame(main_frame)
        actual_frame.pack(fill=tk.X, pady=10)

        actual_label = ttk.Label(actual_frame, text="Numéros réels:")
        actual_label.pack(side=tk.LEFT, padx=5)

        actual_entry = ttk.Entry(actual_frame, textvariable=self.actual_numbers_var, width=40)
        actual_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Score
        score_frame = ttk.Frame(main_frame)
        score_frame.pack(fill=tk.X, pady=10)

        score_label = ttk.Label(score_frame, text="Score:")
        score_label.pack(side=tk.LEFT, padx=5)

        correct_spinbox = ttk.Spinbox(score_frame, from_=0, to=20, textvariable=self.score_correct_var, width=5)
        correct_spinbox.pack(side=tk.LEFT, padx=5)

        slash_label = ttk.Label(score_frame, text="/")
        slash_label.pack(side=tk.LEFT)

        total_spinbox = ttk.Spinbox(score_frame, from_=1, to=20, textvariable=self.score_total_var, width=5)
        total_spinbox.pack(side=tk.LEFT, padx=5)

        # Bouton de calcul automatique du score
        calc_button = ttk.Button(score_frame, text="Calculer", command=self._calculate_score)
        calc_button.pack(side=tk.LEFT, padx=20)

        # Boutons d'action
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)

        save_button = ttk.Button(button_frame, text="Enregistrer", command=self._save_feedback)
        save_button.pack(side=tk.LEFT, padx=10)

        cancel_button = ttk.Button(button_frame, text="Annuler", command=self.dialog.destroy)
        cancel_button.pack(side=tk.LEFT, padx=10)

        # Résumé des performances
        summary_frame = ttk.LabelFrame(main_frame, text="Résumé des performances récentes")
        summary_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # Créer un widget Text pour afficher le résumé
        self.summary_text = tk.Text(summary_frame, height=10, width=60, font=('Courier New', 10))
        self.summary_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Ajouter un bouton pour prédire avec l'apprentissage par renforcement
        rl_frame = ttk.Frame(main_frame)
        rl_frame.pack(fill=tk.X, pady=5)

        self.use_rl_var = tk.BooleanVar(value=False)
        rl_check = ttk.Checkbutton(rl_frame, text="Utiliser l'apprentissage par renforcement",
                                  variable=self.use_rl_var, command=self._toggle_rl)
        rl_check.pack(side=tk.LEFT, padx=5)

        self.rl_button = ttk.Button(rl_frame, text="Prédire avec RL",
                                  command=self._predict_with_rl, state=tk.DISABLED)
        self.rl_button.pack(side=tk.RIGHT, padx=5)

        # Afficher le résumé
        self._display_summary()

    def _calculate_score(self):
        """Calcule automatiquement le score en comparant les numéros prédits et réels"""
        predicted = self._parse_numbers(self.predicted_numbers_var.get())
        actual = self._parse_numbers(self.actual_numbers_var.get())

        if not predicted or not actual:
            messagebox.showwarning("Avertissement", "Veuillez saisir les numéros prédits et réels.")
            return

        # Calculer le score
        correct = set(predicted).intersection(set(actual))
        self.score_correct_var.set(len(correct))
        self.score_total_var.set(len(predicted))

        messagebox.showinfo("Score calculé", f"Score: {len(correct)}/{len(predicted)}")

    def _save_feedback(self):
        """Enregistre le feedback"""
        # Récupérer les données
        method = self.method_var.get()
        predicted = self._parse_numbers(self.predicted_numbers_var.get())
        actual = self._parse_numbers(self.actual_numbers_var.get())
        score = (self.score_correct_var.get(), self.score_total_var.get())

        # Vérifier les données
        if not method:
            messagebox.showwarning("Avertissement", "Veuillez sélectionner une méthode de prédiction.")
            return

        if not predicted:
            messagebox.showwarning("Avertissement", "Veuillez saisir les numéros prédits.")
            return

        if not actual:
            messagebox.showwarning("Avertissement", "Veuillez saisir les numéros réels.")
            return

        if score[1] == 0:
            messagebox.showwarning("Avertissement", "Le nombre total de numéros prédits ne peut pas être zéro.")
            return

        # Enregistrer le feedback
        success = self.feedback_manager.add_prediction_result(method, predicted, actual, score)

        if success:
            messagebox.showinfo("Succès", "Le résultat a été enregistré avec succès.")
            # Ajuster les paramètres des modèles
            self.feedback_manager.adjust_model_parameters()
            self.dialog.destroy()
        else:
            messagebox.showerror("Erreur", "Une erreur est survenue lors de l'enregistrement du résultat.")

    def _toggle_rl(self):
        """Active ou désactive le bouton de prédiction RL"""
        if self.use_rl_var.get():
            # Vérifier si l'agent RL est disponible
            if hasattr(self.feedback_manager, 'rl_agent') and self.feedback_manager.rl_agent:
                self.rl_button.config(state=tk.NORMAL)
            else:
                messagebox.showwarning("Avertissement", "L'agent d'apprentissage par renforcement n'est pas disponible.")
                self.use_rl_var.set(False)
        else:
            self.rl_button.config(state=tk.DISABLED)

    def _predict_with_rl(self):
        """Prédit les numéros avec l'apprentissage par renforcement"""
        if not hasattr(self.feedback_manager, 'rl_agent') or not self.feedback_manager.rl_agent:
            messagebox.showwarning("Avertissement", "L'agent d'apprentissage par renforcement n'est pas disponible.")
            return

        try:
            # Prédire les numéros
            num_predictions = self.score_total_var.get()
            predicted_numbers = self.feedback_manager.predict_with_rl(num_predictions)

            if not predicted_numbers:
                messagebox.showwarning("Avertissement", "Aucune prédiction générée.")
                return

            # Mettre à jour l'interface
            self.predicted_numbers_var.set(self._format_numbers(predicted_numbers))
            self.method_var.set("reinforcement_learning")

            messagebox.showinfo("Prédiction RL", f"Prédiction générée avec succès: {sorted(predicted_numbers)}")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la prédiction: {e}")

    def _display_summary(self):
        """Affiche un résumé des performances récentes"""
        try:
            # Récupérer les prédictions récentes
            recent_predictions = self.feedback_manager.get_recent_predictions(5)

            # Effacer le texte
            self.summary_text.delete(1.0, tk.END)

            # Afficher l'en-tête
            self.summary_text.insert(tk.END, "RÉSULTATS RÉCENTS\n")
            self.summary_text.insert(tk.END, "================\n\n")

            if not recent_predictions:
                self.summary_text.insert(tk.END, "Aucun résultat enregistré.\n")
                return

            # Afficher les prédictions récentes
            for i, pred in enumerate(recent_predictions):
                date = datetime.datetime.fromisoformat(pred['date']).strftime("%d/%m/%Y %H:%M")
                self.summary_text.insert(tk.END, f"{i+1}. {date} - Méthode: {pred['method']}\n")
                self.summary_text.insert(tk.END, f"   Score: {pred['score'][0]}/{pred['score'][1]} ")
                self.summary_text.insert(tk.END, f"({pred['accuracy']*100:.1f}%)\n")

                # Afficher la récompense RL si disponible
                if 'rl_reward' in pred:
                    self.summary_text.insert(tk.END, f"   Récompense RL: {pred['rl_reward']}\n")

                self.summary_text.insert(tk.END, "\n")

            # Afficher les performances des modèles
            self.summary_text.insert(tk.END, "PERFORMANCES DES MODÈLES\n")
            self.summary_text.insert(tk.END, "=======================\n\n")

            summary = self.feedback_manager.get_performance_summary()
            for method, perf in summary.get('models', {}).items():
                self.summary_text.insert(tk.END, f"{method}: {perf['average_accuracy']*100:.1f}% ")
                if perf['trend'] > 0:
                    self.summary_text.insert(tk.END, f"(↑ {perf['trend']*100:.1f}%)\n")
                elif perf['trend'] < 0:
                    self.summary_text.insert(tk.END, f"(↓ {abs(perf['trend'])*100:.1f}%)\n")
                else:
                    self.summary_text.insert(tk.END, "(→)\n")

            # Afficher les statistiques de l'agent RL si disponible
            if 'reinforcement_learning' in summary:
                self.summary_text.insert(tk.END, "\nAPPRENTISSAGE PAR RENFORCEMENT\n")
                self.summary_text.insert(tk.END, "=============================\n\n")

                rl_stats = summary['reinforcement_learning']
                self.summary_text.insert(tk.END, f"Entraînements: {rl_stats.get('training_count', 0)}\n")
                self.summary_text.insert(tk.END, f"Précision moyenne: {rl_stats.get('average_accuracy', 0)*100:.1f}%\n")
                self.summary_text.insert(tk.END, f"Récompense moyenne: {rl_stats.get('average_reward', 0):.2f}\n")
                self.summary_text.insert(tk.END, f"Exploration: {rl_stats.get('epsilon', 0)*100:.1f}%\n")
        except Exception as e:
            self.summary_text.delete(1.0, tk.END)
            self.summary_text.insert(tk.END, f"Erreur lors de l'affichage du résumé: {e}")
