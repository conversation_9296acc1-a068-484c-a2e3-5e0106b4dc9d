"""
Script de débogage pour vérifier l'importation des modules
"""

import sys
import os

print("Répertoire de travail actuel:", os.getcwd())
print("Chemins Python:", sys.path)

try:
    print("\nTentative d'importation de keno_advanced_analyzer...")
    import keno_advanced_analyzer
    print("Importation réussie!")
    print("Chemin du module:", keno_advanced_analyzer.__file__)
except ImportError as e:
    print("Erreur d'importation:", e)
    
    # Vérifier si le fichier existe
    if os.path.exists("keno_advanced_analyzer.py"):
        print("Le fichier keno_advanced_analyzer.py existe dans le répertoire courant.")
        
        # Afficher les premières lignes du fichier pour vérifier son contenu
        try:
            with open("keno_advanced_analyzer.py", "r", encoding="utf-8") as f:
                content = f.read(500)  # Lire les 500 premiers caractères
                print("\nDébut du fichier keno_advanced_analyzer.py:")
                print(content)
        except Exception as e2:
            print("Erreur lors de la lecture du fichier:", e2)
    else:
        print("Le fichier keno_advanced_analyzer.py n'existe pas dans le répertoire courant.")
        
        # Chercher le fichier dans les sous-répertoires
        for root, dirs, files in os.walk("."):
            if "keno_advanced_analyzer.py" in files:
                print(f"Fichier trouvé dans: {os.path.join(root, 'keno_advanced_analyzer.py')}")
