"""
Script pour intégrer la correction de scale_pos_weight dans l'optimiseur XGBoost
"""

import os
import sys
import json
import shutil

def backup_file(file_path):
    """Crée une sauvegarde du fichier"""
    backup_path = file_path + '.bak'
    if os.path.exists(file_path):
        shutil.copy2(file_path, backup_path)
        print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def modify_xgboost_optimizer():
    """Modifie l'optimiseur XGBoost pour utiliser les paramètres optimisés"""
    # Fichier à modifier
    file_path = 'keno_xgboost_optimizer_simple.py'
    
    if not os.path.exists(file_path):
        print(f"Erreur: Le fichier {file_path} n'existe pas")
        return False
    
    # Créer une sauvegarde
    backup_file(file_path)
    
    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Modifier le chemin du fichier de cache
    new_content = content.replace(
        "PARAMS_CACHE_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'xgboost_optimal_params.json')",
        "PARAMS_CACHE_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'xgboost_optimal_params.json')\n\n# Chemin du fichier de paramètres corrigés\nCORRECTED_PARAMS_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'xgboost_optimal_params.json')"
    )
    
    # Modifier la méthode _load_cached_params
    old_load_method = """    def _load_cached_params(self):
        \"\"\"Charge les paramètres optimaux depuis le cache
        
        Returns:
            dict: Paramètres optimaux par numéro
        \"\"\"
        try:
            if os.path.exists(PARAMS_CACHE_FILE):
                with open(PARAMS_CACHE_FILE, 'r') as f:
                    params = json.load(f)
                
                # Convertir les clés en entiers
                return {int(k): v for k, v in params.items()}
            else:
                return {}
        except Exception as e:
            print(f"Erreur lors du chargement des paramètres optimaux: {e}")
            return {}"""
    
    new_load_method = """    def _load_cached_params(self):
        \"\"\"Charge les paramètres optimaux depuis le cache
        
        Returns:
            dict: Paramètres optimaux par numéro
        \"\"\"
        try:
            # Essayer d'abord de charger les paramètres corrigés
            if os.path.exists(CORRECTED_PARAMS_FILE):
                with open(CORRECTED_PARAMS_FILE, 'r') as f:
                    params = json.load(f)
                
                # Convertir les clés en entiers
                corrected_params = {int(k): v for k, v in params.items()}
                
                if self.verbose > 0:
                    print(f"Paramètres optimisés chargés depuis {CORRECTED_PARAMS_FILE}")
                    print(f"Nombre de numéros avec paramètres optimisés: {len(corrected_params)}")
                
                return corrected_params
            
            # Sinon, essayer de charger les paramètres du cache
            elif os.path.exists(PARAMS_CACHE_FILE):
                with open(PARAMS_CACHE_FILE, 'r') as f:
                    params = json.load(f)
                
                # Convertir les clés en entiers
                return {int(k): v for k, v in params.items()}
            else:
                return {}
        except Exception as e:
            print(f"Erreur lors du chargement des paramètres optimaux: {e}")
            return {}"""
    
    new_content = new_content.replace(old_load_method, new_load_method)
    
    # Modifier la méthode calculate_optimal_scale_pos_weight
    old_calc_method = """    def calculate_optimal_scale_pos_weight(self, y_train):
        \"\"\"
        Calcule la valeur optimale de scale_pos_weight basée sur la distribution des classes
        
        Args:
            y_train: Étiquettes d'entraînement
            
        Returns:
            float: Valeur optimale de scale_pos_weight
        \"\"\"
        # Compter les exemples positifs et négatifs
        neg_count = np.sum(y_train == 0)
        pos_count = np.sum(y_train == 1)
        
        # Éviter la division par zéro
        if pos_count > 0:
            # Calculer le ratio
            ratio = neg_count / pos_count
            return ratio
        else:
            # Valeur par défaut si aucun exemple positif
            return 1.0"""
    
    new_calc_method = """    def calculate_optimal_scale_pos_weight(self, y_train, num=None):
        \"\"\"
        Calcule la valeur optimale de scale_pos_weight basée sur la distribution des classes
        
        Args:
            y_train: Étiquettes d'entraînement
            num: Numéro Keno (pour le logging)
            
        Returns:
            float: Valeur optimale de scale_pos_weight
        \"\"\"
        # Compter les exemples positifs et négatifs
        neg_count = np.sum(y_train == 0)
        pos_count = np.sum(y_train == 1)
        
        # Éviter la division par zéro
        if pos_count > 0:
            # Calculer le ratio
            ratio = neg_count / pos_count
            
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
                print(f"  Numéro {num}: Ratio négatif/positif calculé = {ratio:.4f}")
            
            return ratio
        else:
            # Valeur par défaut si aucun exemple positif
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Aucun exemple positif trouvé, utilisation de scale_pos_weight=1.0")
            
            return 1.0"""
    
    new_content = new_content.replace(old_calc_method, new_calc_method)
    
    # Modifier la méthode optimize_hyperparameters
    old_optimize_call = "scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train)"
    new_optimize_call = "scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train, num)"
    
    new_content = new_content.replace(old_optimize_call, new_optimize_call)
    
    # Modifier la méthode train_model
    old_train_call = "scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train)"
    new_train_call = "scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train, num)"
    
    new_content = new_content.replace(old_train_call, new_train_call)
    
    # Écrire le contenu modifié
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Le fichier {file_path} a été modifié avec succès")
    return True

def run_correction_script():
    """Exécute le script de correction de scale_pos_weight"""
    print("Exécution du script de correction de scale_pos_weight...")
    
    # Vérifier si le script existe
    if not os.path.exists('correct_scale_pos_weight.py'):
        print("Erreur: Le script correct_scale_pos_weight.py n'existe pas")
        return False
    
    # Exécuter le script
    import subprocess
    result = subprocess.run(['python', 'correct_scale_pos_weight.py'], capture_output=True, text=True)
    
    if result.returncode == 0:
        print("Le script de correction a été exécuté avec succès")
        print(result.stdout)
        return True
    else:
        print("Erreur lors de l'exécution du script de correction")
        print(result.stderr)
        return False

def main():
    """Fonction principale"""
    print("Intégration de la correction de scale_pos_weight dans l'optimiseur XGBoost")
    
    # Exécuter le script de correction
    correction_success = run_correction_script()
    
    if correction_success:
        # Modifier l'optimiseur XGBoost
        optimizer_success = modify_xgboost_optimizer()
        
        if optimizer_success:
            print("\nL'intégration a été effectuée avec succès!")
            print("L'optimiseur XGBoost utilisera maintenant les valeurs correctes de scale_pos_weight pour chaque numéro.")
        else:
            print("\nErreur lors de la modification de l'optimiseur XGBoost")
    else:
        print("\nErreur lors de l'exécution du script de correction")

if __name__ == "__main__":
    main()
