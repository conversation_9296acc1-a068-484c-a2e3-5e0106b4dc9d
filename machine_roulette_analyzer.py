from datetime import datetime
from collections import deque
import numpy as np

class RouletteSpinData:
    def __init__(self, number, timestamp, distance_from_prev=None, spin_time=None):
        self.number = number
        self.timestamp = timestamp
        self.distance_from_prev = distance_from_prev
        self.spin_time = spin_time

class MachineRouletteAnalyzer:
    def __init__(self, history_size=20):
        self.history = deque(maxlen=history_size)
        self.wheel_numbers = list(range(37))  # 0 à 36

    def calculate_distance(self, num1, num2):
        """Calcule la distance entre deux numéros sur la roue"""
        return abs(num1 - num2)

    def add_spin(self, number, timestamp):
        """Ajoute un nouveau résultat de spin"""
        distance = None
        spin_time = None

        if self.history:
            last_spin = self.history[-1]
            distance = self.calculate_distance(number, last_spin.number)
            spin_time = (timestamp - last_spin.timestamp).total_seconds()

        spin_data = RouletteSpinData(number, timestamp, distance, spin_time)
        self.history.append(spin_data)

    def analyze_force_pattern(self):
        """Analyse le pattern de force basé sur l'historique"""
        if len(self.history) < 2:
            return {'force': 'Insuffisant', 'confidence': 0.0}

        distances = [spin.distance_from_prev for spin in list(self.history)[1:]]
        avg_distance = np.mean(distances)

        if avg_distance < 5:
            force = 'Faible'
            confidence = 70.0
        elif avg_distance < 12:
            force = 'Moyenne'
            confidence = 60.0
        else:
            force = 'Forte'
            confidence = 50.0

        return {'force': force, 'confidence': confidence}

    def predict_landing_zone(self):
        """Prédit la zone probable pour le prochain numéro (réduite à 8 chiffres)"""
        if not self.history:
            return set(range(8)), 0.0

        last_number = self.history[-1].number
        force_pattern = self.analyze_force_pattern()

        # Calcul de la distance probable basée sur la force
        if force_pattern['force'] == 'Faible':
            distances = [1, 2, 3, 4]  # Distances plus courtes pour force faible
        elif force_pattern['force'] == 'Moyenne':
            distances = [3, 6, 9, 12]  # Distances moyennes
        else:
            distances = [8, 12, 16, 20]  # Grandes distances pour force forte

        # Générer la zone probable (8 chiffres maximum)
        likely_zone = set()
        for d in distances:
            # Ajouter les numéros dans les deux directions (+ et -)
            likely_zone.add((last_number + d) % 37)
            likely_zone.add((last_number - d) % 37)

        # Si on n'a pas assez de numéros, ajouter des numéros adjacents
        while len(likely_zone) < 8 and len(likely_zone) < 37:
            # Trouver la distance suivante à ajouter
            next_distance = max(distances) + 1
            likely_zone.add((last_number + next_distance) % 37)
            if len(likely_zone) < 8:
                likely_zone.add((last_number - next_distance) % 37)
            distances.append(next_distance)

        # Limiter à 8 numéros maximum
        if len(likely_zone) > 8:
            # Convertir en liste pour pouvoir trier et limiter
            zone_list = list(likely_zone)
            # Trier par proximité avec le dernier numéro
            zone_list.sort(key=lambda x: min((x - last_number) % 37, (last_number - x) % 37))
            likely_zone = set(zone_list[:8])

        return likely_zone, force_pattern['confidence']

    def get_force_statistics(self):
        """Retourne les statistiques sur la force"""
        if len(self.history) < 2:
            return None

        spins = list(self.history)[1:]
        distances = [spin.distance_from_prev for spin in spins]
        times = [spin.spin_time for spin in spins]

        return {
            'avg_distance': np.mean(distances),
            'std_distance': np.std(distances),
            'avg_time': np.mean(times) if times[0] is not None else 0
        }

    def predict_next_number(self):
        """Prédit le prochain numéro basé sur plusieurs facteurs d'analyse"""
        if len(self.history) < 3:
            return None, 0.0

        # Récupération de l'historique complet
        spins = list(self.history)
        last_number = spins[-1].number

        # Analyse des patterns
        force_pattern = self.analyze_force_pattern()
        stats = self.get_force_statistics()

        # Calcul de la tendance directionnelle sur les 3 derniers spins
        last_spins = spins[-3:]
        direction = 1 if last_spins[-1].number > last_spins[-2].number else -1

        # Prédiction basée sur la force
        if force_pattern['force'] == 'Faible':
            predicted_distance = round(min(5, stats['avg_distance']))
        elif force_pattern['force'] == 'Moyenne':
            predicted_distance = round(min(10, stats['avg_distance']))
        else:
            predicted_distance = round(min(15, stats['avg_distance']))

        # Calcul du numéro prédit basé sur la distance
        distance_prediction = (last_number + (predicted_distance * direction)) % 37

        # Analyse des fréquences
        number_counts = {i: 0 for i in range(37)}
        for spin in spins:
            number_counts[spin.number] += 1

        # Calcul des probabilités basées sur la fréquence
        total_spins = len(spins)
        frequency_probabilities = {num: count / total_spins for num, count in number_counts.items()}

        # Analyse des séquences (recherche de patterns récurrents)
        sequence_predictions = []
        if len(spins) >= 6:
            # Recherche de séquences de 2 ou 3 numéros qui se répètent
            for seq_length in [2, 3]:
                if len(spins) >= seq_length * 2:
                    current_seq = [spin.number for spin in spins[-seq_length:]]

                    # Recherche de cette séquence dans l'historique
                    for i in range(len(spins) - seq_length * 2 + 1):
                        hist_seq = [spin.number for spin in spins[i:i+seq_length]]
                        if hist_seq == current_seq:
                            # Si on trouve la séquence, on prédit le numéro qui l'a suivie
                            if i + seq_length < len(spins):
                                sequence_predictions.append(spins[i + seq_length].number)

        # Combinaison des prédictions
        final_predictions = {}

        # Ajout de la prédiction basée sur la distance
        final_predictions[distance_prediction] = force_pattern['confidence'] * 0.5

        # Ajout des prédictions basées sur les séquences
        for num in sequence_predictions:
            if num in final_predictions:
                final_predictions[num] += 15  # Bonus de confiance pour les patterns récurrents
            else:
                final_predictions[num] = 15

        # Ajout des prédictions basées sur la fréquence (pour les numéros fréquents)
        for num, prob in frequency_probabilities.items():
            if prob > 1/37:  # Si la fréquence est supérieure à la probabilité uniforme
                bonus = (prob - 1/37) * 100  # Bonus proportionnel à l'excès de fréquence
                if num in final_predictions:
                    final_predictions[num] += bonus
                else:
                    final_predictions[num] = bonus

        # Sélection du numéro avec la plus haute probabilité
        if final_predictions:
            predicted_number = max(final_predictions, key=final_predictions.get)
            confidence = min(95.0, final_predictions[predicted_number])  # Plafond à 95% de confiance
        else:
            predicted_number = distance_prediction
            confidence = force_pattern['confidence'] * 0.5

        return predicted_number, confidence

    def get_top_predictions(self, count=4):
        """Retourne les 4 numéros les plus probables avec leur confiance, en tenant compte de la force précédente"""
        if len(self.history) < 3:
            return []

        # Récupération de l'historique complet
        spins = list(self.history)

        # Analyse des patterns
        force_pattern = self.analyze_force_pattern()
        force = force_pattern['force']

        # Calcul des prédictions pour tous les numéros
        predictions = {i: 0.5 for i in range(37)}  # Initialisation avec une probabilité de base minimale

        # 1. Prédiction basée sur la force et la distance depuis le dernier numéro
        last_number = spins[-1].number

        # Définir les plages de distance en fonction de la force
        if force == 'Faible':
            # Pour force faible, favoriser fortement les petites distances
            primary_distances = range(1, 6)  # 1-5
            secondary_distances = range(6, 11)  # 6-10
            primary_weight = 30.0
            secondary_weight = 5.0
        elif force == 'Moyenne':
            # Pour force moyenne, favoriser les distances moyennes
            primary_distances = range(5, 13)  # 5-12
            secondary_distances = range(1, 5)  # 1-4
            primary_weight = 25.0
            secondary_weight = 10.0
        else:  # Force forte
            # Pour force forte, favoriser les grandes distances
            primary_distances = range(12, 19)  # 12-18
            secondary_distances = range(8, 12)  # 8-11
            primary_weight = 20.0
            secondary_weight = 8.0

        # Appliquer les poids en fonction des distances
        for distance in primary_distances:
            for direction in [1, -1]:
                num = (last_number + (distance * direction)) % 37
                # Plus la distance est proche du milieu de la plage, plus le poids est important
                mid_primary = (min(primary_distances) + max(primary_distances)) / 2
                weight_factor = 1.0 - abs(distance - mid_primary) / len(primary_distances)
                predictions[num] += primary_weight * (0.5 + weight_factor)

        for distance in secondary_distances:
            for direction in [1, -1]:
                num = (last_number + (distance * direction)) % 37
                predictions[num] += secondary_weight

        # 2. Analyse des séquences récurrentes (avec plus de poids)
        if len(spins) >= 6:
            for seq_length in [2, 3]:
                if len(spins) >= seq_length * 2:
                    current_seq = [spin.number for spin in spins[-seq_length:]]

                    for i in range(len(spins) - seq_length * 2 + 1):
                        hist_seq = [spin.number for spin in spins[i:i+seq_length]]
                        if hist_seq == current_seq and i + seq_length < len(spins):
                            next_num = spins[i + seq_length].number
                            predictions[next_num] += 25  # Bonus important pour les patterns récurrents

        # 3. Analyse des voisins sur la roue physique
        wheel_order = [0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5, 24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26]

        try:
            last_index = wheel_order.index(last_number)
            # Ajouter un bonus pour les voisins physiques en fonction de la force
            if force == 'Faible':
                # Pour force faible, favoriser les voisins très proches
                neighbor_offsets = [-1, 1]
                neighbor_weight = 15.0
            elif force == 'Moyenne':
                # Pour force moyenne, voisins à distance moyenne
                neighbor_offsets = [-2, -1, 1, 2]
                neighbor_weight = 12.0
            else:  # Force forte
                # Pour force forte, voisins plus éloignés
                neighbor_offsets = [-3, -2, 2, 3]
                neighbor_weight = 10.0

            for offset in neighbor_offsets:
                neighbor_index = (last_index + offset) % len(wheel_order)
                neighbor = wheel_order[neighbor_index]
                predictions[neighbor] += neighbor_weight / abs(offset)
        except ValueError:
            pass

        # 4. Analyse des fréquences (avec moins de poids pour éviter de biaiser les prédictions)
        number_counts = {i: 0 for i in range(37)}
        for spin in spins:
            number_counts[spin.number] += 1

        total_spins = len(spins)
        for num, count in number_counts.items():
            frequency = count / total_spins
            if frequency > 1/37:  # Si plus fréquent que la moyenne
                bonus = (frequency - 1/37) * 50  # Bonus réduit
                predictions[num] += bonus

        # Normalisation des probabilités
        total_prob = sum(predictions.values())
        if total_prob > 0:
            for num in predictions:
                predictions[num] = (predictions[num] / total_prob) * 100
                predictions[num] = min(95.0, predictions[num])  # Plafond à 95%

        # Tri des prédictions par probabilité décroissante
        sorted_predictions = sorted(predictions.items(), key=lambda x: x[1], reverse=True)

        # Retourne les 4 meilleures prédictions
        return sorted_predictions[:count]

