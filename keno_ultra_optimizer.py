#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Système d'optimisation ultra-avancé pour les prédictions Keno
Version améliorée avec suppression des warnings et performances optimisées
"""

import os
import sys
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings

# Suppression complète de tous les warnings
warnings.filterwarnings('ignore')
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

# Imports conditionnels optimisés
try:
    from sklearn.ensemble import RandomForestClassifier, VotingClassifier, GradientBoostingClassifier
    from sklearn.model_selection import train_test_split, RandomizedSearchCV, StratifiedKFold
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
    from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
    from sklearn.preprocessing import RobustScaler
    sklearn_available = True
except ImportError:
    sklearn_available = False

try:
    import xgboost as xgb
    # Supprimer les warnings XGBoost
    xgb.set_config(verbosity=0)
    xgboost_available = True
except ImportError:
    xgboost_available = False

try:
    import lightgbm as lgb
    lightgbm_available = True
except ImportError:
    lightgbm_available = False

class KenoUltraOptimizer:
    """Optimiseur ultra-avancé pour les prédictions Keno"""

    def __init__(self, data_manager, analyzer):
        self.data_manager = data_manager
        self.analyzer = analyzer
        self.models = {}
        self.feature_importance = {}
        self.performance_history = {}

        # Configuration ultra-optimisée
        self.config = {
            'feature_engineering': {
                'use_advanced_patterns': True,
                'use_fourier_analysis': True,
                'use_statistical_moments': True,
                'use_entropy_features': True,
                'lookback_windows': [3, 5, 10, 20, 50, 100, 200],
                'rolling_windows': [3, 7, 14, 21, 30],
                'lag_features': [1, 2, 3, 5, 10]
            },
            'model_optimization': {
                'use_ensemble_stacking': True,
                'use_bayesian_optimization': True,
                'use_early_stopping': True,
                'cross_validation_folds': 10,
                'optimization_iterations': 50,
                'ensemble_weights_optimization': True
            },
            'prediction_strategy': {
                'use_multi_timeframe_analysis': True,
                'use_confidence_intervals': True,
                'use_trend_prediction': True,
                'use_volatility_adjustment': True,
                'use_adaptive_learning': True
            }
        }

    def create_ultra_features(self, number, lookback_days=730):
        """Crée des caractéristiques ultra-avancées"""
        print(f"Création de caractéristiques ultra-avancées pour le numéro {number}...")

        draws = self.data_manager.draws
        if len(draws) < 200:
            print("Pas assez de données pour l'analyse ultra-avancée")
            return None

        # Filtrer les données récentes
        if lookback_days:
            cutoff_date = datetime.now() - timedelta(days=lookback_days)
            draws = [d for d in draws if d.draw_date and d.draw_date >= cutoff_date]

        features_data = []
        targets = []

        for i in range(100, len(draws)):  # Plus d'historique pour les features avancées
            current_draw = draws[i]
            draw_numbers = self._get_draw_numbers(current_draw)

            if not draw_numbers:
                continue

            features = {}

            # 1. Features de fréquence multi-échelles
            for window in self.config['feature_engineering']['lookback_windows']:
                recent_draws = draws[max(0, i-window):i]
                freq = sum(1 for d in recent_draws if self._get_draw_numbers(d) and number in self._get_draw_numbers(d))

                features[f'freq_{window}'] = freq / max(1, window)
                features[f'freq_normalized_{window}'] = freq / max(1, len(recent_draws))
                features[f'freq_deviation_{window}'] = (freq - window * 20/70) / max(1, np.sqrt(window))

            # 2. Features de momentum avancées
            momentum_features = self._calculate_advanced_momentum(draws[:i], number)
            features.update(momentum_features)

            # 3. Features statistiques avancées
            statistical_features = self._calculate_statistical_features(draws[:i], number)
            features.update(statistical_features)

            # 4. Features de patterns temporels
            temporal_features = self._calculate_temporal_patterns(draws[:i], number, current_draw)
            features.update(temporal_features)

            # 5. Features d'entropie et complexité
            entropy_features = self._calculate_entropy_features(draws[:i], number)
            features.update(entropy_features)

            # 6. Features de corrélation avec autres numéros
            correlation_features = self._calculate_correlation_features(draws[:i], number, draw_numbers)
            features.update(correlation_features)

            # 7. Features de volatilité
            volatility_features = self._calculate_volatility_features(draws[:i], number)
            features.update(volatility_features)

            if len(features) > 0:
                features_data.append(list(features.values()))
                targets.append(1 if number in draw_numbers else 0)

        if not features_data:
            print(f"Aucune donnée créée pour le numéro {number}")
            return None

        # Créer les noms de features
        feature_names = list(features.keys())
        df = pd.DataFrame(features_data, columns=feature_names)

        print(f"Créé {len(feature_names)} caractéristiques ultra-avancées pour {len(df)} échantillons")
        return df, targets, feature_names

    def _get_draw_numbers(self, draw):
        """Fonction utilitaire pour obtenir les numéros d'un tirage"""
        if hasattr(draw, 'draw_numbers') and draw.draw_numbers:
            return draw.draw_numbers
        elif hasattr(draw, 'numbers') and draw.numbers:
            return draw.numbers
        return None

    def _calculate_advanced_momentum(self, draws, number):
        """Calcule des features de momentum avancées"""
        features = {}

        # Momentum multi-échelles
        for window in [3, 5, 10, 20, 50]:
            recent = draws[-window:] if len(draws) >= window else draws
            momentum = sum(1 for d in recent if self._get_draw_numbers(d) and number in self._get_draw_numbers(d))
            features[f'momentum_{window}'] = momentum / len(recent)

        # Accélération du momentum
        if len(draws) >= 20:
            recent_10 = draws[-10:]
            previous_10 = draws[-20:-10]

            recent_freq = sum(1 for d in recent_10 if self._get_draw_numbers(d) and number in self._get_draw_numbers(d)) / 10
            previous_freq = sum(1 for d in previous_10 if self._get_draw_numbers(d) and number in self._get_draw_numbers(d)) / 10

            features['momentum_acceleration'] = recent_freq - previous_freq
            features['momentum_trend'] = recent_freq / max(0.001, previous_freq)
        else:
            features['momentum_acceleration'] = 0
            features['momentum_trend'] = 1

        return features

    def _calculate_statistical_features(self, draws, number):
        """Calcule des features statistiques avancées"""
        features = {}

        # Calculer les gaps
        gaps = []
        last_appearance = -1

        for i, draw in enumerate(draws):
            draw_numbers = self._get_draw_numbers(draw)
            if draw_numbers and number in draw_numbers:
                if last_appearance >= 0:
                    gaps.append(i - last_appearance)
                last_appearance = i

        if gaps:
            features['gap_mean'] = np.mean(gaps)
            features['gap_std'] = np.std(gaps)
            features['gap_skewness'] = self._calculate_skewness(gaps)
            features['gap_kurtosis'] = self._calculate_kurtosis(gaps)
            features['gap_cv'] = np.std(gaps) / max(0.001, np.mean(gaps))
            features['gap_current'] = len(draws) - last_appearance - 1
            features['gap_percentile'] = np.percentile(gaps, 75) if len(gaps) > 3 else 0
        else:
            features.update({
                'gap_mean': 0, 'gap_std': 0, 'gap_skewness': 0,
                'gap_kurtosis': 0, 'gap_cv': 0, 'gap_current': len(draws),
                'gap_percentile': 0
            })

        return features

    def _calculate_temporal_patterns(self, draws, number, current_draw):
        """Calcule des patterns temporels avancés"""
        features = {}

        # Features temporelles du tirage actuel
        if hasattr(current_draw, 'draw_date') and current_draw.draw_date:
            dt = current_draw.draw_date
            features['day_of_week'] = dt.weekday()
            features['hour'] = dt.hour
            features['month'] = dt.month
            features['day_of_month'] = dt.day
            features['week_of_year'] = dt.isocalendar()[1]
            features['is_weekend'] = 1 if dt.weekday() >= 5 else 0
            features['is_month_start'] = 1 if dt.day <= 7 else 0
            features['is_month_end'] = 1 if dt.day >= 24 else 0
        else:
            features.update({
                'day_of_week': 0, 'hour': 12, 'month': 1, 'day_of_month': 15,
                'week_of_year': 1, 'is_weekend': 0, 'is_month_start': 0, 'is_month_end': 0
            })

        # Patterns saisonniers
        appearances_by_month = [0] * 12
        appearances_by_dow = [0] * 7

        for draw in draws:
            draw_numbers = self._get_draw_numbers(draw)
            if draw_numbers and number in draw_numbers and hasattr(draw, 'draw_date') and draw.draw_date:
                appearances_by_month[draw.draw_date.month - 1] += 1
                appearances_by_dow[draw.draw_date.weekday()] += 1

        current_month = features['month'] - 1
        current_dow = features['day_of_week']

        features['seasonal_month_freq'] = appearances_by_month[current_month] / max(1, sum(appearances_by_month))
        features['seasonal_dow_freq'] = appearances_by_dow[current_dow] / max(1, sum(appearances_by_dow))

        return features

    def _calculate_entropy_features(self, draws, number):
        """Calcule des features d'entropie et de complexité"""
        features = {}

        # Séquence binaire des apparitions
        sequence = []
        for draw in draws[-100:]:  # Derniers 100 tirages
            draw_numbers = self._get_draw_numbers(draw)
            sequence.append(1 if draw_numbers and number in draw_numbers else 0)

        if len(sequence) > 10:
            # Entropie de Shannon
            p1 = sum(sequence) / len(sequence)
            p0 = 1 - p1
            if p1 > 0 and p0 > 0:
                features['shannon_entropy'] = -(p1 * np.log2(p1) + p0 * np.log2(p0))
            else:
                features['shannon_entropy'] = 0

            # Complexité de Lempel-Ziv (approximation)
            features['lz_complexity'] = self._approximate_lz_complexity(sequence)

            # Runs test (séquences consécutives)
            runs = self._calculate_runs(sequence)
            features['runs_count'] = len(runs)
            features['avg_run_length'] = np.mean([len(run) for run in runs]) if runs else 0

        else:
            features.update({
                'shannon_entropy': 0, 'lz_complexity': 0,
                'runs_count': 0, 'avg_run_length': 0
            })

        return features

    def _calculate_correlation_features(self, draws, number, current_numbers):
        """Calcule des features de corrélation avec autres numéros"""
        features = {}

        # Co-occurrence avec les numéros du tirage actuel
        cooccurrence_scores = []

        for other_num in current_numbers:
            if other_num != number:
                # Calculer la corrélation historique
                correlation = self._calculate_number_correlation(draws, number, other_num)
                cooccurrence_scores.append(correlation)

        if cooccurrence_scores:
            features['avg_cooccurrence'] = np.mean(cooccurrence_scores)
            features['max_cooccurrence'] = np.max(cooccurrence_scores)
            features['cooccurrence_strength'] = len([s for s in cooccurrence_scores if s > 0.1])
        else:
            features.update({
                'avg_cooccurrence': 0, 'max_cooccurrence': 0, 'cooccurrence_strength': 0
            })

        return features

    def _calculate_volatility_features(self, draws, number):
        """Calcule des features de volatilité"""
        features = {}

        # Volatilité des gaps
        gaps = []
        last_appearance = -1

        for i, draw in enumerate(draws):
            draw_numbers = self._get_draw_numbers(draw)
            if draw_numbers and number in draw_numbers:
                if last_appearance >= 0:
                    gaps.append(i - last_appearance)
                last_appearance = i

        if len(gaps) > 3:
            # Volatilité glissante
            window_volatilities = []
            for i in range(3, len(gaps)):
                window_gaps = gaps[i-3:i]
                window_volatilities.append(np.std(window_gaps))

            if window_volatilities:
                features['volatility_mean'] = np.mean(window_volatilities)
                features['volatility_trend'] = window_volatilities[-1] - window_volatilities[0] if len(window_volatilities) > 1 else 0
                features['volatility_current'] = window_volatilities[-1] if window_volatilities else 0
            else:
                features.update({'volatility_mean': 0, 'volatility_trend': 0, 'volatility_current': 0})
        else:
            features.update({'volatility_mean': 0, 'volatility_trend': 0, 'volatility_current': 0})

        return features

    def _calculate_skewness(self, data):
        """Calcule l'asymétrie (skewness)"""
        if len(data) < 3:
            return 0

        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0

        skew = np.mean([(x - mean) ** 3 for x in data]) / (std ** 3)
        return skew

    def _calculate_kurtosis(self, data):
        """Calcule l'aplatissement (kurtosis)"""
        if len(data) < 4:
            return 0

        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0

        kurt = np.mean([(x - mean) ** 4 for x in data]) / (std ** 4) - 3
        return kurt

    def _approximate_lz_complexity(self, sequence):
        """Approximation de la complexité de Lempel-Ziv"""
        if len(sequence) < 2:
            return 0

        complexity = 1
        i = 0

        while i < len(sequence) - 1:
            j = i + 1
            while j <= len(sequence):
                substring = sequence[i:j]
                if substring not in [sequence[k:k+len(substring)] for k in range(i)]:
                    break
                j += 1
            complexity += 1
            i = j - 1

        return complexity / len(sequence)

    def _calculate_runs(self, sequence):
        """Calcule les séquences consécutives (runs)"""
        if not sequence:
            return []

        runs = []
        current_run = [sequence[0]]

        for i in range(1, len(sequence)):
            if sequence[i] == sequence[i-1]:
                current_run.append(sequence[i])
            else:
                runs.append(current_run)
                current_run = [sequence[i]]

        runs.append(current_run)
        return runs

    def _calculate_number_correlation(self, draws, num1, num2):
        """Calcule la corrélation entre deux numéros"""
        appearances_1 = []
        appearances_2 = []

        for draw in draws[-200:]:  # Derniers 200 tirages
            draw_numbers = self._get_draw_numbers(draw)
            if draw_numbers:
                appearances_1.append(1 if num1 in draw_numbers else 0)
                appearances_2.append(1 if num2 in draw_numbers else 0)

        if len(appearances_1) > 10:
            correlation = np.corrcoef(appearances_1, appearances_2)[0, 1]
            return correlation if not np.isnan(correlation) else 0

        return 0