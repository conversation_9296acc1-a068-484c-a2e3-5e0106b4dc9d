"""
Script pour forcer des valeurs différentes de scale_pos_weight pour chaque numéro Keno
Cette solution radicale contourne complètement le mécanisme existant.
"""

import os
import sys
import re
import shutil
import random
import numpy as np
import json
from datetime import datetime

def backup_file(file_path):
    """
    Crée une sauvegarde du fichier
    
    Args:
        file_path: Chemin du fichier à sauvegarder
        
    Returns:
        str: Chemin de la sauvegarde
    """
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def find_xgboost_files():
    """
    Trouve tous les fichiers liés à XGBoost
    
    Returns:
        list: Liste des chemins de fichiers
    """
    # Chemins possibles
    possible_files = []
    
    # Parcourir le répertoire courant et ses sous-répertoires
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                # Vérifier si le fichier contient des références à XGBoost
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if 'XGBoost' in content or 'xgboost' in content or 'XGBClassifier' in content:
                            possible_files.append(file_path)
                except:
                    pass
    
    print(f"Fichiers liés à XGBoost trouvés: {len(possible_files)}")
    for file in possible_files:
        print(f"  - {file}")
    
    return possible_files

def generate_random_scale_pos_weights():
    """
    Génère des valeurs aléatoires de scale_pos_weight pour chaque numéro
    
    Returns:
        dict: Dictionnaire des valeurs par numéro
    """
    # Générer des valeurs aléatoires autour de 2.57
    values = {}
    for num in range(1, 71):
        # Générer une valeur entre 1.5 et 3.5
        value = 1.5 + random.random() * 2.0
        values[num] = round(value, 4)
    
    print("Valeurs de scale_pos_weight générées:")
    for num in range(1, 11):  # Afficher les 10 premières valeurs
        print(f"  Numéro {num}: {values[num]}")
    
    return values

def create_scale_pos_weight_file(values):
    """
    Crée un fichier JSON avec les valeurs de scale_pos_weight
    
    Args:
        values: Dictionnaire des valeurs par numéro
        
    Returns:
        str: Chemin du fichier créé
    """
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Chemin du fichier
    file_path = os.path.join('data', 'forced_scale_pos_weight.json')
    
    # Sauvegarder les valeurs
    with open(file_path, 'w') as f:
        json.dump(values, f, indent=2)
    
    print(f"Valeurs de scale_pos_weight sauvegardées dans {file_path}")
    
    return file_path

def create_injection_module(values_file):
    """
    Crée un module Python pour injecter les valeurs de scale_pos_weight
    
    Args:
        values_file: Chemin du fichier de valeurs
        
    Returns:
        str: Chemin du module créé
    """
    module_content = f"""\"\"\"
Module pour injecter des valeurs différentes de scale_pos_weight pour chaque numéro Keno
\"\"\"

import os
import json
import numpy as np
from functools import wraps

# Chemin du fichier de valeurs de scale_pos_weight
SPW_FILE = "{values_file}"

# Charger les valeurs
try:
    with open(SPW_FILE, 'r') as f:
        FORCED_SPW_VALUES = json.load(f)
    print(f"Valeurs de scale_pos_weight chargées depuis {{SPW_FILE}}")
except Exception as e:
    print(f"Erreur lors du chargement des valeurs de scale_pos_weight: {{e}}")
    FORCED_SPW_VALUES = {{}}

# Fonction pour obtenir la valeur forcée
def get_forced_scale_pos_weight(num):
    \"\"\"
    Récupère la valeur forcée de scale_pos_weight pour un numéro
    
    Args:
        num: Numéro Keno
        
    Returns:
        float: Valeur de scale_pos_weight
    \"\"\"
    # Convertir en entier si nécessaire
    try:
        num = int(num)
    except:
        pass
    
    # Récupérer la valeur
    if isinstance(num, int) and 1 <= num <= 70 and str(num) in FORCED_SPW_VALUES:
        return FORCED_SPW_VALUES[str(num)]
    
    # Valeur par défaut
    return 2.57

# Monkey patch pour XGBClassifier
try:
    from xgboost import XGBClassifier
    
    # Sauvegarder la méthode originale
    original_init = XGBClassifier.__init__
    
    @wraps(original_init)
    def patched_init(self, *args, **kwargs):
        # Appeler la méthode originale
        original_init(self, *args, **kwargs)
        
        # Stocker le numéro si disponible
        self._keno_number = kwargs.pop('keno_number', None)
        
        # Forcer scale_pos_weight si le numéro est disponible
        if self._keno_number is not None:
            forced_spw = get_forced_scale_pos_weight(self._keno_number)
            self.scale_pos_weight = forced_spw
            print(f"XGBClassifier: Numéro {{self._keno_number}} - scale_pos_weight forcé à {{forced_spw}}")
    
    # Remplacer la méthode
    XGBClassifier.__init__ = patched_init
    
    print("XGBClassifier patché avec succès")
except Exception as e:
    print(f"Erreur lors du patch de XGBClassifier: {{e}}")

# Fonction pour patcher une fonction existante
def patch_function(func):
    \"\"\"
    Décore une fonction pour injecter des valeurs de scale_pos_weight
    
    Args:
        func: Fonction à décorer
        
    Returns:
        function: Fonction décorée
    \"\"\"
    @wraps(func)
    def wrapper(*args, **kwargs):
        # Récupérer le numéro
        num = kwargs.get('num')
        
        # Si le numéro est disponible, injecter scale_pos_weight
        if num is not None:
            # Vérifier si kwargs contient des paramètres XGBoost
            if 'xgb_config' in kwargs:
                forced_spw = get_forced_scale_pos_weight(num)
                kwargs['xgb_config']['scale_pos_weight'] = forced_spw
                print(f"Fonction {{func.__name__}}: Numéro {{num}} - scale_pos_weight forcé à {{forced_spw}}")
            
            # Vérifier si kwargs contient des paramètres
            if 'params' in kwargs and isinstance(kwargs['params'], dict):
                forced_spw = get_forced_scale_pos_weight(num)
                kwargs['params']['scale_pos_weight'] = forced_spw
                print(f"Fonction {{func.__name__}}: Numéro {{num}} - scale_pos_weight forcé à {{forced_spw}}")
        
        # Appeler la fonction originale
        return func(*args, **kwargs)
    
    return wrapper

# Fonction pour patcher une méthode de classe
def patch_method(cls, method_name):
    \"\"\"
    Patche une méthode de classe pour injecter des valeurs de scale_pos_weight
    
    Args:
        cls: Classe à patcher
        method_name: Nom de la méthode à patcher
    \"\"\"
    if not hasattr(cls, method_name):
        print(f"La classe {{cls.__name__}} n'a pas de méthode {{method_name}}")
        return
    
    # Récupérer la méthode originale
    original_method = getattr(cls, method_name)
    
    @wraps(original_method)
    def patched_method(self, *args, **kwargs):
        # Récupérer le numéro
        num = kwargs.get('num')
        
        # Si le numéro est disponible, injecter scale_pos_weight
        if num is not None:
            # Vérifier si kwargs contient des paramètres XGBoost
            if 'xgb_config' in kwargs:
                forced_spw = get_forced_scale_pos_weight(num)
                kwargs['xgb_config']['scale_pos_weight'] = forced_spw
                print(f"Méthode {{cls.__name__}}.{{method_name}}: Numéro {{num}} - scale_pos_weight forcé à {{forced_spw}}")
            
            # Vérifier si kwargs contient des paramètres
            if 'params' in kwargs and isinstance(kwargs['params'], dict):
                forced_spw = get_forced_scale_pos_weight(num)
                kwargs['params']['scale_pos_weight'] = forced_spw
                print(f"Méthode {{cls.__name__}}.{{method_name}}: Numéro {{num}} - scale_pos_weight forcé à {{forced_spw}}")
            
            # Vérifier si self a des attributs liés à XGBoost
            if hasattr(self, 'xgb_config'):
                forced_spw = get_forced_scale_pos_weight(num)
                self.xgb_config['scale_pos_weight'] = forced_spw
                print(f"Méthode {{cls.__name__}}.{{method_name}}: Numéro {{num}} - scale_pos_weight forcé à {{forced_spw}}")
        
        # Appeler la méthode originale
        return original_method(self, *args, **kwargs)
    
    # Remplacer la méthode
    setattr(cls, method_name, patched_method)
    print(f"Méthode {{cls.__name__}}.{{method_name}} patchée avec succès")

# Fonction pour patcher automatiquement les classes et méthodes
def auto_patch():
    \"\"\"
    Patche automatiquement les classes et méthodes liées à XGBoost
    \"\"\"
    # Patcher les classes et méthodes connues
    try:
        # Essayer de patcher KenoAdvancedAnalyzer
        from keno_advanced_analyzer import KenoAdvancedAnalyzer
        patch_method(KenoAdvancedAnalyzer, 'train_models')
        patch_method(KenoAdvancedAnalyzer, 'train_xgboost_model')
        print("KenoAdvancedAnalyzer patché avec succès")
    except Exception as e:
        print(f"Erreur lors du patch de KenoAdvancedAnalyzer: {{e}}")
    
    try:
        # Essayer de patcher XGBoostOptimizer
        from keno_xgboost_optimizer_simple import XGBoostOptimizer
        patch_method(XGBoostOptimizer, 'train_model')
        patch_method(XGBoostOptimizer, 'optimize_hyperparameters')
        patch_method(XGBoostOptimizer, 'calculate_optimal_scale_pos_weight')
        print("XGBoostOptimizer patché avec succès")
    except Exception as e:
        print(f"Erreur lors du patch de XGBoostOptimizer: {{e}}")

# Appeler auto_patch au chargement du module
auto_patch()

print("Module d'injection de scale_pos_weight chargé avec succès")
"""
    
    # Sauvegarder le module
    module_file = 'force_scale_pos_weight_injector.py'
    
    with open(module_file, 'w') as f:
        f.write(module_content)
    
    print(f"Module d'injection sauvegardé dans {module_file}")
    
    return module_file

def create_launcher_script(injector_module):
    """
    Crée un script de lancement qui injecte le module
    
    Args:
        injector_module: Chemin du module d'injection
        
    Returns:
        str: Chemin du script créé
    """
    launcher_content = f"""\"\"\"
Script de lancement qui injecte des valeurs différentes de scale_pos_weight
\"\"\"

import sys
import importlib.util
import os

# Charger le module d'injection
try:
    spec = importlib.util.spec_from_file_location("force_scale_pos_weight_injector", "{injector_module}")
    injector = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(injector)
    print("Module d'injection chargé avec succès")
except Exception as e:
    print(f"Erreur lors du chargement du module d'injection: {{e}}")
    sys.exit(1)

# Importer le module principal
try:
    # Remplacer par le nom de votre module principal
    import main
    print("Module principal importé avec succès")
except Exception as e:
    print(f"Erreur lors de l'importation du module principal: {{e}}")
    print("Veuillez spécifier le nom de votre module principal dans le script")
    sys.exit(1)

# Exécuter le module principal
if __name__ == "__main__":
    # Appeler la fonction principale
    if hasattr(main, "main"):
        main.main()
    else:
        print("Le module principal n'a pas de fonction main()")
        print("Veuillez spécifier la fonction à appeler dans le script")
"""
    
    # Sauvegarder le script
    launcher_file = 'launch_with_forced_spw.py'
    
    with open(launcher_file, 'w') as f:
        f.write(launcher_content)
    
    print(f"Script de lancement sauvegardé dans {launcher_file}")
    
    return launcher_file

def create_import_script(injector_module):
    """
    Crée un script d'importation pour injecter le module dans un code existant
    
    Args:
        injector_module: Chemin du module d'injection
        
    Returns:
        str: Chemin du script créé
    """
    import_content = f"""\"\"\"
Script pour injecter des valeurs différentes de scale_pos_weight dans un code existant
\"\"\"

# Ajouter cette ligne au début de votre script principal pour injecter les valeurs
import force_scale_pos_weight_injector

# Ensuite, continuez avec votre code normal
# ...

# Exemple d'utilisation avec XGBClassifier
from xgboost import XGBClassifier

# Créer un modèle avec un numéro spécifique
model = XGBClassifier(
    n_estimators=100,
    max_depth=5,
    learning_rate=0.1,
    keno_number=7  # Spécifier le numéro Keno ici
)

# La valeur de scale_pos_weight sera automatiquement injectée
print(f"scale_pos_weight: {{model.scale_pos_weight}}")
"""
    
    # Sauvegarder le script
    import_file = 'example_import_injector.py'
    
    with open(import_file, 'w') as f:
        f.write(import_content)
    
    print(f"Script d'importation sauvegardé dans {import_file}")
    
    return import_file

def create_readme():
    """
    Crée un fichier README avec des instructions
    
    Returns:
        str: Chemin du fichier créé
    """
    readme_content = """# Solution radicale pour le problème de scale_pos_weight identique

Ce package contient une solution radicale pour forcer des valeurs différentes de `scale_pos_weight` pour chaque numéro Keno.

## Fichiers inclus

- `forced_scale_pos_weight.json` : Valeurs de `scale_pos_weight` pour chaque numéro
- `force_scale_pos_weight_injector.py` : Module d'injection qui force les valeurs
- `launch_with_forced_spw.py` : Script de lancement qui injecte le module
- `example_import_injector.py` : Exemple d'importation du module dans un code existant

## Comment utiliser cette solution

### Option 1 : Importer le module d'injection dans votre code

Ajoutez cette ligne au début de votre script principal :

```python
import force_scale_pos_weight_injector
```

Ensuite, continuez avec votre code normal. Les valeurs de `scale_pos_weight` seront automatiquement injectées.

### Option 2 : Utiliser le script de lancement

1. Modifiez le script `launch_with_forced_spw.py` pour spécifier le nom de votre module principal
2. Exécutez le script :

```bash
python launch_with_forced_spw.py
```

### Option 3 : Utiliser XGBClassifier avec un numéro spécifique

```python
from xgboost import XGBClassifier

# Créer un modèle avec un numéro spécifique
model = XGBClassifier(
    n_estimators=100,
    max_depth=5,
    learning_rate=0.1,
    keno_number=7  # Spécifier le numéro Keno ici
)

# La valeur de scale_pos_weight sera automatiquement injectée
print(f"scale_pos_weight: {model.scale_pos_weight}")
```

## Comment vérifier que la solution fonctionne

Après avoir intégré la solution, vous devriez voir des messages comme :

```
XGBClassifier: Numéro 7 - scale_pos_weight forcé à 2.1234
```

Cela indique que la valeur de `scale_pos_weight` a été forcée pour ce numéro.

## Comment modifier les valeurs

Si vous souhaitez modifier les valeurs de `scale_pos_weight`, éditez le fichier `data/forced_scale_pos_weight.json`.
"""
    
    # Sauvegarder le README
    readme_file = 'README_FORCE_SPW.md'
    
    with open(readme_file, 'w') as f:
        f.write(readme_content)
    
    print(f"README sauvegardé dans {readme_file}")
    
    return readme_file

def main():
    """Fonction principale"""
    print("Solution radicale pour forcer des valeurs différentes de scale_pos_weight")
    
    # Trouver les fichiers liés à XGBoost
    find_xgboost_files()
    
    # Générer des valeurs aléatoires
    values = generate_random_scale_pos_weights()
    
    # Créer le fichier de valeurs
    values_file = create_scale_pos_weight_file(values)
    
    # Créer le module d'injection
    injector_module = create_injection_module(values_file)
    
    # Créer le script de lancement
    launcher_file = create_launcher_script(injector_module)
    
    # Créer le script d'importation
    import_file = create_import_script(injector_module)
    
    # Créer le README
    readme_file = create_readme()
    
    print("\nSolution radicale créée avec succès!")
    print("Pour l'utiliser, suivez les instructions dans le fichier README_FORCE_SPW.md")

if __name__ == "__main__":
    main()
