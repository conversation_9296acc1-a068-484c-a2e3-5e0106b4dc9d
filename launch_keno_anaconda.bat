@echo off
REM Script de lancement pour l'application Keno avec Anaconda

echo === Lanceur d'application Keno (Anaconda) ===

REM Changer vers le répertoire du script
cd /d "%~dp0"

echo Répertoire de travail: %CD%

REM Activer l'environnement Anaconda de base
echo Activation de l'environnement Anaconda...
call conda activate base

REM Vérifier que Python est disponible
python --version
if %errorlevel% neq 0 (
    echo ERREUR: Python non disponible après activation d'Anaconda
    pause
    exit /b 1
)

REM Lancer l'application
echo Lancement de l'application Keno...
python main.py

echo Application terminée avec le code: %errorlevel%
pause
