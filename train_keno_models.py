#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script pour entraîner les modèles de prédiction Keno
Ce script simplifie l'utilisation de keno_ml_trainer.py
"""

import os
import sys
import argparse
from keno_ml_trainer import KenoMLTrainer

def parse_arguments():
    """Parse les arguments de ligne de commande"""
    parser = argparse.ArgumentParser(description="Entraîne les modèles de prédiction Keno")
    
    parser.add_argument("database", help="Chemin vers la base de données Keno (.keno)")
    parser.add_argument("--steps", type=int, default=5, help="Nombre d'étapes d'entraînement (défaut: 5)")
    parser.add_argument("--output", default="ml_models", help="Répertoire de sortie pour les modèles (défaut: ml_models)")
    parser.add_argument("--predict", type=int, default=10, help="Nombre de numéros à prédire (défaut: 10)")
    parser.add_argument("--no-plot", action="store_true", help="Ne pas afficher les graphiques")
    
    return parser.parse_args()

def main():
    """Fonction principale"""
    try:
        args = parse_arguments()
        
        if not os.path.exists(args.database):
            print(f"Erreur: Le fichier {args.database} n'existe pas")
            return 1
            
        print(f"=== Entraînement des modèles Keno ===")
        print(f"Base de données: {args.database}")
        print(f"Étapes d'entraînement: {args.steps}")
        print(f"Répertoire de sortie: {args.output}")
        print(f"Nombre de numéros à prédire: {args.predict}")
        print("=" * 40)
        
        # Créer le répertoire de sortie s'il n'existe pas
        os.makedirs(args.output, exist_ok=True)
        
        trainer = KenoMLTrainer(args.database, args.output)
        
        success = trainer.train_incremental(steps=args.steps)
        if not success:
            print("Erreur lors de l'entraînement des modèles")
            return 1
        
        if not args.no_plot:
            trainer.plot_training_progress()
            
        return 0
        
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

