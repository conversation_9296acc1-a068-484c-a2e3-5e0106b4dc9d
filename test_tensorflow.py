import os
import sys

# Désactiver les messages de log verbeux de TensorFlow avant l'import
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR
# Désactiver les opérations oneDNN personnalisées
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

print("Tentative d'importation de TensorFlow...")

try:
    import tensorflow as tf
    print(f"TensorFlow importé avec succès (version {tf.__version__})")
    
    # Vérifier si TensorFlow est correctement initialisé
    try:
        # Tester une opération simple pour vérifier que TensorFlow fonctionne
        test_tensor = tf.constant([1, 2, 3])
        test_result = tf.reduce_sum(test_tensor).numpy()
        print(f"Test TensorFlow réussi: {test_result}")
    except Exception as tf_test_error:
        print(f"TensorFlow importé mais non fonctionnel: {tf_test_error}")
except ImportError as import_error:
    print(f"TensorFlow n'est pas disponible: {import_error}")
except Exception as general_error:
    print(f"Erreur lors de l'initialisation de TensorFlow: {general_error}")

print("Test terminé.")
