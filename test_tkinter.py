import tkinter as tk
from tkinter import messagebox

def main():
    try:
        # Créer une fenêtre Tkinter
        root = tk.Tk()
        root.title("Test Tkinter")
        root.geometry("300x200")
        
        # Ajouter un label
        label = tk.Label(root, text="Test Tkinter", font=("Helvetica", 16))
        label.pack(pady=20)
        
        # Ajouter un bouton pour fermer l'application
        def close_app():
            root.destroy()
            print("Application fermée par l'utilisateur")
        
        button = tk.Button(root, text="Fermer", command=close_app)
        button.pack(pady=10)
        
        # Configurer le gestionnaire de fermeture
        root.protocol("WM_DELETE_WINDOW", close_app)
        
        # Démarrer la boucle principale
        print("Démarrage de la boucle principale Tkinter...")
        root.mainloop()
        print("Boucle principale Tkinter terminée")
        
        return 0
    except Exception as e:
        print(f"Erreur: {e}")
        return 1

if __name__ == "__main__":
    import sys
    result = main()
    print(f"Code de sortie: {result}")
    sys.exit(result)
