#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de test pour vérifier que le deep learning fonctionne correctement
"""

import os
import sys
import time

# Ajouter le répertoire parent au chemin de recherche des modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Importer les modules nécessaires
try:
    from keno_deep_learning import Keno<PERSON>eep<PERSON>earning
    from keno_data import KenoDataManager
    from keno_advanced_analyzer import KenoAdvancedAnalyzer
    
    # Vérifier si TensorFlow est disponible
    try:
        import tensorflow as tf
        print(f"TensorFlow version: {tf.__version__}")
        print(f"Keras version: {tf.keras.__version__}")
        
        # Vérifier les dispositifs disponibles
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"GPU disponible: {len(gpus)} dispositif(s)")
            for gpu in gpus:
                print(f"  - {gpu.name}")
        else:
            print("Aucun GPU détecté")
        
        # Vérifier les autres dispositifs
        devices = tf.config.list_physical_devices()
        print(f"Tous les dispositifs disponibles: {len(devices)}")
        for device in devices:
            print(f"  - {device.name}")
        
        # Vérifier si CUDA est disponible
        if tf.test.is_built_with_cuda():
            print("TensorFlow est compilé avec CUDA")
        else:
            print("TensorFlow n'est pas compilé avec CUDA")
        
        # Vérifier si le GPU est disponible pour TensorFlow
        if tf.test.is_gpu_available():
            print("GPU disponible pour TensorFlow")
        else:
            print("GPU non disponible pour TensorFlow")
    except ImportError:
        print("TensorFlow n'est pas installé")
    
    # Fonction principale de test
    def test_deep_learning():
        print("\n=== Test du module de deep learning ===\n")
        
        # Créer un gestionnaire de données
        data_manager = KenoDataManager()
        
        # Charger les données
        data_file = "data/keno_data.csv"
        if os.path.exists(data_file):
            print(f"Chargement des données depuis {data_file}...")
            data_manager.load_from_csv(data_file)
        else:
            print(f"Fichier {data_file} introuvable. Utilisation de données de test...")
            # Créer des données de test
            import random
            from datetime import datetime, timedelta
            
            # Générer 1000 tirages aléatoires
            base_date = datetime.now() - timedelta(days=1000)
            for i in range(1000):
                draw_date = base_date + timedelta(days=i)
                draw_numbers = random.sample(range(1, 71), 20)
                data_manager.add_draw(draw_date, draw_numbers)
            
            print(f"Données de test générées: {len(data_manager.draws)} tirages")
        
        # Créer un analyseur avancé
        analyzer = KenoAdvancedAnalyzer(data_manager)
        
        # Vérifier si le deep learning est disponible
        if not hasattr(analyzer, 'deep_learning') or not analyzer.deep_learning:
            print("Le module de deep learning n'est pas disponible")
            return
        
        # Préparer les données
        print("Préparation des données...")
        analyzer.prepare_data()
        
        # Tester la création d'un modèle simple
        print("\nTest de création d'un modèle simple...")
        X = analyzer.df[['day_of_week', 'hour', 'is_weekend', 'is_afternoon']].values
        y = analyzer.df['has_1'].values
        
        start_time = time.time()
        model_simple = analyzer.deep_learning.train_model(
            X, y, model_type='simple', test_size=0.2, random_state=42,
            batch_size=32, epochs=5, early_stopping=True, verbose=1
        )
        end_time = time.time()
        
        if model_simple:
            print(f"Modèle simple créé en {end_time - start_time:.2f} secondes")
            print(f"Précision: {model_simple['accuracy']:.4f}")
            print(f"F1-score: {model_simple['f1']:.4f}")
        else:
            print("Erreur lors de la création du modèle simple")
        
        # Tester la sauvegarde et le chargement d'un modèle
        if model_simple:
            print("\nTest de sauvegarde d'un modèle...")
            save_path = "data/test_dl_model.pkl"
            save_success = analyzer.deep_learning.save_model(model_simple, save_path)
            
            if save_success:
                print(f"Modèle sauvegardé dans {save_path}")
                
                print("\nTest de chargement d'un modèle...")
                loaded_model = analyzer.deep_learning.load_model(save_path)
                
                if loaded_model:
                    print("Modèle chargé avec succès")
                    print(f"Précision: {loaded_model['accuracy']:.4f}")
                    
                    # Tester la prédiction
                    print("\nTest de prédiction...")
                    X_test = X[:5]
                    predictions = loaded_model['model'].predict_proba(X_test)
                    
                    print("Prédictions:")
                    for i, pred in enumerate(predictions):
                        print(f"  Exemple {i+1}: {pred[1]:.4f}")
                else:
                    print("Erreur lors du chargement du modèle")
            else:
                print("Erreur lors de la sauvegarde du modèle")
        
        print("\n=== Test terminé ===\n")
    
    # Exécuter le test si le script est exécuté directement
    if __name__ == "__main__":
        test_deep_learning()

except ImportError as e:
    print(f"Erreur d'importation: {e}")
except Exception as e:
    import traceback
    print(f"Erreur: {e}")
    traceback.print_exc()
