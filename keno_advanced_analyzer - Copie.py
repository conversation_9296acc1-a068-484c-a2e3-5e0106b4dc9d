import numpy as np
import pandas as pd
import sys
import os
import warnings
from datetime import datetime, timedelta
from collections import Counter, defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import joblib

class KenoAdvancedAnalyzer:
    """Classe pour l'analyse avancée des données Keno et la prédiction par apprentissage automatique"""

    def __init__(self, data_manager):
        """Initialise l'analyseur avancé avec un gestionnaire de données"""
        self.data_manager = data_manager
        self.max_number = data_manager.max_number
        self.numbers_per_draw = data_manager.numbers_per_draw
        self.models = {}
        self.patterns = {}
        self.correlations = None
        self.series = None
        self.df = None
        self.n_jobs = -1  # Utiliser tous les cœurs CPU par défaut
        self.cuda_available = False  # Détection automatique du GPU
        self.npu_available = False  # Détection automatique du NPU
        self.hardware_acceleration = 'auto'  # 'auto', 'cpu', 'gpu', 'npu'

    def set_cpu_cores(self, n_jobs):
        """Définit le nombre de cœurs CPU à utiliser pour l'entraînement

        Args:
            n_jobs (int): Nombre de cœurs à utiliser. -1 pour tous les cœurs disponibles.

        Returns:
            bool: True si la configuration a réussi
        """
        self.n_jobs = n_jobs
        print(f"Nombre de cœurs CPU configuré à {n_jobs} ({'-1 = tous' if n_jobs == -1 else n_jobs} cœurs)")
        return True

    def set_hardware_acceleration(self, mode='auto'):
        """Configure le type d'accélération matérielle à utiliser

        Args:
            mode (str): Type d'accélération - 'auto', 'cpu', 'gpu', 'npu'

        Returns:
            dict: Informations sur les accélérateurs disponibles
        """
        # Réinitialiser les drapeaux
        self.cuda_available = False
        self.npu_available = False
        self.hardware_acceleration = 'cpu'  # Par défaut

        result = {
            'cpu': True,
            'gpu': False,
            'npu': False,
            'active': 'cpu'
        }

        # Si mode est 'cpu', on s'arrête là
        if mode.lower() == 'cpu':
            print("Mode CPU forcé, aucune accélération matérielle ne sera utilisée")
            self.hardware_acceleration = 'cpu'
            result['active'] = 'cpu'
            return result

        # Vérifier la disponibilité du GPU
        try:
            # Essayer d'importer TensorFlow
            import tensorflow as tf
            gpus = tf.config.list_physical_devices('GPU')
            if gpus:
                print(f"GPU détecté: {len(gpus)} dispositif(s)")
                self.cuda_available = True
                result['gpu'] = True

                # Configurer TensorFlow pour utiliser le GPU
                for gpu in gpus:
                    try:
                        tf.config.experimental.set_memory_growth(gpu, True)
                    except:
                        pass
            else:
                # Essayer avec nvidia-smi
                import subprocess
                try:
                    result_cmd = subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                    if result_cmd.returncode == 0:
                        print("GPU NVIDIA détecté via nvidia-smi")
                        self.cuda_available = True
                        result['gpu'] = True
                except:
                    pass
        except Exception as e:
            print(f"Erreur lors de la vérification du GPU: {e}")

        # Vérifier la disponibilité du NPU
        try:
            # Vérifier si TensorFlow peut détecter des accélérateurs NPU/TPU
            import tensorflow as tf

            # Vérifier les dispositifs disponibles
            devices = tf.config.list_physical_devices()
            for device in devices:
                if 'NPU' in device.name.upper() or 'TPU' in device.name.upper() or 'NEURAL' in device.name.upper():
                    print(f"NPU/TPU détecté: {device.name}")
                    self.npu_available = True
                    result['npu'] = True
                    break

            # Vérifier si OpenVINO est disponible (pour processeurs Intel)
            try:
                import openvino as ov
                print(f"OpenVINO détecté (version {ov.__version__})")

                # Créer un Core OpenVINO pour accéder aux dispositifs
                core = ov.Core()
                available_devices = core.available_devices
                print(f"Dispositifs OpenVINO disponibles: {available_devices}")

                # Vérifier les dispositifs d'accélération
                has_accelerator = False

                for device in available_devices:
                    try:
                        device_name = device
                        full_name = core.get_property(device_name, "FULL_DEVICE_NAME")
                        print(f"Dispositif OpenVINO: {device_name} - {full_name}")

                        if device != 'CPU':
                            has_accelerator = True
                            print(f"Accélérateur OpenVINO détecté: {device_name}")
                        elif 'VNNI' in full_name or 'AVX' in full_name or 'SSE' in full_name:
                            has_accelerator = True
                            print(f"CPU Intel avec accélération IA détecté: {full_name}")

                        try:
                            capabilities = core.get_property(device_name, "OPTIMIZATION_CAPABILITIES")
                            print(f"Capacités d'optimisation: {capabilities}")

                            if device == 'CPU' and capabilities:
                                if any(cap in str(capabilities) for cap in ['VNNI', 'AVX', 'FP16', 'INT8', 'BF16']):
                                    has_accelerator = True
                                    print(f"CPU avec instructions d'accélération IA: {capabilities}")
                        except Exception as e:
                            print(f"Impossible de lire les capacités: {e}")
                    except Exception as e:
                        print(f"Erreur lors de l'analyse du dispositif {device}: {e}")

                if 'MYRIAD' in available_devices:
                    print("Intel Neural Compute Stick détecté")
                    has_accelerator = True

                if 'GPU' in available_devices:
                    print("Intel GPU détecté via OpenVINO")
                    has_accelerator = True

                if has_accelerator:
                    self.npu_available = True
                    result['npu'] = True
                    print("Accélération NPU Intel disponible via OpenVINO")
            except ImportError:
                print("OpenVINO n'est pas installé. Utilisez 'pip install openvino' pour l'installer.")
            except Exception as e:
                print(f"Erreur lors de la vérification d'OpenVINO: {e}")

            # Vérifier si Apple Neural Engine est disponible (macOS)
            try:
                import platform
                if platform.system() == 'Darwin' and hasattr(tf, 'config') and hasattr(tf.config, 'list_physical_devices'):
                    if 'CPU' in [d.device_type for d in tf.config.list_physical_devices()]:
                        # Sur macOS récent avec Apple Silicon, le NPU est intégré
                        import subprocess
                        result_cmd = subprocess.run(['sysctl', '-n', 'machdep.cpu.brand_string'],
                                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                        if 'Apple' in result_cmd.stdout:
                            print("Apple Neural Engine détecté")
                            self.npu_available = True
                            result['npu'] = True
            except:
                pass
        except Exception as e:
            print(f"Erreur lors de la vérification du NPU: {e}")

        # Déterminer le mode d'accélération à utiliser
        if mode.lower() == 'auto':
            if self.npu_available and result['npu']:
                self.hardware_acceleration = 'npu'
                result['active'] = 'npu'
                print("Mode NPU activé automatiquement")
            elif self.cuda_available and result['gpu']:
                self.hardware_acceleration = 'gpu'
                result['active'] = 'gpu'
                print("Mode GPU activé automatiquement")
            else:
                self.hardware_acceleration = 'cpu'
                result['active'] = 'cpu'
                print("Mode CPU activé automatiquement (aucun accélérateur détecté)")
        elif mode.lower() == 'gpu' and result['gpu']:
            self.hardware_acceleration = 'gpu'
            result['active'] = 'gpu'
            print("Mode GPU activé manuellement")
        elif mode.lower() == 'npu' and result['npu']:
            self.hardware_acceleration = 'npu'
            result['active'] = 'npu'
            print("Mode NPU activé manuellement")
        else:
            self.hardware_acceleration = 'cpu'
            result['active'] = 'cpu'
            print(f"Mode {mode} non disponible, utilisation du CPU")

        return result

    def set_gpu_usage(self, enabled=True):
        """Active ou désactive l'utilisation du GPU (méthode maintenue pour compatibilité)

        Args:
            enabled (bool): True pour activer le GPU, False pour le désactiver

        Returns:
            bool: True si la configuration a réussi, False si le GPU n'est pas disponible
        """
        if enabled:
            result = self.set_hardware_acceleration('gpu')
            return result['active'] == 'gpu'
        else:
            self.set_hardware_acceleration('cpu')
            return True

    def prepare_data(self):
        """Prépare les données pour l'analyse avancée"""
        if not self.data_manager.draws:
            return None

        # Convertir les tirages en DataFrame pandas
        data = []
        for draw in self.data_manager.draws:
            row = {
                'date': draw.draw_date,
                'id': draw.draw_id,
                'day_of_week': draw.draw_date.weekday(),
                'hour': draw.draw_date.hour,
                'is_weekend': 1 if draw.draw_date.weekday() >= 5 else 0,
                'is_afternoon': 1 if draw.draw_date.hour >= 12 else 0
            }

            # Ajouter les numéros tirés
            for i, num in enumerate(sorted(draw.draw_numbers)):
                row[f'num_{i+1}'] = num

            # Ajouter des indicateurs pour chaque numéro possible
            for i in range(1, self.max_number + 1):
                row[f'has_{i}'] = 1 if i in draw.draw_numbers else 0

            data.append(row)

        # Créer le DataFrame
        df = pd.DataFrame(data)
        df = df.sort_values('date')

        # Ajouter des caractéristiques temporelles
        df['day'] = df['date'].dt.day
        df['month'] = df['date'].dt.month
        df['year'] = df['date'].dt.year
        df['day_of_year'] = df['date'].dt.dayofyear

        # Stocker le DataFrame
        self.df = df

        return df

    def analyze_patterns(self):
        """Analyse les motifs dans les tirages en utilisant le traitement parallèle si disponible"""
        import time
        from collections import Counter

        start_time = time.time()

        if self.df is None:
            self.prepare_data()

        if self.df is None or self.df.empty:
            return {}

        print(f"Analyse des motifs sur {len(self.df)} tirages...")
        patterns = {}

        # Essayer d'utiliser le module de traitement parallèle si disponible
        try:
            import parallel_processing
            print("Utilisation du traitement parallèle pour l'analyse des motifs")

            # Déterminer le nombre optimal de workers
            n_workers = self.n_jobs if self.n_jobs > 0 else parallel_processing.get_optimal_workers()
            print(f"Utilisation de {n_workers} workers pour l'analyse parallèle")

            # Convertir les données DataFrame en objets KenoDrawData pour le traitement parallèle
            from keno_data import KenoDrawData
            draws = []
            for _, row in self.df.iterrows():
                nums = [row[f'num_{i+1}'] for i in range(self.numbers_per_draw) if f'num_{i+1}' in row]
                if nums:
                    draw = KenoDrawData()
                    draw.draw_numbers = nums
                    draws.append(draw)

            # Analyser les motifs en parallèle
            if draws:
                print(f"Traitement parallèle de {len(draws)} tirages...")
                pattern_results = parallel_processing.analyze_patterns_parallel(draws, max_workers=n_workers)

                # Extraire les résultats
                if 'common_pairs' in pattern_results:
                    patterns['common_pairs'] = dict(pattern_results['common_pairs'])
                if 'sequence_counts' in pattern_results:
                    patterns['sequence_counts'] = dict(pattern_results['sequence_counts'])
        except Exception as e:
            print(f"Erreur lors de l'importation du module parallèle: {e}")
            print("Utilisation du traitement séquentiel")

        # Analyser la distribution pairs/impairs (toujours séquentiel car rapide)
        print("Analyse de la distribution pairs/impairs...")
        even_odd_counts = []
        for _, row in self.df.iterrows():
            nums = [row[f'num_{i+1}'] for i in range(self.numbers_per_draw) if f'num_{i+1}' in row]
            if nums:
                even_count = sum(1 for num in nums if num % 2 == 0)
                odd_count = len(nums) - even_count
                even_odd_counts.append((even_count, odd_count))

        patterns['even_odd_distribution'] = Counter(even_odd_counts)

        # Analyser la distribution haut/bas
        print("Analyse de la distribution haut/bas...")
        high_low_counts = []
        mid_point = self.max_number // 2
        for _, row in self.df.iterrows():
            nums = [row[f'num_{i+1}'] for i in range(self.numbers_per_draw) if f'num_{i+1}' in row]
            if nums:
                high_count = sum(1 for num in nums if num > mid_point)
                low_count = len(nums) - high_count
                high_low_counts.append((high_count, low_count))

        patterns['high_low_distribution'] = Counter(high_low_counts)

        # Analyser les écarts entre les numéros consécutifs (si pas déjà fait en parallèle)
        if 'consecutive_gaps' not in patterns:
            print("Analyse des écarts entre numéros consécutifs...")
            gaps = []
            for _, row in self.df.iterrows():
                nums = sorted([row[f'num_{i+1}'] for i in range(self.numbers_per_draw) if f'num_{i+1}' in row])
                if len(nums) > 1:
                    draw_gaps = [nums[i+1] - nums[i] for i in range(len(nums)-1)]
                    gaps.extend(draw_gaps)

            patterns['consecutive_gaps'] = Counter(gaps)

        # Analyser les sommes des tirages
        print("Analyse des sommes de tirage...")
        sums = []
        for _, row in self.df.iterrows():
            nums = [row[f'num_{i+1}'] for i in range(self.numbers_per_draw) if f'num_{i+1}' in row]
            if nums:
                sums.append(sum(nums))

        if sums:
            patterns['draw_sums'] = {
                'min': min(sums),
                'max': max(sums),
                'mean': np.mean(sums),
                'median': np.median(sums),
                'distribution': np.histogram(sums, bins=10)
            }

        # Stocker les motifs
        self.patterns = patterns

        end_time = time.time()
        print(f"Analyse des motifs terminée en {end_time - start_time:.2f} secondes")

        return patterns

    def analyze_correlations(self):
        """Analyse les corrélations entre les numéros"""
        if self.df is None:
            self.prepare_data()

        if self.df is None or self.df.empty:
            return None

        # Extraire les colonnes 'has_X' qui indiquent si le numéro X est présent
        has_columns = [col for col in self.df.columns if col.startswith('has_')]
        correlation_matrix = self.df[has_columns].corr()

        # Stocker la matrice de corrélation
        self.correlations = correlation_matrix

        return correlation_matrix

    def analyze_series(self):
        """Analyse les séries et les séquences dans les tirages en utilisant le traitement parallèle si disponible"""
        import time

        start_time = time.time()

        if not self.data_manager.draws:
            return None

        print(f"Analyse des séries pour {len(self.data_manager.draws)} tirages...")
        series_data = {}

        # Utiliser l'ensemble des données disponibles
        total_draws = len(self.data_manager.draws)
        print(f"Utilisation de l'ensemble des {total_draws} tirages disponibles pour l'analyse")
        sample_draws = self.data_manager.draws

        # Essayer d'utiliser le module de traitement parallèle si disponible
        try:
            import parallel_processing
            print("Utilisation du traitement parallèle pour l'analyse des séries")

            # Déterminer le nombre optimal de workers
            n_workers = self.n_jobs if self.n_jobs > 0 else parallel_processing.get_optimal_workers()
            print(f"Utilisation de {n_workers} workers pour l'analyse parallèle")

            # Analyser les séries en parallèle
            series_results = parallel_processing.analyze_series_parallel(sample_draws, max_workers=n_workers)

            # Traiter les résultats
            if series_results:
                # Extraire les informations sur les séries temporelles
                follow_patterns = {}
                for num, data in series_results.items():
                    if 'appearances' in data and data['appearances'] > 0:
                        follow_patterns[num] = {
                            'count': data['appearances'],
                            'repeat_rate': data.get('frequency', 0)
                        }

                series_data['follow_patterns'] = follow_patterns
                print(f"Analyse parallèle terminée: {len(follow_patterns)} motifs de suivi identifiés")

                # Continuer avec l'analyse séquentielle pour les autres aspects
                print("Complément d'analyse séquentielle...")
        except (ImportError, Exception) as e:
            print(f"Erreur lors de l'importation du module parallèle: {e}")
            print("Utilisation du traitement séquentiel")

        # Si l'analyse parallèle n'a pas été effectuée ou est incomplète, continuer avec l'analyse séquentielle
        if 'follow_patterns' not in series_data:
            # Utiliser tqdm si disponible
            try:
                from tqdm import tqdm
                use_tqdm = True
            except ImportError:
                use_tqdm = False

            # 1. Analyser les séquences de numéros qui se suivent
            print("1/3: Analyse des séquences consécutives...")
            sequences = []
            sequence_counts = []

            iterator = tqdm(sample_draws, desc="Analyse des séquences", unit="tirage") if use_tqdm else sample_draws
            for draw in iterator:
                nums = sorted(draw.draw_numbers)
                seq_count = 0
                current_seq = []

                for i in range(len(nums) - 1):
                    if nums[i+1] == nums[i] + 1:
                        if not current_seq:
                            current_seq = [nums[i], nums[i+1]]
                        else:
                            current_seq.append(nums[i+1])
                    else:
                        if current_seq:
                            sequences.append(tuple(current_seq))
                            seq_count += 1
                            current_seq = []

                if current_seq:
                    sequences.append(tuple(current_seq))
                    seq_count += 1

                sequence_counts.append(seq_count)

            series_data['sequence_counts'] = Counter(sequence_counts)
            series_data['common_sequences'] = Counter(sequences).most_common(20)

            # 2. Analyser les numéros qui apparaissent souvent ensemble
            print("2/3: Analyse des paires fréquentes...")
            pairs_counter = Counter()

            iterator = tqdm(sample_draws, desc="Analyse des paires", unit="tirage") if use_tqdm else sample_draws
            for draw in iterator:
                nums = draw.draw_numbers
                # Utiliser une compréhension de liste pour accélérer
                draw_pairs = [(min(nums[i], nums[j]), max(nums[i], nums[j]))
                             for i in range(len(nums))
                             for j in range(i+1, len(nums))]
                pairs_counter.update(draw_pairs)

            series_data['common_pairs'] = pairs_counter.most_common(30)

            # 3. Analyser les numéros qui se suivent d'un tirage à l'autre
            print("3/3: Analyse des motifs de suivi...")
            follow_patterns = defaultdict(list)

            # Pré-calculer les ensembles pour accélérer
            draw_sets = [set(draw.draw_numbers) for draw in self.data_manager.draws]

            iterator = tqdm(range(len(draw_sets) - 1), desc="Analyse des motifs de suivi", unit="tirage") if use_tqdm else range(len(draw_sets) - 1)
            for i in iterator:
                current_draw = draw_sets[i]
                next_draw = draw_sets[i+1]

                for num in current_draw:
                    follow_patterns[num].append(1 if num in next_draw else 0)

            # Calculer les taux de répétition en une seule fois
            series_data['follow_patterns'] = {
                num: {
                    'count': len(patterns),
                    'repeat_rate': sum(patterns) / len(patterns) if patterns else 0
                }
                for num, patterns in follow_patterns.items()
            }

        print("Analyse des séries terminée")

        # Stocker les données de séries
        self.series = series_data

        # Afficher les statistiques de performance
        end_time = time.time()
        duration = end_time - start_time
        print(f"Analyse des séries terminée en {duration:.2f} secondes")

        # Afficher les statistiques des résultats
        if 'common_sequences' in series_data:
            print(f"Séquences identifiées: {len(series_data.get('common_sequences', []))}")
        if 'common_pairs' in series_data:
            print(f"Paires fréquentes: {len(series_data.get('common_pairs', []))}")
        if 'follow_patterns' in series_data:
            print(f"Motifs de suivi: {len(series_data.get('follow_patterns', {}))}")

        return series_data

    def train_models(self, test_size=0.2, random_state=42):
        """Entraîne des modèles d'apprentissage automatique pour la prédiction en utilisant le traitement parallèle si disponible

        Args:
            test_size: Proportion des données à utiliser pour le test (défaut: 0.2)
            random_state: Graine aléatoire pour la reproductibilité (défaut: 42)

        Returns:
            bool: True si l'entraînement a réussi, False sinon
        """
        import time
        start_time = time.time()
        if self.df is None:
            self.prepare_data()

        if self.df is None or self.df.empty or len(self.df) < 50:
            print("Pas assez de données pour entraîner les modèles (minimum 50 tirages requis)")
            return False

        # Initialiser les variables
        use_parallel = False
        n_workers = 1
        use_gpu = False

        # Essayer d'utiliser le module de traitement parallèle si disponible
        try:
            import parallel_processing
            print("Utilisation du traitement parallèle pour l'entraînement des modèles")

            # Déterminer le nombre optimal de workers
            n_workers = self.n_jobs if hasattr(self, 'n_jobs') and self.n_jobs > 0 else parallel_processing.get_optimal_workers()
            print(f"Utilisation de {n_workers} workers pour l'entraînement parallèle")

            # Vérifier si l'accélération GPU est disponible
            if hasattr(self, 'hardware_acceleration'):
                use_gpu = self.hardware_acceleration in ['auto', 'gpu'] and parallel_processing.is_cuda_available()
                if use_gpu:
                    print("Accélération GPU activée pour l'entraînement des modèles")

            use_parallel = True
        except (ImportError, Exception) as e:
            print(f"Erreur lors de l'importation du module parallèle: {e}")
            print("Utilisation du traitement séquentiel")

        print(f"Entraînement des modèles ML avec {len(self.df)} tirages...")

        # Supprimer les avertissements de TensorFlow
        import os
        os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR

        # Détecter et configurer l'accélération matérielle
        os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'true'  # Permet à TensorFlow d'allouer la mémoire progressivement

        # Détecter automatiquement les accélérateurs matériels disponibles
        if self.hardware_acceleration == 'auto':
            accel_info = self.set_hardware_acceleration('auto')
            print(f"Accélérateurs détectés: {', '.join([k for k, v in accel_info.items() if v and k != 'active'])}")
            print(f"Mode actif: {accel_info['active'].upper()}")

        # Utiliser le GPU si disponible
        try:
            import tensorflow as tf
            # Désactiver les messages d'avertissement de TensorFlow
            import logging
            logging.getLogger('tensorflow').setLevel(logging.ERROR)

            # Vérifier si CUDA est disponible
            print("Vérification de la disponibilité de CUDA...")
            try:
                if hasattr(tf, 'test') and hasattr(tf.test, 'is_built_with_cuda'):
                    cuda_available = tf.test.is_built_with_cuda()
                    if cuda_available:
                        print("CUDA est disponible pour TensorFlow")
                        self.cuda_available = True
                    else:
                        print("CUDA n'est pas disponible pour TensorFlow")
                else:
                    print("Méthode tf.test.is_built_with_cuda non disponible")
                    # Vérifier si des GPU sont disponibles
                    if hasattr(tf, 'config') and hasattr(tf.config, 'list_physical_devices'):
                        gpus = tf.config.list_physical_devices('GPU')
                        if gpus:
                            print(f"GPU détecté via list_physical_devices: {len(gpus)} dispositif(s)")
                            self.cuda_available = True
            except Exception as e:
                print(f"Erreur lors de la vérification de CUDA: {e}")

            # Vérifier les GPU disponibles
            gpus = tf.config.list_physical_devices('GPU')
            if gpus:
                print(f"GPU détecté: {len(gpus)} dispositif(s)")
                for i, gpu in enumerate(gpus):
                    print(f"  GPU {i}: {gpu.name}")
                    # Configurer TensorFlow pour utiliser le GPU
                    try:
                        tf.config.experimental.set_memory_growth(gpu, True)
                    except:
                        # Si l'allocation dynamique de mémoire échoue, essayer une allocation fixe
                        tf.config.set_logical_device_configuration(
                            gpu,
                            [tf.config.LogicalDeviceConfiguration(memory_limit=4096)]  # Allouer 4GB
                        )
                print("Utilisation du GPU pour l'accélération des calculs")
                self.cuda_available = True

                # Afficher des informations sur le GPU
                try:
                    from tensorflow.python.client import device_lib
                    devices = device_lib.list_local_devices()
                    for device in devices:
                        if device.device_type == 'GPU':
                            print(f"  Détails du GPU: {device.physical_device_desc}")
                except Exception as e:
                    print(f"Impossible d'obtenir les détails du GPU: {e}")
            else:
                print("Aucun GPU détecté par TensorFlow, vérification de CUDA...")
                # Vérifier si CUDA est disponible via une autre méthode
                try:
                    import subprocess
                    result = subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                    if result.returncode == 0:
                        print("GPU NVIDIA détecté via nvidia-smi, mais non reconnu par TensorFlow:")
                        print(result.stdout)
                        print("Essai de configuration manuelle de CUDA...")
                        os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # Utiliser le premier GPU
                        self.cuda_available = True
                    else:
                        print("Aucun GPU NVIDIA détecté, utilisation du CPU")
                except:
                    print("Impossible de vérifier la présence de GPU NVIDIA, utilisation du CPU")

            # Import numpy ici pour éviter les erreurs
            import numpy as np

            # Vérifier si XGBoost peut utiliser le GPU
            try:
                import xgboost as xgb
                # Tester si XGBoost peut utiliser le GPU
                try:
                    # Créer un petit DMatrix pour tester
                    data = np.random.rand(10, 10)
                    label = np.random.randint(2, size=10)
                    dtrain = xgb.DMatrix(data, label=label)

                    # Essayer de créer un modèle avec GPU (nouvelle méthode recommandée)
                    param = {'tree_method': 'hist', 'device': 'cuda'}
                    bst = xgb.train(param, dtrain, num_boost_round=1)
                    print("XGBoost peut utiliser le GPU (CUDA)")
                    self.cuda_available = True
                except Exception as e:
                    print(f"XGBoost ne peut pas utiliser le GPU: {e}")
            except Exception as e:
                print(f"Erreur lors de la vérification de XGBoost GPU: {e}")

            # Vérifier si LightGBM peut utiliser le GPU
            try:
                import lightgbm as lgb
                # Tester si LightGBM peut utiliser le GPU
                try:
                    # Créer un petit jeu de données pour tester
                    data = np.random.rand(10, 10)
                    label = np.random.randint(2, size=10)
                    lgb_train = lgb.Dataset(data, label=label)

                    # Essayer de créer un modèle avec GPU
                    param = {'device': 'gpu', 'gpu_platform_id': 0, 'gpu_device_id': 0}
                    bst = lgb.train(param, lgb_train, num_boost_round=1)
                    print("LightGBM peut utiliser le GPU")
                    self.cuda_available = True
                except Exception as e:
                    print(f"LightGBM ne peut pas utiliser le GPU: {e}")
            except Exception as e:
                print(f"Erreur lors de la vérification de LightGBM GPU: {e}")

        except Exception as e:
            print(f"Impossible d'initialiser le GPU: {e}")
            print("Utilisation du CPU pour l'entraînement")

        # Créer des caractéristiques pour chaque numéro
        features = {}
        targets = {}

        # Ajouter des caractéristiques temporelles avancées
        self.df['day'] = self.df['date'].dt.day
        self.df['month'] = self.df['date'].dt.month
        self.df['year'] = self.df['date'].dt.year
        self.df['day_sin'] = np.sin(2 * np.pi * self.df['day_of_week'] / 7)
        self.df['day_cos'] = np.cos(2 * np.pi * self.df['day_of_week'] / 7)
        self.df['month_sin'] = np.sin(2 * np.pi * self.df['month'] / 12)
        self.df['month_cos'] = np.cos(2 * np.pi * self.df['month'] / 12)
        self.df['hour_sin'] = np.sin(2 * np.pi * self.df['hour'] / 24)
        self.df['hour_cos'] = np.cos(2 * np.pi * self.df['hour'] / 24)

        # Vérifier que max_number est correct (ne devrait pas dépasser 70 pour le Keno)
        valid_max_number = min(70, self.max_number)  # Limiter à 70 maximum
        print(f"Entraînement des modèles pour les numéros de 1 à {valid_max_number}")

        # Préparer les paramètres pour l'entraînement parallèle si disponible
        if use_parallel:
            models_params = []
            print("Préparation des données pour l'entraînement parallèle...")

        # Préparer les données pour tous les numéros
        for num in range(1, valid_max_number + 1):
            # Préparer les données pour ce numéro
            num_data = self.df.copy()

            # Créer la cible : le numéro apparaît-il dans le prochain tirage?
            num_data[f'next_has_{num}'] = num_data[f'has_{num}'].shift(-1)
            num_data = num_data.dropna()

            if len(num_data) < 30:
                print(f"  Numéro {num}: Pas assez de données (minimum 30 tirages requis)")
                continue

            # Caractéristiques à utiliser
            feature_cols = [
                f'has_{num}',  # Le numéro est-il dans le tirage actuel?
                'day_of_week',
                'hour',
                'is_weekend',
                'is_afternoon',
                'day',
                'month',
                'year'
            ]

            # Ajouter des caractéristiques sur l'historique récent
            for i in range(1, 6):  # Regarder les 5 derniers tirages
                num_data[f'has_{num}_lag_{i}'] = num_data[f'has_{num}'].shift(i).fillna(0)
                feature_cols.append(f'has_{num}_lag_{i}')

            # Ajouter des caractéristiques sur les numéros voisins
            for offset in [-2, -1, 1, 2]:
                neighbor = num + offset
                if 1 <= neighbor <= self.max_number:
                    num_data[f'has_neighbor_{neighbor}'] = num_data[f'has_{neighbor}']
                    feature_cols.append(f'has_neighbor_{neighbor}')

            # Diviser en ensembles d'entraînement et de test
            X = num_data[feature_cols]
            y = num_data[f'next_has_{num}']

            # Stocker les informations sur les caractéristiques
            features[num] = {
                'feature_cols': feature_cols,
                'scaler': None  # Sera rempli plus tard
            }

            # Si on utilise le traitement parallèle, préparer les paramètres pour ce numéro
            if use_parallel:
                # Convertir les données pandas en numpy pour le traitement parallèle
                X_np = X.values if isinstance(X, pd.DataFrame) else X
                y_np = y.values if isinstance(y, pd.Series) else y

                # Ajouter les paramètres pour ce numéro
                models_params.append({
                    'number': num,
                    'X': X_np,
                    'y': y_np,
                    'test_size': test_size,
                    'random_state': random_state,
                    'use_gpu': use_gpu,
                    'n_jobs': 1  # Chaque processus utilise 1 cœur pour éviter la surcharge
                })
            else:
                # Normaliser les caractéristiques
                X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state)
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)

                # Stocker le scaler
                features[num]['scaler'] = scaler

        # Entraîner les modèles
        if use_parallel:
            # Utiliser le traitement parallèle pour entraîner les modèles
            print(f"Entraînement parallèle de {len(models_params)} modèles avec {n_workers} workers...")
            try:
                # Entraîner les modèles en parallèle
                results = parallel_processing.train_models_parallel(models_params, max_workers=n_workers, use_gpu=use_gpu)

                # Traiter les résultats
                for num, result in results.items():
                    if result['success']:
                        # Stocker le modèle et le scaler
                        targets[num] = {
                            'model': result['model'],
                            'accuracy': result['metrics']['accuracy'],
                            'type': result['model_type']
                        }
                        features[num]['scaler'] = result['scaler']
                        print(f"  Numéro {num}: {result['model_type']} - Précision: {result['metrics']['accuracy']:.4f}")
                    else:
                        print(f"  Numéro {num}: Erreur - {result.get('message', 'Erreur inconnue')}")
            except Exception as e:
                import traceback
                print(f"Erreur lors de l'entraînement parallèle: {e}")
                traceback.print_exc()
                print("Fallback sur l'entraînement séquentiel...")
                use_parallel = False

        # Si le traitement parallèle n'est pas disponible ou a échoué, utiliser le traitement séquentiel
        if not use_parallel:
            # Continuer avec l'entraînement séquentiel pour chaque numéro
            for num in range(1, valid_max_number + 1):
                if num not in features:
                    continue

                # Déterminer le nombre de cœurs à utiliser
                n_jobs = getattr(self, 'n_jobs', -1)  # Utiliser tous les cœurs par défaut

                # Entraîner plusieurs modèles et choisir le meilleur
                models = {}

                # 1. Random Forest avec paramètres optimisés
                try:
                    rf_params = {
                        'n_estimators': 300,       # Plus d'arbres pour une meilleure précision
                        'max_depth': 15,          # Profondeur accrue pour capturer plus de motifs
                        'min_samples_split': 4,    # Valeur réduite pour plus de flexibilité
                        'min_samples_leaf': 2,     # Valeur réduite pour plus de flexibilité
                        'max_features': 'sqrt',    # Utiliser la racine carrée du nombre de caractéristiques
                        'bootstrap': True,         # Utiliser le bootstrap pour la diversité
                        'random_state': random_state,
                        'n_jobs': n_jobs,          # Parallélisation
                        'class_weight': 'balanced_subsample'  # Gérer les déséquilibres de classes
                    }
                    rf_model = RandomForestClassifier(**rf_params)
                    rf_model.fit(X_train_scaled, y_train)
                    rf_pred = rf_model.predict(X_test_scaled)
                    rf_accuracy = accuracy_score(y_test, rf_pred)
                    models['random_forest'] = {'model': rf_model, 'accuracy': rf_accuracy}
                    print(f"  Numéro {num}: Random Forest - Précision: {rf_accuracy:.4f}")
                except Exception as e:
                    print(f"  Erreur lors de l'entraînement du Random Forest pour le numéro {num}: {e}")

                # 2. Gradient Boosting avec paramètres optimisés
                try:
                    gb_params = {
                        'n_estimators': 200,        # Plus d'arbres pour une meilleure précision
                        'max_depth': 6,            # Profondeur légèrement accrue
                        'learning_rate': 0.05,      # Taux d'apprentissage réduit pour éviter le surajustement
                        'subsample': 0.8,           # Utiliser 80% des données pour chaque arbre
                        'min_samples_split': 4,     # Valeur réduite pour plus de flexibilité
                        'min_samples_leaf': 2,      # Valeur réduite pour plus de flexibilité
                        'max_features': 'sqrt',     # Utiliser la racine carrée du nombre de caractéristiques
                        'random_state': random_state
                    }
                    gb_model = GradientBoostingClassifier(**gb_params)
                    gb_model.fit(X_train_scaled, y_train)
                    gb_pred = gb_model.predict(X_test_scaled)
                    gb_accuracy = accuracy_score(y_test, gb_pred)
                    models['gradient_boosting'] = {'model': gb_model, 'accuracy': gb_accuracy}
                    print(f"  Numéro {num}: Gradient Boosting - Précision: {gb_accuracy:.4f}")
                except Exception as e:
                    print(f"  Erreur lors de l'entraînement du Gradient Boosting pour le numéro {num}: {e}")

                # 3. XGBoost si disponible
                try:
                    # Essayer d'importer XGBoost de différentes façons
                    try:
                        import xgboost
                        from xgboost import XGBClassifier
                    except ImportError:
                        # Essayer d'installer XGBoost si non disponible
                        import subprocess
                        import sys
                        print("  Tentative d'installation de XGBoost...")
                        subprocess.check_call([sys.executable, "-m", "pip", "install", "xgboost"])
                        from xgboost import XGBClassifier

                    # Configurer XGBoost avec des paramètres optimisés pour GPU
                    xgb_params = {
                        'n_estimators': 300,           # Plus d'arbres pour une meilleure précision
                        'max_depth': 8,               # Profondeur accrue pour capturer plus de motifs
                        'learning_rate': 0.03,         # Taux d'apprentissage réduit pour éviter le surajustement
                        'min_child_weight': 3,         # Valeur optimale pour éviter le surajustement
                        'gamma': 0.1,                  # Paramètre de régularisation pour éviter le surapprentissage
                        'subsample': 0.85,             # Utiliser 85% des données pour chaque arbre
                        'colsample_bytree': 0.85,      # Utiliser 85% des caractéristiques pour chaque arbre
                        'colsample_bylevel': 0.9,      # Utiliser 90% des caractéristiques pour chaque niveau
                        'reg_alpha': 0.1,              # Régularisation L1
                        'reg_lambda': 1.0,             # Régularisation L2
                        'scale_pos_weight': 1.0,       # Équilibrer les classes
                        'verbosity': 0,                # Désactive les messages d'avertissement
                        'random_state': random_state,
                        'n_jobs': n_jobs               # Parallélisation
                    }

                    # Configurer l'accélération matérielle en fonction du mode actif
                    if self.hardware_acceleration == 'gpu' and hasattr(self, 'cuda_available') and self.cuda_available:
                        xgb_params.update({
                            'tree_method': 'hist',      # Méthode optimisée pour GPU
                            'device': 'cuda',          # Utiliser CUDA
                            'predictor': 'gpu_predictor' # Utiliser GPU pour les prédictions
                        })
                        print("  Utilisation du GPU pour XGBoost (CUDA optimisé)")
                    elif self.hardware_acceleration == 'npu' and hasattr(self, 'npu_available') and self.npu_available:
                        # Configuration pour NPU - dépend du type de NPU
                        try:
                            # Vérifier d'abord si OpenVINO est disponible (pour processeurs Intel)
                            try:
                                import openvino as ov
                                # Configurer XGBoost pour fonctionner efficacement avec OpenVINO
                                xgb_params.update({
                                    'tree_method': 'hist',      # Méthode compatible avec la plupart des accélérateurs
                                    'device': 'cpu',           # XGBoost utilisera le CPU optimisé par OpenVINO
                                    'enable_categorical': True, # Améliore les performances
                                    'predictor': 'cpu_predictor', # Plus stable
                                    'n_jobs': self.n_jobs,      # Utiliser tous les cœurs disponibles
                                    'max_bin': 63              # Optimisé pour Intel VNNI/AVX
                                })
                                print("  Utilisation du NPU Intel pour XGBoost (via OpenVINO)")

                                # Définir une variable d'environnement pour OpenVINO
                                import os
                                os.environ['OPENVINO_FORCE_VPU'] = '1'  # Forcer l'utilisation du VPU si disponible
                                os.environ['OPENVINO_ENABLE_CPU_OPTIMIZATIONS'] = '1'  # Activer les optimisations CPU
                            except ImportError:
                                # OpenVINO n'est pas disponible, essayer d'autres méthodes
                                pass

                            # Pour les NPU compatibles avec TensorFlow
                            import tensorflow as tf
                            if hasattr(tf, 'config') and hasattr(tf.config, 'list_physical_devices'):
                                devices = tf.config.list_physical_devices()
                                npu_detected = any('NPU' in d.name.upper() or 'TPU' in d.name.upper() or 'NEURAL' in d.name.upper() for d in devices)

                                if npu_detected:
                                    # Utiliser une configuration optimisée pour NPU
                                    xgb_params.update({
                                        'tree_method': 'hist',      # Méthode compatible avec la plupart des accélérateurs
                                        'device': 'cpu',           # XGBoost utilisera TensorFlow pour l'accélération
                                        'enable_categorical': True, # Améliore les performances sur certains NPU
                                        'predictor': 'cpu_predictor' # Plus stable avec NPU
                                    })
                                    print("  Utilisation du NPU pour XGBoost (via TensorFlow)")
                                else:
                                    # Fallback sur CPU optimisé
                                    xgb_params['tree_method'] = 'hist'
                                    print("  Utilisation du CPU pour XGBoost (NPU non compatible)")
                        except Exception as e:
                            print(f"  Erreur lors de la configuration NPU pour XGBoost: {e}")
                            xgb_params['tree_method'] = 'hist'
                            print("  Utilisation du CPU pour XGBoost (fallback)")
                    else:
                        xgb_params['tree_method'] = 'hist'  # Hist est aussi rapide sur CPU
                        print("  Utilisation du CPU pour XGBoost (optimisé)")

                    xgb_model = XGBClassifier(**xgb_params)
                    xgb_model.fit(X_train_scaled, y_train)
                    xgb_pred = xgb_model.predict(X_test_scaled)
                    xgb_accuracy = accuracy_score(y_test, xgb_pred)
                    models['xgboost'] = {'model': xgb_model, 'accuracy': xgb_accuracy}
                    print(f"  Numéro {num}: XGBoost - Précision: {xgb_accuracy:.4f}")
                except Exception as e:
                    print(f"  XGBoost non disponible ou erreur: {e}")
                    print(f"  Chemin Python: {sys.path}")

                # 4. LightGBM si disponible
                try:
                    # Essayer d'importer LightGBM de différentes façons
                    try:
                        import lightgbm
                        from lightgbm import LGBMClassifier
                    except ImportError:
                        # Essayer d'installer LightGBM si non disponible
                        import subprocess
                        import sys
                        print("  Tentative d'installation de LightGBM...")
                        subprocess.check_call([sys.executable, "-m", "pip", "install", "lightgbm"])
                        from lightgbm import LGBMClassifier

                    # Configurer LightGBM avec des paramètres optimisés pour GPU
                    lgbm_params = {
                        'n_estimators': 100,
                        'max_depth': 3,
                        'learning_rate': 0.05,
                        'min_child_samples': 20,
                        'min_split_gain': 0.01,  # Évite les divisions avec gain négatif
                        'verbose': -1,  # Désactive les messages d'avertissement
                        'random_state': random_state,
                        'feature_name': 'auto'  # Utilise des noms automatiques pour éviter les avertissements
                    }

                    # Configurer l'accélération matérielle pour LightGBM
                    if self.hardware_acceleration == 'gpu' and hasattr(self, 'cuda_available') and self.cuda_available:
                        # Tenter d'utiliser le GPU avec des paramètres sécurisés
                        try:
                            # Vérifier si LightGBM peut utiliser le GPU
                            import lightgbm as lgb
                            import numpy as np

                            # Créer un petit jeu de données de test
                            X_test = np.random.rand(10, 5)
                            y_test = np.random.randint(0, 2, 10)
                            lgb_test = lgb.Dataset(X_test, y_test)

                            # Essayer de créer un modèle avec GPU
                            test_params = {
                                'objective': 'binary',
                                'device': 'gpu',
                                'gpu_platform_id': 0,
                                'gpu_device_id': 0,
                                'max_bin': 31,         # Valeur plus petite pour éviter les erreurs
                                'min_data_in_leaf': 20, # Valeur plus grande pour éviter les avertissements
                                'num_leaves': 31,       # Limiter le nombre de feuilles
                                'verbose': -1
                            }

                            # Essayer d'entraîner un modèle simple
                            test_bst = lgb.train(test_params, lgb_test, num_boost_round=1)

                            # Si on arrive ici, le GPU fonctionne pour LightGBM
                            lgbm_params.update({
                                'device': 'gpu',
                                'gpu_platform_id': 0,
                                'gpu_device_id': 0,
                                'max_bin': 31,         # Valeur plus petite pour éviter les erreurs
                                'min_data_in_leaf': 20, # Valeur plus grande pour éviter les avertissements
                                'num_leaves': 31,       # Limiter le nombre de feuilles
                                'verbose': -1           # Réduire les messages
                            })
                            print("  Utilisation du GPU pour LightGBM (paramètres sécurisés)")
                        except Exception as e:
                            print(f"  Erreur lors de la configuration GPU pour LightGBM: {e}")
                            # Fallback sur CPU
                            lgbm_params.update({
                                'device': 'cpu',
                                'num_leaves': 31,       # Limiter le nombre de feuilles
                                'min_data_in_leaf': 20,  # Valeur plus grande pour éviter les avertissements
                                'max_bin': 255,          # Valeur standard pour CPU
                                'verbose': -1            # Réduire les messages
                            })
                            print("  Utilisation du CPU pour LightGBM (fallback après erreur GPU)")
                    elif self.hardware_acceleration == 'npu' and hasattr(self, 'npu_available') and self.npu_available:
                            # Configuration pour NPU - dépend du type de NPU
                            try:
                                # Vérifier d'abord si OpenVINO est disponible (pour processeurs Intel)
                                try:
                                    import openvino as ov
                                    # Configurer LightGBM pour fonctionner efficacement avec OpenVINO
                                    lgbm_params.update({
                                        'device': 'cpu',           # LightGBM utilisera le CPU optimisé par OpenVINO
                                        'num_leaves': 31,         # Limiter le nombre de feuilles
                                        'min_data_in_leaf': 20,    # Valeur plus grande pour éviter les avertissements
                                        'max_bin': 63,             # Optimisé pour Intel VNNI/AVX
                                        'verbose': -1,             # Réduire les messages
                                        'boost_from_average': True, # Améliore les performances
                                        'num_threads': self.n_jobs if self.n_jobs > 0 else None,  # Utiliser tous les cœurs
                                        'force_row_wise': True,    # Optimisé pour Intel
                                        'histogram_pool_size': -1  # Utiliser autant de mémoire que nécessaire
                                    })
                                    print("  Utilisation du NPU Intel pour LightGBM (via OpenVINO)")

                                    # Définir une variable d'environnement pour OpenVINO
                                    import os
                                    os.environ['OPENVINO_FORCE_VPU'] = '1'  # Forcer l'utilisation du VPU si disponible
                                    os.environ['OPENVINO_ENABLE_CPU_OPTIMIZATIONS'] = '1'  # Activer les optimisations CPU

                                    # Succès avec OpenVINO
                                    continue
                                except ImportError:
                                    # OpenVINO n'est pas disponible, essayer d'autres méthodes
                                    pass

                                # Pour les NPU compatibles avec TensorFlow
                                import tensorflow as tf
                                if hasattr(tf, 'config') and hasattr(tf.config, 'list_physical_devices'):
                                    devices = tf.config.list_physical_devices()
                                    npu_detected = any('NPU' in d.name.upper() or 'TPU' in d.name.upper() or 'NEURAL' in d.name.upper() for d in devices)

                                    if npu_detected:
                                        # Utiliser une configuration optimisée pour NPU via TensorFlow
                                        lgbm_params.update({
                                            'device': 'cpu',           # LightGBM utilisera TensorFlow pour l'accélération
                                            'num_leaves': 31,         # Limiter le nombre de feuilles
                                            'min_data_in_leaf': 20,    # Valeur plus grande pour éviter les avertissements
                                            'max_bin': 63,             # Valeur intermédiaire pour NPU
                                            'verbose': -1,             # Réduire les messages
                                            'boost_from_average': True # Améliore les performances sur certains NPU
                                        })
                                        print("  Utilisation du NPU pour LightGBM (via TensorFlow)")
                                    else:
                                        # Fallback sur CPU optimisé
                                        lgbm_params.update({
                                            'device': 'cpu',
                                            'num_leaves': 31,       # Limiter le nombre de feuilles
                                            'min_data_in_leaf': 20,  # Valeur plus grande pour éviter les avertissements
                                            'max_bin': 255,          # Valeur standard pour CPU
                                            'verbose': -1            # Réduire les messages
                                        })
                                        print("  Utilisation du CPU pour LightGBM (NPU non compatible)")
                            except Exception as e:
                                print(f"  Erreur lors de la configuration NPU pour LightGBM: {e}")
                                lgbm_params.update({
                                    'device': 'cpu',
                                    'num_leaves': 31,       # Limiter le nombre de feuilles
                                    'min_data_in_leaf': 20,  # Valeur plus grande pour éviter les avertissements
                                    'max_bin': 255,          # Valeur standard pour CPU
                                    'verbose': -1            # Réduire les messages
                                })
                                print("  Utilisation du CPU pour LightGBM (fallback)")
                    else:
                        # Configuration CPU standard
                        lgbm_params.update({
                            'device': 'cpu',
                            'num_leaves': 31,       # Limiter le nombre de feuilles
                            'min_data_in_leaf': 20,  # Valeur plus grande pour éviter les avertissements
                            'max_bin': 255,          # Valeur standard pour CPU
                            'verbose': -1            # Réduire les messages
                        })
                        print("  Utilisation du CPU pour LightGBM (mode CPU sélectionné)")

                    lgbm_model = LGBMClassifier(**lgbm_params)

                    # Supprimer les avertissements de scikit-learn
                    import warnings
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        lgbm_model.fit(X_train_scaled, y_train)
                    lgbm_pred = lgbm_model.predict(X_test_scaled)
                    lgbm_accuracy = accuracy_score(y_test, lgbm_pred)
                    models['lightgbm'] = {'model': lgbm_model, 'accuracy': lgbm_accuracy}
                    print(f"  Numéro {num}: LightGBM - Précision: {lgbm_accuracy:.4f}")
                except Exception as e:
                    print(f"  LightGBM non disponible ou erreur: {e}")
                    print(f"  Chemin Python: {sys.path}")

                    # Choisir le meilleur modèle
                    if models:
                        best_model_name = max(models, key=lambda k: models[k]['accuracy'])
                        best_model_info = models[best_model_name]

                        targets[num] = {
                            'model': best_model_info['model'],
                            'accuracy': best_model_info['accuracy'],
                            'type': best_model_name
                        }

                        print(f"  Meilleur modèle pour le numéro {num}: {best_model_name} (Précision: {best_model_info['accuracy']:.4f})")
                        print(f"  Modèle ajouté au dictionnaire targets pour le numéro {num}")
                    else:
                        print(f"  Aucun modèle n'a pu être entraîné pour le numéro {num}")

        # Vérifier que nous avons des modèles à stocker
        print(f"\nVérification des modèles avant stockage:")
        print(f"Nombre de numéros avec modèles: {len(targets)}")
        print(f"Numéros avec modèles: {sorted(list(targets.keys()))}")

        # Stocker les modèles
        self.models = {
            'features': features,
            'targets': targets
        }

        # Vérifier que les modèles ont été correctement stockés
        print(f"Vérification après stockage:")
        if hasattr(self, 'models') and self.models:
            print(f"Modèles stockés avec succès")
            if 'targets' in self.models:
                print(f"Nombre de numéros dans self.models['targets']: {len(self.models['targets'])}")
                print(f"Numéros dans self.models['targets']: {sorted(list(self.models['targets'].keys()))}")
            else:
                print(f"ERREUR: 'targets' n'est pas dans self.models")
        else:
            print(f"ERREUR: self.models n'existe pas ou est vide")

        # Calculer et afficher des statistiques sur les modèles entraînés
        end_time = time.time()
        training_time = end_time - start_time

        # Calculer les statistiques de précision
        accuracies = [target_info['accuracy'] for target_info in targets.values()]
        avg_accuracy = sum(accuracies) / len(accuracies) if accuracies else 0
        max_accuracy = max(accuracies) if accuracies else 0
        min_accuracy = min(accuracies) if accuracies else 0

        # Compter les types de modèles
        model_types = {}
        for target_info in targets.values():
            model_type = target_info['type']
            model_types[model_type] = model_types.get(model_type, 0) + 1

        # Afficher les résultats
        print(f"\nEntraînement terminé en {training_time:.2f} secondes")
        print(f"Modèles entraînés pour {len(targets)} numéros sur {self.max_number} possibles")
        print(f"Précision moyenne: {avg_accuracy:.4f} (min: {min_accuracy:.4f}, max: {max_accuracy:.4f})")
        print("Types de modèles utilisés:")
        for model_type, count in model_types.items():
            print(f"  - {model_type}: {count} modèles")

        return True

    def predict_with_ml(self, num_predictions=10):
        """Prédit les prochains numéros en utilisant les modèles d'apprentissage automatique"""
        # Import numpy et sys ici pour éviter les erreurs
        import numpy as np
        import time
        import sys
        start_time = time.time()
        print("Début de la prédiction par apprentissage automatique...")

        # Vérifier si un cache existe et est récent (moins de 5 minutes)
        if hasattr(self, '_ml_prediction_cache') and hasattr(self, '_ml_prediction_cache_time'):
            cache_age = time.time() - self._ml_prediction_cache_time
            if cache_age < 300 and self._ml_prediction_cache.get('num_predictions') >= num_predictions:  # 5 minutes
                print(f"Utilisation du cache de prédiction ML (age: {cache_age:.1f} secondes)")
                return self._ml_prediction_cache['predictions'][:num_predictions]

        if not self.models or not self.models.get('targets'):
            print("Aucun modèle d'apprentissage automatique disponible. Utilisez l'auto-amélioration pour entraîner des modèles.")
            return []

        if self.df is None or self.df.empty:
            return []

        # Obtenir le dernier tirage
        last_row = self.df.iloc[-1].copy()

        # Préparer les prédictions pour chaque numéro
        predictions = {}

        # Utiliser le threading pour accélérer les prédictions
        import threading
        import queue

        # File pour stocker les résultats
        results_queue = queue.Queue()

        # Vérifier que nous ne prédisons que pour des numéros valides (1-70 pour le Keno)
        valid_max_number = min(70, self.max_number)  # Limiter à 70 maximum

        # Fonction pour prédire un numéro dans un thread
        def predict_number(num, target_info):
            try:
                # Import numpy, pandas et sys ici pour éviter les erreurs
                import numpy as np
                import pandas as pd
                import sys

                feature_info = self.models['features'][num]
                model = target_info['model']
                scaler = feature_info['scaler']
                feature_cols = feature_info['feature_cols']

                # Préparer les caractéristiques pour ce numéro
                features = {}

                for col in feature_cols:
                    if col in last_row:
                        features[col] = last_row[col]
                    elif col.startswith('has_') and '_lag_' in col:
                        # Gérer les caractéristiques de lag
                        parts = col.split('_lag_')
                        base_col = parts[0]
                        lag = int(parts[1])

                        if lag <= len(self.df):
                            features[col] = self.df.iloc[-lag][base_col]
                        else:
                            features[col] = 0
                    elif col.startswith('has_neighbor_'):
                        # Gérer les caractéristiques de voisinage
                        neighbor = int(col.split('_')[-1])
                        features[col] = 1 if neighbor in self.data_manager.draws[-1].draw_numbers else 0
                    else:
                        features[col] = 0

                # Convertir en DataFrame
                X = pd.DataFrame([features])

                # Normaliser
                X_scaled = scaler.transform(X)

                # Prédire (en supprimant les avertissements)
                import warnings
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    prob = model.predict_proba(X_scaled)[0][1]  # Probabilité de la classe 1 (numéro présent)

                # Ajouter le résultat à la file
                results_queue.put((num, {
                    'probability': prob,
                    'model_accuracy': target_info['accuracy'],
                    'model_type': target_info['type']
                }))
            except Exception as e:
                print(f"Erreur lors de la prédiction pour le numéro {num}: {e}")

        # Créer et démarrer les threads (par lots pour éviter de surcharger le système)
        batch_size = 10  # Nombre de threads à exécuter simultanément

        # Filtrer pour ne garder que les numéros valides (1-70)
        all_nums = [(num, info) for num, info in self.models['targets'].items() if 1 <= num <= valid_max_number]
        print(f"Prédiction pour {len(all_nums)} numéros valides (1-{valid_max_number})")

        for i in range(0, len(all_nums), batch_size):
            batch = all_nums[i:i+batch_size]
            threads = []

            for num, target_info in batch:
                thread = threading.Thread(target=predict_number, args=(num, target_info))
                thread.daemon = True
                thread.start()
                threads.append(thread)

            # Attendre que tous les threads du lot terminent
            for thread in threads:
                thread.join()

        # Récupérer tous les résultats
        while not results_queue.empty():
            num, pred_info = results_queue.get()
            predictions[num] = pred_info

        # Trier les numéros par probabilité décroissante
        sorted_nums = sorted(predictions.keys(), key=lambda x: predictions[x]['probability'], reverse=True)

        # Sélectionner les N premiers numéros valides (entre 1 et max_number)
        selected_nums = []
        for num in sorted_nums:
            # Vérifier que le numéro est valide (entre 1 et max_number)
            if 1 <= num <= self.max_number:
                selected_nums.append(num)
                if len(selected_nums) >= num_predictions:
                    break

        # Ajouter des informations détaillées
        result = []
        for num in selected_nums:
            result.append({
                'number': num,
                'probability': predictions[num]['probability'],
                'model_accuracy': predictions[num]['model_accuracy'],
                'model_type': predictions[num]['model_type']
            })

        # Stocker dans le cache
        self._ml_prediction_cache = {
            'predictions': result,
            'num_predictions': num_predictions,
            'time': time.time()
        }
        self._ml_prediction_cache_time = time.time()

        # Afficher le temps d'exécution
        end_time = time.time()
        print(f"Prédiction ML terminée en {end_time - start_time:.2f} secondes")

        return result

    def predict_with_patterns(self, num_predictions=10):
        """Prédit les prochains numéros en utilisant l'analyse des motifs"""
        if not self.patterns:
            print("Aucun motif disponible. Utilisez l'auto-amélioration pour analyser les motifs.")
            return []

        if not self.data_manager.draws:
            return []

        # Obtenir le dernier tirage
        last_draw = self.data_manager.draws[-1]
        last_nums = last_draw.draw_numbers

        # Calculer les caractéristiques du dernier tirage
        last_even_count = sum(1 for num in last_nums if num % 2 == 0)
        last_odd_count = self.numbers_per_draw - last_even_count

        mid_point = self.max_number // 2
        last_high_count = sum(1 for num in last_nums if num > mid_point)
        last_low_count = self.numbers_per_draw - last_high_count

        # Trouver les motifs les plus fréquents
        if 'even_odd_distribution' in self.patterns:
            common_even_odd = self.patterns['even_odd_distribution'].most_common(3)
            target_even = common_even_odd[0][0][0]  # Nombre de pairs le plus fréquent
        else:
            target_even = self.numbers_per_draw // 2  # Par défaut, équilibré

        if 'high_low_distribution' in self.patterns:
            common_high_low = self.patterns['high_low_distribution'].most_common(3)
            target_high = common_high_low[0][0][0]  # Nombre de hauts le plus fréquent
        else:
            target_high = self.numbers_per_draw // 2  # Par défaut, équilibré

        # Calculer les scores pour chaque numéro
        scores = {}

        # Vérifier que max_number est correct (ne devrait pas dépasser 70 pour le Keno)
        valid_max_number = min(70, self.max_number)  # Limiter à 70 maximum
        print(f"Analyse des motifs pour les numéros de 1 à {valid_max_number}")

        for num in range(1, valid_max_number + 1):
            # Ignorer les numéros du dernier tirage
            if num in last_nums:
                continue

            score = 0

            # Favoriser les numéros qui équilibrent la distribution pairs/impairs
            is_even = (num % 2 == 0)
            if is_even and last_even_count < target_even:
                score += 2
            elif not is_even and last_odd_count < (self.numbers_per_draw - target_even):
                score += 2

            # Favoriser les numéros qui équilibrent la distribution haut/bas
            is_high = (num > mid_point)
            if is_high and last_high_count < target_high:
                score += 2
            elif not is_high and last_low_count < (self.numbers_per_draw - target_high):
                score += 2

            # Favoriser les numéros qui créent des écarts fréquents
            if 'consecutive_gaps' in self.patterns:
                for last_num in last_nums:
                    gap = abs(num - last_num)
                    if gap in self.patterns['consecutive_gaps']:
                        score += self.patterns['consecutive_gaps'][gap] / 100

            # Vérifier si le numéro contribue à une somme typique
            if 'draw_sums' in self.patterns:
                current_sum = sum(last_nums)
                new_sum = current_sum + num
                target_sum = self.patterns['draw_sums']['mean']

                # Favoriser les numéros qui rapprochent de la somme moyenne
                if abs(new_sum - target_sum) < abs(current_sum - target_sum):
                    score += 1

            scores[num] = score

        # Trier les numéros par score décroissant
        sorted_nums = sorted(scores.keys(), key=lambda x: scores[x], reverse=True)

        # Sélectionner les N premiers numéros valides (entre 1 et max_number)
        selected_nums = []
        for num in sorted_nums:
            # Vérifier que le numéro est valide (entre 1 et max_number)
            if 1 <= num <= self.max_number:
                selected_nums.append(num)
                if len(selected_nums) >= num_predictions:
                    break

        # Ajouter des informations détaillées
        result = []
        for num in selected_nums:
            result.append({
                'number': num,
                'pattern_score': scores[num]
            })

        return result

    def predict_with_series(self, num_predictions=10):
        """Prédit les prochains numéros en utilisant l'analyse des séries"""
        if not self.series:
            print("Aucune série disponible. Utilisez l'auto-amélioration pour analyser les séries.")
            return []

        if not self.data_manager.draws or not self.series:
            return []

        # Obtenir le dernier tirage
        last_draw = self.data_manager.draws[-1]
        last_nums = last_draw.draw_numbers

        # Calculer les scores pour chaque numéro
        scores = {}

        # Vérifier que max_number est correct (ne devrait pas dépasser 70 pour le Keno)
        valid_max_number = min(70, self.max_number)  # Limiter à 70 maximum
        print(f"Analyse des séries pour les numéros de 1 à {valid_max_number}")

        for num in range(1, valid_max_number + 1):
            # Ignorer les numéros du dernier tirage
            if num in last_nums:
                continue

            score = 0

            # Vérifier si le numéro forme une séquence connue avec les numéros du dernier tirage
            if 'common_sequences' in self.series:
                for seq, count in self.series['common_sequences']:
                    # Vérifier si le numéro peut compléter une séquence
                    for last_num in last_nums:
                        if (last_num, num) in seq or (num, last_num) in seq:
                            score += count / 10

            # Vérifier si le numéro forme une paire connue avec les numéros du dernier tirage
            if 'common_pairs' in self.series:
                for pair, count in self.series['common_pairs']:
                    for last_num in last_nums:
                        if (min(last_num, num), max(last_num, num)) == pair:
                            score += count / 5

            # Vérifier le taux de répétition du numéro
            if 'follow_patterns' in self.series and num in self.series['follow_patterns']:
                repeat_rate = self.series['follow_patterns'][num]['repeat_rate']
                score += repeat_rate * 3

            scores[num] = score

        # Trier les numéros par score décroissant
        sorted_nums = sorted(scores.keys(), key=lambda x: scores[x], reverse=True)

        # Sélectionner les N premiers numéros valides (entre 1 et max_number)
        selected_nums = []
        for num in sorted_nums:
            # Vérifier que le numéro est valide (entre 1 et 70)
            if 1 <= num <= 70:
                selected_nums.append(num)
                if len(selected_nums) >= num_predictions:
                    break

        # Ajouter des informations détaillées
        result = []
        for num in selected_nums:
            result.append({
                'number': num,
                'series_score': scores[num]
            })

        return result

    def predict_combined_advanced(self, num_predictions=10):
        """Combine toutes les méthodes avancées pour une prédiction optimale"""
        import time
        start_time = time.time()
        print("Génération de prédictions avancées combinées...")

        # Vérifier si un cache existe et est récent (moins de 5 minutes)
        if hasattr(self, '_combined_advanced_cache') and hasattr(self, '_combined_advanced_cache_time'):
            cache_age = time.time() - self._combined_advanced_cache_time
            if cache_age < 300 and self._combined_advanced_cache.get('num_predictions') >= num_predictions:  # 5 minutes
                print(f"Utilisation du cache de prédiction combinée avancée (age: {cache_age:.1f} secondes)")
                return self._combined_advanced_cache['predictions'][:num_predictions]

        # Utiliser le threading pour obtenir les prédictions en parallèle
        import threading
        import queue

        # File pour stocker les résultats
        results_queue = queue.Queue()

        # Fonction pour exécuter une méthode de prédiction dans un thread
        def run_prediction(method_name, num_preds):
            try:
                if method_name == 'ml':
                    print("Obtention des prédictions par apprentissage automatique...")
                    predictions = self.predict_with_ml(num_preds)
                    results_queue.put(('ml', predictions))
                elif method_name == 'patterns':
                    print("Obtention des prédictions par motifs...")
                    predictions = self.predict_with_patterns(num_preds)
                    results_queue.put(('patterns', predictions))
                elif method_name == 'series':
                    print("Obtention des prédictions par séries...")
                    predictions = self.predict_with_series(num_preds)
                    results_queue.put(('series', predictions))
                elif method_name == 'frequency':
                    print("Calcul des scores de fréquence...")
                    scores = self._calculate_frequency_scores()
                    results_queue.put(('frequency', scores))
            except Exception as e:
                print(f"Erreur lors de la prédiction {method_name}: {e}")
                if method_name == 'frequency':
                    results_queue.put((method_name, {}))
                else:
                    results_queue.put((method_name, []))

        # Créer et démarrer les threads
        threads = []
        for method in ['ml', 'patterns', 'series', 'frequency']:
            thread = threading.Thread(target=run_prediction, args=(method, num_predictions * 2))
            thread.daemon = True
            thread.start()
            threads.append(thread)

        # Attendre que tous les threads terminent (avec timeout)
        timeout = 30  # secondes
        for thread in threads:
            thread.join(timeout)

        # Récupérer les résultats
        ml_predictions = []
        pattern_predictions = []
        series_predictions = []
        frequency_scores = {}

        while not results_queue.empty():
            method, result = results_queue.get()
            if method == 'ml':
                ml_predictions = result
            elif method == 'patterns':
                pattern_predictions = result
            elif method == 'series':
                series_predictions = result
            elif method == 'frequency':
                frequency_scores = result

        # Créer un dictionnaire pour combiner les scores
        combined_scores = {}

        # Ajouter les scores des modèles ML avec un poids plus important
        for pred in ml_predictions:
            num = pred['number']
            if num not in combined_scores:
                combined_scores[num] = {'number': num, 'total_score': 0, 'details': {}}

            # Pondérer par la probabilité et la précision du modèle (poids plus élevé)
            ml_score = pred['probability'] * pred['model_accuracy'] * 8
            combined_scores[num]['total_score'] += ml_score
            combined_scores[num]['details']['ml_score'] = ml_score
            combined_scores[num]['details']['ml_probability'] = pred['probability']
            combined_scores[num]['details']['ml_model_type'] = pred['model_type']

        # Ajouter les scores des motifs
        for pred in pattern_predictions:
            num = pred['number']
            if num not in combined_scores:
                combined_scores[num] = {'number': num, 'total_score': 0, 'details': {}}

            pattern_score = pred['pattern_score'] * 1.5  # Augmenter l'importance des motifs
            combined_scores[num]['total_score'] += pattern_score
            combined_scores[num]['details']['pattern_score'] = pattern_score

        # Ajouter les scores des séries
        for pred in series_predictions:
            num = pred['number']
            if num not in combined_scores:
                combined_scores[num] = {'number': num, 'total_score': 0, 'details': {}}

            series_score = pred['series_score'] * 1.2  # Augmenter l'importance des séries
            combined_scores[num]['total_score'] += series_score
            combined_scores[num]['details']['series_score'] = series_score

        # Ajouter les scores de fréquence
        for num, score in frequency_scores.items():
            if num not in combined_scores:
                combined_scores[num] = {'number': num, 'total_score': 0, 'details': {}}

            # Ajouter le score de fréquence avec un poids modéré
            combined_scores[num]['total_score'] += score
            combined_scores[num]['details']['frequency_score'] = score

        # Appliquer un facteur de correction pour éviter les biais
        self._apply_balance_correction(combined_scores)

        # Trier par score total décroissant
        sorted_predictions = sorted(combined_scores.values(), key=lambda x: x['total_score'], reverse=True)

        # Sélectionner les N premiers numéros
        final_predictions = sorted_predictions[:num_predictions]

        # Stocker dans le cache
        self._combined_advanced_cache = {
            'predictions': final_predictions,
            'num_predictions': num_predictions,
            'time': time.time()
        }
        self._combined_advanced_cache_time = time.time()

        # Afficher le temps d'exécution
        end_time = time.time()
        print(f"Prédiction combinée avancée terminée en {end_time - start_time:.2f} secondes")
        print(f"Prédiction combinée générée avec {len(final_predictions)} numéros")

        return final_predictions

    def _calculate_frequency_scores(self):
        """Calcule les scores basés sur la fréquence d'apparition des numéros"""
        if not self.data_manager.draws:
            return {}

        # Vérifier que max_number est correct (ne devrait pas dépasser 70 pour le Keno)
        valid_max_number = min(70, self.max_number)  # Limiter à 70 maximum

        # Compter les occurrences de chaque numéro
        counts = {i: 0 for i in range(1, valid_max_number + 1)}

        # Donner plus de poids aux tirages récents
        recent_draws = self.data_manager.draws[-50:] if len(self.data_manager.draws) > 50 else self.data_manager.draws

        for i, draw in enumerate(recent_draws):
            weight = 1 + (i / len(recent_draws))  # Les tirages plus récents ont un poids plus élevé
            for num in draw.draw_numbers:
                counts[num] = counts.get(num, 0) + weight

        # Normaliser les scores
        max_count = max(counts.values()) if counts else 1
        scores = {num: (count / max_count) * 3 for num, count in counts.items()}

        return scores

    def _apply_balance_correction(self, combined_scores):
        """Applique une correction pour équilibrer les prédictions"""
        if not combined_scores:
            return

        # Vérifier la distribution pairs/impairs
        even_count = 0
        odd_count = 0
        high_count = 0
        low_count = 0
        mid_point = self.max_number // 2

        # Compter les numéros pairs/impairs et hauts/bas parmi les meilleurs scores
        top_nums = sorted(combined_scores.keys(), key=lambda x: combined_scores[x]['total_score'], reverse=True)[:self.numbers_per_draw]

        for num in top_nums:
            if num % 2 == 0:
                even_count += 1
            else:
                odd_count += 1

            if num > mid_point:
                high_count += 1
            else:
                low_count += 1

        # Appliquer une correction si nécessaire
        for num in combined_scores:
            # Correction pairs/impairs
            if even_count > self.numbers_per_draw * 0.7:  # Trop de pairs
                if num % 2 != 0:  # Favoriser les impairs
                    combined_scores[num]['total_score'] *= 1.2
            elif odd_count > self.numbers_per_draw * 0.7:  # Trop d'impairs
                if num % 2 == 0:  # Favoriser les pairs
                    combined_scores[num]['total_score'] *= 1.2

            # Correction hauts/bas
            if high_count > self.numbers_per_draw * 0.7:  # Trop de hauts
                if num <= mid_point:  # Favoriser les bas
                    combined_scores[num]['total_score'] *= 1.2
            elif low_count > self.numbers_per_draw * 0.7:  # Trop de bas
                if num > mid_point:  # Favoriser les hauts
                    combined_scores[num]['total_score'] *= 1.2

    def save_models(self, filepath='keno_ml_models.pkl'):
        """Sauvegarde les modèles entraînés"""
        print("\nDébut de la sauvegarde des modèles...")

        # Vérifier si les modèles existent
        if not hasattr(self, 'models') or not self.models:
            print("Aucun modèle à sauvegarder")
            return False

        # Vérifier le contenu des modèles
        if 'targets' not in self.models or not self.models['targets']:
            print("Aucun modèle cible à sauvegarder")
            return False

        # Afficher des informations sur les modèles à sauvegarder
        num_models = len(self.models['targets'])
        print(f"Sauvegarde de {num_models} modèles dans {filepath}")
        print(f"Numéros avec modèles: {sorted(list(self.models['targets'].keys()))}")

        try:
            # Créer le répertoire parent si nécessaire
            import os
            os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)

            # Sauvegarder les modèles
            joblib.dump(self.models, filepath)

            # Vérifier que le fichier a bien été créé
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath) / (1024 * 1024)  # Taille en Mo
                print(f"Modèles sauvegardés dans {filepath} (taille: {file_size:.2f} Mo)")
                return True
            else:
                print(f"ERREUR: Le fichier {filepath} n'a pas été créé")
                return False
        except Exception as e:
            print(f"Erreur lors de la sauvegarde des modèles: {e}")
            import traceback
            traceback.print_exc()
            return False

    def load_models(self, filepath='keno_ml_models.pkl'):
        """Charge les modèles entraînés"""
        print("\nDébut du chargement des modèles...")

        # Vérifier que le fichier existe
        if not os.path.exists(filepath):
            print(f"Fichier de modèles {filepath} introuvable")
            return False

        # Afficher des informations sur le fichier
        file_size = os.path.getsize(filepath) / (1024 * 1024)  # Taille en Mo
        print(f"Chargement du fichier {filepath} (taille: {file_size:.2f} Mo)")

        try:
            # Charger les modèles
            self.models = joblib.load(filepath)

            # Vérifier que les modèles ont été correctement chargés
            if hasattr(self, 'models') and self.models:
                if 'targets' in self.models and self.models['targets']:
                    num_models = len(self.models['targets'])
                    print(f"Modèles chargés depuis {filepath}: {num_models} modèles")
                    print(f"Numéros avec modèles: {sorted(list(self.models['targets'].keys()))}")
                    return True
                else:
                    print(f"ERREUR: Les modèles chargés ne contiennent pas de cibles valides")
                    return False
            else:
                print(f"ERREUR: Les modèles n'ont pas été correctement chargés")
                return False
        except Exception as e:
            print(f"Erreur lors du chargement des modèles: {e}")
            import traceback
            traceback.print_exc()
            return False

    def visualize_correlations(self):
        """Visualise la matrice de corrélation entre les numéros"""
        if self.correlations is None:
            self.analyze_correlations()

        if self.correlations is None:
            return None

        plt.figure(figsize=(12, 10))
        mask = np.triu(np.ones_like(self.correlations, dtype=bool))
        sns.heatmap(self.correlations, mask=mask, annot=False, cmap='coolwarm', center=0,
                   square=True, linewidths=.5, cbar_kws={"shrink": .5})
        plt.title('Matrice de corrélation entre les numéros')

        return plt.gcf()

    def visualize_patterns(self):
        """Visualise les motifs détectés"""
        if not self.patterns:
            self.analyze_patterns()

        if not self.patterns:
            return None

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # Distribution pairs/impairs
        if 'even_odd_distribution' in self.patterns:
            data = self.patterns['even_odd_distribution']
            labels = [f"{even}/{self.numbers_per_draw-even}" for even, _ in data.keys()]
            values = list(data.values())

            axes[0, 0].bar(labels, values)
            axes[0, 0].set_title('Distribution Pairs/Impairs')
            axes[0, 0].set_xlabel('Nombre de pairs/impairs')
            axes[0, 0].set_ylabel('Fréquence')

        # Distribution haut/bas
        if 'high_low_distribution' in self.patterns:
            data = self.patterns['high_low_distribution']
            labels = [f"{high}/{self.numbers_per_draw-high}" for high, _ in data.keys()]
            values = list(data.values())

            axes[0, 1].bar(labels, values)
            axes[0, 1].set_title('Distribution Haut/Bas')
            axes[0, 1].set_xlabel('Nombre de hauts/bas')
            axes[0, 1].set_ylabel('Fréquence')

        # Écarts consécutifs
        if 'consecutive_gaps' in self.patterns:
            data = self.patterns['consecutive_gaps']
            gaps = sorted(data.keys())
            values = [data[gap] for gap in gaps]

            axes[1, 0].bar(gaps, values)
            axes[1, 0].set_title('Écarts entre numéros consécutifs')
            axes[1, 0].set_xlabel('Écart')
            axes[1, 0].set_ylabel('Fréquence')

        # Somme des tirages
        if 'draw_sums' in self.patterns:
            if 'distribution' in self.patterns['draw_sums']:
                hist, bins = self.patterns['draw_sums']['distribution']
                axes[1, 1].bar(bins[:-1], hist, width=np.diff(bins), align='edge')
                axes[1, 1].set_title('Distribution des sommes de tirage')
                axes[1, 1].set_xlabel('Somme')
                axes[1, 1].set_ylabel('Fréquence')

                # Ajouter une ligne pour la moyenne
                mean = self.patterns['draw_sums']['mean']
                axes[1, 1].axvline(mean, color='r', linestyle='--', label=f'Moyenne: {mean:.1f}')
                axes[1, 1].legend()

        plt.tight_layout()

        return fig
