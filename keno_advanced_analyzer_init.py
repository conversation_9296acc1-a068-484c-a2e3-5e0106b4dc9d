        # Initialiser LightGBM si disponible
        self.lightgbm_available = LIGHTGBM_AVAILABLE
        
        # Initialiser l'optimiseur XGBoost si disponible
        self.xgboost_optimizer = None
        self.xgboost_optimizer_available = XGBOOST_OPTIMIZER_AVAILABLE
        if self.xgboost_optimizer_available:
            try:
                # Déterminer si on utilise le GPU
                use_gpu = self.hardware_acceleration in ['gpu', 'auto'] and self.cuda_available
                
                # Créer l'optimiseur XGBoost
                self.xgboost_optimizer = XGBoostOptimizer(
                    use_gpu=use_gpu,
                    n_jobs=self.n_jobs,
                    verbose=1
                )
                print("Optimiseur XGBoost initialisé avec succès")
                
                # Afficher si le GPU est utilisé
                if use_gpu:
                    print("Optimiseur XGBoost configuré pour utiliser le GPU")
                else:
                    print("Optimiseur XGBoost configuré pour utiliser le CPU")
            except Exception as e:
                print(f"Erreur lors de l'initialisation de l'optimiseur XGBoost: {e}")
                self.xgboost_optimizer_available = False
