"""
Script pour modifier directement les fichiers de l'optimiseur XGBoost
Ce script modifie la méthode calculate_optimal_scale_pos_weight pour forcer des valeurs différentes.
"""

import os
import sys
import re
import json
import random
import shutil
from datetime import datetime

def backup_file(file_path):
    """
    Crée une sauvegarde du fichier
    
    Args:
        file_path: Chemin du fichier à sauvegarder
        
    Returns:
        str: Chemin de la sauvegarde
    """
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def generate_random_weights():
    """
    Génère des valeurs aléatoires de scale_pos_weight pour chaque numéro
    
    Returns:
        dict: Dictionnaire des valeurs par numéro
    """
    # Générer des valeurs aléatoires autour de 2.57
    values = {}
    for num in range(1, 71):
        # Générer une valeur entre 1.5 et 3.5
        value = 1.5 + random.random() * 2.0
        values[num] = round(value, 4)
    
    print("Valeurs de scale_pos_weight générées:")
    for num in range(1, 11):  # Afficher les 10 premières valeurs
        print(f"  Numéro {num}: {values[num]}")
    
    return values

def save_weights_to_json(weights):
    """
    Sauvegarde les poids dans un fichier JSON
    
    Args:
        weights: Dictionnaire des poids par numéro
        
    Returns:
        str: Chemin du fichier créé
    """
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Chemin du fichier
    file_path = os.path.join('data', 'direct_scale_pos_weight.json')
    
    # Convertir les clés en chaînes pour la sérialisation JSON
    weights_str = {str(k): v for k, v in weights.items()}
    
    # Sauvegarder les poids
    with open(file_path, 'w') as f:
        json.dump(weights_str, f, indent=2)
    
    print(f"Poids sauvegardés dans {file_path}")
    
    return file_path

def modify_optimizer_file(file_path, weights_file):
    """
    Modifie le fichier de l'optimiseur XGBoost
    
    Args:
        file_path: Chemin du fichier à modifier
        weights_file: Chemin du fichier de poids
        
    Returns:
        bool: True si la modification a réussi, False sinon
    """
    # Créer une sauvegarde
    backup_file(file_path)
    
    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Ajouter l'import du fichier de poids
    import_code = """import os
import json
import numpy as np
import pandas as pd"""
    
    weights_import_code = """import os
import json
import numpy as np
import pandas as pd

# Charger les poids scale_pos_weight spécifiques à chaque numéro
DIRECT_SPW_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'direct_scale_pos_weight.json')
try:
    with open(DIRECT_SPW_FILE, 'r') as f:
        DIRECT_SPW_VALUES = json.load(f)
    print(f"Valeurs de scale_pos_weight chargées depuis {DIRECT_SPW_FILE}")
except Exception as e:
    print(f"Erreur lors du chargement des valeurs de scale_pos_weight: {e}")
    DIRECT_SPW_VALUES = {}"""
    
    new_content = content.replace(import_code, weights_import_code)
    
    # Remplacer la méthode calculate_optimal_scale_pos_weight
    old_method = """    def calculate_optimal_scale_pos_weight(self, y_train):
        \"\"\"
        Calcule la valeur optimale de scale_pos_weight basée sur la distribution des classes
        
        Args:
            y_train: Étiquettes d'entraînement
            
        Returns:
            float: Valeur optimale de scale_pos_weight
        \"\"\"
        # Compter les exemples positifs et négatifs
        neg_count = np.sum(y_train == 0)
        pos_count = np.sum(y_train == 1)
        
        # Éviter la division par zéro
        if pos_count > 0:
            # Calculer le ratio
            ratio = neg_count / pos_count
            return ratio
        else:
            # Valeur par défaut si aucun exemple positif
            return 1.0"""
    
    new_method = """    def calculate_optimal_scale_pos_weight(self, y_train, num=None):
        \"\"\"
        Calcule la valeur optimale de scale_pos_weight basée sur la distribution des classes
        
        Args:
            y_train: Étiquettes d'entraînement
            num: Numéro Keno (pour utiliser une valeur spécifique)
            
        Returns:
            float: Valeur optimale de scale_pos_weight
        \"\"\"
        # Si un numéro est spécifié, utiliser la valeur spécifique
        if num is not None and str(num) in DIRECT_SPW_VALUES:
            specific_value = DIRECT_SPW_VALUES[str(num)]
            
            if self.verbose > 0:
                print(f"  Numéro {num}: Utilisation de scale_pos_weight spécifique = {specific_value}")
            
            return specific_value
        
        # Sinon, calculer normalement
        # Compter les exemples positifs et négatifs
        neg_count = np.sum(y_train == 0)
        pos_count = np.sum(y_train == 1)
        
        # Éviter la division par zéro
        if pos_count > 0:
            # Calculer le ratio
            ratio = neg_count / pos_count
            
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
                print(f"  Numéro {num}: Ratio calculé = {ratio:.4f}")
            
            return ratio
        else:
            # Valeur par défaut si aucun exemple positif
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Aucun exemple positif trouvé, utilisation de scale_pos_weight=1.0")
            
            return 1.0"""
    
    new_content = new_content.replace(old_method, new_method)
    
    # Modifier les appels à la méthode
    # Dans optimize_hyperparameters
    old_call_1 = "scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train)"
    new_call_1 = "scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train, num=num)"
    
    new_content = new_content.replace(old_call_1, new_call_1)
    
    # Dans train_model
    old_call_2 = "scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train)"
    new_call_2 = "scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train, num=num)"
    
    new_content = new_content.replace(old_call_2, new_call_2)
    
    # Écrire le contenu modifié
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Fichier {file_path} modifié avec succès")
    
    return True

def main():
    """Fonction principale"""
    print("Modification directe des fichiers de l'optimiseur XGBoost")
    
    # Générer des valeurs aléatoires
    weights = generate_random_weights()
    
    # Sauvegarder les poids dans un fichier JSON
    weights_file = save_weights_to_json(weights)
    
    # Modifier les fichiers de l'optimiseur
    files_to_modify = [
        'keno_xgboost_optimizer.py',
        'keno_xgboost_optimizer_simple.py'
    ]
    
    for file_path in files_to_modify:
        if os.path.exists(file_path):
            print(f"Modification du fichier {file_path}...")
            modify_optimizer_file(file_path, weights_file)
        else:
            print(f"Le fichier {file_path} n'existe pas")
    
    print("\nModification terminée!")
    print("Les fichiers de l'optimiseur XGBoost ont été modifiés pour utiliser des valeurs différentes de scale_pos_weight pour chaque numéro.")
    print("Veuillez redémarrer votre application pour que les modifications prennent effet.")

if __name__ == "__main__":
    main()
