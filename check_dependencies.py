"""
Module pour vérifier et installer les dépendances nécessaires
"""

import sys
import subprocess
import importlib.util
import os

def check_dependency(package_name):
    """
    Vérifie si un package est installé
    
    Args:
        package_name (str): Nom du package à vérifier
        
    Returns:
        bool: True si le package est installé, False sinon
    """
    spec = importlib.util.find_spec(package_name)
    return spec is not None

def install_package(package_name):
    """
    Installe un package avec pip
    
    Args:
        package_name (str): Nom du package à installer
        
    Returns:
        bool: True si l'installation a réussi, False sinon
    """
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def print_dependency_status():
    """
    Affiche le statut des dépendances et retourne True si toutes sont installées
    
    Returns:
        bool: True si toutes les dépendances sont installées, False sinon
    """
    # Liste des dépendances requises
    dependencies = {
        'numpy': 'numpy',
        'pandas': 'pandas',
        'matplotlib': 'matplotlib',
        'scikit-learn': 'sklearn',
        'tensorflow': 'tensorflow',
        'joblib': 'joblib',
        'tqdm': 'tqdm'
    }
    
    # Vérifier chaque dépendance
    all_installed = True
    missing = []
    
    print("Vérification des dépendances:")
    for package_name, import_name in dependencies.items():
        installed = check_dependency(import_name)
        status = "✓ Installé" if installed else "✗ Manquant"
        print(f"  {package_name}: {status}")
        
        if not installed:
            all_installed = False
            missing.append(package_name)
    
    # Afficher un résumé
    if all_installed:
        print("\nToutes les dépendances sont installées.")
    else:
        print(f"\nDépendances manquantes: {', '.join(missing)}")
    
    return all_installed

def install_dependencies():
    """
    Installe toutes les dépendances manquantes
    
    Returns:
        bool: True si toutes les installations ont réussi, False sinon
    """
    # Liste des dépendances requises
    dependencies = [
        'numpy',
        'pandas',
        'matplotlib',
        'scikit-learn',
        'tensorflow',
        'joblib',
        'tqdm'
    ]
    
    # Installer chaque dépendance manquante
    success = True
    
    print("Installation des dépendances manquantes:")
    for package_name in dependencies:
        if not check_dependency(package_name.split('==')[0].replace('-', '_')):
            print(f"  Installation de {package_name}...")
            if install_package(package_name):
                print(f"  ✓ {package_name} installé avec succès")
            else:
                print(f"  ✗ Échec de l'installation de {package_name}")
                success = False
    
    # Afficher un résumé
    if success:
        print("\nToutes les dépendances ont été installées avec succès.")
    else:
        print("\nCertaines dépendances n'ont pas pu être installées.")
    
    return success

if __name__ == "__main__":
    # Si le script est exécuté directement, vérifier les dépendances
    all_installed = print_dependency_status()
    
    # Proposer d'installer les dépendances manquantes
    if not all_installed:
        response = input("\nVoulez-vous installer les dépendances manquantes? (o/n): ")
        if response.lower() in ['o', 'oui', 'y', 'yes']:
            install_dependencies()
