#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module pour améliorer les prédictions Keno avec des techniques avancées
Ce module utilise des techniques avancées pour améliorer la précision des prédictions.
"""

import os
import sys
import numpy as np
import pandas as pd
import json
import time
import datetime
import random
from collections import Counter, defaultdict
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, roc_auc_score
from sklearn.ensemble import RandomForestClassifier, VotingClassifier
from sklearn.preprocessing import StandardScaler

# Ajouter le répertoire courant au chemin de recherche Python
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, BASE_DIR)

# Importer les modules nécessaires
from keno_data import KenoDataManager
from keno_advanced_analyzer import KenoAdvancedAnalyzer

# Supprimer les avertissements
import warnings
warnings.filterwarnings('ignore')

# Supprimer les avertissements de TensorFlow
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR

# Essayer d'importer TensorFlow
try:
    import tensorflow as tf
    # Désactiver les messages de log verbeux de TensorFlow
    tf.get_logger().setLevel('ERROR')
    # Désactiver les opérations oneDNN personnalisées
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("TensorFlow n'est pas disponible. Les fonctionnalités de deep learning seront désactivées.")

# Essayer d'importer XGBoost
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("XGBoost n'est pas disponible. Les fonctionnalités XGBoost seront désactivées.")

# Essayer d'importer LightGBM
try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("LightGBM n'est pas disponible. Les fonctionnalités LightGBM seront désactivées.")

class AdvancedPrediction:
    """Classe pour améliorer les prédictions Keno avec des techniques avancées"""

    def __init__(self, data_manager=None):
        """
        Initialise la classe AdvancedPrediction

        Args:
            data_manager (KenoDataManager, optional): Gestionnaire de données Keno
        """
        # Initialiser le gestionnaire de données
        if data_manager is None:
            self.data_manager = KenoDataManager()
            self._load_data()
        else:
            self.data_manager = data_manager

        # Initialiser l'analyseur avancé
        self.analyzer = KenoAdvancedAnalyzer(self.data_manager)

        # Initialiser les modèles
        self.models = {}
        self.ensemble_models = {}
        self.best_params = {}
        self.feature_importance = {}
        self.prediction_accuracy = {}

        # Initialiser les poids des numéros
        self.number_weights = self._calculate_number_weights()

        # Initialiser les patterns
        self.patterns = self._analyze_patterns()

    def _load_data(self):
        """Charge les données Keno"""
        # Essayer d'abord le fichier CSV
        csv_file = os.path.join(BASE_DIR, "data", "datafull.csv")
        if os.path.exists(csv_file):
            print(f"Chargement des données depuis le fichier CSV: {csv_file}")
            try:
                self.data_manager.load_csv(csv_file, clear_existing=True)
                return
            except Exception as e:
                print(f"Erreur lors du chargement du fichier CSV: {e}")

        # Essayer le fichier .keno
        data_file = os.path.join(BASE_DIR, "data", "datafull.keno")
        if os.path.exists(data_file):
            print(f"Chargement des données depuis le fichier .keno: {data_file}")
            try:
                self.data_manager.load_csv(data_file, clear_existing=True)
                return
            except Exception as e:
                print(f"Erreur lors du chargement du fichier .keno: {e}")

        # Créer des données factices
        print("Aucun fichier de données trouvé. Création de données factices...")
        self._create_dummy_data()

    def _create_dummy_data(self):
        """Crée des données factices pour permettre à l'application de démarrer"""
        print("Création de données factices pour permettre à l'application de démarrer...")

        # Générer 100 tirages aléatoires
        for i in range(1, 101):
            # Générer une date aléatoire dans les 6 derniers mois
            days_ago = random.randint(1, 180)
            date = (datetime.datetime.now() - datetime.timedelta(days=days_ago)).strftime('%Y-%m-%d %H:%M:%S')

            # Générer un numéro de tirage
            draw_number = 2023000 + i

            # Générer 20 numéros aléatoires entre 1 et 70
            numbers = [random.randint(1, 70) for _ in range(20)]

            # Générer un multiplicateur aléatoire
            multiplier = random.choice([1, 2, 3, 5, 10])

            # Créer le tirage
            draw = {
                'date': date,
                'draw_number': draw_number,
                'numbers': numbers,
                'multiplier': multiplier
            }

            # Ajouter le tirage
            self.data_manager.add_draw(draw)

        print(f"Données factices créées: {len(self.data_manager.draws)} tirages")

        # Sauvegarder les données
        csv_file = os.path.join(BASE_DIR, "data", "datafull.csv")
        self.data_manager.save_csv(csv_file)
        print(f"Données factices sauvegardées dans {csv_file}")

    def _calculate_number_weights(self):
        """
        Calcule les poids des numéros en fonction de leur fréquence d'apparition

        Returns:
            dict: Dictionnaire des poids des numéros
        """
        print("Calcul des poids des numéros...")

        # Compter la fréquence de chaque numéro
        number_counts = Counter()
        for draw in self.data_manager.draws:
            if 'numbers' in draw:
                number_counts.update(draw['numbers'])

        # Calculer les poids en fonction de la fréquence
        total_draws = len(self.data_manager.draws)
        weights = {}
        for num in range(1, self.data_manager.max_number + 1):
            count = number_counts.get(num, 0)
            frequency = count / total_draws if total_draws > 0 else 0
            weights[num] = frequency

        # Normaliser les poids entre 0.1 et 1.0
        min_weight = min(weights.values()) if weights else 0
        max_weight = max(weights.values()) if weights else 1
        normalized_weights = {}
        for num, weight in weights.items():
            # Normaliser entre 0.1 et 1.0
            if max_weight > min_weight:
                normalized_weight = 0.1 + 0.9 * (weight - min_weight) / (max_weight - min_weight)
            else:
                normalized_weight = 0.5  # Valeur par défaut si tous les poids sont égaux
            normalized_weights[num] = normalized_weight

        return normalized_weights

    def _analyze_patterns(self):
        """
        Analyse les patterns dans les tirages Keno

        Returns:
            dict: Dictionnaire des patterns identifiés
        """
        print("Analyse des patterns dans les tirages Keno...")

        patterns = {
            'common_numbers': defaultdict(int),  # Nombre de numéros communs entre tirages consécutifs
            'position_patterns': defaultdict(int),  # Patterns de position des numéros communs
            'evening_noon_patterns': defaultdict(int),  # Patterns entre tirages du soir et du midi
            'noon_evening_patterns': defaultdict(int),  # Patterns entre tirages du midi et du soir
            'evening_evening_patterns': defaultdict(int),  # Patterns entre tirages du soir consécutifs
            'noon_noon_patterns': defaultdict(int),  # Patterns entre tirages du midi consécutifs
            '48h_patterns': defaultdict(int),  # Patterns sur 48 heures
        }

        # Trier les tirages par date
        sorted_draws = sorted(self.data_manager.draws, key=lambda x: x.get('date', ''))

        # Analyser les tirages consécutifs
        for i in range(1, len(sorted_draws)):
            prev_draw = sorted_draws[i-1]
            curr_draw = sorted_draws[i]

            # Vérifier que les deux tirages ont des numéros
            if 'numbers' not in prev_draw or 'numbers' not in curr_draw:
                continue

            # Obtenir les numéros des tirages
            prev_numbers = set(prev_draw['numbers'])
            curr_numbers = set(curr_draw['numbers'])

            # Calculer les numéros communs
            common = prev_numbers.intersection(curr_numbers)
            patterns['common_numbers'][len(common)] += 1

        return patterns

    def prepare_features_for_number(self, num):
        """
        Prépare les caractéristiques pour un numéro donné

        Args:
            num (int): Numéro à prédire

        Returns:
            tuple: (X, y) où X est la matrice de caractéristiques et y est le vecteur cible
        """
        print(f"Préparation des caractéristiques pour le numéro {num}...")

        # Vérifier que le numéro est valide
        if num < 1 or num > self.data_manager.max_number:
            print(f"Numéro invalide: {num} (doit être entre 1 et {self.data_manager.max_number})")
            return None, None

        # Obtenir les tirages
        draws = self.data_manager.draws

        # Vérifier qu'il y a suffisamment de données
        if len(draws) < 10:
            print(f"Pas assez de données pour l'analyse (minimum 10 tirages)")
            return None, None

        # Préparer les données
        X = []
        y = []

        # Utiliser une fenêtre glissante pour créer les caractéristiques
        window_size = 10  # Utiliser les 10 derniers tirages pour prédire le prochain

        for i in range(window_size, len(draws)):
            # Obtenir les tirages précédents
            prev_draws = draws[i-window_size:i]

            # Obtenir le tirage actuel
            current_draw = draws[i]

            # Vérifier que les tirages ont des numéros
            if 'numbers' not in current_draw:
                continue

            # Créer les caractéristiques
            features = []

            # Caractéristique 1: Nombre de fois où le numéro est apparu dans la fenêtre
            count = sum(1 for draw in prev_draws if 'numbers' in draw and num in draw['numbers'])
            features.append(count / window_size)

            # Caractéristique 2: Nombre de tirages depuis la dernière apparition du numéro
            last_seen = 0
            for j in range(i-1, i-window_size-1, -1):
                if j >= 0 and 'numbers' in draws[j] and num in draws[j]['numbers']:
                    break
                last_seen += 1
            features.append(last_seen / window_size)

            # Caractéristique 3: Poids du numéro
            features.append(self.number_weights.get(num, 0.5))

            # Caractéristique 4: Nombre moyen de numéros communs entre tirages consécutifs
            common_counts = []
            for j in range(i-window_size, i-1):
                if j >= 0 and j+1 < len(draws) and 'numbers' in draws[j] and 'numbers' in draws[j+1]:
                    common = set(draws[j]['numbers']).intersection(set(draws[j+1]['numbers']))
                    common_counts.append(len(common))
            avg_common = sum(common_counts) / len(common_counts) if common_counts else 0
            features.append(avg_common / 20)  # Normaliser par le nombre de numéros par tirage

            # Ajouter les caractéristiques
            X.append(features)

            # Ajouter la cible (1 si le numéro est dans le tirage actuel, 0 sinon)
            y.append(1 if num in current_draw['numbers'] else 0)

        # Convertir en tableaux numpy
        X = np.array(X)
        y = np.array(y)

        return X, y

    def prepare_features_for_prediction(self, num):
        """
        Prépare les caractéristiques pour la prédiction d'un numéro donné

        Args:
            num (int): Numéro à prédire

        Returns:
            numpy.ndarray: Matrice de caractéristiques pour la prédiction
        """
        print(f"Préparation des caractéristiques pour la prédiction du numéro {num}...")

        # Vérifier que le numéro est valide
        if num < 1 or num > self.data_manager.max_number:
            print(f"Numéro invalide: {num} (doit être entre 1 et {self.data_manager.max_number})")
            return None

        # Obtenir les tirages
        draws = self.data_manager.draws

        # Vérifier qu'il y a suffisamment de données
        if len(draws) < 10:
            print(f"Pas assez de données pour la prédiction (minimum 10 tirages)")
            return None

        # Utiliser les 10 derniers tirages pour la prédiction
        window_size = 10
        prev_draws = draws[-window_size:]

        # Créer les caractéristiques
        features = []

        # Caractéristique 1: Nombre de fois où le numéro est apparu dans la fenêtre
        count = sum(1 for draw in prev_draws if 'numbers' in draw and num in draw['numbers'])
        features.append(count / window_size)

        # Caractéristique 2: Nombre de tirages depuis la dernière apparition du numéro
        last_seen = 0
        for j in range(len(draws)-1, len(draws)-window_size-1, -1):
            if j >= 0 and 'numbers' in draws[j] and num in draws[j]['numbers']:
                break
            last_seen += 1
        features.append(last_seen / window_size)

        # Caractéristique 3: Poids du numéro
        features.append(self.number_weights.get(num, 0.5))

        # Caractéristique 4: Nombre moyen de numéros communs entre tirages consécutifs
        common_counts = []
        for j in range(len(draws)-window_size, len(draws)-1):
            if j >= 0 and j+1 < len(draws) and 'numbers' in draws[j] and 'numbers' in draws[j+1]:
                common = set(draws[j]['numbers']).intersection(set(draws[j+1]['numbers']))
                common_counts.append(len(common))
        avg_common = sum(common_counts) / len(common_counts) if common_counts else 0
        features.append(avg_common / 20)  # Normaliser par le nombre de numéros par tirage

        # Convertir en tableau numpy
        features = np.array([features])

        return features

    def train_ensemble_model(self, num):
        """
        Entraîne un modèle d'ensemble pour un numéro donné

        Args:
            num (int): Numéro à prédire

        Returns:
            object: Modèle d'ensemble entraîné
        """
        print(f"Entraînement du modèle d'ensemble pour le numéro {num}...")

        # Préparer les données
        X, y = self.prepare_features_for_number(num)
        if X is None or y is None:
            print(f"Pas assez de données pour entraîner le modèle pour le numéro {num}")
            return None

        # Diviser les données en ensembles d'entraînement et de test
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # Créer les modèles individuels
        models = []

        # Ajouter RandomForest
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        models.append(('rf', rf))

        # Ajouter XGBoost si disponible
        if XGBOOST_AVAILABLE:
            xgb_model = xgb.XGBClassifier(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=5,
                random_state=42,
                scale_pos_weight=2.0
            )
            models.append(('xgb', xgb_model))

        # Ajouter LightGBM si disponible
        if LIGHTGBM_AVAILABLE:
            lgb_model = lgb.LGBMClassifier(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=5,
                random_state=42,
                scale_pos_weight=2.0
            )
            models.append(('lgb', lgb_model))

        # Créer le modèle d'ensemble
        ensemble = VotingClassifier(estimators=models, voting='soft')

        # Entraîner le modèle
        ensemble.fit(X_train, y_train)

        # Évaluer le modèle
        y_pred = ensemble.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        precision, recall, f1, _ = precision_recall_fscore_support(y_test, y_pred, average='binary')

        print(f"Modèle d'ensemble pour le numéro {num}:")
        print(f"  Accuracy: {accuracy:.4f}")
        print(f"  Precision: {precision:.4f}")
        print(f"  Recall: {recall:.4f}")
        print(f"  F1-score: {f1:.4f}")

        # Sauvegarder le modèle
        self.ensemble_models[num] = ensemble
        self.prediction_accuracy[num] = accuracy

        return ensemble

    def predict_next_draw(self, num_predictions=10):
        """
        Prédit les numéros du prochain tirage

        Args:
            num_predictions (int): Nombre de numéros à prédire

        Returns:
            list: Liste des numéros prédits
        """
        print(f"Prédiction des {num_predictions} numéros du prochain tirage...")

        # Vérifier si les modèles d'ensemble sont disponibles
        if not self.ensemble_models:
            print("Aucun modèle d'ensemble disponible. Entraînement des modèles...")
            for num in range(1, self.data_manager.max_number + 1):
                self.train_ensemble_model(num)

        # Préparer les données pour la prédiction
        predictions = {}

        # Si aucun modèle n'a été entraîné, utiliser les poids des numéros
        if not self.ensemble_models:
            print("Aucun modèle n'a pu être entraîné. Utilisation des poids des numéros pour la prédiction...")

            # Utiliser les poids des numéros comme prédictions
            for num in range(1, self.data_manager.max_number + 1):
                weight = self.number_weights.get(num, 0.5)
                predictions[num] = weight
        else:
            # Utiliser les modèles entraînés pour la prédiction
            for num in range(1, self.data_manager.max_number + 1):
                if num in self.ensemble_models:
                    # Préparer les caractéristiques pour ce numéro
                    try:
                        X = self.prepare_features_for_prediction(num)
                        if X is not None:
                            # Prédire la probabilité
                            try:
                                prob = self.ensemble_models[num].predict_proba(X)[0][1]
                                # Ajuster la probabilité en fonction du poids du numéro
                                weight = self.number_weights.get(num, 0.5)
                                adjusted_prob = prob * weight
                                predictions[num] = adjusted_prob
                            except Exception as e:
                                print(f"Erreur lors de la prédiction pour le numéro {num}: {e}")
                                # Utiliser le poids du numéro comme prédiction de secours
                                predictions[num] = self.number_weights.get(num, 0.5)
                    except Exception as e:
                        print(f"Erreur lors de la préparation des caractéristiques pour le numéro {num}: {e}")
                        # Utiliser le poids du numéro comme prédiction de secours
                        predictions[num] = self.number_weights.get(num, 0.5)
                else:
                    # Utiliser le poids du numéro comme prédiction de secours
                    weight = self.number_weights.get(num, 0.5)
                    predictions[num] = weight

        # Si aucune prédiction n'a été faite, générer des numéros aléatoires
        if not predictions:
            print("Aucune prédiction n'a pu être faite. Génération de numéros aléatoires...")
            import random
            predicted_numbers = random.sample(range(1, self.data_manager.max_number + 1), num_predictions)
            print(f"Numéros générés aléatoirement: {predicted_numbers}")
            return predicted_numbers

        # Trier les prédictions par probabilité décroissante
        sorted_predictions = sorted(predictions.items(), key=lambda x: x[1], reverse=True)

        # Sélectionner les numéros avec les probabilités les plus élevées
        predicted_numbers = [num for num, _ in sorted_predictions[:num_predictions]]

        print(f"Numéros prédits: {predicted_numbers}")
        return predicted_numbers

# Fonction principale
def main():
    """Fonction principale"""
    # Créer l'objet AdvancedPrediction
    predictor = AdvancedPrediction()

    # Entraîner les modèles d'ensemble pour tous les numéros
    for num in range(1, predictor.data_manager.max_number + 1):
        predictor.train_ensemble_model(num)

    # Prédire les numéros du prochain tirage
    predicted_numbers = predictor.predict_next_draw(num_predictions=10)

    print(f"Numéros prédits pour le prochain tirage: {predicted_numbers}")

if __name__ == "__main__":
    main()
