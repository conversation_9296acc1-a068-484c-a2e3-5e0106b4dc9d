# Guide des améliorations pour la prédiction et l'auto-amélioration

Ce document explique les améliorations apportées aux fonctionnalités de prédiction et d'auto-amélioration de l'application Keno.

## Nouvelles fonctionnalités

### 1. Optimisation de l'apprentissage automatique

- **Utilisation du GPU** : L'application détecte et utilise automatiquement le GPU si disponible pour accélérer l'entraînement des modèles.
- **Caractéristiques temporelles avancées** : Ajout de transformations cycliques pour les caractéristiques temporelles (jour, mois, heure).
- **Diversification des algorithmes** : Utilisation de plusieurs algorithmes (Random Forest, Gradient Boosting, XGBoost, LightGBM) et sélection du meilleur pour chaque numéro.
- **Paramètres optimisés** : Ajustement des hyperparamètres pour chaque algorithme.
- **Statistiques détaillées** : Affichage des statistiques d'entraînement (précision, temps, types de modèles).

### 2. Amélioration des prédictions

- **Pondération intelligente** : Augmentation du poids des modèles ML qui sont généralement plus précis.
- **Scores de fréquence** : Prise en compte des fréquences d'apparition récentes des numéros.
- **Correction d'équilibre** : Ajustement automatique pour éviter les biais (trop de pairs/impairs, trop de hauts/bas).
- **Logging amélioré** : Affichage d'informations détaillées sur le processus de prédiction.

### 3. Sauvegarde et chargement des données d'apprentissage

- **Sauvegarde automatique** : Les données d'apprentissage sont sauvegardées automatiquement après chaque session d'auto-amélioration.
- **Chargement au démarrage** : Les données d'apprentissage sont chargées automatiquement au démarrage de l'application.

## Comment utiliser ces fonctionnalités

### Auto-amélioration

1. Cliquez sur le bouton "LANCER L'AUTO-AMÉLIORATION" dans l'interface.
2. L'application va :
   - Préparer les données
   - Analyser les motifs
   - Analyser les corrélations
   - Analyser les séries
   - Entraîner les modèles avec différentes configurations
   - Valider les modèles
   - Sauvegarder les modèles et les données d'apprentissage

3. Une fois l'auto-amélioration terminée, vous verrez un résumé des résultats, notamment :
   - Le nombre de modèles entraînés
   - Le taux de réussite
   - Les types de modèles utilisés
   - La précision moyenne

### Prédiction

Pour obtenir les meilleures prédictions :

1. Utilisez la méthode "Analyse avancée combinée" dans le menu déroulant des méthodes de prédiction.
2. Cette méthode combine :
   - Les prédictions des modèles d'apprentissage automatique
   - L'analyse des motifs
   - L'analyse des séries
   - Les scores de fréquence
   - La correction d'équilibre

## Dépendances requises

Pour utiliser toutes les fonctionnalités d'apprentissage automatique et de prédiction, les modules suivants doivent être installés :

- numpy
- pandas
- scikit-learn
- tensorflow (pour l'accélération GPU)
- xgboost (optionnel, pour des prédictions plus précises)
- lightgbm (optionnel, pour des prédictions plus précises)
- joblib (pour la sauvegarde des modèles)

Vous pouvez installer ces dépendances avec la commande :

```
pip install numpy pandas scikit-learn tensorflow xgboost lightgbm joblib
```

## Conseils pour de meilleures performances

1. **Données suffisantes** : Assurez-vous d'avoir au moins 100 tirages pour obtenir des prédictions fiables.

2. **Entraînement régulier** : Lancez l'auto-amélioration régulièrement, surtout après avoir ajouté de nouveaux tirages.

3. **Utilisation du GPU** : Si vous disposez d'une carte graphique compatible CUDA, l'entraînement sera beaucoup plus rapide.

4. **Évaluation des méthodes** : Comparez les résultats des différentes méthodes de prédiction pour trouver celle qui fonctionne le mieux avec vos données.

5. **Combinaison de méthodes** : La méthode "Analyse avancée combinée" donne généralement les meilleurs résultats car elle combine plusieurs approches.

## Résolution des problèmes

Si vous rencontrez des problèmes avec l'auto-amélioration ou les prédictions :

1. **Vérifiez les dépendances** : Assurez-vous que tous les modules nécessaires sont installés.

2. **Données insuffisantes** : Si vous avez moins de 50 tirages, l'auto-amélioration ne fonctionnera pas correctement.

3. **Erreurs d'entraînement** : Si vous voyez des erreurs lors de l'entraînement, essayez de relancer l'auto-amélioration.

4. **Problèmes de GPU** : Si vous rencontrez des erreurs liées au GPU, vous pouvez désactiver l'utilisation du GPU en modifiant le code dans `keno_advanced_analyzer.py`.

5. **Fichiers de données corrompus** : Si les fichiers de données d'apprentissage sont corrompus, supprimez les fichiers `keno_ml_models.pkl` et `keno_learning_data.json` et relancez l'auto-amélioration.
