# Guide d'utilisation du système d'auto-amélioration optimisé pour Keno

## Vue d'ensemble

Ce système d'auto-amélioration optimisé utilise des techniques avancées de machine learning pour améliorer significativement la précision des prédictions Keno. Il intègre :

- **Feature Engineering avancé** : Création de caractéristiques sophistiquées
- **Ensemble Learning** : Combinaison de plusieurs modèles (Random Forest, XGBoost, LightGBM)
- **Optimisation automatique** : Hyperparamètres optimisés pour chaque numéro
- **Sélection de caractéristiques** : Identification automatique des features les plus importantes
- **Scoring de confiance** : Évaluation de la fiabilité des prédictions

## Améliorations par rapport au système existant

### 1. Caractéristiques avancées
- **Analyse des gaps** : Patterns d'intervalles entre apparitions
- **Momentum analysis** : Tendances à court et moyen terme
- **Co-occurrence patterns** : Relations entre numéros
- **Stabilité temporelle** : Variance des comportements
- **Features cycliques** : Patterns temporels (jour, heure, mois)

### 2. Modèles optimisés
- **Random Forest** avec hyperparamètres optimisés
- **XGBoost** avec scale_pos_weight adaptatif
- **LightGBM** pour la rapidité
- **Ensemble Voting** combinant les meilleurs modèles

### 3. Stratégies de prédiction
- **Scoring de confiance** basé sur la performance historique
- **Diversification** pour éviter la sur-concentration
- **Facteurs de stabilité** pour ajuster les prédictions

## Installation et intégration

### Étape 1 : Vérifier les dépendances

```bash
python test_dependencies.py
```

Assurez-vous que ces packages sont installés :
- scikit-learn
- xgboost
- lightgbm
- numpy
- pandas

### Étape 2 : Intégrer le système

```bash
python integrate_enhanced_system.py
```

Ce script :
- Charge vos données existantes
- Intègre le système optimisé
- Lance un test d'entraînement
- Génère des prédictions de test

### Étape 3 : Patcher l'interface (optionnel)

```bash
python patch_keno_gui.py
```

Cela ajoute un bouton "Auto-Amélioration OPTIMISÉE" à votre interface.

## Utilisation

### Mode d'entraînement

1. **Rapide** (5-10 minutes)
   - Entraîne 10 numéros représentatifs
   - Idéal pour tester le système
   - Performance : Bonne

2. **Moyen** (20-30 minutes)
   - Entraîne 35 numéros
   - Bon compromis temps/performance
   - Performance : Très bonne

3. **Complet** (1-2 heures)
   - Entraîne tous les 70 numéros
   - Performance maximale
   - Recommandé pour la production

### Utilisation programmatique

```python
from keno_enhanced_auto_improve import KenoEnhancedAutoImprove, integrate_enhanced_auto_improve
from keno_data_manager import KenoDataManager
from keno_analyzer import KenoAnalyzer

# Initialiser
data_manager = KenoDataManager()
analyzer = KenoAnalyzer(data_manager)

# Charger les données
data_manager.load_database("path/to/your/data.json")

# Intégrer le système optimisé
enhanced_improver = integrate_enhanced_auto_improve(analyzer)

# Lancer l'entraînement
results = enhanced_improver.run_enhanced_auto_improve()

# Générer des prédictions
predictions = enhanced_improver.predict_next_draw_enhanced(10)
print(f"Prédictions optimisées: {predictions}")
```

## Métriques de performance

Le système utilise plusieurs métriques pour évaluer la performance :

- **F1-Score** : Métrique principale (équilibre précision/rappel)
- **Accuracy** : Précision globale
- **Precision** : Taux de vrais positifs
- **Recall** : Taux de détection
- **AUC** : Aire sous la courbe ROC

### Objectifs de performance

- **F1-Score cible** : > 0.3 (excellent pour Keno)
- **Accuracy cible** : > 0.75
- **Amélioration attendue** : +50% par rapport au système de base

## Stratégies d'optimisation

### 1. Feature Engineering
- **Fenêtres temporelles** : 5, 10, 20, 50, 100 tirages
- **Analyse des gaps** : Moyenne, variance, tendance
- **Momentum** : Court terme (5), moyen terme (20)
- **Co-occurrence** : Relations avec autres numéros

### 2. Sélection de modèles
- **Validation croisée** : 5 folds
- **Optimisation hyperparamètres** : RandomizedSearchCV
- **Ensemble voting** : Combinaison des meilleurs modèles

### 3. Post-processing
- **Facteurs de confiance** : Basés sur la performance historique
- **Diversification** : Évite la concentration sur des numéros proches
- **Stabilité** : Ajustement selon la variance historique

## Conseils d'utilisation

### Pour de meilleures prédictions

1. **Données suffisantes** : Minimum 500 tirages, idéal 1000+
2. **Entraînement régulier** : Relancer l'entraînement chaque semaine
3. **Mode complet** : Utiliser le mode complet pour la production
4. **Validation** : Comparer les prédictions avec les résultats réels

### Interprétation des résultats

- **Probabilité > 0.4** : Prédiction très forte
- **Probabilité 0.3-0.4** : Prédiction forte
- **Probabilité 0.2-0.3** : Prédiction modérée
- **Confiance > 1.2** : Modèle très fiable pour ce numéro

## Dépannage

### Problèmes courants

1. **"Pas assez de données"**
   - Solution : Télécharger plus de données historiques
   - Minimum requis : 100 tirages par numéro

2. **"Modèle non disponible"**
   - Solution : Relancer l'entraînement
   - Vérifier les dépendances ML

3. **Performance faible**
   - Solution : Utiliser le mode complet
   - Vérifier la qualité des données

### Optimisation des performances

1. **Utiliser un SSD** pour les données
2. **RAM suffisante** : Minimum 8GB, idéal 16GB+
3. **CPU multi-core** : Le système utilise le parallélisme
4. **GPU optionnel** : XGBoost peut utiliser CUDA

## Fichiers générés

- `enhanced_auto_improve_summary.json` : Résumé de l'entraînement
- `predictions_enhanced_YYYYMMDD_HHMMSS.json` : Prédictions horodatées
- `backup_YYYYMMDD_HHMMSS/` : Sauvegarde des fichiers originaux

## Support et maintenance

### Mise à jour du système

1. Sauvegarder vos données
2. Télécharger la nouvelle version
3. Relancer l'intégration
4. Tester avec vos données

### Monitoring des performances

- Suivre le F1-score moyen
- Comparer avec les résultats réels
- Ajuster la fréquence d'entraînement

## Conclusion

Ce système d'auto-amélioration optimisé représente une évolution majeure par rapport aux méthodes traditionnelles. En combinant feature engineering avancé, ensemble learning et optimisation automatique, il vise à améliorer significativement la précision des prédictions Keno.

L'objectif est de passer de scores de 3/7 ou 4/10 à des scores de 7/7 ou 10/10 grâce à une approche scientifique rigoureuse et des algorithmes de pointe.
