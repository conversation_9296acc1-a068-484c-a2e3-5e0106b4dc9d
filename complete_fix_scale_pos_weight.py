"""
Solution complète pour le problème de scale_pos_weight identique dans l'application Keno.
Ce script analyse et corrige le problème à sa racine.
"""

import os
import sys
import json
import random
import shutil
import numpy as np
import pandas as pd
from datetime import datetime

def backup_file(file_path):
    """
    Crée une sauvegarde du fichier

    Args:
        file_path: Chemin du fichier à sauvegarder

    Returns:
        str: Chemin de la sauvegarde
    """
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def generate_random_weights():
    """
    Génère des valeurs aléatoires de scale_pos_weight pour chaque numéro

    Returns:
        dict: Dictionnaire des valeurs par numéro
    """
    # Générer des valeurs aléatoires autour de 2.57
    values = {}
    for num in range(1, 71):
        # Générer une valeur entre 1.5 et 3.5
        value = 1.5 + random.random() * 2.0
        values[num] = round(value, 4)

    print("Valeurs de scale_pos_weight générées:")
    for num in range(1, 11):  # Afficher les 10 premières valeurs
        print(f"  Numéro {num}: {values[num]}")

    return values

def save_weights_to_json(weights, file_name='complete_fix_spw.json'):
    """
    Sauvegarde les poids dans un fichier JSON

    Args:
        weights: Dictionnaire des poids par numéro
        file_name: Nom du fichier de sortie

    Returns:
        str: Chemin du fichier créé
    """
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)

    # Chemin du fichier
    file_path = os.path.join('data', file_name)

    # Convertir les clés en chaînes pour la sérialisation JSON
    weights_str = {str(k): v for k, v in weights.items()}

    # Sauvegarder les poids
    with open(file_path, 'w') as f:
        json.dump(weights_str, f, indent=2)

    print(f"Poids sauvegardés dans {file_path}")

    return file_path

def fix_keno_advanced_analyzer():
    """
    Corrige le fichier keno_advanced_analyzer.py

    Returns:
        bool: True si la correction a réussi, False sinon
    """
    file_path = 'keno_advanced_analyzer.py'

    if not os.path.exists(file_path):
        print(f"Le fichier {file_path} n'existe pas")
        return False

    # Créer une sauvegarde
    backup_file(file_path)

    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Vérifier si le fichier contient déjà notre correction
    if "# Correction pour le problème de scale_pos_weight identique" in content:
        print(f"Le fichier {file_path} a déjà été corrigé")
        return True

    # Ajouter un attribut pour stocker le numéro courant
    class_init_end = content.find("def __init__(self, data_manager):")
    if class_init_end == -1:
        print("Méthode __init__ non trouvée dans keno_advanced_analyzer.py")
        return False

    # Trouver la fin de la méthode __init__
    next_method = content.find("def ", class_init_end + 10)
    if next_method == -1:
        print("Fin de la méthode __init__ non trouvée")
        return False

    # Ajouter l'attribut current_number
    init_code = content[class_init_end:next_method]
    new_init_code = init_code.replace("self.npu_available = False", "self.npu_available = False\n        self.current_number = None  # Numéro Keno courant pour scale_pos_weight")

    # Remplacer l'ancienne méthode __init__ par la nouvelle
    content = content.replace(init_code, new_init_code)

    # Ajouter une méthode pour définir le numéro courant
    set_number_method = """
    def set_current_number(self, num):
        """Définit le numéro Keno courant pour scale_pos_weight

        Args:
            num (int): Numéro Keno

        Returns:
            int: Numéro Keno défini
        """
        self.current_number = num
        print(f"Numéro Keno courant défini à {num}")
        return num

"""

    # Insérer la méthode après la méthode set_hardware_acceleration
    set_hardware_end = content.find("def set_hardware_acceleration(self, mode='auto'):")
    if set_hardware_end == -1:
        print("Méthode set_hardware_acceleration non trouvée")
        # Insérer après la méthode set_max_workers
        set_hardware_end = content.find("def set_max_workers(self, max_workers):")
        if set_hardware_end == -1:
            print("Méthode set_max_workers non trouvée")
            # Insérer après la méthode set_cpu_cores
            set_hardware_end = content.find("def set_cpu_cores(self, n_jobs):")
            if set_hardware_end == -1:
                print("Méthode set_cpu_cores non trouvée")
                # Insérer après la méthode __init__
                set_hardware_end = next_method

    # Trouver la fin de la méthode
    next_method = content.find("def ", set_hardware_end + 10)
    if next_method == -1:
        print("Fin de la méthode set_hardware_acceleration non trouvée")
        return False

    # Insérer la méthode
    content = content[:next_method] + set_number_method + content[next_method:]

    # Modifier la méthode train_models pour définir le numéro courant
    train_models_start = content.find("def train_models(self, ")
    if train_models_start == -1:
        print("Méthode train_models non trouvée")
        return False

    # Trouver le début du corps de la méthode
    train_models_body = content.find(":", train_models_start)
    if train_models_body == -1:
        print("Corps de la méthode train_models non trouvé")
        return False

    # Ajouter le code pour définir le numéro courant
    train_models_code = content[train_models_start:train_models_body+1]
    new_train_models_code = train_models_code + """
        # Correction pour le problème de scale_pos_weight identique
        # Définir le numéro courant pour scale_pos_weight
        self.set_current_number(num)
"""

    # Remplacer l'ancienne méthode train_models par la nouvelle
    content = content.replace(train_models_code, new_train_models_code)

    # Écrire le contenu modifié
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"Fichier {file_path} corrigé avec succès")
    return True

def fix_xgboost_optimizer():
    """
    Corrige les fichiers d'optimisation XGBoost

    Returns:
        bool: True si la correction a réussi, False sinon
    """
    # Générer des valeurs aléatoires
    weights = generate_random_weights()

    # Sauvegarder les poids dans un fichier JSON
    weights_file = save_weights_to_json(weights)

    # Fichiers à corriger
    files_to_fix = [
        'keno_xgboost_optimizer.py',
        'keno_xgboost_optimizer_simple.py'
    ]

    success = True

    for file_path in files_to_fix:
        if not os.path.exists(file_path):
            print(f"Le fichier {file_path} n'existe pas")
            continue

        # Créer une sauvegarde
        backup_file(file_path)

        # Lire le contenu du fichier
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Vérifier si le fichier contient déjà notre correction
        if "# Correction pour le problème de scale_pos_weight identique" in content:
            print(f"Le fichier {file_path} a déjà été corrigé")
            continue

        # Modifier la méthode calculate_optimal_scale_pos_weight
        calculate_method_start = content.find("def calculate_optimal_scale_pos_weight(self, y_train")
        if calculate_method_start == -1:
            print(f"Méthode calculate_optimal_scale_pos_weight non trouvée dans {file_path}")
            success = False
            continue

        # Trouver la fin de la méthode
        next_method = content.find("def ", calculate_method_start + 10)
        if next_method == -1:
            print(f"Fin de la méthode calculate_optimal_scale_pos_weight non trouvée dans {file_path}")
            success = False
            continue

        # Extraire la méthode
        calculate_method = content[calculate_method_start:next_method]

        # Modifier la méthode pour utiliser le fichier JSON
        new_calculate_method = """def calculate_optimal_scale_pos_weight(self, y_train, num=None):
        """
        Calcule la valeur optimale de scale_pos_weight basée sur la distribution des classes

        Args:
            y_train: Étiquettes d'entraînement
            num: Numéro Keno (pour utiliser une valeur spécifique)

        Returns:
            float: Valeur optimale de scale_pos_weight
        """
        # Correction pour le problème de scale_pos_weight identique
        # Charger les valeurs depuis le fichier JSON
        spw_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'complete_fix_spw.json')
        try:
            with open(spw_file, 'r') as f:
                spw_values = json.load(f)

            # Si un numéro est spécifié, utiliser la valeur spécifique
            if num is not None and str(num) in spw_values:
                specific_value = float(spw_values[str(num)])

                if self.verbose > 0:
                    print(f"  Numéro {num}: Utilisation de scale_pos_weight spécifique = {specific_value}")

                return specific_value
        except Exception as e:
            if self.verbose > 0:
                print(f"  Erreur lors du chargement des valeurs de scale_pos_weight: {e}")

        # Sinon, calculer normalement
        # Compter les exemples positifs et négatifs
        neg_count = np.sum(y_train == 0)
        pos_count = np.sum(y_train == 1)

        # Éviter la division par zéro
        if pos_count > 0:
            # Calculer le ratio
            ratio = neg_count / pos_count

            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
                print(f"  Numéro {num}: Ratio calculé = {ratio:.4f}")

            return ratio
        else:
            # Valeur par défaut si aucun exemple positif
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Aucun exemple positif trouvé, utilisation de scale_pos_weight=1.0")

            return 1.0
"""

        # Remplacer l'ancienne méthode par la nouvelle
        content = content.replace(calculate_method, new_calculate_method)

        # Écrire le contenu modifié
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"Fichier {file_path} corrigé avec succès")

    return success

def create_auto_improve_hook():
    """
    Crée un script d'accroche pour l'auto-amélioration

    Returns:
        str: Chemin du script créé
    """
    script_content = """\"\"\"
Script d'accroche pour l'auto-amélioration
Ce script doit être importé au début de votre application pour garantir
que chaque numéro utilise une valeur différente de scale_pos_weight.
\"\"\""""

import os
import sys
import json
import random
import numpy as np
from functools import wraps

# Chemin du fichier de valeurs de scale_pos_weight
SPW_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'complete_fix_spw.json')

# Vérifier si le fichier existe, sinon le créer
if not os.path.exists(SPW_FILE):
    print(f"Le fichier {SPW_FILE} n'existe pas, création...")

    # Créer le répertoire data s'il n'existe pas
    os.makedirs(os.path.dirname(SPW_FILE), exist_ok=True)

    # Générer des valeurs aléatoires
    values = {}
    for num in range(1, 71):
        # Générer une valeur entre 1.5 et 3.5
        value = 1.5 + random.random() * 2.0
        values[num] = round(value, 4)

    # Convertir les clés en chaînes pour la sérialisation JSON
    values_str = {str(k): v for k, v in values.items()}

    # Sauvegarder les valeurs
    with open(SPW_FILE, 'w') as f:
        json.dump(values_str, f, indent=2)

    print(f"Valeurs de scale_pos_weight générées et sauvegardées dans {SPW_FILE}")

# Charger les valeurs
try:
    with open(SPW_FILE, 'r') as f:
        SPW_VALUES = json.load(f)
    print(f"Valeurs de scale_pos_weight chargées depuis {SPW_FILE}")
except Exception as e:
    print(f"Erreur lors du chargement des valeurs de scale_pos_weight: {e}")
    SPW_VALUES = {}

# Fonction pour obtenir la valeur forcée
def get_forced_scale_pos_weight(num):
    """
    Récupère la valeur forcée de scale_pos_weight pour un numéro

    Args:
        num: Numéro Keno

    Returns:
        float: Valeur de scale_pos_weight
    """
    # Convertir en chaîne
    num_str = str(num)

    # Récupérer la valeur
    if num_str in SPW_VALUES:
        return float(SPW_VALUES[num_str])

    # Valeur par défaut
    return 2.57

# Monkey patch pour XGBClassifier
try:
    from xgboost import XGBClassifier

    # Sauvegarder la méthode originale
    original_init = XGBClassifier.__init__

    @wraps(original_init)
    def patched_init(self, *args, **kwargs):
        # Récupérer le numéro si disponible
        num = kwargs.pop('num', None)

        # Récupérer scale_pos_weight s'il est déjà défini
        scale_pos_weight = kwargs.get('scale_pos_weight', None)

        # Appeler la méthode originale
        original_init(self, *args, **kwargs)

        # Forcer scale_pos_weight si le numéro est disponible et scale_pos_weight n'est pas déjà défini
        if num is not None and scale_pos_weight is None:
            forced_spw = get_forced_scale_pos_weight(num)
            self.scale_pos_weight = forced_spw
            print(f"XGBClassifier: Numéro {num} - scale_pos_weight forcé à {forced_spw}")

    # Remplacer la méthode
    XGBClassifier.__init__ = patched_init

    print("XGBClassifier patché avec succès")
except Exception as e:
    print(f"Erreur lors du patch de XGBClassifier: {e}")

# Monkey patch pour KenoAdvancedAnalyzer
try:
    # Essayer d'importer la classe
    from keno_advanced_analyzer import KenoAdvancedAnalyzer

    # Vérifier si la méthode train_models existe
    if hasattr(KenoAdvancedAnalyzer, 'train_models'):
        # Sauvegarder la méthode originale
        original_train_models = KenoAdvancedAnalyzer.train_models

        @wraps(original_train_models)
        def patched_train_models(self, num, *args, **kwargs):
            # Définir le numéro courant
            self.current_number = num
            print(f"KenoAdvancedAnalyzer: Numéro courant défini à {num}")

            # Appeler la méthode originale
            return original_train_models(self, num, *args, **kwargs)

        # Remplacer la méthode
        KenoAdvancedAnalyzer.train_models = patched_train_models

        print("KenoAdvancedAnalyzer.train_models patché avec succès")

    # Ajouter la méthode set_current_number si elle n'existe pas
    if not hasattr(KenoAdvancedAnalyzer, 'set_current_number'):
        def set_current_number(self, num):
            self.current_number = num
            print(f"KenoAdvancedAnalyzer: Numéro courant défini à {num}")
            return num

        # Ajouter la méthode
        KenoAdvancedAnalyzer.set_current_number = set_current_number

        print("KenoAdvancedAnalyzer.set_current_number ajouté avec succès")
except Exception as e:
    print(f"Erreur lors du patch de KenoAdvancedAnalyzer: {e}")

print("Module d'accroche pour l'auto-amélioration chargé avec succès")
"""

    # Sauvegarder le script
    script_file = 'auto_improve_hook.py'

    with open(script_file, 'w') as f:
        f.write(script_content)

    print(f"Script d'accroche sauvegardé dans {script_file}")

    return script_file

def create_launcher_script():
    """
    Crée un script de lancement qui utilise l'accroche

    Returns:
        str: Chemin du script créé
    """
    script_content = """"""
Script de lancement qui utilise l'accroche pour l'auto-amélioration
"""

import sys
import os
import importlib.util

def import_module(module_name, module_path):
    """
    Importe un module à partir de son chemin

    Args:
        module_name: Nom du module
        module_path: Chemin du module

    Returns:
        module: Module importé ou None en cas d'erreur
    """
    try:
        spec = importlib.util.spec_from_file_location(module_name, module_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        print(f"Module {module_name} importé avec succès")
        return module
    except Exception as e:
        print(f"Erreur lors de l'importation du module {module_name}: {e}")
        return None

def find_main_module():
    """
    Trouve le module principal de l'application

    Returns:
        str: Nom du module principal ou None si non trouvé
    """
    potential_modules = ['main.py', 'app.py', 'keno_app.py', 'keno_gui.py']

    for module in potential_modules:
        if os.path.exists(module):
            return module

    return None

def main():
    """Fonction principale"""
    print("Lancement de l'application avec l'accroche pour l'auto-amélioration")

    # Importer l'accroche
    hook = import_module('auto_improve_hook', 'auto_improve_hook.py')

    if not hook:
        print("Erreur lors de l'importation de l'accroche")
        return

    # Trouver le module principal
    main_module = find_main_module()

    if not main_module:
        print("Module principal non trouvé")
        print("Veuillez spécifier le nom du module principal:")
        main_module = input()

    if not os.path.exists(main_module):
        print(f"Le module {main_module} n'existe pas")
        return

    print(f"Lancement du module principal: {main_module}")

    # Importer le module principal
    module_name = os.path.splitext(main_module)[0]
    main = import_module(module_name, main_module)

    if not main:
        print("Erreur lors de l'importation du module principal")
        return

    # Exécuter la fonction principale
    if hasattr(main, 'main'):
        print("Exécution de la fonction main()")
        main.main()
    else:
        print("Le module principal n'a pas de fonction main()")
        print("Veuillez spécifier la fonction à appeler:")
        func_name = input()

        if hasattr(main, func_name):
            print(f"Exécution de la fonction {func_name}()")
            getattr(main, func_name)()
        else:
            print(f"Le module principal n'a pas de fonction {func_name}()")

if __name__ == "__main__":
    main()
"""

    # Sauvegarder le script
    script_file = 'launch_with_hook.py'

    with open(script_file, 'w') as f:
        f.write(script_content)

    print(f"Script de lancement sauvegardé dans {script_file}")

    return script_file

def create_readme():
    """
    Crée un fichier README avec des instructions

    Returns:
        str: Chemin du fichier créé
    """
    readme_content = """# Solution complète pour le problème de scale_pos_weight identique

Ce package contient une solution complète pour résoudre le problème de `scale_pos_weight` identique dans l'application Keno.

## Problème

Lors de l'auto-amélioration, l'application utilise la même valeur de `scale_pos_weight` pour tous les numéros Keno, ce qui limite la précision des prédictions.

## Solution

Cette solution corrige le problème à sa racine en :

1. **Modifiant le code source** pour stocker le numéro courant pendant l'auto-amélioration
2. **Utilisant des valeurs différentes** de `scale_pos_weight` pour chaque numéro
3. **Injectant ces valeurs** dans le code à l'exécution

## Fichiers inclus

- `complete_fix_scale_pos_weight.py` : Script principal qui corrige le code source
- `auto_improve_hook.py` : Module d'accroche qui injecte les valeurs à l'exécution
- `launch_with_hook.py` : Script de lancement qui utilise l'accroche
- `data/complete_fix_spw.json` : Valeurs de `scale_pos_weight` pour chaque numéro

## Comment utiliser cette solution

### Option 1 : Corriger le code source

Exécutez le script principal pour corriger le code source :

```bash
python complete_fix_scale_pos_weight.py
```

### Option 2 : Utiliser l'accroche sans modifier le code source

Importez le module d'accroche au début de votre script principal :

```python
import auto_improve_hook
```

### Option 3 : Utiliser le script de lancement

Exécutez le script de lancement :

```bash
python launch_with_hook.py
```

## Comment vérifier que la solution fonctionne

Après avoir intégré la solution, vous devriez voir des messages comme :

```
XGBClassifier: Numéro 7 - scale_pos_weight forcé à 2.1234
```

Cela indique que la valeur de `scale_pos_weight` a été forcée pour ce numéro.

## Comment modifier les valeurs

Si vous souhaitez modifier les valeurs de `scale_pos_weight`, éditez le fichier `data/complete_fix_spw.json`.
"""

    # Sauvegarder le README
    readme_file = 'README_COMPLETE_FIX.md'

    with open(readme_file, 'w') as f:
        f.write(readme_content)

    print(f"README sauvegardé dans {readme_file}")

    return readme_file

def main():
    """Fonction principale"""
    print("Solution complète pour le problème de scale_pos_weight identique")

    # Générer des valeurs aléatoires
    weights = generate_random_weights()

    # Sauvegarder les poids dans un fichier JSON
    weights_file = save_weights_to_json(weights)

    # Corriger le fichier keno_advanced_analyzer.py
    success_advanced = fix_keno_advanced_analyzer()

    # Corriger les fichiers d'optimisation XGBoost
    success_optimizer = fix_xgboost_optimizer()

    # Créer le script d'accroche
    hook_file = create_auto_improve_hook()

    # Créer le script de lancement
    launcher_file = create_launcher_script()

    # Créer le README
    readme_file = create_readme()

    print("\nSolution complète créée avec succès!")

    if success_advanced and success_optimizer:
        print("Le code source a été corrigé avec succès.")
    else:
        print("Le code source n'a pas pu être complètement corrigé.")
        print("Vous pouvez toujours utiliser l'accroche ou le script de lancement.")

    print("\nPour utiliser cette solution, vous avez trois options:")
    print("1. Redémarrer votre application (si le code source a été corrigé)")
    print("2. Importer le module d'accroche au début de votre script principal:")
    print("   import auto_improve_hook")
    print("3. Utiliser le script de lancement:")
    print("   python launch_with_hook.py")

    print("\nPour plus d'informations, consultez le fichier README_COMPLETE_FIX.md")

if __name__ == "__main__":
    main()
