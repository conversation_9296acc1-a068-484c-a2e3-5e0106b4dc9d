#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de mise à niveau vers le système ULTRA-OPTIMISÉ
Remplace automatiquement l'ancien système par la version améliorée
"""

import os
import sys
import shutil
from datetime import datetime

def create_backup():
    """Crée une sauvegarde des fichiers existants"""
    
    backup_dir = f"backup_before_ultra_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    files_to_backup = [
        'keno_enhanced_auto_improve.py',
        'integrate_enhanced_system.py',
        'test_enhanced_fix.py'
    ]
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        
        for file_name in files_to_backup:
            if os.path.exists(file_name):
                shutil.copy2(file_name, os.path.join(backup_dir, file_name))
                print(f"✓ Sauvegardé: {file_name}")
        
        print(f"✓ Sauvegarde créée dans: {backup_dir}")
        return backup_dir
        
    except Exception as e:
        print(f"✗ Erreur lors de la sauvegarde: {e}")
        return None

def update_main_gui():
    """Met à jour l'interface principale pour utiliser le système ultra-optimisé"""
    
    gui_file = 'keno_gui.py'
    
    if not os.path.exists(gui_file):
        print(f"✗ Fichier {gui_file} non trouvé")
        return False
    
    try:
        # Lire le fichier
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifier si déjà mis à jour
        if 'keno_ultra_optimizer' in content:
            print("✓ Interface déjà mise à jour avec le système ultra-optimisé")
            return True
        
        # Ajouter l'import du système ultra-optimisé
        ultra_import = """
# Import du système ULTRA-OPTIMISÉ
try:
    from keno_ultra_optimizer import KenoUltraOptimizer
    from keno_ultra_integration import integrate_ultra_system, run_ultra_training
    ultra_system_available = True
    print("✓ Système ULTRA-OPTIMISÉ disponible")
except ImportError as e:
    ultra_system_available = False
    print(f"✗ Système ULTRA-OPTIMISÉ non disponible: {e}")
"""
        
        # Trouver où insérer l'import
        import_pos = content.find("from keno_data import KenoDataManager")
        if import_pos != -1:
            content = content[:import_pos] + ultra_import + "\n" + content[import_pos:]
        
        # Ajouter l'initialisation du système ultra-optimisé
        ultra_init = """
        # Initialiser le système ULTRA-OPTIMISÉ
        self.ultra_optimizer = None
        if ultra_system_available:
            try:
                success, analyzer, ultra_optimizer = integrate_ultra_system()
                if success:
                    self.ultra_optimizer = ultra_optimizer
                    print("✓ Système ULTRA-OPTIMISÉ initialisé avec succès")
                else:
                    print("✗ Échec de l'initialisation du système ULTRA-OPTIMISÉ")
            except Exception as e:
                print(f"Erreur lors de l'initialisation du système ultra-optimisé: {e}")
"""
        
        # Trouver la fin de __init__
        init_pos = content.find("self.create_interface()")
        if init_pos != -1:
            content = content[:init_pos] + ultra_init + "\n        " + content[init_pos:]
        
        # Ajouter le bouton ultra-optimisé
        ultra_button = """
        # Bouton ULTRA-OPTIMISÉ
        if ultra_system_available:
            ultra_button = ttk.Button(
                buttons_frame,
                text="🚀 ULTRA-OPTIMISÉ 🚀",
                command=self.start_ultra_optimization,
                style="Action.TButton"
            )
            ultra_button.pack(side=tk.LEFT, padx=5)
            
            # Style spécial pour le bouton ultra
            try:
                style = ttk.Style()
                style.configure("Ultra.TButton", 
                               foreground="white", 
                               background="red",
                               font=("Helvetica", 10, "bold"))
                ultra_button.configure(style="Ultra.TButton")
            except:
                pass
"""
        
        # Trouver où ajouter le bouton
        button_pos = content.find('text="Auto-Amélioration"')
        if button_pos != -1:
            end_pos = content.find('.pack(side=tk.LEFT, padx=5)', button_pos)
            if end_pos != -1:
                end_pos = content.find('\n', end_pos)
                content = content[:end_pos] + "\n" + ultra_button + content[end_pos:]
        
        # Ajouter la méthode ultra-optimisée
        ultra_method = '''
    def start_ultra_optimization(self):
        """Lance l'optimisation ULTRA-AVANCÉE"""
        if not self.ultra_optimizer:
            messagebox.showerror("Erreur", "Système ULTRA-OPTIMISÉ non disponible")
            return
        
        # Vérifier les données
        if self.data_manager.get_draws_count() < 200:
            messagebox.showerror("Erreur", "Pas assez de données pour l'optimisation ULTRA (minimum 200 tirages).")
            return
        
        # Dialogue de sélection du mode
        mode_dialog = tk.Toplevel(self)
        mode_dialog.title("🚀 OPTIMISATION ULTRA-AVANCÉE 🚀")
        mode_dialog.geometry("500x400")
        mode_dialog.transient(self)
        mode_dialog.grab_set()
        
        # Centrer la fenêtre
        mode_dialog.update_idletasks()
        x = (self.winfo_width() // 2) - (500 // 2) + self.winfo_x()
        y = (self.winfo_height() // 2) - (400 // 2) + self.winfo_y()
        mode_dialog.geometry(f"500x400+{x}+{y}")
        
        # Configuration de la fenêtre
        mode_dialog.configure(bg='#1a1a1a')
        
        # Titre avec style
        title_label = tk.Label(mode_dialog, 
                              text="🚀 SYSTÈME ULTRA-OPTIMISÉ 🚀", 
                              font=("Helvetica", 16, "bold"),
                              fg="red", bg='#1a1a1a')
        title_label.pack(pady=15)
        
        # Description
        desc_text = """NOUVELLE GÉNÉRATION D'OPTIMISATION KENO

✨ Caractéristiques ultra-avancées (50+ features)
🧠 Intelligence artificielle de pointe
📊 Analyse d'entropie et de complexité
🎯 Prédictions ultra-précises
⚡ Suppression complète des warnings
🔥 Performances maximales

Choisissez votre mode d'entraînement:"""
        
        desc_label = tk.Label(mode_dialog, text=desc_text, 
                             wraplength=450, justify=tk.LEFT,
                             fg="white", bg='#1a1a1a',
                             font=("Helvetica", 10))
        desc_label.pack(pady=10)
        
        # Variable pour le mode
        mode_var = tk.StringVar(value="fast")
        
        # Options de mode avec style
        modes_frame = tk.Frame(mode_dialog, bg='#1a1a1a')
        modes_frame.pack(pady=15)
        
        tk.Radiobutton(modes_frame, text="🚀 RAPIDE (5 numéros, ~3-5 min)", 
                      variable=mode_var, value="fast",
                      fg="lime", bg='#1a1a1a', selectcolor='#333333',
                      font=("Helvetica", 10, "bold")).pack(anchor=tk.W, pady=2)
        tk.Radiobutton(modes_frame, text="⚡ MOYEN (15 numéros, ~10-15 min)", 
                      variable=mode_var, value="medium",
                      fg="yellow", bg='#1a1a1a', selectcolor='#333333',
                      font=("Helvetica", 10, "bold")).pack(anchor=tk.W, pady=2)
        tk.Radiobutton(modes_frame, text="🔥 COMPLET (70 numéros, ~45-60 min)", 
                      variable=mode_var, value="complete",
                      fg="red", bg='#1a1a1a', selectcolor='#333333',
                      font=("Helvetica", 10, "bold")).pack(anchor=tk.W, pady=2)
        
        # Boutons
        buttons_frame = tk.Frame(mode_dialog, bg='#1a1a1a')
        buttons_frame.pack(pady=25)
        
        def start_ultra_training():
            mode = mode_var.get()
            mode_dialog.destroy()
            self._run_ultra_optimization(mode)
        
        def cancel():
            mode_dialog.destroy()
        
        start_btn = tk.Button(buttons_frame, text="🚀 DÉMARRER", 
                             command=start_ultra_training,
                             bg="red", fg="white", font=("Helvetica", 12, "bold"),
                             padx=20, pady=5)
        start_btn.pack(side=tk.LEFT, padx=10)
        
        cancel_btn = tk.Button(buttons_frame, text="Annuler", 
                              command=cancel,
                              bg="gray", fg="white", font=("Helvetica", 10),
                              padx=15, pady=5)
        cancel_btn.pack(side=tk.LEFT, padx=10)
    
    def _run_ultra_optimization(self, mode):
        """Exécute l'optimisation ultra-avancée"""
        try:
            # Importer la fonction d'entraînement
            from keno_ultra_integration import run_ultra_training
            
            # Créer une fenêtre de progression ultra-stylée
            progress_window = tk.Toplevel(self)
            progress_window.title("🚀 OPTIMISATION ULTRA EN COURS 🚀")
            progress_window.geometry("700x500")
            progress_window.transient(self)
            progress_window.grab_set()
            progress_window.configure(bg='#0a0a0a')
            
            # Interface de progression stylée
            title_label = tk.Label(progress_window, 
                                  text="🚀 SYSTÈME ULTRA-OPTIMISÉ EN ACTION 🚀", 
                                  font=("Helvetica", 16, "bold"),
                                  fg="red", bg='#0a0a0a')
            title_label.pack(pady=15)
            
            status_var = tk.StringVar(value="Initialisation du système ultra-avancé...")
            status_label = tk.Label(progress_window, textvariable=status_var,
                                   fg="lime", bg='#0a0a0a',
                                   font=("Helvetica", 12, "bold"))
            status_label.pack(pady=10)
            
            # Zone de texte pour les détails
            details_frame = tk.Frame(progress_window, bg='#0a0a0a')
            details_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
            
            scrollbar = tk.Scrollbar(details_frame)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            details_text = tk.Text(details_frame, yscrollcommand=scrollbar.set,
                                  bg='#1a1a1a', fg='white', font=("Consolas", 9))
            details_text.pack(fill=tk.BOTH, expand=True)
            scrollbar.config(command=details_text.yview)
            
            def update_details(message, color="white"):
                details_text.insert(tk.END, message + "\\n")
                details_text.see(tk.END)
                progress_window.update()
            
            # Lancer l'entraînement dans un thread
            import threading
            
            def run_ultra_training_thread():
                try:
                    update_details("🚀 Démarrage de l'optimisation ULTRA-AVANCÉE...", "lime")
                    status_var.set("Entraînement ultra-optimisé en cours...")
                    
                    success, results = run_ultra_training(self.ultra_optimizer, mode)
                    
                    if success and results:
                        update_details("✅ SUCCÈS ! Optimisation ultra-avancée terminée !", "lime")
                        update_details(f"📊 Numéros entraînés: {len(results['trained_numbers'])}", "yellow")
                        
                        if results['performance_summary']:
                            import numpy as np
                            avg_f1 = np.mean([p['f1_score'] for p in results['performance_summary'].values()])
                            update_details(f"🎯 F1-score moyen: {avg_f1:.4f}", "lime")
                        
                        status_var.set("🚀 SYSTÈME ULTRA-OPTIMISÉ OPÉRATIONNEL ! 🚀")
                        update_details("🔥 Prêt pour des prédictions ultra-précises !", "red")
                        
                    else:
                        update_details("❌ Échec de l'optimisation ultra-avancée", "red")
                        status_var.set("Échec de l'optimisation")
                        
                except Exception as e:
                    update_details(f"❌ Erreur: {e}", "red")
                    status_var.set("Erreur lors de l'optimisation")
                
                # Ajouter un bouton de fermeture
                def close_window():
                    progress_window.destroy()
                
                close_button = tk.Button(progress_window, text="🚀 FERMER", 
                                        command=close_window,
                                        bg="red", fg="white", 
                                        font=("Helvetica", 12, "bold"),
                                        padx=20, pady=5)
                close_button.pack(pady=15)
            
            # Démarrer le thread
            training_thread = threading.Thread(target=run_ultra_training_thread)
            training_thread.daemon = True
            training_thread.start()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'optimisation ultra-avancée: {e}")
'''
        
        # Ajouter la méthode à la fin de la classe
        class_end = content.rfind("if __name__ == \"__main__\":")
        if class_end != -1:
            content = content[:class_end] + ultra_method + "\n\n" + content[class_end:]
        
        # Écrire le fichier modifié
        with open(gui_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✓ Interface mise à jour avec le système ULTRA-OPTIMISÉ")
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors de la mise à jour de l'interface: {e}")
        return False

def main():
    """Fonction principale"""
    
    print("=== MISE À NIVEAU VERS LE SYSTÈME ULTRA-OPTIMISÉ ===")
    print("🚀 Nouvelle génération d'optimisation Keno")
    print("⚡ Suppression complète des warnings")
    print("🎯 Performances maximales")
    print("🧠 Intelligence artificielle de pointe\\n")
    
    # 1. Créer une sauvegarde
    backup_dir = create_backup()
    if not backup_dir:
        print("Échec de la sauvegarde. Arrêt de la mise à niveau.")
        return 1
    
    # 2. Mettre à jour l'interface principale
    success = update_main_gui()
    
    if success:
        print("\\n✅ MISE À NIVEAU RÉUSSIE !")
        print(f"✓ Fichiers originaux sauvegardés dans: {backup_dir}")
        print("✓ Interface mise à jour avec le système ULTRA-OPTIMISÉ")
        print("\\n🚀 Votre application Keno dispose maintenant du système le plus avancé !")
        print("Lancez votre application et cherchez le bouton '🚀 ULTRA-OPTIMISÉ 🚀'")
        print("\\n🎯 Préparez-vous à des prédictions d'une précision inégalée !")
        return 0
    else:
        print("\\n❌ ÉCHEC DE LA MISE À NIVEAU")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
