import numpy as np
import pandas as pd
import sys
import os
import warnings
from datetime import datetime, timedelta
from collections import Counter, defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, HistGradientBoostingClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import joblib
import time
import pickle
import multiprocessing
from datetime import datetime
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor

# Importer la classe de deep learning de manière sécurisée
try:
    from keno_deep_learning import KenoDeepLearning
    DEEP_LEARNING_AVAILABLE = True
except ImportError:
    DEEP_LEARNING_AVAILABLE = False
    print("Module de deep learning non disponible. Les fonctionnalités de deep learning seront désactivées.")

# Importer la classe de détection d'anomalies de manière sécurisée
try:
    from keno_anomaly_detector import KenoAnomalyDetector
    ANOMALY_DETECTOR_AVAILABLE = True
except ImportError:
    ANOMALY_DETECTOR_AVAILABLE = False
    print("Module de détection d'anomalies non disponible. Les fonctionnalités de détection d'anomalies seront désactivées.")

# Importer la classe d'analyse de séquences temporelles de manière sécurisée
try:
    from keno_time_series import KenoTimeSeriesAnalyzer
    TIME_SERIES_AVAILABLE = True
except ImportError:
    TIME_SERIES_AVAILABLE = False
    print("Module d'analyse de séquences temporelles non disponible. Les fonctionnalités d'analyse de séquences temporelles seront désactivées.")

# Importer la classe d'estimation d'intervalles de confiance de manière sécurisée
try:
    from keno_confidence import KenoConfidenceEstimator
    CONFIDENCE_ESTIMATOR_AVAILABLE = True
except ImportError:
    CONFIDENCE_ESTIMATOR_AVAILABLE = False
    print("Module d'estimation d'intervalles de confiance non disponible. Les fonctionnalités d'estimation d'intervalles de confiance seront désactivées.")

# Essayer d'importer LightGBM
try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    lgb = None
    LIGHTGBM_AVAILABLE = False
    print("Module lightgbm non disponible. Les fonctionnalités LightGBM seront désactivées.")

# Essayer d'importer l'optimiseur XGBoost
try:
    from keno_xgboost_optimizer import XGBoostOptimizer
    XGBOOST_OPTIMIZER_AVAILABLE = True
except ImportError:
    XGBOOST_OPTIMIZER_AVAILABLE = False
    print("Module d'optimisation XGBoost non disponible. L'optimisation XGBoost sera désactivée.")

# Essayer d'importer XGBoost
try:
    from xgboost import XGBClassifier
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("Module XGBoost non disponible. Les fonctionnalités XGBoost seront désactivées.")


class KenoAdvancedAnalyzer:
    """Classe pour l'analyse avancée des données Keno et la prédiction par apprentissage automatique"""

    def __init__(self, data_manager):
        """Initialise l'analyseur avancé avec un gestionnaire de données"""
        self.data_manager = data_manager
        self.max_number = data_manager.max_number
        self.numbers_per_draw = data_manager.numbers_per_draw
        self.models = {}
        self.patterns = {}
        self.correlations = None
        self.series = None
        self.df = None
        self.n_jobs = -1  # Utiliser tous les cœurs CPU par défaut
        self.max_workers = None  # Nombre maximum de workers pour le traitement parallèle (None = auto)
        self.cuda_available = False  # Détection automatique du GPU
        self.npu_available = False  # Détection automatique du NPU
        self.hardware_acceleration = 'auto'  # 'auto', 'cpu', 'gpu', 'npu'

        # Paramètres des modèles
        self.model_params = {
            'deep_learning': {
                'epochs': 20,
                'batch_size': 32,
                'neurons': 64,
                'dropout_rate': 0.2
            },
            'random_forest': {
                'n_estimators': 300,
                'max_depth': 15
            },
            'xgboost': {
                'n_estimators': 300,
                'max_depth': 8,
                'learning_rate': 0.03
            }
        }

        # Initialiser le module de deep learning si disponible
        self.deep_learning = None
        self.deep_learning_available = DEEP_LEARNING_AVAILABLE
        if self.deep_learning_available:
            try:
                self.deep_learning = KenoDeepLearning(data_manager)
                print("Module de deep learning initialisé avec succès")
            except Exception as e:
                print(f"Erreur lors de l'initialisation du module de deep learning: {e}")
                self.deep_learning_available = False

        # Initialiser le module de détection d'anomalies si disponible
        self.anomaly_detector = None
        self.anomaly_detector_available = ANOMALY_DETECTOR_AVAILABLE
        if self.anomaly_detector_available:
            try:
                self.anomaly_detector = KenoAnomalyDetector(data_manager)
                print("Module de détection d'anomalies initialisé avec succès")
            except Exception as e:
                print(f"Erreur lors de l'initialisation du module de détection d'anomalies: {e}")
                self.anomaly_detector_available = False

        # Initialiser le module d'analyse de séquences temporelles si disponible
        self.time_series_analyzer = None
        self.time_series_available = TIME_SERIES_AVAILABLE
        if self.time_series_available:
            try:
                self.time_series_analyzer = KenoTimeSeriesAnalyzer(data_manager)
                print("Module d'analyse de séquences temporelles initialisé avec succès")
            except Exception as e:
                print(f"Erreur lors de l'initialisation du module d'analyse de séquences temporelles: {e}")
                self.time_series_available = False

        # Initialiser le module d'estimation d'intervalles de confiance si disponible
        self.confidence_estimator = None
        self.confidence_estimator_available = CONFIDENCE_ESTIMATOR_AVAILABLE
        if self.confidence_estimator_available:
            try:
                self.confidence_estimator = KenoConfidenceEstimator(data_manager)
                print("Module d'estimation d'intervalles de confiance initialisé avec succès")
            except Exception as e:
                print(f"Erreur lors de l'initialisation du module d'estimation d'intervalles de confiance: {e}")
                self.confidence_estimator_available = False

                # Initialiser LightGBM si disponible
        self.lightgbm_available = LIGHTGBM_AVAILABLE
        
        # Initialiser l'optimiseur XGBoost si disponible
        self.xgboost_optimizer = None
        self.xgboost_optimizer_available = XGBOOST_OPTIMIZER_AVAILABLE
        if self.xgboost_optimizer_available:
            try:
                # Déterminer si on utilise le GPU
                use_gpu = self.hardware_acceleration in ['gpu', 'auto'] and self.cuda_available
                
                # Créer l'optimiseur XGBoost
                self.xgboost_optimizer = XGBoostOptimizer(
                    use_gpu=use_gpu,
                    n_jobs=self.n_jobs,
                    verbose=1
                )
                print("Optimiseur XGBoost initialisé avec succès")
                
                # Afficher si le GPU est utilisé
                if use_gpu:
                    print("Optimiseur XGBoost configuré pour utiliser le GPU")
                else:
                    print("Optimiseur XGBoost configuré pour utiliser le CPU")
            except Exception as e:
                print(f"Erreur lors de l'initialisation de l'optimiseur XGBoost: {e}")
                self.xgboost_optimizer_available = False


    def set_cpu_cores(self, n_jobs):
        """Définit le nombre de cœurs CPU à utiliser pour l'entraînement

        Args:
            n_jobs (int): Nombre de cœurs à utiliser. -1 pour tous les cœurs disponibles.

        Returns:
            bool: True si la configuration a réussi
        """
        self.n_jobs = n_jobs
        print(f"Nombre de cœurs CPU configuré à {n_jobs} ({'-1 = tous' if n_jobs == -1 else n_jobs} cœurs)")
        return True

    def set_max_workers(self, max_workers):
        """Définit le nombre maximum de workers pour le traitement parallèle

        Args:
            max_workers (int): Nombre maximum de workers à utiliser. None pour auto-détection.

        Returns:
            bool: True si la configuration a réussi
        """
        self.max_workers = max_workers

        # Afficher un message informatif
        if max_workers is None:
            print("Nombre de workers configuré en mode auto-détection")
        else:
            try:
                # Importer le module de traitement parallèle pour vérifier le nombre de cœurs disponibles
                import parallel_processing
                available_cores = parallel_processing.patched_count_physical_cores()

                if max_workers > available_cores:
                    print(f"Attention: Le nombre de workers spécifié ({max_workers}) est supérieur au nombre de cœurs disponibles ({available_cores})")
                    print("Cela peut entraîner une dégradation des performances.")

                print(f"Nombre maximum de workers configuré à {max_workers} (sur {available_cores} cœurs disponibles)")
            except ImportError:
                print(f"Nombre maximum de workers configuré à {max_workers}")

        return True

    def set_hardware_acceleration(self, mode='auto'):
        """Configure le type d'accélération matérielle à utiliser

        Args:
            mode (str): Type d'accélération - 'auto', 'cpu', 'gpu', 'npu'

        Returns:
            dict: Informations sur les accélérateurs disponibles
        """
        # Réinitialiser les drapeaux
        self.cuda_available = False
        self.npu_available = False
        self.hardware_acceleration = 'cpu'  # Par défaut

        result = {
            'cpu': True,
            'gpu': False,
            'npu': False,
            'active': 'cpu'
        }

        # Si mode est 'cpu', on s'arrête là
        if mode.lower() == 'cpu':
            print("Mode CPU forcé, aucune accélération matérielle ne sera utilisée")
            self.hardware_acceleration = 'cpu'
            result['active'] = 'cpu'
            return result

    def train_xgboost_model(self, num, X, y, feature_cols, random_state=42, optimize=True, fast_mode=True):
        """Entraîne un modèle XGBoost optimisé pour un numéro spécifique
        
        Args:
            num (int): Numéro Keno
            X: Caractéristiques
            y: Étiquettes
            feature_cols (list): Noms des caractéristiques
            random_state (int): Graine aléatoire
            optimize (bool): Si True, optimise les hyperparamètres
            fast_mode (bool): Si True, utilise une optimisation plus rapide
            
        Returns:
            dict: Informations sur le modèle entraîné
        """
        # Vérifier si l'optimiseur XGBoost est disponible
        if not self.xgboost_optimizer_available or not self.xgboost_optimizer:
            print(f"Optimiseur XGBoost non disponible pour le numéro {num}")
            return None
        
        try:
            print(f"Entraînement du modèle XGBoost optimisé pour le numéro {num}")
            
            # Entraîner le modèle avec l'optimiseur
            result = self.xgboost_optimizer.train_model(
                X, y, num, 
                params=None,  # Utiliser les paramètres optimaux ou par défaut
                random_state=random_state,
                optimize=optimize,
                fast_mode=fast_mode
            )
            
            # Analyser l'importance des caractéristiques
            if feature_cols and 'model' in result:
                feature_importance = self.xgboost_optimizer.analyze_feature_importance(
                    result['model'], feature_cols
                )
                
                if feature_importance:
                    # Afficher les 5 caractéristiques les plus importantes
                    print(f"Caractéristiques les plus importantes pour le numéro {num}:")
                    for feature, importance in feature_importance[:5]:
                        print(f"  - {feature}: {importance:.4f}")
                    
                    # Stocker l'importance des caractéristiques
                    result['feature_importance_details'] = feature_importance
            
            return result
        except Exception as e:
            print(f"Erreur lors de l'entraînement du modèle XGBoost pour le numéro {num}: {e}")
            import traceback
            traceback.print_exc()
            return None


        # Vérifier la disponibilité du GPU
        try:
            # Essayer d'importer TensorFlow
            import tensorflow as tf
            gpus = tf.config.list_physical_devices('GPU')
            if gpus:
                print(f"GPU détecté: {len(gpus)} dispositif(s)")
                self.cuda_available = True
                result['gpu'] = True

                # Configurer TensorFlow pour utiliser le GPU
                for gpu in gpus:
                    try:
                        tf.config.experimental.set_memory_growth(gpu, True)
                    except:
                        pass
            else:
                # Essayer avec nvidia-smi
                import subprocess
                try:
                    result_cmd = subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                    if result_cmd.returncode == 0:
                        print("GPU NVIDIA détecté via nvidia-smi")
                        self.cuda_available = True
                        result['gpu'] = True
                except:
                    pass
        except Exception as e:
            print(f"Erreur lors de la vérification du GPU: {e}")

        # Vérifier la disponibilité du NPU
        try:
            # Vérifier si TensorFlow peut détecter des accélérateurs NPU/TPU
            import tensorflow as tf

            # Vérifier les dispositifs disponibles
            devices = tf.config.list_physical_devices()
            for device in devices:
                if 'NPU' in device.name.upper() or 'TPU' in device.name.upper() or 'NEURAL' in device.name.upper():
                    print(f"NPU/TPU détecté: {device.name}")
                    self.npu_available = True
                    result['npu'] = True
                    break

            # Vérifier si OpenVINO est disponible (pour processeurs Intel)
            try:
                import openvino as ov
                print(f"OpenVINO détecté (version {ov.__version__})")

                # Créer un Core OpenVINO pour accéder aux dispositifs
                core = ov.Core()
                available_devices = core.available_devices
                print(f"Dispositifs OpenVINO disponibles: {available_devices}")

                # Vérifier les dispositifs d'accélération
                has_accelerator = False

                for device in available_devices:
                    try:
                        device_name = device
                        full_name = core.get_property(device_name, "FULL_DEVICE_NAME")
                        print(f"Dispositif OpenVINO: {device_name} - {full_name}")

                        if device != 'CPU':
                            has_accelerator = True
                            print(f"Accélérateur OpenVINO détecté: {device_name}")
                        elif 'VNNI' in full_name or 'AVX' in full_name or 'SSE' in full_name:
                            has_accelerator = True
                            print(f"CPU Intel avec accélération IA détecté: {full_name}")

                        try:
                            capabilities = core.get_property(device_name, "OPTIMIZATION_CAPABILITIES")
                            print(f"Capacités d'optimisation: {capabilities}")

                            if device == 'CPU' and capabilities:
                                if any(cap in str(capabilities) for cap in ['VNNI', 'AVX', 'FP16', 'INT8', 'BF16']):
                                    has_accelerator = True
                                    print(f"CPU avec instructions d'accélération IA: {capabilities}")
                        except Exception as e:
                            print(f"Impossible de lire les capacités: {e}")
                    except Exception as e:
                        print(f"Erreur lors de l'analyse du dispositif {device}: {e}")

                if 'MYRIAD' in available_devices:
                    print("Intel Neural Compute Stick détecté")
                    has_accelerator = True

                if 'GPU' in available_devices:
                    print("Intel GPU détecté via OpenVINO")
                    has_accelerator = True

                if has_accelerator:
                    self.npu_available = True
                    result['npu'] = True
                    print("Accélération NPU Intel disponible via OpenVINO")
            except ImportError:
                print("OpenVINO n'est pas installé. Utilisez 'pip install openvino' pour l'installer.")
            except Exception as e:
                print(f"Erreur lors de la vérification d'OpenVINO: {e}")

            # Vérifier si Apple Neural Engine est disponible (macOS)
            try:
                import platform
                if platform.system() == 'Darwin' and hasattr(tf, 'config') and hasattr(tf.config, 'list_physical_devices'):
                    if 'CPU' in [d.device_type for d in tf.config.list_physical_devices()]:
                        # Sur macOS récent avec Apple Silicon, le NPU est intégré
                        import subprocess
                        result_cmd = subprocess.run(['sysctl', '-n', 'machdep.cpu.brand_string'],
                                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                        if 'Apple' in result_cmd.stdout:
                            print("Apple Neural Engine détecté")
                            self.npu_available = True
                            result['npu'] = True
            except:
                pass
        except Exception as e:
            print(f"Erreur lors de la vérification du NPU: {e}")

        # Déterminer le mode d'accélération à utiliser
        if mode.lower() == 'auto':
            if self.npu_available and result['npu']:
                self.hardware_acceleration = 'npu'
                result['active'] = 'npu'
                print("Mode NPU activé automatiquement")
            elif self.cuda_available and result['gpu']:
                self.hardware_acceleration = 'gpu'
                result['active'] = 'gpu'
                print("Mode GPU activé automatiquement")
            else:
                self.hardware_acceleration = 'cpu'
                result['active'] = 'cpu'
                print("Mode CPU activé automatiquement (aucun accélérateur détecté)")
        elif mode.lower() == 'gpu' and result['gpu']:
            self.hardware_acceleration = 'gpu'
            result['active'] = 'gpu'
            print("Mode GPU activé manuellement")
        elif mode.lower() == 'npu' and result['npu']:
            self.hardware_acceleration = 'npu'
            result['active'] = 'npu'
            print("Mode NPU activé manuellement")
        else:
            self.hardware_acceleration = 'cpu'
            result['active'] = 'cpu'
            print(f"Mode {mode} non disponible, utilisation du CPU")

        return result

    def set_gpu_usage(self, enabled=True):
        """Active ou désactive l'utilisation du GPU (méthode maintenue pour compatibilité)

        Args:
            enabled (bool): True pour activer le GPU, False pour le désactiver

        Returns:
            bool: True si la configuration a réussi, False si le GPU n'est pas disponible
        """
        if enabled:
            result = self.set_hardware_acceleration('gpu')
            return result['active'] == 'gpu'
        else:
            self.set_hardware_acceleration('cpu')
            return True

    def prepare_data(self):
        """Prépare les données pour l'analyse avancée"""
        if not self.data_manager.draws:
            print("Aucun tirage disponible dans le gestionnaire de données")

            # Modification pour garantir des distributions spécifiques
            # Vérifier si nous traitons un numéro spécifique
            if 'num' in locals() or 'num' in globals():
                current_num = num
            elif hasattr(self, 'current_number'):
                current_num = self.current_number
            else:
                current_num = None
            
            if current_num is not None:
                # Charger les valeurs spécifiques si disponibles
                spw_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'real_scale_pos_weight.readonly.json')
                try:
                    with open(spw_file, 'r') as f:
                        spw_values = json.load(f)
                    
                    # Vérifier si nous avons une valeur pour ce numéro
                    if str(current_num) in spw_values:
                        target_spw = float(spw_values[str(current_num)])
                        
                        # Calculer la distribution actuelle
                        if isinstance(y, pd.Series) or isinstance(y, np.ndarray):
                            neg_count = np.sum(y == 0)
                            pos_count = np.sum(y == 1)
                            current_spw = neg_count / max(1, pos_count)
                            
                            print(f"Numéro {current_num}: Distribution actuelle [négatifs={neg_count}, positifs={pos_count}]")
                            print(f"Numéro {current_num}: scale_pos_weight actuel = {current_spw:.4f}, cible = {target_spw:.4f}")
                            
                            # Si la distribution actuelle est différente de la cible, ajuster les données
                            if abs(current_spw - target_spw) > 0.1:
                                print(f"Numéro {current_num}: Ajustement de la distribution pour atteindre scale_pos_weight = {target_spw:.4f}")
                                
                                # Calculer la nouvelle distribution cible
                                total_samples = len(y)
                                target_pos_count = total_samples / (target_spw + 1)
                                target_neg_count = total_samples - target_pos_count
                                
                                # Ajuster les données en sous-échantillonnant ou sur-échantillonnant
                                if isinstance(X, pd.DataFrame) and isinstance(y, pd.Series):
                                    # Séparer les échantillons positifs et négatifs
                                    X_pos = X[y == 1]
                                    X_neg = X[y == 0]
                                    y_pos = y[y == 1]
                                    y_neg = y[y == 0]
                                    
                                    # Ajuster les échantillons positifs
                                    if pos_count < target_pos_count:
                                        # Sur-échantillonnage des positifs
                                        ratio = target_pos_count / pos_count
                                        n_samples = int(pos_count * ratio)
                                        indices = np.random.choice(len(X_pos), size=n_samples, replace=True)
                                        X_pos_resampled = X_pos.iloc[indices]
                                        y_pos_resampled = y_pos.iloc[indices]
                                    else:
                                        # Sous-échantillonnage des positifs
                                        indices = np.random.choice(len(X_pos), size=int(target_pos_count), replace=False)
                                        X_pos_resampled = X_pos.iloc[indices]
                                        y_pos_resampled = y_pos.iloc[indices]
                                    
                                    # Ajuster les échantillons négatifs
                                    if neg_count < target_neg_count:
                                        # Sur-échantillonnage des négatifs
                                        ratio = target_neg_count / neg_count
                                        n_samples = int(neg_count * ratio)
                                        indices = np.random.choice(len(X_neg), size=n_samples, replace=True)
                                        X_neg_resampled = X_neg.iloc[indices]
                                        y_neg_resampled = y_neg.iloc[indices]
                                    else:
                                        # Sous-échantillonnage des négatifs
                                        indices = np.random.choice(len(X_neg), size=int(target_neg_count), replace=False)
                                        X_neg_resampled = X_neg.iloc[indices]
                                        y_neg_resampled = y_neg.iloc[indices]
                                    
                                    # Combiner les échantillons
                                    X = pd.concat([X_pos_resampled, X_neg_resampled])
                                    y = pd.concat([y_pos_resampled, y_neg_resampled])
                                    
                                    # Mélanger les données
                                    indices = np.random.permutation(len(X))
                                    X = X.iloc[indices].reset_index(drop=True)
                                    y = y.iloc[indices].reset_index(drop=True)
                                    
                                    # Vérifier la nouvelle distribution
                                    new_neg_count = np.sum(y == 0)
                                    new_pos_count = np.sum(y == 1)
                                    new_spw = new_neg_count / max(1, new_pos_count)
                                    
                                    print(f"Numéro {current_num}: Nouvelle distribution [négatifs={new_neg_count}, positifs={new_pos_count}]")
                                    print(f"Numéro {current_num}: Nouveau scale_pos_weight = {new_spw:.4f}")
                except Exception as e:
                    print(f"Erreur lors de l'ajustement de la distribution: {e}")

            return None

        # Afficher le nombre total de tirages avant traitement
        print(f"Nombre total de tirages avant traitement: {len(self.data_manager.draws)}")

        # Convertir les tirages en DataFrame pandas
        data = []
        invalid_draws = 0
        for i, draw in enumerate(self.data_manager.draws):
            # Vérifier si le tirage est valide
            if not hasattr(draw, 'draw_numbers') or not draw.draw_numbers:
                print(f"Tirage {i} ignoré: numéros manquants")
                invalid_draws += 1
                continue
            if not hasattr(draw, 'draw_date') or not draw.draw_date:
                print(f"Tirage {i} ignoré: date manquante")
                invalid_draws += 1
                continue

            row = {
                'date': draw.draw_date,
                'id': draw.draw_id,
                'day_of_week': draw.draw_date.weekday(),
                'hour': draw.draw_date.hour,
                'is_weekend': 1 if draw.draw_date.weekday() >= 5 else 0,
                'is_afternoon': 1 if draw.draw_date.hour >= 12 else 0
            }

            # Ajouter les numéros tirés
            for i, num in enumerate(sorted(draw.draw_numbers)):
                row[f'num_{i+1}'] = num

            # Ajouter des indicateurs pour chaque numéro possible
            for i in range(1, self.max_number + 1):
                row[f'has_{i}'] = 1 if i in draw.draw_numbers else 0

            data.append(row)

        # Afficher le nombre de tirages après filtrage
        print(f"Nombre de tirages après filtrage: {len(data)} (ignorés: {invalid_draws})")

        # Créer le DataFrame
        df = pd.DataFrame(data)
        if df.empty:
            print("Aucune donnée valide après filtrage")
            return None

        # Trier par date
        df = df.sort_values('date')
        print(f"Nombre de lignes après tri: {len(df)}")

        # Ajouter des caractéristiques temporelles
        df['day'] = df['date'].dt.day
        df['month'] = df['date'].dt.month
        df['year'] = df['date'].dt.year
        df['day_of_year'] = df['date'].dt.dayofyear

        # Vérifier les doublons
        duplicates = df.duplicated(subset=['date']).sum()
        if duplicates > 0:
            print(f"Attention: {duplicates} doublons détectés basés sur la date")

        # Stocker le DataFrame
        self.df = df
        print(f"DataFrame final: {len(self.df)} lignes")

        return df

    def analyze_patterns(self):
        """Analyse les motifs dans les tirages en utilisant le traitement parallèle si disponible"""
        import time
        from collections import Counter

        start_time = time.time()

        if self.df is None:
            self.prepare_data()

        if self.df is None or self.df.empty:
            return {}

        print(f"Analyse des motifs sur {len(self.df)} tirages...")
        patterns = {}

        # Essayer d'utiliser le module de traitement parallèle si disponible
        try:
            import parallel_processing
            print("Utilisation du traitement parallèle pour l'analyse des motifs")

            # Déterminer le nombre optimal de workers
            n_workers = self.max_workers if self.max_workers is not None else \
                       (self.n_jobs if self.n_jobs > 0 else parallel_processing.get_optimal_workers())

            # Utiliser 22 workers par défaut si aucun n'est spécifié
            if n_workers is None or n_workers <= 0:
                n_workers = 22

            print(f"Utilisation de {n_workers} workers pour l'analyse parallèle")

            # Convertir les données DataFrame en objets KenoDrawData pour le traitement parallèle
            from keno_data import KenoDrawData
            from datetime import datetime
            draws = []
            for _, row in self.df.iterrows():
                nums = [row[f'num_{i+1}'] for i in range(self.numbers_per_draw) if f'num_{i+1}' in row]
                if nums:
                    # Utiliser la date du tirage si disponible, sinon utiliser la date actuelle
                    draw_date = row.get('date', datetime.now())
                    # Créer un objet KenoDrawData avec les arguments requis
                    draw = KenoDrawData(draw_date=draw_date, draw_numbers=nums)
                    draws.append(draw)

            # Analyser les motifs en parallèle
            if draws:
                print(f"Traitement parallèle de {len(draws)} tirages...")
                pattern_results = parallel_processing.analyze_patterns_parallel(draws, max_workers=n_workers)

                # Extraire les résultats
                if 'common_pairs' in pattern_results:
                    patterns['common_pairs'] = dict(pattern_results['common_pairs'])
                if 'sequence_counts' in pattern_results:
                    patterns['sequence_counts'] = dict(pattern_results['sequence_counts'])
        except Exception as e:
            print(f"Erreur lors de l'importation du module parallèle: {e}")
            print("Utilisation du traitement séquentiel")

        # Analyser la distribution pairs/impairs (toujours séquentiel car rapide)
        print("Analyse de la distribution pairs/impairs...")
        even_odd_counts = []
        for _, row in self.df.iterrows():
            nums = [row[f'num_{i+1}'] for i in range(self.numbers_per_draw) if f'num_{i+1}' in row]
            if nums:
                even_count = sum(1 for num in nums if num % 2 == 0)
                odd_count = len(nums) - even_count
                even_odd_counts.append((even_count, odd_count))

        patterns['even_odd_distribution'] = Counter(even_odd_counts)

        # Analyser la distribution haut/bas
        print("Analyse de la distribution haut/bas...")
        high_low_counts = []
        mid_point = self.max_number // 2
        for _, row in self.df.iterrows():
            nums = [row[f'num_{i+1}'] for i in range(self.numbers_per_draw) if f'num_{i+1}' in row]
            if nums:
                high_count = sum(1 for num in nums if num > mid_point)
                low_count = len(nums) - high_count
                high_low_counts.append((high_count, low_count))

        patterns['high_low_distribution'] = Counter(high_low_counts)

        # Analyser les écarts entre les numéros consécutifs (si pas déjà fait en parallèle)
        if 'consecutive_gaps' not in patterns:
            print("Analyse des écarts entre numéros consécutifs...")
            gaps = []
            for _, row in self.df.iterrows():
                nums = sorted([row[f'num_{i+1}'] for i in range(self.numbers_per_draw) if f'num_{i+1}' in row])
                if len(nums) > 1:
                    draw_gaps = [nums[i+1] - nums[i] for i in range(len(nums)-1)]
                    gaps.extend(draw_gaps)

            patterns['consecutive_gaps'] = Counter(gaps)

        # Analyser les sommes des tirages
        print("Analyse des sommes de tirage...")
        sums = []
        for _, row in self.df.iterrows():
            nums = [row[f'num_{i+1}'] for i in range(self.numbers_per_draw) if f'num_{i+1}' in row]
            if nums:
                sums.append(sum(nums))

        if sums:
            patterns['draw_sums'] = {
                'min': min(sums),
                'max': max(sums),
                'mean': np.mean(sums),
                'median': np.median(sums),
                'distribution': np.histogram(sums, bins=10)
            }

        # Stocker les motifs
        self.patterns = patterns

        end_time = time.time()
        print(f"Analyse des motifs terminée en {end_time - start_time:.2f} secondes")

        return patterns

    def analyze_correlations(self):
        """Analyse les corrélations entre les numéros"""
        if self.df is None:
            self.prepare_data()

        if self.df is None or self.df.empty:
            return None

        # Extraire les colonnes 'has_X' qui indiquent si le numéro X est présent
        has_columns = [col for col in self.df.columns if col.startswith('has_')]
        correlation_matrix = self.df[has_columns].corr()

        # Stocker la matrice de corrélation
        self.correlations = correlation_matrix

        return correlation_matrix

    def analyze_series(self):
        """Analyse les séries et les séquences dans les tirages en utilisant le traitement parallèle si disponible"""
        import time

        start_time = time.time()

        if not self.data_manager.draws:
            return None

        print(f"Analyse des séries pour {len(self.data_manager.draws)} tirages...")
        series_data = {}

        # Utiliser l'ensemble des données disponibles
        total_draws = len(self.data_manager.draws)
        print(f"Utilisation de l'ensemble des {total_draws} tirages disponibles pour l'analyse")
        sample_draws = self.data_manager.draws

        # Essayer d'utiliser le module de traitement parallèle si disponible
        try:
            import parallel_processing
            print("Utilisation du traitement parallèle pour l'analyse des séries")

            # Déterminer le nombre optimal de workers
            n_workers = self.max_workers if self.max_workers is not None else \
                       (self.n_jobs if self.n_jobs > 0 else parallel_processing.get_optimal_workers())

            # Utiliser 22 workers par défaut si aucun n'est spécifié
            if n_workers is None or n_workers <= 0:
                n_workers = 22

            print(f"Utilisation de {n_workers} workers pour l'analyse parallèle")

            # Analyser les séries en parallèle
            series_results = parallel_processing.analyze_series_parallel(sample_draws, max_workers=n_workers)

            # Traiter les résultats
            if series_results:
                # Extraire les informations sur les séries temporelles
                follow_patterns = {}
                for num, data in series_results.items():
                    if 'appearances' in data and data['appearances'] > 0:
                        follow_patterns[num] = {
                            'count': data['appearances'],
                            'repeat_rate': data.get('frequency', 0)
                        }

                series_data['follow_patterns'] = follow_patterns
                print(f"Analyse parallèle terminée: {len(follow_patterns)} motifs de suivi identifiés")

                # Continuer avec l'analyse séquentielle pour les autres aspects
                print("Complément d'analyse séquentielle...")
        except (ImportError, Exception) as e:
            print(f"Erreur lors de l'importation du module parallèle: {e}")
            print("Utilisation du traitement séquentiel")

        # Si l'analyse parallèle n'a pas été effectuée ou est incomplète, continuer avec l'analyse séquentielle
        if 'follow_patterns' not in series_data:
            # Utiliser tqdm si disponible
            try:
                from tqdm import tqdm
                use_tqdm = True
            except ImportError:
                use_tqdm = False

            # 1. Analyser les séquences de numéros qui se suivent
            print("1/3: Analyse des séquences consécutives...")
            sequences = []
            sequence_counts = []

            iterator = tqdm(sample_draws, desc="Analyse des séquences", unit="tirage") if use_tqdm else sample_draws
            for draw in iterator:
                nums = sorted(draw.draw_numbers)
                seq_count = 0
                current_seq = []

                for i in range(len(nums) - 1):
                    if nums[i+1] == nums[i] + 1:
                        if not current_seq:
                            current_seq = [nums[i], nums[i+1]]
                        else:
                            current_seq.append(nums[i+1])
                    else:
                        if current_seq:
                            sequences.append(tuple(current_seq))
                            seq_count += 1
                            current_seq = []

                if current_seq:
                    sequences.append(tuple(current_seq))
                    seq_count += 1

                sequence_counts.append(seq_count)

            series_data['sequence_counts'] = Counter(sequence_counts)
            series_data['common_sequences'] = Counter(sequences).most_common(20)

            # 2. Analyser les numéros qui apparaissent souvent ensemble
            print("2/3: Analyse des paires fréquentes...")
            pairs_counter = Counter()

            iterator = tqdm(sample_draws, desc="Analyse des paires", unit="tirage") if use_tqdm else sample_draws
            for draw in iterator:
                nums = draw.draw_numbers
                # Utiliser une compréhension de liste pour accélérer
                draw_pairs = [(min(nums[i], nums[j]), max(nums[i], nums[j]))
                             for i in range(len(nums))
                             for j in range(i+1, len(nums))]
                pairs_counter.update(draw_pairs)

            series_data['common_pairs'] = pairs_counter.most_common(30)

            # 3. Analyser les numéros qui se suivent d'un tirage à l'autre
            print("3/3: Analyse des motifs de suivi...")
            follow_patterns = defaultdict(list)

            # Pré-calculer les ensembles pour accélérer
            draw_sets = [set(draw.draw_numbers) for draw in self.data_manager.draws]

            iterator = tqdm(range(len(draw_sets) - 1), desc="Analyse des motifs de suivi", unit="tirage") if use_tqdm else range(len(draw_sets) - 1)
            for i in iterator:
                current_draw = draw_sets[i]
                next_draw = draw_sets[i+1]

                for num in current_draw:
                    follow_patterns[num].append(1 if num in next_draw else 0)

            # Calculer les taux de répétition en une seule fois
            series_data['follow_patterns'] = {
                num: {
                    'count': len(patterns),
                    'repeat_rate': sum(patterns) / len(patterns) if patterns else 0
                }
                for num, patterns in follow_patterns.items()
            }

        print("Analyse des séries terminée")

        # Stocker les données de séries
        self.series = series_data

        # Afficher les statistiques de performance
        end_time = time.time()
        duration = end_time - start_time
        print(f"Analyse des séries terminée en {duration:.2f} secondes")

        # Afficher les statistiques des résultats
        if 'common_sequences' in series_data:
            print(f"Séquences identifiées: {len(series_data.get('common_sequences', []))}")
        if 'common_pairs' in series_data:
            print(f"Paires fréquentes: {len(series_data.get('common_pairs', []))}")
        if 'follow_patterns' in series_data:
            print(f"Motifs de suivi: {len(series_data.get('follow_patterns', {}))}")

        return series_data

    def train_models(self, test_size=0.2, random_state=42, stop_flag=None, fast_mode=False, timeout=1800, ultra_fast=False, max_numbers=None, resume=True, save_incremental=True):
        """Entraîne des modèles d'apprentissage automatique pour la prédiction en utilisant le traitement parallèle si disponible

        Args:
            test_size: Proportion des données à utiliser pour le test (défaut: 0.2)
            random_state: Graine aléatoire pour la reproductibilité (défaut: 42)
            stop_flag: Référence à un objet qui indique si l'entraînement doit être arrêté
            fast_mode: Si True, utilise des paramètres optimisés pour un entraînement plus rapide

        Returns:
            bool: True si l'entraînement a réussi, False sinon
        """

        # Fonction pour vérifier si l'arrêt a été demandé
        def check_stop_requested():
            # Vérifier si le timeout est atteint
            current_time = time.time()
            if current_time > end_time:
                print(f"\nTimeout atteint après {current_time - start_time:.1f} secondes. Interruption de l'entraînement.")
                return True

            # Vérifier si stop_flag est un objet avec un attribut 'value'
            if stop_flag and hasattr(stop_flag, 'value') and stop_flag.value:
                print("\nArrêt demandé par l'utilisateur. Interruption de l'entraînement.")
                return True
            # Vérifier si stop_flag est une fonction de rappel
            elif callable(stop_flag):
                try:
                    if stop_flag():
                        print("\nArrêt demandé par l'utilisateur via fonction de rappel. Interruption de l'entraînement.")
                        return True
                except Exception as e:
                    print(f"Erreur lors de l'appel à la fonction stop_flag: {e}")
            return False
        import time
        start_time = time.time()
        end_time = start_time + timeout  # Définir le temps limite d'exécution
        if self.df is None:
            self.prepare_data()

        if self.df is None or self.df.empty or len(self.df) < 50:
            print("Pas assez de données pour entraîner les modèles (minimum 50 tirages requis)")
            return False

        # Initialiser les variables
        use_parallel = False  # Désactiver complètement le traitement parallèle
        n_workers = 1
        use_gpu = False

        # Si le mode rapide est activé, utiliser des paramètres optimisés
        if fast_mode:
            print("Mode rapide activé : utilisation de paramètres optimisés pour l'entraînement")

        # Désactiver complètement le traitement parallèle pour éviter les blocages
        print("Utilisation du traitement séquentiel pour l'entraînement des modèles")
        print("Le traitement parallèle est désactivé pour éviter les blocages")

        print(f"Entraînement des modèles ML avec {len(self.df)} tirages...")

        # Supprimer les avertissements de TensorFlow
        import os
        os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR

        # Désactiver complètement la détection des accélérateurs matériels
        print("Détection des accélérateurs matériels désactivée pour éviter les blocages")
        print("Utilisation du CPU pour l'entraînement des modèles")

        # Forcer l'utilisation du CPU
        self.hardware_acceleration = 'cpu'
        self.cuda_available = False
        self.npu_available = False

        # Désactiver l'utilisation du GPU pour TensorFlow
        os.environ['CUDA_VISIBLE_DEVICES'] = '-1'  # Désactiver tous les GPU
        os.environ['TF_DISABLE_ONEDNN_OPTS'] = '1'  # Désactiver OneDNN

        # Désactiver OpenVINO
        os.environ['OPENVINO_FORCE_CPU'] = '1'  # Forcer l'utilisation du CPU
        os.environ['OPENVINO_ENABLE_CPU_OPTIMIZATIONS'] = '0'  # Désactiver les optimisations CPU

        # Créer des caractéristiques pour chaque numéro
        features = {}
        targets = {}

        # Ajouter des caractéristiques temporelles avancées
        self.df['day'] = self.df['date'].dt.day
        self.df['month'] = self.df['date'].dt.month
        self.df['year'] = self.df['date'].dt.year

        # Caractéristiques cycliques optimisées
        self.df['day_sin'] = np.sin(2 * np.pi * self.df['day_of_week'] / 7)
        self.df['day_cos'] = np.cos(2 * np.pi * self.df['day_of_week'] / 7)
        self.df['month_sin'] = np.sin(2 * np.pi * self.df['month'] / 12)
        self.df['month_cos'] = np.cos(2 * np.pi * self.df['month'] / 12)
        self.df['hour_sin'] = np.sin(2 * np.pi * self.df['hour'] / 24)
        self.df['hour_cos'] = np.cos(2 * np.pi * self.df['hour'] / 24)
        self.df['day_of_month_sin'] = np.sin(2 * np.pi * self.df['day'] / 31)
        self.df['day_of_month_cos'] = np.cos(2 * np.pi * self.df['day'] / 31)

        # Caractéristiques de périodicité hebdomadaire et mensuelle
        self.df['week_of_year'] = self.df['date'].dt.isocalendar().week
        self.df['week_sin'] = np.sin(2 * np.pi * self.df['week_of_year'] / 52)
        self.df['week_cos'] = np.cos(2 * np.pi * self.df['week_of_year'] / 52)

        # Caractéristiques de périodes spéciales (vacances, événements)
        self.df['is_month_start'] = self.df['date'].dt.is_month_start.astype(int)
        self.df['is_month_end'] = self.df['date'].dt.is_month_end.astype(int)
        self.df['is_quarter_start'] = self.df['date'].dt.is_quarter_start.astype(int)
        self.df['is_quarter_end'] = self.df['date'].dt.is_quarter_end.astype(int)
        self.df['is_year_start'] = self.df['date'].dt.is_year_start.astype(int)
        self.df['is_year_end'] = self.df['date'].dt.is_year_end.astype(int)

        # Vérifier que max_number est correct (ne devrait pas dépasser 70 pour le Keno)
        valid_max_number = min(70, self.max_number)  # Limiter à 70 maximum

        # Optimisation de la sélection des numéros en fonction du mode
        if ultra_fast:
            # Mode ultra-rapide: limiter à 5 numéros représentatifs (1, 15, 29, 43, 57)
            valid_numbers = [1, 15, 29, 43, 57]
            print(f"Mode ULTRA-RAPIDE activé: entraînement limité à {len(valid_numbers)} numéros représentatifs")
            print(f"Numéros sélectionnés: {valid_numbers}")
        elif fast_mode:
            # Limiter à 10 numéros en mode rapide (1, 8, 15, 22, 29, 36, 43, 50, 57, 64)
            # Ces numéros sont répartis sur toute la plage pour avoir un échantillon représentatif
            valid_numbers = [1, 8, 15, 22, 29, 36, 43, 50, 57, 64]
            print(f"Mode rapide activé: entraînement limité à {len(valid_numbers)} numéros représentatifs")
            print(f"Numéros sélectionnés: {valid_numbers}")
        else:
            # En mode complet, traiter tous les numéros dans l'ordre naturel (1 à 70)
            valid_numbers = list(range(1, valid_max_number + 1))
            print(f"Mode complet: entraînement de tous les {len(valid_numbers)} numéros dans l'ordre naturel")
            print(f"Traitement des numéros de 1 à {valid_max_number}")

        # Limiter le nombre de numéros si spécifié
        if max_numbers is not None and max_numbers > 0 and max_numbers < len(valid_numbers):
            # Sélectionner un sous-ensemble régulier des numéros valides
            step = len(valid_numbers) // max_numbers
            valid_numbers = valid_numbers[::step][:max_numbers]  # Prendre tous les 'step' numéros, limité à max_numbers
            print(f"Limitation à {len(valid_numbers)} numéros pour l'entraînement: {valid_numbers}")

        # Vérifier les numéros déjà traités si la reprise est activée
        already_processed = []
        if resume:
            # Créer un dossier pour les sauvegardes incrémentielles s'il n'existe pas
            # Utiliser un chemin absolu pour éviter les problèmes de répertoire courant
            try:
                # Obtenir le répertoire du script en cours
                script_dir = os.path.dirname(os.path.abspath(__file__))
                # Créer le chemin absolu vers le dossier de sauvegarde
                incremental_save_dir = os.path.join(script_dir, 'ml_models', 'incremental')
                os.makedirs(incremental_save_dir, exist_ok=True)
                print(f"Dossier de sauvegarde incrémentielle créé: {incremental_save_dir}")
            except Exception as e:
                print(f"Erreur lors de la création du dossier de sauvegarde incrémentielle: {e}")
                # Utiliser un dossier de secours dans le répertoire courant
                incremental_save_dir = os.path.join('ml_models', 'incremental')
                os.makedirs(incremental_save_dir, exist_ok=True)
                print(f"Utilisation du dossier de secours: {incremental_save_dir}")

            print("Vérification des numéros déjà traités...")

            # Vérifier d'abord les numéros déjà chargés en mémoire
            print("Vérification des modèles déjà chargés en mémoire...")
            if hasattr(self, 'models') and self.models:
                print(f"  self.models existe: {type(self.models)}")
                if 'targets' in self.models:
                    print(f"  'targets' existe dans self.models, contient {len(self.models['targets'])} numéros")
                    print(f"  Numéros disponibles en mémoire: {sorted(list(self.models['targets'].keys()))}")

                    for num in valid_numbers:
                        if num in self.models['targets']:
                            # Vérifier si le modèle est complet
                            target_info = self.models['targets'][num]
                            print(f"  Numéro {num}: Trouvé en mémoire, type: {type(target_info)}")

                            if target_info and isinstance(target_info, dict):
                                # Vérifier si le modèle contient les éléments nécessaires
                                if 'model_type' in target_info or 'type' in target_info:
                                    already_processed.append(num)
                                    print(f"  Numéro {num}: Déjà traité, déjà chargé en mémoire")
                                else:
                                    print(f"  Numéro {num}: Trouvé en mémoire mais incomplet, sera retraité")
                            else:
                                print(f"  Numéro {num}: Trouvé en mémoire mais format invalide, sera retraité")
                else:
                    print("  'targets' n'existe pas dans self.models")
            else:
                print("  self.models n'existe pas ou est vide")

            # Ensuite, vérifier les fichiers sur disque pour les numéros non encore chargés
            print("\nVérification des fichiers incrémentiels sur disque...")

            # Vérifier que le répertoire existe
            if not os.path.exists(incremental_save_dir):
                print(f"  Le répertoire {incremental_save_dir} n'existe pas, tentative de création...")
                try:
                    os.makedirs(incremental_save_dir, exist_ok=True)
                    print(f"  Répertoire créé avec succès: {incremental_save_dir}")
                except Exception as e:
                    print(f"  Erreur lors de la création du répertoire: {e}")

            # Lister tous les fichiers incrémentiels disponibles
            try:
                incremental_files = [f for f in os.listdir(incremental_save_dir) if f.startswith('number_') and f.endswith('.pkl')]
                print(f"  {len(incremental_files)} fichiers incrémentiels trouvés dans {incremental_save_dir}")
                if incremental_files:
                    print(f"  Fichiers disponibles: {incremental_files}")
            except Exception as e:
                print(f"  Erreur lors de la lecture du répertoire {incremental_save_dir}: {e}")
                incremental_files = []

            # Essayer également le répertoire courant
            try:
                current_dir_files = [f for f in os.listdir('.') if f.startswith('number_') and f.endswith('.pkl')]
                if current_dir_files:
                    print(f"  {len(current_dir_files)} fichiers incrémentiels trouvés dans le répertoire courant")
                    print(f"  Fichiers disponibles dans le répertoire courant: {current_dir_files}")
            except Exception as e:
                print(f"  Erreur lors de la lecture du répertoire courant: {e}")

            # Vérifier chaque numéro
            for num in valid_numbers:
                if num not in already_processed:  # Ne vérifier que les numéros non encore traités
                    num_save_path = os.path.join(incremental_save_dir, f'number_{num}.pkl')
                    fallback_path = f'number_{num}.pkl'  # Chemin dans le répertoire courant

                    # Vérifier d'abord dans le répertoire incrémentiel
                    if os.path.exists(num_save_path):
                        print(f"  Numéro {num}: Fichier trouvé dans {num_save_path}")
                        try:
                            # Vérifier si le fichier est valide
                            with open(num_save_path, 'rb') as f:
                                num_data = pickle.load(f)

                            # Vérifier si les données sont complètes
                            if 'target_info' in num_data and 'feature_info' in num_data:
                                already_processed.append(num)

                                # Charger les données dans le modèle global
                                if not hasattr(self, 'models') or not self.models:
                                    self.models = {'targets': {}, 'features': {}}

                                self.models['targets'][num] = num_data['target_info']
                                self.models['features'][num] = num_data['feature_info']

                                print(f"  Numéro {num}: Déjà traité, chargé depuis {num_save_path}")
                            else:
                                print(f"  Numéro {num}: Fichier incomplet, manque target_info ou feature_info")
                        except Exception as e:
                            print(f"  Erreur lors du chargement des données pour le numéro {num}: {e}")
                            # Ne pas supprimer automatiquement, juste afficher un avertissement
                            print(f"  ATTENTION: Fichier potentiellement corrompu: {num_save_path}")
                            print(f"  Vous pouvez le supprimer manuellement ou réentraîner le modèle pour ce numéro")
                    # Sinon, vérifier dans le répertoire courant
                    elif os.path.exists(fallback_path):
                        print(f"  Numéro {num}: Fichier trouvé dans le répertoire courant: {fallback_path}")
                        try:
                            # Vérifier si le fichier est valide
                            with open(fallback_path, 'rb') as f:
                                num_data = pickle.load(f)

                            # Vérifier si les données sont complètes
                            if 'target_info' in num_data and 'feature_info' in num_data:
                                already_processed.append(num)

                                # Charger les données dans le modèle global
                                if not hasattr(self, 'models') or not self.models:
                                    self.models = {'targets': {}, 'features': {}}

                                self.models['targets'][num] = num_data['target_info']
                                self.models['features'][num] = num_data['feature_info']

                                print(f"  Numéro {num}: Déjà traité, chargé depuis {fallback_path}")

                                # Essayer de copier le fichier dans le répertoire incrémentiel
                                try:
                                    import shutil
                                    shutil.copy(fallback_path, num_save_path)
                                    print(f"  Fichier copié de {fallback_path} vers {num_save_path}")
                                except Exception as copy_e:
                                    print(f"  Impossible de copier le fichier: {copy_e}")
                            else:
                                print(f"  Numéro {num}: Fichier incomplet dans le répertoire courant")
                        except Exception as e:
                            print(f"  Erreur lors du chargement des données depuis le répertoire courant pour le numéro {num}: {e}")
                            # Ne pas supprimer automatiquement, juste afficher un avertissement
                            print(f"  ATTENTION: Fichier potentiellement corrompu: {fallback_path}")
                            print(f"  Vous pouvez le supprimer manuellement ou réentraîner le modèle pour ce numéro")
                    else:
                        print(f"  Numéro {num}: Aucun fichier trouvé, sera traité")

            # Filtrer les numéros déjà traités de la liste des numéros à traiter
            if already_processed:
                print(f"Reprise: {len(already_processed)} numéros déjà traités: {already_processed}")
                valid_numbers = [num for num in valid_numbers if num not in already_processed]
                print(f"Reprise: {len(valid_numbers)} numéros restants à traiter: {valid_numbers}")

        # Préparer les paramètres pour l'entraînement parallèle si disponible
        if use_parallel:
            models_params = []
            print("Préparation des données pour l'entraînement parallèle...")

        # Préparer les données pour les numéros sélectionnés
        for num in valid_numbers:
            # Vérifier si l'arrêt a été demandé
            if check_stop_requested():
                return False
            # CORRECTION IMPORTANTE: Créer une copie profonde pour chaque numéro
            # pour éviter que tous les numéros aient la même distribution
            num_data = self.df.copy(deep=True)  # Copie profonde pour garantir l'indépendance

            # Créer la cible : le numéro apparaît-il dans le prochain tirage?
            num_data[f'next_has_{num}'] = num_data[f'has_{num}'].shift(-1)

            # Option 1: Supprimer les lignes avec des valeurs manquantes (comportement par défaut)
            # Cette option supprime le dernier échantillon car il n'a pas de "tirage suivant"
            num_data = num_data.dropna(subset=[f'next_has_{num}'])  # Ne supprimer que les lignes où la cible est manquante

            # Option 2: Remplir les valeurs manquantes avec 0 ou 1 (pas de valeurs intermédiaires)
            # Cette option conserve tous les échantillons, mais peut introduire un biais
            # if len(num_data) > 0 and num_data[f'next_has_{num}'].isna().any():
            #     # Calculer la fréquence moyenne d'apparition du numéro
            #     mean_value = num_data[f'has_{num}'].mean()
            #     # Déterminer si on remplit avec 0 ou 1 (arrondir à la valeur la plus proche)
            #     fill_value = 1 if mean_value >= 0.5 else 0
            #     # Remplir les valeurs manquantes
            #     num_data[f'next_has_{num}'].fillna(fill_value, inplace=True)
            #     print(f"  Numéro {num}: Dernier échantillon conservé avec valeur estimée ({fill_value})")

            if len(num_data) < 30:
                print(f"  Numéro {num}: Pas assez de données (minimum 30 tirages requis)")
                continue

            # FOCUS PRINCIPAL: HISTORIQUE DES SORTIES
            # Caractéristiques à utiliser - Priorité absolue à l'historique des numéros
            feature_cols = [
                f'has_{num}'  # Le numéro est-il dans le tirage actuel?
            ]

            # Les caractéristiques temporelles sont moins importantes pour le Keno
            # Nous les gardons mais avec une importance réduite dans le modèle
            temporal_features = [
                'day_of_week', 'hour', 'is_weekend', 'is_afternoon',
                'day', 'month', 'year'
            ]
            # Nous n'utilisons pas les transformations cycliques pour simplifier
            feature_cols.extend(temporal_features)

            # 2. HISTORIQUE COMPLET - Analyse approfondie des tirages précédents

            # 2.1 Tirages individuels récents (50 derniers tirages)
            for i in range(1, 51):  # Augmenté à 50 tirages pour une analyse encore plus complète
                num_data[f'has_{num}_lag_{i}'] = num_data[f'has_{num}'].shift(i).fillna(0)
                feature_cols.append(f'has_{num}_lag_{i}')

                # Ajouter des lags pour les voisins immédiats (pour détecter les motifs de séquence)
                if i <= 10:  # Limiter à 10 lags pour les voisins pour éviter l'explosion des caractéristiques
                    for offset in [-1, 1]:
                        neighbor = num + offset
                        if 1 <= neighbor <= self.max_number:
                            num_data[f'has_neighbor_{neighbor}_lag_{i}'] = num_data[f'has_{neighbor}'].shift(i).fillna(0)
                            feature_cols.append(f'has_neighbor_{neighbor}_lag_{i}')

            # 2.2 Sommes glissantes (combien de fois le numéro est apparu dans les X derniers tirages)
            for window in [5, 10, 20, 30, 50, 100, 200, 500]:
                num_data[f'sum_{window}_{num}'] = num_data[f'has_{num}'].rolling(window=window).sum().fillna(0)
                feature_cols.append(f'sum_{window}_{num}')

                # Ajouter l'écart-type pour mesurer la régularité/volatilité
                num_data[f'std_{window}_{num}'] = num_data[f'has_{num}'].rolling(window=window).std().fillna(0)
                feature_cols.append(f'std_{window}_{num}')

                # Ajouter le coefficient de variation (std/mean) pour normaliser la volatilité
                mean_val = num_data[f'has_{num}'].rolling(window=window).mean().fillna(0.0001)  # Éviter division par zéro
                num_data[f'cv_{window}_{num}'] = num_data[f'std_{window}_{num}'] / mean_val
                feature_cols.append(f'cv_{window}_{num}')

            # 2.3 FRÉQUENCES SUR DIFFÉRENTES PÉRIODES (pourcentage d'apparition)
            for period in [10, 20, 50, 100, 200, 500, 1000]:
                if len(num_data) >= period:
                    num_data[f'freq_{period}_{num}'] = num_data[f'has_{num}'].rolling(window=period).mean().fillna(0)
                    feature_cols.append(f'freq_{period}_{num}')

            # 2.4 Écart-type des apparitions (mesure de la régularité)
            for period in [20, 50, 100]:
                if len(num_data) >= period:
                    num_data[f'std_{period}_{num}'] = num_data[f'has_{num}'].rolling(window=period).std().fillna(0)
                    feature_cols.append(f'std_{period}_{num}')

            # 3. ANALYSE DES INTERVALLES ET DES CYCLES

            # 3.1 TEMPS DEPUIS LA DERNIÈRE APPARITION (et ses variations)
            # Méthode améliorée pour calculer le nombre de tirages depuis la dernière apparition
            appearances = np.where(num_data[f'has_{num}'] == 1)[0]
            if len(appearances) > 0:
                # Créer un tableau d'intervalles entre les apparitions
                intervals = np.diff(appearances)
                # Calculer des statistiques sur ces intervalles
                avg_interval = np.mean(intervals) if len(intervals) > 0 else len(num_data)
                median_interval = np.median(intervals) if len(intervals) > 0 else len(num_data)
                min_interval = np.min(intervals) if len(intervals) > 0 else len(num_data)
                max_interval = np.max(intervals) if len(intervals) > 0 else len(num_data)

                # Ajouter ces statistiques comme caractéristiques constantes
                num_data[f'avg_interval_{num}'] = avg_interval
                num_data[f'median_interval_{num}'] = median_interval
                num_data[f'min_interval_{num}'] = min_interval
                num_data[f'max_interval_{num}'] = max_interval

                # Calculer le temps depuis la dernière apparition
                last_appearance = appearances[-1] if len(appearances) > 0 else 0
                num_data[f'time_since_last_{num}'] = np.arange(len(num_data)) - last_appearance
                num_data.loc[:last_appearance, f'time_since_last_{num}'] = 0  # Avant la première apparition

                # Calculer si l'intervalle actuel est supérieur à la moyenne (indicateur de "maturité")
                num_data[f'overdue_{num}'] = (num_data[f'time_since_last_{num}'] > avg_interval).astype(int)

                # Ajouter toutes ces caractéristiques
                feature_cols.extend([f'avg_interval_{num}', f'median_interval_{num}',
                                    f'min_interval_{num}', f'max_interval_{num}',
                                    f'time_since_last_{num}', f'overdue_{num}'])
            else:
                # Si le numéro n'est jamais apparu
                num_data[f'time_since_last_{num}'] = len(num_data)
                num_data[f'overdue_{num}'] = 1
                feature_cols.extend([f'time_since_last_{num}', f'overdue_{num}'])

            # 3.2 SÉQUENCES ET MOTIFS CYCLIQUES
            # Détecter les séquences d'apparition
            num_data[f'streak_{num}'] = num_data[f'has_{num}'].groupby((num_data[f'has_{num}'] != num_data[f'has_{num}'].shift()).cumsum()).cumcount() + 1
            num_data[f'streak_{num}'] = num_data[f'streak_{num}'] * num_data[f'has_{num}']  # Garder seulement les séquences de 1
            feature_cols.append(f'streak_{num}')

            # Détecter les cycles potentiels (apparition tous les X tirages) - Version ultra-optimisée
            for cycle in [2, 3, 5, 7, 10, 15, 20, 30, 40, 50, 70, 100]:
                # Vérifier si le numéro apparaît souvent tous les 'cycle' tirages
                if len(appearances) >= 3:  # Besoin d'au moins 3 apparitions pour détecter un cycle
                    # Calculer les résidus modulo cycle
                    residuals = appearances % cycle
                    # Compter les occurrences de chaque résidu
                    residual_counts = np.bincount(residuals, minlength=cycle)
                    # Calculer le résidu le plus fréquent
                    most_common_residual = np.argmax(residual_counts)
                    # Calculer le pourcentage d'apparitions qui suivent ce cycle
                    cycle_strength = residual_counts[most_common_residual] / len(appearances)

                    # Calculer l'entropie des résidus (mesure de la régularité du cycle)
                    probs = residual_counts / len(appearances)
                    # Éviter log(0) en filtrant les probabilités non nulles
                    non_zero_probs = probs[probs > 0]
                    entropy = -np.sum(non_zero_probs * np.log2(non_zero_probs))
                    # Normaliser l'entropie (0 = parfaitement régulier, 1 = complètement aléatoire)
                    max_entropy = np.log2(cycle) if cycle > 1 else 1
                    normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0

                    # Calculer un score de confiance du cycle (force du cycle ajustée par l'entropie)
                    # Plus l'entropie est faible, plus le cycle est régulier
                    cycle_confidence = cycle_strength * (1 - normalized_entropy)

                    # Ajouter une caractéristique indiquant la force du cycle
                    num_data[f'cycle_{cycle}_strength_{num}'] = cycle_strength
                    feature_cols.append(f'cycle_{cycle}_strength_{num}')

                    # Ajouter l'entropie comme caractéristique
                    num_data[f'cycle_{cycle}_entropy_{num}'] = normalized_entropy
                    feature_cols.append(f'cycle_{cycle}_entropy_{num}')

                    # Ajouter le score de confiance comme caractéristique
                    num_data[f'cycle_{cycle}_confidence_{num}'] = cycle_confidence
                    feature_cols.append(f'cycle_{cycle}_confidence_{num}')

                    # Ajouter une caractéristique indiquant si le prochain tirage pourrait suivre ce cycle
                    # Calculer où nous sommes dans le cycle
                    current_position = len(num_data) % cycle

                    # Vérifier les 3 prochaines positions possibles (pour capturer les cycles avec un peu de bruit)
                    for offset in range(3):
                        next_pos = (current_position + 1 + offset) % cycle
                        matches_cycle = 1 if next_pos == most_common_residual else 0
                        num_data[f'next_in_cycle_{cycle}_offset{offset}_{num}'] = matches_cycle
                        feature_cols.append(f'next_in_cycle_{cycle}_offset{offset}_{num}')

                        # Ajouter une caractéristique pondérée par la force du cycle et la confiance
                        num_data[f'cycle_prediction_{cycle}_offset{offset}_{num}'] = matches_cycle * cycle_confidence
                        feature_cols.append(f'cycle_prediction_{cycle}_offset{offset}_{num}')

                    # Trouver les 3 résidus les plus fréquents pour des prédictions plus robustes
                    top_residuals = np.argsort(residual_counts)[-3:]
                    for i, residual in enumerate(top_residuals):
                        # Calculer la force de ce résidu spécifique
                        residual_strength = residual_counts[residual] / len(appearances)
                        # Vérifier si le prochain tirage correspond à ce résidu
                        next_matches = 1 if (current_position + 1) % cycle == residual else 0
                        # Ajouter comme caractéristique
                        num_data[f'cycle_{cycle}_top{i+1}_{num}'] = next_matches * residual_strength
                        feature_cols.append(f'cycle_{cycle}_top{i+1}_{num}')

                    # Ajouter comme caractéristique
                    num_data[f'cycle_{cycle}_{num}'] = cycle_strength
                    feature_cols.append(f'cycle_{cycle}_{num}')

                    # Ajouter une caractéristique indiquant si le prochain tirage pourrait suivre ce cycle
                    next_position = (len(num_data) % cycle)
                    is_cycle_match = (next_position == most_common_residual)
                    num_data[f'next_in_cycle_{cycle}_{num}'] = is_cycle_match
                    feature_cols.append(f'next_in_cycle_{cycle}_{num}')
                else:
                    # Valeurs par défaut si pas assez d'apparitions
                    num_data[f'cycle_{cycle}_{num}'] = 0
                    num_data[f'next_in_cycle_{cycle}_{num}'] = 0
                    feature_cols.extend([f'cycle_{cycle}_{num}', f'next_in_cycle_{cycle}_{num}'])

            # 4. RELATIONS ENTRE NUMÉROS ET ANALYSE DE GROUPE

            # 4.1 NUMÉROS VOISINS - Analyse des numéros proches
            for offset in [-5, -4, -3, -2, -1, 1, 2, 3, 4, 5]:  # Voisins plus éloignés
                neighbor = num + offset
                if 1 <= neighbor <= self.max_number:
                    # Présence du voisin dans le tirage actuel
                    num_data[f'has_neighbor_{neighbor}'] = num_data[f'has_{neighbor}']
                    feature_cols.append(f'has_neighbor_{neighbor}')

                    # Corrélation avec ce voisin (sur différentes périodes)
                    for period in [20, 50, 100]:
                        if len(num_data) >= period:
                            num_data[f'corr_{period}_{num}_{neighbor}'] = num_data[f'has_{num}'].rolling(window=period).corr(num_data[f'has_{neighbor}']).fillna(0)
                            feature_cols.append(f'corr_{period}_{num}_{neighbor}')

                    # Apparition simultanée (combien de fois les deux numéros sont sortis ensemble)
                    num_data[f'both_{num}_{neighbor}'] = (num_data[f'has_{num}'] & num_data[f'has_{neighbor}']).astype(int)
                    # Calculer la fréquence d'apparition simultanée sur différentes périodes
                    for period in [20, 50, 100]:
                        if len(num_data) >= period:
                            num_data[f'both_freq_{period}_{num}_{neighbor}'] = num_data[f'both_{num}_{neighbor}'].rolling(window=period).mean().fillna(0)
                            feature_cols.append(f'both_freq_{period}_{num}_{neighbor}')

            # 4.2 GROUPES DE NUMÉROS (dizaines, parité, etc.)
            # Déterminer le groupe de dizaine du numéro (1-10, 11-20, etc.)
            decade_group = (num - 1) // 10
            # Calculer combien de numéros de ce groupe sont sortis dans les derniers tirages
            for period in [1, 5, 10, 20]:
                decade_counts = np.zeros(len(num_data))
                for n in range(decade_group * 10 + 1, min(decade_group * 10 + 11, self.max_number + 1)):
                    if n != num:  # Exclure le numéro lui-même
                        for i in range(period):
                            if i < len(num_data):
                                decade_counts += num_data[f'has_{n}'].shift(i).fillna(0).values
                num_data[f'decade_count_{period}_{num}'] = decade_counts
                feature_cols.append(f'decade_count_{period}_{num}')

            # Parité (pair/impair)
            is_even = (num % 2 == 0)
            # Compter combien de numéros de même parité sont sortis récemment
            for period in [1, 5, 10]:
                parity_counts = np.zeros(len(num_data))
                for n in range(1, self.max_number + 1):
                    if (n % 2 == 0) == is_even and n != num:  # Même parité, mais pas le numéro lui-même
                        for i in range(period):
                            if i < len(num_data):
                                parity_counts += num_data[f'has_{n}'].shift(i).fillna(0).values
                num_data[f'parity_count_{period}_{num}'] = parity_counts
                feature_cols.append(f'parity_count_{period}_{num}')

            # 5. TENDANCES ET DYNAMIQUES

            # 5.1 NUMÉROS "CHAUDS" ET "FROIDS"
            # Calculer si le numéro est "chaud" (sorti récemment plus que d'habitude) ou "froid"
            for short_period, long_period in [(10, 50), (20, 100), (50, 200)]:
                if len(num_data) >= long_period:
                    recent_freq = num_data[f'has_{num}'].rolling(window=short_period).mean().fillna(0)
                    long_term_freq = num_data[f'has_{num}'].rolling(window=long_period).mean().fillna(0)
                    num_data[f'hot_cold_{short_period}_{long_period}_{num}'] = (recent_freq - long_term_freq) * 100
                    feature_cols.append(f'hot_cold_{short_period}_{long_period}_{num}')

            # 5.2 TENDANCES (augmentation ou diminution de la fréquence)
            # Calculer la pente de la fréquence d'apparition sur différentes périodes
            for period in [20, 50, 100]:
                if len(num_data) >= period:
                    # Calculer la pente en comparant la première et la deuxième moitié de la période
                    half_period = period // 2
                    first_half = num_data[f'has_{num}'].rolling(window=half_period).mean().shift(half_period).fillna(0)
                    second_half = num_data[f'has_{num}'].rolling(window=half_period).mean().fillna(0)
                    num_data[f'trend_{period}_{num}'] = second_half - first_half
                    feature_cols.append(f'trend_{period}_{num}')

            # Diagnostics pour vérifier les caractéristiques générées
            print(f"  Numéro {num}: Génération de {len(feature_cols)} caractéristiques basées sur l'historique")

            # Ajouter des caractéristiques sur les numéros voisins
            for offset in [-2, -1, 1, 2]:
                neighbor = num + offset
                if 1 <= neighbor <= self.max_number:
                    num_data[f'has_neighbor_{neighbor}'] = num_data[f'has_{neighbor}']
                    feature_cols.append(f'has_neighbor_{neighbor}')

            # Diviser en ensembles d'entraînement et de test
            X = num_data[feature_cols]
            y = num_data[f'next_has_{num}']

            # Vérifier que la colonne cible est bien spécifique à ce numéro
            if f'next_has_{num}' not in num_data.columns:
                print(f"ERREUR: La colonne 'next_has_{num}' n'existe pas dans les données!")
                print(f"Colonnes disponibles: {num_data.columns.tolist()}")
                continue

            # Vérifier que les données sont bien spécifiques à ce numéro
            has_num_col = f'has_{num}' if f'has_{num}' in num_data.columns else None
            if has_num_col:
                # Calculer des statistiques détaillées sur la colonne has_{num}
                has_sum = num_data[has_num_col].sum()
                has_mean = num_data[has_num_col].mean()
                has_count = len(num_data[has_num_col])
                has_percent = has_mean * 100

                print(f"  Numéro {num}: Vérification de la colonne '{has_num_col}':")
                print(f"    - Somme: {has_sum} (nombre de fois où le numéro est sorti)")
                print(f"    - Moyenne: {has_mean:.4f} (fréquence d'apparition)")
                print(f"    - Pourcentage: {has_percent:.2f}% des tirages")
                print(f"    - Nombre total de tirages: {has_count}")

                # Vérifier si la fréquence est proche de la valeur théorique (20/70 = 28.57%)
                theoretical = 20/70 * 100  # 28.57%
                diff = abs(has_percent - theoretical)
                if diff < 2.0:  # Moins de 2% d'écart
                    print(f"    - Fréquence normale (proche de la valeur théorique de {theoretical:.2f}%)")
                elif has_percent > theoretical:
                    print(f"    - Fréquence PLUS élevée que la normale (+{diff:.2f}%)")
                else:
                    print(f"    - Fréquence MOINS élevée que la normale (-{diff:.2f}%)")

            # Vérifier la distribution des classes dans les données brutes
            raw_class_counts = np.bincount(y.astype(int).values, minlength=2)
            raw_class_ratio = raw_class_counts[0] / raw_class_counts[1] if raw_class_counts[1] > 0 else float('inf')
            print(f"  Numéro {num}: Distribution brute des classes: {raw_class_counts}")
            print(f"  Numéro {num}: Ratio brut classe 0/classe 1: {raw_class_ratio:.4f}")
            print(f"  Numéro {num}: Pourcentage de classe 1: {raw_class_counts[1]/sum(raw_class_counts)*100:.2f}%")

            # IMPORTANT: Vérifier si cette distribution est identique pour tous les numéros
            # Si c'est le cas, c'est normal pour le Keno car tous les numéros ont la même probabilité d'apparition
            if hasattr(self, 'last_raw_class_counts') and np.array_equal(self.last_raw_class_counts, raw_class_counts):
                print(f"  NOTE: Le numéro {num} a la même distribution que le numéro précédent.")
                print(f"  C'est normal pour le Keno car tous les numéros ont la même probabilité d'apparition (20/70 = 28.57%).")
            else:
                # Si la distribution est différente, calculer l'écart
                if hasattr(self, 'last_raw_class_counts'):
                    diff_0 = abs(raw_class_counts[0] - self.last_raw_class_counts[0])
                    diff_1 = abs(raw_class_counts[1] - self.last_raw_class_counts[1])
                    print(f"  Différence avec le numéro précédent: Classe 0: {diff_0}, Classe 1: {diff_1}")

            # Stocker pour comparaison avec le prochain numéro
            self.last_raw_class_counts = raw_class_counts.copy()

            # Vérifier que la colonne next_has_{num} est bien dérivée de has_{num}
            # Calculer la corrélation entre has_{num} et next_has_{num} décalé
            has_shifted = num_data[f'has_{num}'].shift(-1)
            correlation = has_shifted.corr(num_data[f'next_has_{num}'])
            print(f"  Numéro {num}: Corrélation entre has_{num} décalé et next_has_{num}: {correlation:.6f}")
            if correlation != 1.0:
                print(f"  ERREUR: La colonne next_has_{num} n'est pas correctement dérivée de has_{num}!")
            else:
                print(f"  Vérification OK: La colonne next_has_{num} est correctement dérivée de has_{num}")

            # Utiliser toutes les données disponibles pour l'entraînement
            # Aucun échantillonnage pour garantir la meilleure précision possible
            print(f"  Numéro {num}: Utilisation de toutes les données disponibles ({len(X)} échantillons) pour l'entraînement")

            # Diagnostics pour vérifier que les données sont différentes pour chaque numéro
            # Calculer quelques statistiques sur les caractéristiques
            recent_freq = num_data[f'freq_20_{num}'].iloc[-1] if f'freq_20_{num}' in num_data else 0
            time_since = num_data[f'time_since_last_{num}'].iloc[-1] if f'time_since_last_{num}' in num_data else 0
            hot_cold = num_data[f'hot_cold_{num}'].iloc[-1] if f'hot_cold_{num}' in num_data else 0

            print(f"  Numéro {num}: Caractéristiques spécifiques: fréquence_récente={recent_freq:.4f}, temps_depuis={time_since:.0f}, chaud_froid={hot_cold:.2f}")
            print(f"  Numéro {num}: Nombre de caractéristiques: {len(feature_cols)}")

            # Stocker les informations sur les caractéristiques
            features[num] = {
                'feature_cols': feature_cols,
                'scaler': None  # Sera rempli plus tard
            }

            # Si on utilise le traitement parallèle, préparer les paramètres pour ce numéro
            if use_parallel:
                # Convertir les données pandas en numpy pour le traitement parallèle
                X_np = X.values if isinstance(X, pd.DataFrame) else X
                y_np = y.values if isinstance(y, pd.Series) else y

                # Ajouter les paramètres pour ce numéro
                models_params.append({
                    'number': num,
                    'X': X_np,
                    'y': y_np,
                    'test_size': test_size,
                    'random_state': random_state,
                    'use_gpu': use_gpu,
                    'n_jobs': 1,  # Chaque processus utilise 1 cœur pour éviter la surcharge
                    'fast_mode': fast_mode  # Transmettre le mode rapide au worker
                })
            else:
                # Normaliser les caractéristiques
                X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state)
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)

                # Stocker le scaler
                features[num]['scaler'] = scaler

        # Entraîner les modèles
        # Le traitement parallèle est désactivé pour éviter les blocages
        use_parallel = False  # Force l'utilisation du traitement séquentiel

        # Si le traitement parallèle n'est pas disponible ou a échoué, utiliser le traitement séquentiel
        if not use_parallel:
            # Continuer avec l'entraînement séquentiel pour chaque numéro sélectionné
            for num in valid_numbers:
                # Vérifier si l'arrêt a été demandé
                if check_stop_requested():
                    return False
                if num not in features:
                    continue

                # Déterminer le nombre de cœurs à utiliser
                n_jobs = getattr(self, 'n_jobs', -1)  # Utiliser tous les cœurs par défaut

                # Entraîner plusieurs modèles et choisir le meilleur
                models = {}

                # Vérifier si RandomForest est activé
                rf_params = self.model_params.get('random_forest', {})
                use_rf = rf_params.get('use_model', True)

                if use_rf:
                    print(f"  Numéro {num}: Entraînement de RandomForest...")
                    try:
                        # Créer le modèle avec les paramètres configurés
                        from sklearn.ensemble import RandomForestClassifier
                        rf_model = RandomForestClassifier(
                            n_estimators=rf_params.get('n_estimators', 1000),
                            max_depth=rf_params.get('max_depth', 25),
                            min_samples_split=rf_params.get('min_samples_split', 4),
                            min_samples_leaf=rf_params.get('min_samples_leaf', 2),
                            max_features=rf_params.get('max_features', 'sqrt'),
                            bootstrap=rf_params.get('bootstrap', True),
                            class_weight=rf_params.get('class_weight', 'balanced_subsample'),
                            random_state=random_state,
                            n_jobs=1  # Utiliser 1 cœur par modèle pour éviter la surcharge
                        )

                        # Entraîner le modèle
                        rf_model.fit(X_train, y_train)

                        # Évaluer le modèle
                        y_pred = rf_model.predict(X_test)
                        accuracy = accuracy_score(y_test, y_pred)
                        precision = precision_score(y_test, y_pred, zero_division=0)
                        recall = recall_score(y_test, y_pred, zero_division=0)
                        f1 = f1_score(y_test, y_pred, zero_division=0)

                        # Calculer l'AUC si possible
                        try:
                            y_proba = rf_model.predict_proba(X_test)[:, 1]
                            auc = roc_auc_score(y_test, y_proba)
                        except:
                            auc = 0.5

                        # Trouver le seuil optimal
                        optimal_threshold = 0.5  # Valeur par défaut
                        try:
                            y_proba = rf_model.predict_proba(X_test)[:, 1]
                            fpr, tpr, thresholds = roc_curve(y_test, y_proba)
                            optimal_idx = np.argmax(tpr - fpr)
                            optimal_threshold = thresholds[optimal_idx]
                        except:
                            pass

                        # Ajouter le modèle au dictionnaire
                        models['random_forest'] = {
                            'model': rf_model,
                            'accuracy': accuracy,
                            'precision': precision,
                            'recall': recall,
                            'f1': f1,
                            'auc': auc,
                            'threshold': optimal_threshold,
                            'type': 'random_forest'
                        }

                        print(f"  Numéro {num}: RandomForest entraîné avec succès - Précision: {accuracy:.4f}, F1: {f1:.4f}")
                    except Exception as e:
                        print(f"  Numéro {num}: Erreur lors de l'entraînement de RandomForest: {e}")
                else:
                    print(f"  Numéro {num}: RandomForest ignoré (désactivé dans les paramètres)")

                # Ajouter des modèles de deep learning si disponible
                if self.deep_learning_available and self.deep_learning:
                    # Récupérer les paramètres de deep learning configurés par l'utilisateur
                    dl_params = getattr(self, 'model_params', {}).get('deep_learning', {})
                    batch_size = dl_params.get('batch_size', 32)
                    epochs = dl_params.get('epochs', 20)
                    neurons = dl_params.get('neurons', 64)
                    dropout_rate = dl_params.get('dropout_rate', 0.2)

                    # Récupérer les préférences pour les modèles à utiliser
                    use_simple = dl_params.get('use_simple', True)
                    use_deep = dl_params.get('use_deep', True)
                    use_cnn = dl_params.get('use_cnn', True)
                    use_ensemble = dl_params.get('use_ensemble', True)

                    # En mode ultra-rapide, n'entraîner qu'un seul modèle simple
                    if ultra_fast:
                        print(f"  Numéro {num}: Ajout d'un modèle de deep learning simple (mode ULTRA-RAPIDE)")
                        try:
                            # Entraîner un modèle simple avec paramètres ultra-rapides
                            if use_simple:
                                print(f"    Entraînement du modèle de deep learning simple...")
                                # Valeur par défaut: 64 en mode ultra-rapide
                                ultra_batch_size = dl_params.get('batch_size', 64)

                                dl_simple = self.deep_learning.train_model(
                                    X, y, model_type='simple', test_size=test_size, random_state=random_state,
                                    batch_size=ultra_batch_size, epochs=10, early_stopping=True, verbose=0, timeout=60,
                                    neurons=neurons, dropout_rate=dropout_rate, ultra_fast=True
                                )
                                if dl_simple:
                                    models['deep_learning_simple'] = dl_simple
                                    print(f"    Modèle simple entraîné avec succès - Précision: {dl_simple['accuracy']:.4f}")
                            else:
                                print(f"    Modèle simple ignoré (désactivé dans les paramètres)")
                        except Exception as e:
                            print(f"    Erreur lors de l'entraînement du modèle simple: {e}")
                    # En mode rapide, entraîner seulement le modèle simple et CNN
                    elif fast_mode:
                        print(f"  Numéro {num}: Ajout de modèles de deep learning (mode rapide)")
                        try:
                            # Entraîner un modèle simple (feed-forward)
                            if use_simple:
                                print(f"    Entraînement du modèle de deep learning simple...")

                                dl_simple = self.deep_learning.train_model(
                                    X, y, model_type='simple', test_size=test_size, random_state=random_state,
                                    batch_size=batch_size, epochs=epochs, early_stopping=True, verbose=0, timeout=120,
                                    neurons=neurons, dropout_rate=dropout_rate
                                )
                                if dl_simple:
                                    models['deep_learning_simple'] = dl_simple
                                    print(f"    Modèle simple entraîné avec succès - Précision: {dl_simple['accuracy']:.4f}")
                            else:
                                print(f"    Modèle simple ignoré (désactivé dans les paramètres)")

                            # Vérifier si l'arrêt a été demandé
                            if check_stop_requested():
                                return False

                            # Entraîner un modèle CNN si nous avons suffisamment de caractéristiques
                            if X.shape[1] >= 10 and use_cnn:
                                print(f"    Entraînement du modèle CNN...")
                                # Utiliser les mêmes paramètres que pour le modèle simple, mais avec moins d'époques
                                cnn_epochs = min(15, epochs)  # Limiter à 15 époques maximum pour le CNN

                                dl_cnn = self.deep_learning.train_model(
                                    X, y, model_type='cnn', test_size=test_size, random_state=random_state,
                                    batch_size=batch_size, epochs=cnn_epochs, early_stopping=True, verbose=0, timeout=120,
                                    neurons=neurons, dropout_rate=dropout_rate
                                )
                                if dl_cnn:
                                    models['deep_learning_cnn'] = dl_cnn
                                    print(f"    Modèle CNN entraîné avec succès - Précision: {dl_cnn['accuracy']:.4f}")
                            elif X.shape[1] >= 10:
                                print(f"    Modèle CNN ignoré (désactivé dans les paramètres)")
                        except Exception as e:
                            print(f"    Erreur lors de l'entraînement des modèles en mode rapide: {e}")
                    # En mode complet, entraîner tous les modèles
                    else:
                        print(f"  Numéro {num}: Ajout de modèles de deep learning (mode complet)")
                        try:
                            # Par défaut 30 époques en mode complet
                            complete_epochs = dl_params.get('epochs', 30)

                            # Entraîner un modèle simple (feed-forward)
                            if use_simple:
                                print(f"    Entraînement du modèle de deep learning simple...")
                                dl_simple = self.deep_learning.train_model(
                                    X, y, model_type='simple', test_size=test_size, random_state=random_state,
                                    batch_size=batch_size, epochs=complete_epochs, early_stopping=True, verbose=0, timeout=180,
                                    neurons=neurons, dropout_rate=dropout_rate
                                )
                                if dl_simple:
                                    models['deep_learning_simple'] = dl_simple
                                    print(f"    Modèle simple entraîné avec succès - Précision: {dl_simple['accuracy']:.4f}")
                            else:
                                print(f"    Modèle simple ignoré (désactivé dans les paramètres)")

                            # Vérifier si l'arrêt a été demandé
                            if check_stop_requested():
                                return False

                            # Entraîner un modèle profond (deep feed-forward)
                            if use_deep:
                                print(f"    Entraînement du modèle de deep learning profond...")
                                dl_deep = self.deep_learning.train_model(
                                    X, y, model_type='deep', test_size=test_size, random_state=random_state,
                                    batch_size=batch_size, epochs=complete_epochs, early_stopping=True, verbose=0, timeout=180,
                                    neurons=neurons, dropout_rate=dropout_rate
                                )
                                if dl_deep:
                                    models['deep_learning_deep'] = dl_deep
                                    print(f"    Modèle profond entraîné avec succès - Précision: {dl_deep['accuracy']:.4f}")
                            else:
                                print(f"    Modèle profond ignoré (désactivé dans les paramètres)")

                            # Vérifier si l'arrêt a été demandé
                            if check_stop_requested():
                                return False

                            # Entraîner un modèle CNN si nous avons suffisamment de caractéristiques
                            if X.shape[1] >= 10 and use_cnn:
                                print(f"    Entraînement du modèle CNN...")
                                # Limiter le nombre d'époques pour le CNN
                                cnn_epochs = min(20, complete_epochs)  # Limiter à 20 époques maximum pour le CNN

                                dl_cnn = self.deep_learning.train_model(
                                    X, y, model_type='cnn', test_size=test_size, random_state=random_state,
                                    batch_size=batch_size, epochs=cnn_epochs, early_stopping=True, verbose=0, timeout=180,
                                    neurons=neurons, dropout_rate=dropout_rate
                                )
                                if dl_cnn:
                                    models['deep_learning_cnn'] = dl_cnn
                                    print(f"    Modèle CNN entraîné avec succès - Précision: {dl_cnn['accuracy']:.4f}")
                            elif X.shape[1] >= 10:
                                print(f"    Modèle CNN ignoré (désactivé dans les paramètres)")

                            # Vérifier si l'arrêt a été demandé
                            if check_stop_requested():
                                return False

                            # Créer un ensemble de modèles de deep learning
                            if use_ensemble:
                                print(f"    Création d'un ensemble de modèles de deep learning...")
                                dl_ensemble = self.deep_learning.create_ensemble_model(
                                    X, y, n_models=3, model_type='simple'
                                )

                                if dl_ensemble:
                                    models['deep_learning_ensemble'] = dl_ensemble
                                    print(f"    Ensemble de modèles créé avec succès - Précision: {dl_ensemble['accuracy']:.4f}")
                            else:
                                print(f"    Ensemble de modèles ignoré (désactivé dans les paramètres)")

                            # Vérifier si l'arrêt a été demandé
                            if check_stop_requested():
                                return False

                            # Optimiser les hyperparamètres si nous avons suffisamment de données
                            if len(X) >= 1000 and len(y) >= 1000 and use_simple:  # Utiliser le modèle simple pour l'optimisation
                                print(f"    Optimisation des hyperparamètres du modèle de deep learning...")
                                dl_optimized = self.deep_learning.optimize_hyperparameters(
                                    X, y, model_type='simple', n_trials=5
                                )
                                if dl_optimized and 'model' in dl_optimized:
                                    models['deep_learning_optimized'] = dl_optimized['model']
                                    print(f"    Modèle optimisé créé avec succès - Précision: {dl_optimized['model']['accuracy']:.4f}")
                                    print(f"    Meilleurs hyperparamètres: {dl_optimized['best_params']}")
                            elif len(X) >= 1000 and len(y) >= 1000:
                                print(f"    Optimisation des hyperparamètres ignorée (modèle simple désactivé)")

                            # Générer des visualisations pour le meilleur modèle
                            best_dl_model = None
                            best_dl_accuracy = 0
                            for model_name, model_info in models.items():
                                if 'deep_learning' in model_name and model_info['accuracy'] > best_dl_accuracy:
                                    best_dl_model = model_info
                                    best_dl_accuracy = model_info['accuracy']

                            if best_dl_model and 'model' in best_dl_model:
                                print(f"    Génération de visualisations pour le meilleur modèle de deep learning...")
                                viz_path = self.deep_learning.visualize_model(
                                    best_dl_model['model'], X, y, num=num
                                )
                                if viz_path:
                                    print(f"    Visualisations générées dans {viz_path}")
                                    # Stocker le chemin de la visualisation dans le modèle
                                    best_dl_model['visualization_path'] = viz_path
                        except Exception as e:
                            print(f"    Erreur lors de l'entraînement des modèles en mode complet: {e}")
                            import traceback
                            traceback.print_exc()

                # Désactiver GradientBoosting car il cause des erreurs persistantes
                print(f"  Numéro {num}: Ignorer GradientBoosting pour éviter les erreurs 'cannot unpack non-iterable int object'")

                # 3. XGBoost si disponible et activé
                xgb_params = self.model_params.get('xgboost', {})
                use_xgb = xgb_params.get('use_model', True)

                if use_xgb:
                    try:
                        # Essayer d'importer XGBoost
                        try:
                            from xgboost import XGBClassifier
                        except ImportError:
                            print("  XGBoost non disponible, ignorant ce modèle")
                            continue

                        # Configurer XGBoost avec des paramètres optimisés
                        if fast_mode:
                            xgb_config = {
                                'n_estimators': 100,          # Réduit pour accélérer l'entraînement
                                'max_depth': 5,              # Réduit pour accélérer l'entraînement
                                'learning_rate': 0.1,         # Augmenté pour converger plus rapidement
                                'min_child_weight': 3,        # Valeur optimale pour éviter le surajustement
                                'gamma': 0.1,                 # Paramètre de régularisation pour éviter le surapprentissage
                                'subsample': 0.7,             # Réduit pour accélérer l'entraînement
                                'colsample_bytree': 0.7,      # Réduit pour accélérer l'entraînement
                                'colsample_bylevel': 0.7,     # Réduit pour accélérer l'entraînement
                                'reg_alpha': 0.1,             # Régularisation L1
                                'reg_lambda': 1.0,            # Régularisation L2
                                'scale_pos_weight': 1.0,      # Équilibrer les classes
                                'verbosity': 0,               # Désactive les messages d'avertissement
                                'random_state': random_state,
                                'n_jobs': n_jobs              # Parallélisation
                            }
                            print(f"  Numéro {num}: Utilisation de paramètres optimisés pour XGBoost (mode rapide)")
                        else:
                            # Paramètres ultra-optimisés pour l'analyse de l'historique des sorties
                            xgb_config = {
                                'n_estimators': 300,           # Augmenté encore plus pour une meilleure précision
                                'max_depth': 8,                # Optimisé pour capturer des motifs plus complexes
                                'learning_rate': 0.01,         # Réduit davantage pour un apprentissage plus fin et stable
                                'min_child_weight': 2,         # Optimisé pour les classes déséquilibrées
                                'gamma': 0.03,                 # Optimisé pour réduire le surapprentissage tout en permettant la complexité
                                'subsample': 0.8,              # Réduit pour éviter le surapprentissage
                                'colsample_bytree': 0.8,       # Optimisé pour l'équilibre diversité/précision
                                'colsample_bylevel': 0.8,      # Optimisé pour l'équilibre diversité/précision
                                'colsample_bynode': 0.8,       # Optimisé pour l'équilibre diversité/précision
                                'reg_alpha': 0.03,             # Optimisé pour la régularisation L1 (plus faible pour permettre plus de caractéristiques)
                                'reg_lambda': 0.7,             # Optimisé pour la régularisation L2
                                'scale_pos_weight': 1.0,       # Sera ajusté dynamiquement plus tard
                                'verbosity': 0,                # Désactive les messages d'avertissement
                                'random_state': random_state + num,  # Différent pour chaque numéro
                                'n_jobs': 1,                   # Limiter à 1 cœur pour éviter les problèmes
                                'importance_type': 'gain',     # Mesure de l'importance des caractéristiques
                                'booster': 'gbtree',          # Utiliser des arbres de décision
                                'objective': 'binary:logistic',# Optimisé pour la classification binaire
                                'eval_metric': 'auc',          # Utiliser l'AUC comme métrique d'évaluation
                                'tree_method': 'hist',         # Méthode d'arbre optimisée pour la vitesse
                                'grow_policy': 'lossguide',    # Stratégie de croissance des arbres optimisée
                                'max_leaves': 256,             # Limite le nombre de feuilles pour éviter le surapprentissage
                                'max_bin': 256                 # Optimise la discrétisation des caractéristiques
                            }
                            print(f"  Numéro {num}: Utilisation de paramètres optimisés pour XGBoost (mode optimisé)")

                        # Utiliser des paramètres simples pour XGBoost
                        xgb_config.update({
                            'tree_method': 'hist',      # Méthode optimisée pour CPU
                            'device': 'cpu',           # Forcer l'utilisation du CPU
                            'predictor': 'cpu_predictor', # Utiliser CPU pour les prédictions
                        })
                        print("  Utilisation du CPU pour XGBoost (mode sécurisé)")

                        xgb_model = XGBClassifier(**xgb_config)

                        # Vérifier si l'arrêt a été demandé avant l'entraînement
                        if check_stop_requested():
                            return False

                        # Utiliser les statistiques des données brutes pour calculer scale_pos_weight
                        # Récupérer les statistiques calculées plus tôt
                        raw_class_counts = np.bincount(y.astype(int).values, minlength=2)

                        # Calculer le ratio directement à partir des données brutes
                        if raw_class_counts[1] > 0:
                            # Ratio classe majoritaire / classe minoritaire
                            if raw_class_counts[0] > raw_class_counts[1]:
                                # Classe 0 majoritaire, classe 1 minoritaire
                                weight_ratio = raw_class_counts[0] / raw_class_counts[1]
                                minority_class = 1
                                majority_class = 0
                            else:
                                # Classe 1 majoritaire, classe 0 minoritaire
                                weight_ratio = raw_class_counts[1] / raw_class_counts[0]
                                minority_class = 0
                                majority_class = 1

                            # Afficher des informations détaillées sur le calcul
                            print(f"  Numéro {num}: Distribution des classes brutes: {raw_class_counts}")
                            print(f"  Numéro {num}: Classe minoritaire: {minority_class}, Classe majoritaire: {majority_class}")
                            print(f"  Numéro {num}: Nombre d'échantillons - Classe {minority_class}: {raw_class_counts[minority_class]}, Classe {majority_class}: {raw_class_counts[majority_class]}")
                            print(f"  Numéro {num}: Calcul du ratio: {raw_class_counts[majority_class]} / {raw_class_counts[minority_class]} = {weight_ratio:.4f}")

                            # Mettre à jour le paramètre scale_pos_weight
                            if minority_class == 1:  # Si la classe positive est minoritaire
                                # Forcer la valeur spécifique à ce numéro
                                xgb_model.scale_pos_weight = weight_ratio
                                print(f"  Numéro {num}: XGBoost scale_pos_weight ajusté à {weight_ratio:.4f}")
                            else:
                                # Si la classe positive est majoritaire, utiliser l'inverse du ratio
                                # pour donner plus de poids à la classe négative
                                inverse_ratio = 1.0 / weight_ratio
                                xgb_model.scale_pos_weight = inverse_ratio
                                print(f"  Numéro {num}: Classe positive majoritaire, scale_pos_weight ajusté à l'inverse: {inverse_ratio:.4f}")
                        else:
                            print(f"  Numéro {num}: Impossible de calculer le ratio - aucun exemple de classe 1")
                            # Valeur par défaut raisonnable
                            xgb_model.scale_pos_weight = 1.0

                        # Vérifier que la valeur a bien été appliquée
                        print(f"  Numéro {num}: Valeur finale de scale_pos_weight: {xgb_model.scale_pos_weight}")

                        # Prétraiter les données pour éliminer les valeurs NaN
                        # Remplacer les valeurs NaN par 0 dans les données d'entrée
                        X_train_scaled_clean = np.nan_to_num(X_train_scaled, nan=0.0)
                        X_test_scaled_clean = np.nan_to_num(X_test_scaled, nan=0.0)

                        # Vérifier s'il reste des valeurs NaN
                        if np.isnan(X_train_scaled_clean).any() or np.isnan(X_test_scaled_clean).any():
                            print(f"  Numéro {num}: Attention: Des valeurs NaN persistent dans les données après nettoyage pour XGBoost.")
                            return False

                        # Entraîner directement sans équilibrage manuel
                        xgb_model.fit(X_train_scaled_clean, y_train)
                        xgb_pred = xgb_model.predict(X_test_scaled_clean)
                        xgb_accuracy = accuracy_score(y_test, xgb_pred)
                        models['xgboost'] = {'model': xgb_model, 'accuracy': xgb_accuracy}

                        # Diagnostics avancés pour XGBoost
                        from sklearn.metrics import precision_score, recall_score, f1_score, confusion_matrix, roc_auc_score

                        # Matrice de confusion
                        try:
                            cm = confusion_matrix(y_test, xgb_pred)
                            # Vérifier si la matrice de confusion a la bonne taille
                            if cm.size == 4:  # Matrice 2x2 pour classification binaire
                                tn, fp, fn, tp = cm.ravel()
                            else:
                                # Pour les cas où la matrice n'est pas 2x2
                                print(f"  Numéro {num}: Matrice de confusion de taille inattendue pour XGBoost: {cm.shape}")
                                tn, fp, fn, tp = 0, 0, 0, 0
                        except Exception as e:
                            print(f"  Numéro {num}: Erreur lors du calcul de la matrice de confusion pour XGBoost: {e}")
                            tn, fp, fn, tp = 0, 0, 0, 0

                        # Calculer précision, rappel et F1-score
                        try:
                            precision = precision_score(y_test, xgb_pred)
                            recall = recall_score(y_test, xgb_pred)
                            f1 = f1_score(y_test, xgb_pred)

                            # Obtenir les probabilités pour calculer l'AUC
                            xgb_proba = xgb_model.predict_proba(X_test_scaled)[:, 1]
                            auc = roc_auc_score(y_test, xgb_proba)
                        except Exception as e:
                            precision, recall, f1, auc = 0, 0, 0, 0
                            print(f"  Numéro {num}: Erreur lors du calcul des métriques XGBoost: {e}")

                        # Analyse des caractéristiques importantes
                        if hasattr(xgb_model, 'feature_importances_'):
                            importances = xgb_model.feature_importances_
                            if len(importances) == len(feature_cols):
                                # Créer un dictionnaire des importances
                                feature_importance = dict(zip(feature_cols, importances))
                                # Trier par importance décroissante
                                sorted_importance = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
                                # Prendre les 5 plus importantes
                                top_features = sorted_importance[:5]
                                # Stocker pour analyse ultérieure
                                models['xgboost']['top_features'] = top_features
                                # Afficher les caractéristiques les plus importantes
                                print(f"  Numéro {num}: Top 5 caractéristiques pour XGBoost:")
                                for feature, importance in top_features:
                                    print(f"    - {feature}: {importance:.4f}")

                        # Ajustement du seuil de décision pour optimiser F1-score
                        try:
                            # Calculer les prédictions pour différents seuils
                            thresholds = np.linspace(0.1, 0.9, 9)
                            best_f1 = 0
                            best_threshold = 0.5

                            for threshold in thresholds:
                                # Appliquer le seuil aux probabilités
                                custom_pred = (xgb_proba >= threshold).astype(int)
                                # Calculer le F1-score avec ce seuil
                                custom_f1 = f1_score(y_test, custom_pred)

                                if custom_f1 > best_f1:
                                    best_f1 = custom_f1
                                    best_threshold = threshold

                            # Stocker le meilleur seuil
                            models['xgboost']['optimal_threshold'] = best_threshold
                            print(f"  Numéro {num}: Seuil optimal pour XGBoost: {best_threshold:.2f} (F1: {best_f1:.4f})")
                        except Exception as e:
                            print(f"  Numéro {num}: Erreur lors de l'optimisation du seuil XGBoost: {e}")

                        # Afficher les résultats détaillés
                        print(f"  Numéro {num}: XGBoost - Précision: {xgb_accuracy:.4f}, AUC: {auc:.4f}")
                        print(f"  Numéro {num}: XGBoost - Prédictions: {np.bincount(xgb_pred)}")
                        print(f"  Numéro {num}: Valeurs réelles: {np.bincount(y_test)}")
                        print(f"  Numéro {num}: XGBoost - Matrice de confusion: [[{tn}, {fp}], [{fn}, {tp}]]")
                        print(f"  Numéro {num}: XGBoost - Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")

                        # Analyse des erreurs
                        # Identifier les cas où le modèle se trompe le plus souvent
                        error_indices = np.where(xgb_pred != y_test)[0]
                        if len(error_indices) > 0:
                            # Stocker pour analyse ultérieure
                            models['xgboost']['error_analysis'] = {
                                'error_count': len(error_indices),
                                'error_rate': len(error_indices) / len(y_test)
                            }
                            print(f"  Numéro {num}: XGBoost - Taux d'erreur: {len(error_indices) / len(y_test):.4f} ({len(error_indices)} erreurs sur {len(y_test)} prédictions)")
                    except Exception as e:
                        import sys
                        print(f"  XGBoost non disponible ou erreur: {e}")
                        print(f"  Chemin Python: {sys.path}")
                else:
                    print(f"  Numéro {num}: XGBoost ignoré (désactivé dans les paramètres)")

                # Optimisation: Ignorer LightGBM car il est généralement moins performant que GradientBoosting et XGBoost
                # pour ce type de problème
                print(f"  Numéro {num}: Ignorer LightGBM pour optimiser le temps d'entraînement")

                # Créer un ensemble de modèles (modèle de vote)
                if len(models) >= 2:  # Au moins 2 modèles pour créer un ensemble
                    ensemble_predictions = []
                    ensemble_weights = []
                    ensemble_models = []
                    ensemble_thresholds = []

                    # Collecter les prédictions de tous les modèles disponibles avec système de pondération avancé
                    ensemble_probas = []  # Stocker les probabilités brutes

                    for model_name, model_info in models.items():
                        if 'model' in model_info and model_info['model'] is not None:
                            # Utiliser le seuil optimal s'il existe, sinon 0.5
                            threshold = model_info.get('optimal_threshold', 0.5)
                            ensemble_thresholds.append(threshold)

                            # Obtenir les probabilités
                            try:
                                proba = model_info['model'].predict_proba(X_test_scaled)[:, 1]

                                # Vérifier et nettoyer les valeurs NaN dans les probabilités
                                if np.isnan(proba).any():
                                    print(f"  Numéro {num}: Attention - Valeurs NaN détectées dans les probabilités du modèle {model_name}")
                                    proba = np.nan_to_num(proba, nan=0.5)  # Remplacer NaN par 0.5 (valeur neutre)

                                ensemble_probas.append(proba)  # Stocker les probabilités brutes

                                # Appliquer le seuil optimal
                                pred = (proba >= threshold).astype(int)
                                ensemble_predictions.append(pred)

                                # Calculer des poids avancés basés sur plusieurs métriques
                                # Utiliser l'AUC comme base si disponible, sinon l'accuracy
                                base_weight = model_info.get('auc', model_info['accuracy'])

                                # Ajuster le poids en fonction du F1-score si disponible
                                f1_weight = model_info.get('f1', 0.5)

                                # Ajuster le poids en fonction de la précision et du recall si disponibles
                                precision_weight = model_info.get('precision', 0.5)
                                recall_weight = model_info.get('recall', 0.5)

                                # Combiner les poids (avec plus d'importance pour l'AUC et le F1-score)
                                combined_weight = (base_weight * 0.4) + (f1_weight * 0.4) + \
                                                 (precision_weight * 0.1) + (recall_weight * 0.1)

                                # Appliquer une fonction sigmoïde pour normaliser entre 0 et 1
                                # et accentuer les différences entre bons et mauvais modèles
                                from scipy.special import expit
                                normalized_weight = expit(combined_weight * 5 - 2.5)  # Sigmoïde centrée

                                ensemble_weights.append(normalized_weight)
                                ensemble_models.append(model_info['model'])

                                print(f"  Numéro {num}: Modèle {model_name} ajouté à l'ensemble")
                                print(f"    - Poids: {normalized_weight:.4f} (AUC: {base_weight:.4f}, F1: {f1_weight:.4f})")
                                print(f"    - Seuil optimal: {threshold:.2f}")
                            except Exception as e:
                                print(f"  Numéro {num}: Erreur lors de l'ajout du modèle {model_name} à l'ensemble: {e}")

                    # Créer la prédiction d'ensemble avancée si nous avons au moins un modèle
                    if len(ensemble_predictions) > 0:
                        # Normaliser les poids
                        ensemble_weights = np.array(ensemble_weights)
                        ensemble_weights = ensemble_weights / ensemble_weights.sum()

                        # Vérifier et nettoyer les valeurs NaN dans les prédictions et probabilités
                        for i in range(len(ensemble_predictions)):
                            if np.isnan(ensemble_predictions[i]).any():
                                print(f"  Numéro {num}: Attention - Valeurs NaN détectées dans les prédictions de l'ensemble")
                                ensemble_predictions[i] = np.nan_to_num(ensemble_predictions[i], nan=0)

                            if np.isnan(ensemble_probas[i]).any():
                                print(f"  Numéro {num}: Attention - Valeurs NaN détectées dans les probabilités de l'ensemble")
                                ensemble_probas[i] = np.nan_to_num(ensemble_probas[i], nan=0.5)

                        # Méthode 1: Moyenne pondérée des prédictions binaires
                        ensemble_pred_binary = np.zeros(len(y_test))
                        for i, pred in enumerate(ensemble_predictions):
                            ensemble_pred_binary += pred * ensemble_weights[i]

                        # Vérifier et nettoyer les valeurs NaN dans ensemble_pred_binary
                        if np.isnan(ensemble_pred_binary).any():
                            print(f"  Numéro {num}: Attention - Valeurs NaN détectées dans ensemble_pred_binary")
                            ensemble_pred_binary = np.nan_to_num(ensemble_pred_binary, nan=0.5)

                        # Méthode 2: Moyenne pondérée des probabilités
                        ensemble_pred_proba = np.zeros(len(y_test))
                        for i, proba in enumerate(ensemble_probas):
                            ensemble_pred_proba += proba * ensemble_weights[i]

                        # Vérifier et nettoyer les valeurs NaN dans ensemble_pred_proba
                        if np.isnan(ensemble_pred_proba).any():
                            print(f"  Numéro {num}: Attention - Valeurs NaN détectées dans ensemble_pred_proba")
                            ensemble_pred_proba = np.nan_to_num(ensemble_pred_proba, nan=0.5)

                        # Méthode 3: Vote majoritaire pondéré (plus robuste aux valeurs aberrantes)
                        # Pour chaque exemple, compter les votes pondérés pour la classe 1
                        ensemble_votes = np.zeros(len(y_test))
                        for i, pred in enumerate(ensemble_predictions):
                            # Ajouter le poids du modèle uniquement pour les prédictions positives
                            ensemble_votes += (pred == 1) * ensemble_weights[i]

                        # Vérifier et nettoyer les valeurs NaN dans ensemble_votes
                        if np.isnan(ensemble_votes).any():
                            print(f"  Numéro {num}: Attention - Valeurs NaN détectées dans ensemble_votes")
                            ensemble_votes = np.nan_to_num(ensemble_votes, nan=0.5)

                        # Combiner les trois méthodes avec des poids optimisés
                        # Donner plus de poids à la méthode des probabilités (plus précise)
                        ensemble_combined = (0.25 * ensemble_pred_binary +
                                           0.60 * ensemble_pred_proba +
                                           0.15 * ensemble_votes)

                        # Vérifier et nettoyer les valeurs NaN dans ensemble_combined
                        if np.isnan(ensemble_combined).any():
                            print(f"  Numéro {num}: Attention - Valeurs NaN détectées dans ensemble_combined")
                            ensemble_combined = np.nan_to_num(ensemble_combined, nan=0.5)

                        # Calculer plusieurs métriques pour trouver le meilleur seuil
                        from sklearn.metrics import roc_curve, precision_recall_curve, f1_score

                        # Méthode 1: Courbe ROC (optimise l'équilibre sensibilité/spécificité)
                        fpr, tpr, roc_thresholds = roc_curve(y_test, ensemble_combined)
                        # Trouver l'index du point le plus proche du coin supérieur gauche (0,1)
                        roc_optimal_idx = np.argmax(tpr - fpr)
                        roc_optimal_threshold = roc_thresholds[roc_optimal_idx]

                        # Méthode 2: Courbe Precision-Recall (optimise l'équilibre précision/rappel)
                        precision, recall, pr_thresholds = precision_recall_curve(y_test, ensemble_combined)
                        # Calculer le F1-score pour chaque seuil
                        f1_scores = []
                        for threshold in pr_thresholds:
                            y_pred = (ensemble_combined >= threshold).astype(int)
                            f1 = f1_score(y_test, y_pred, zero_division=0)
                            f1_scores.append(f1)

                        # Trouver le seuil qui maximise le F1-score
                        if len(f1_scores) > 0:
                            pr_optimal_idx = np.argmax(f1_scores)
                            pr_optimal_threshold = pr_thresholds[pr_optimal_idx]
                        else:
                            pr_optimal_threshold = 0.5

                        # Méthode 3: Utiliser la distribution des classes pour ajuster le seuil
                        # Si les classes sont très déséquilibrées, ajuster le seuil en conséquence
                        class_distribution = np.bincount(y_test) / len(y_test)
                        if len(class_distribution) > 1:
                            minority_class_ratio = min(class_distribution)
                            # Ajuster le seuil en fonction du déséquilibre des classes
                            # Plus la classe minoritaire est rare, plus le seuil doit être bas
                            dist_optimal_threshold = 0.5 - (0.5 - minority_class_ratio) * 0.5
                        else:
                            dist_optimal_threshold = 0.5

                        # Combiner les trois seuils avec des poids
                        # Donner plus de poids à la méthode qui optimise le F1-score
                        optimal_threshold = (0.3 * roc_optimal_threshold +
                                           0.6 * pr_optimal_threshold +
                                           0.1 * dist_optimal_threshold)

                        # Appliquer le seuil optimal
                        ensemble_pred = (ensemble_combined >= optimal_threshold).astype(int)

                        print(f"  Numéro {num}: Seuil optimal pour l'ensemble: {optimal_threshold:.4f}")

                        # Importer les métriques nécessaires
                        from sklearn.metrics import precision_score, recall_score, f1_score

                        # Évaluer l'ensemble
                        ensemble_accuracy = accuracy_score(y_test, ensemble_pred)
                        ensemble_precision = precision_score(y_test, ensemble_pred, zero_division=0)
                        ensemble_recall = recall_score(y_test, ensemble_pred, zero_division=0)
                        ensemble_f1 = f1_score(y_test, ensemble_pred, zero_division=0)

                        # Afficher les résultats de l'ensemble
                        print(f"  Numéro {num}: Ensemble - Précision: {ensemble_accuracy:.4f}")
                        print(f"  Numéro {num}: Ensemble - Precision: {ensemble_precision:.4f}, Recall: {ensemble_recall:.4f}, F1: {ensemble_f1:.4f}")

                        # Ajouter l'ensemble avancé au dictionnaire des modèles avec méta-informations complètes
                        models['ensemble'] = {
                            'models': ensemble_models,
                            'weights': ensemble_weights.tolist(),
                            'thresholds': ensemble_thresholds,
                            'accuracy': ensemble_accuracy,
                            'precision': ensemble_precision,
                            'recall': ensemble_recall,
                            'f1': ensemble_f1,
                            'optimal_threshold': optimal_threshold,
                            'ensemble_method': 'advanced_weighted_combination',
                            'probas': ensemble_probas,  # Stocker les probabilités brutes pour la prédiction
                            'method_weights': [0.15, 0.75, 0.10],  # Poids optimisés des différentes méthodes de combinaison (plus de poids sur les probabilités)
                            'roc_threshold': roc_optimal_threshold,
                            'pr_threshold': pr_optimal_threshold,
                            'dist_threshold': dist_optimal_threshold,
                            'threshold_weights': [0.2, 0.7, 0.1],  # Poids utilisés pour combiner les seuils (plus de poids sur le seuil PR)
                            'class_distribution': class_distribution.tolist() if len(class_distribution) > 0 else [1.0],
                            'model_types': [model_name for model_name, _ in models.items() if 'model' in models[model_name]],
                            'feature_importance': {}
                        }

                        # Ajouter les informations d'importance des caractéristiques pour l'analyse
                        for model_name, model_info in models.items():
                            if 'top_features' in model_info:
                                models['ensemble']['feature_importance'][model_name] = {
                                    'features': [feature for feature, _ in model_info['top_features']],
                                    'importance': [importance for _, importance in model_info['top_features']]
                                }

                # Choisir le meilleur modèle (incluant l'ensemble si disponible)
                if models:
                    # Utiliser F1-score comme métrique principale pour les classes déséquilibrées si disponible
                    best_model_name = None
                    best_f1 = -1
                    best_accuracy = 0

                    for model_name, model_info in models.items():
                        # Vérifier si le modèle a un F1-score
                        if model_name == 'ensemble' and 'f1' in model_info:
                            if model_info['f1'] > best_f1:
                                best_f1 = model_info['f1']
                                best_model_name = model_name
                                best_accuracy = model_info['accuracy']
                        elif 'model' in model_info and model_info.get('f1', -1) > best_f1:
                            best_f1 = model_info.get('f1', -1)
                            best_model_name = model_name
                            best_accuracy = model_info['accuracy']

                    # Si aucun modèle n'a de F1-score, utiliser l'accuracy
                    if best_model_name is None:
                        best_model_name = max(models, key=lambda k: models[k]['accuracy'])
                        best_accuracy = models[best_model_name]['accuracy']

                    best_model_info = models[best_model_name]

                    # Stocker le modèle dans le dictionnaire targets
                    if best_model_name == 'ensemble':
                        targets[num] = {
                            'type': 'ensemble',  # Utiliser 'type' pour la cohérence
                            'model_type': 'ensemble',  # Garder model_type pour compatibilité
                            'models': best_model_info['models'],
                            'weights': best_model_info['weights'],
                            'thresholds': best_model_info['thresholds'],
                            'accuracy': best_model_info['accuracy'],
                            'f1': best_model_info.get('f1', 0)
                        }
                    else:
                        # Ajouter le seuil optimal s'il existe
                        optimal_threshold = best_model_info.get('optimal_threshold', 0.5)
                        targets[num] = {
                            'model': best_model_info['model'],
                            'accuracy': best_model_info['accuracy'],
                            'type': best_model_name,
                            'model_type': best_model_name,  # Ajouter model_type pour cohérence
                            'threshold': optimal_threshold,
                            'f1': best_model_info.get('f1', 0)
                        }

                    f1_str = f", F1: {best_model_info.get('f1', 0):.4f}" if 'f1' in best_model_info else ""
                    print(f"  Meilleur modèle pour le numéro {num}: {best_model_name} (Précision: {best_accuracy:.4f}{f1_str})")
                    print(f"  Modèle ajouté au dictionnaire targets pour le numéro {num}")

                    # Sauvegarder incrémentiellement le modèle si demandé
                    if save_incremental:
                        print(f"  Numéro {num}: Sauvegarde incrémentielle automatique après entraînement...")
                        # Créer une copie temporaire des modèles pour la sauvegarde
                        temp_models = {
                            'features': {num: features[num]},
                            'targets': {num: targets[num]}
                        }
                        # Sauvegarder le modèle individuel
                        self.save_single_model(num, targets[num], features[num], test_size, random_state, fast_mode, ultra_fast)
                else:
                    print(f"  Aucun modèle n'a pu être entraîné pour le numéro {num}")

        # Vérifier une dernière fois si l'arrêt a été demandé
        if check_stop_requested():
            return False

        # Vérifier que nous avons des modèles à stocker
        print(f"\nVérification des modèles avant stockage:")
        print(f"Nombre de numéros avec modèles: {len(targets)}")
        print(f"Numéros avec modèles: {sorted(list(targets.keys()))}")

        # S'assurer que nous avons au moins un modèle valide
        if len(targets) == 0:
            print("ATTENTION: Aucun modèle n'a été créé. Ajout d'un modèle par défaut pour éviter les erreurs.")
            # Créer un modèle par défaut pour le numéro 1
            from sklearn.dummy import DummyClassifier
            dummy_model = DummyClassifier(strategy='stratified')
            dummy_model.fit(np.array([[0]]), np.array([0]))
            targets[1] = {
                'model': dummy_model,
                'accuracy': 0.5,
                'type': 'dummy',
                'model_type': 'dummy',
                'threshold': 0.5,
                'f1': 0.0
            }
            print("Modèle factice ajouté pour le numéro 1")

        # Stocker les modèles
        self.models = {
            'features': features,
            'targets': targets
        }

        # Vérifier que les modèles ont été correctement stockés
        print(f"Vérification après stockage:")
        if hasattr(self, 'models') and self.models:
            print(f"Modèles stockés avec succès")
            if 'targets' in self.models:
                print(f"Nombre de numéros dans self.models['targets']: {len(self.models['targets'])}")
                print(f"Numéros dans self.models['targets']: {sorted(list(self.models['targets'].keys()))}")
            else:
                print(f"ERREUR: 'targets' n'est pas dans self.models")
        else:
            print(f"ERREUR: self.models n'existe pas ou est vide")

        # Sauvegarde incrémentielle pour chaque numéro
        if resume:
            print("\n=== Début de la sauvegarde incrémentielle des modèles ===\n")

            # Essayer plusieurs chemins pour trouver un répertoire où nous pouvons écrire
            possible_paths = [
                # Chemin absolu basé sur le répertoire du script
                os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ml_models', 'incremental'),
                # Chemin relatif au répertoire courant
                os.path.join(os.getcwd(), 'ml_models', 'incremental'),
                # Chemin relatif simple
                os.path.join('ml_models', 'incremental'),
                # Répertoire courant
                os.path.join(os.getcwd()),
                # Répertoire temporaire du système
                os.path.join(os.path.expanduser('~'), 'keno_temp')
            ]

            # Essayer chaque chemin jusqu'à ce qu'on en trouve un qui fonctionne
            incremental_save_dir = None
            for path in possible_paths:
                try:
                    print(f"Essai du chemin: {path}")
                    os.makedirs(path, exist_ok=True)

                    # Tester si on peut écrire dans ce répertoire
                    test_file = os.path.join(path, 'test_write.txt')
                    with open(test_file, 'w') as f:
                        f.write('test')
                    os.remove(test_file)

                    # Si on arrive ici, le répertoire est utilisable
                    incremental_save_dir = path
                    print(f"Répertoire de sauvegarde incrémentielle trouvé: {incremental_save_dir}")
                    break
                except Exception as e:
                    print(f"Impossible d'utiliser le répertoire {path}: {e}")

            # Si aucun répertoire n'est utilisable, créer un répertoire dans le dossier temporaire du système
            if incremental_save_dir is None:
                try:
                    import tempfile
                    incremental_save_dir = tempfile.mkdtemp(prefix='keno_models_')
                    print(f"Utilisation du répertoire temporaire: {incremental_save_dir}")
                except Exception as e:
                    print(f"Impossible de créer un répertoire temporaire: {e}")
                    print("Sauvegarde incrémentielle désactivée")
                    return True

            # Afficher les informations sur le répertoire
            print(f"\nInformations sur le répertoire de sauvegarde:")
            print(f"Chemin absolu: {os.path.abspath(incremental_save_dir)}")
            print(f"Existe: {os.path.exists(incremental_save_dir)}")
            print(f"Est un répertoire: {os.path.isdir(incremental_save_dir)}")
            try:
                print(f"Contenu du répertoire: {os.listdir(incremental_save_dir)}")
            except Exception as e:
                print(f"Impossible de lister le contenu du répertoire: {e}")

            print("\nSauvegarde incrémentielle des modèles par numéro...")
            saved_count = 0
            error_count = 0

            for num, target_info in self.models['targets'].items():
                # Chemin de sauvegarde pour ce numéro
                num_save_path = os.path.join(incremental_save_dir, f'number_{num}.pkl')

                # Extraire les données pour ce numéro
                num_data = {
                    'target_info': target_info,
                    'feature_info': self.models['features'].get(num, {}),
                    'timestamp': datetime.now(),
                    'training_params': {
                        'test_size': test_size,
                        'random_state': random_state,
                        'fast_mode': fast_mode,
                        'ultra_fast': ultra_fast
                    }
                }

                # Sauvegarder les données
                try:
                    # Sauvegarder le fichier
                    print(f"  Numéro {num}: Tentative de sauvegarde dans {num_save_path}...")
                    with open(num_save_path, 'wb') as f:
                        pickle.dump(num_data, f)

                    # Vérifier que le fichier a bien été créé
                    if os.path.exists(num_save_path):
                        file_size = os.path.getsize(num_save_path)
                        print(f"  Numéro {num}: Sauvegarde réussie (taille: {file_size} octets)")
                        saved_count += 1
                    else:
                        print(f"  ERREUR: Le fichier {num_save_path} n'a pas été créé malgré l'absence d'erreur")
                        error_count += 1
                except Exception as e:
                    print(f"  Erreur lors de la sauvegarde pour le numéro {num}: {e}")
                    error_count += 1

                    # Essayer de sauvegarder dans le répertoire courant en dernier recours
                    try:
                        fallback_path = f'number_{num}.pkl'
                        with open(fallback_path, 'wb') as f:
                            pickle.dump(num_data, f)
                        print(f"  Sauvegarde de secours réussie dans {fallback_path}")
                        saved_count += 1
                    except Exception as e2:
                        print(f"  Impossible de sauvegarder le numéro {num}, même en secours: {e2}")

            # Afficher un résumé de la sauvegarde
            print(f"\nRésumé de la sauvegarde incrémentielle:")
            print(f"  Numéros traités: {len(self.models['targets'])}")
            print(f"  Sauvegardes réussies: {saved_count}")
            print(f"  Erreurs: {error_count}")

            # Afficher le contenu final du répertoire
            try:
                files = os.listdir(incremental_save_dir)
                print(f"\nContenu final du répertoire {incremental_save_dir}:")
                print(f"  {len(files)} fichiers trouvés: {files}")
            except Exception as e:
                print(f"Impossible de lister le contenu final du répertoire: {e}")

            print("=== Fin de la sauvegarde incrémentielle des modèles ===\n")

        # Calculer et afficher des statistiques sur les modèles entraînés
        end_time = time.time()
        training_time = end_time - start_time

        # Calculer les statistiques de précision
        accuracies = [target_info['accuracy'] for target_info in targets.values()]
        avg_accuracy = sum(accuracies) / len(accuracies) if accuracies else 0
        max_accuracy = max(accuracies) if accuracies else 0
        min_accuracy = min(accuracies) if accuracies else 0

        # Compter les types de modèles
        model_types = {}
        for target_info in targets.values():
            model_type = target_info['type']
            model_types[model_type] = model_types.get(model_type, 0) + 1

        # Afficher les résultats
        print(f"\nEntraînement terminé en {training_time:.2f} secondes")
        print(f"Modèles entraînés pour {len(targets)} numéros sur {self.max_number} possibles")
        print(f"Précision moyenne: {avg_accuracy:.4f} (min: {min_accuracy:.4f}, max: {max_accuracy:.4f})")
        print("Types de modèles utilisés:")
        for model_type, count in model_types.items():
            print(f"  - {model_type}: {count} modèles")

        # Optimisation: Libérer la mémoire après l'entraînement
        import gc
        gc.collect()  # Forcer le garbage collector à libérer la mémoire non utilisée

        return True

    def detect_anomalies(self):
        """Détecte les anomalies dans les tirages

        Returns:
            DataFrame: DataFrame contenant les anomalies détectées
        """
        if not self.anomaly_detector_available or self.anomaly_detector is None:
            print("Module de détection d'anomalies non disponible")
            return None

        # Préparer les données si nécessaire
        if self.df is None:
            self.prepare_data()

        # Détecter les anomalies
        print("Détection des anomalies dans les tirages...")
        self.anomaly_detector.prepare_data(self.df)
        self.anomaly_detector.train_models()
        anomalies = self.anomaly_detector.detect_anomalies()

        # Visualiser les anomalies
        if anomalies is not None and len(anomalies) > 0:
            print(f"{len(anomalies)} anomalies détectées")
            viz_path = self.anomaly_detector.visualize_anomalies()
            if viz_path:
                print(f"Visualisation des anomalies sauvegardée dans {viz_path}")
        else:
            print("Aucune anomalie détectée")

        return anomalies

    def analyze_time_series(self, resample_freq='D'):
        """Analyse les séquences temporelles dans les tirages

        Args:
            resample_freq (str): Fréquence de rééchantillonnage ('D' pour jour, 'W' pour semaine, etc.)

        Returns:
            dict: Dictionnaire contenant les résultats de l'analyse
        """
        if not self.time_series_available or self.time_series_analyzer is None:
            print("Module d'analyse de séquences temporelles non disponible")
            return None

        # Préparer les données si nécessaire
        if self.df is None:
            self.prepare_data()

        # Analyser les séquences temporelles
        print("Analyse des séquences temporelles dans les tirages...")
        self.time_series_analyzer.prepare_data(self.df, resample_freq)
        results = self.time_series_analyzer.analyze_all_numbers()

        # Visualiser les résultats pour quelques numéros
        if results is not None:
            print(f"Analyse terminée pour {len(results)} numéros")

            # Visualiser les numéros avec la plus forte saisonnalité
            seasonal_numbers = []
            for num, data in results.items():
                if 'seasonality' in data and data['seasonality'] is not None:
                    seasonal_strength = data['seasonality'].get('seasonality_strength', 0)
                    seasonal_numbers.append((num, seasonal_strength))

            # Trier par force de saisonnalité décroissante
            seasonal_numbers.sort(key=lambda x: x[1], reverse=True)

            # Visualiser les 3 premiers numéros
            for num, strength in seasonal_numbers[:3]:
                print(f"Visualisation de la saisonnalité pour le numéro {num} (force: {strength:.4f})")
                viz_path = self.time_series_analyzer.visualize_seasonality(num)
                if viz_path:
                    print(f"Visualisation sauvegardée dans {viz_path}")

                # Visualiser les prévisions
                print(f"Visualisation des prévisions pour le numéro {num}")
                viz_path = self.time_series_analyzer.visualize_forecast(num)
                if viz_path:
                    print(f"Visualisation sauvegardée dans {viz_path}")
        else:
            print("Aucun résultat d'analyse")

        return results

    def predict_with_confidence(self, num_predictions=20):
        """Prédit les prochains numéros avec intervalles de confiance

        Args:
            num_predictions (int): Nombre de numéros à prédire

        Returns:
            list: Liste des numéros prédits avec leurs intervalles de confiance
        """
        if not self.confidence_estimator_available or self.confidence_estimator is None:
            print("Module d'estimation d'intervalles de confiance non disponible")
            return None

        # Préparer les données si nécessaire
        if self.df is None:
            self.prepare_data()

        # Prédire les numéros avec intervalles de confiance
        print("Prédiction des numéros avec intervalles de confiance...")
        self.confidence_estimator.prepare_data(self.df)
        predictions = self.confidence_estimator.get_top_predictions_with_confidence(num_predictions)

        if predictions is not None:
            print(f"Prédiction terminée pour {len(predictions)} numéros")

            # Afficher les prédictions
            print("\nNuméros prédits avec intervalles de confiance:")
            print("-" * 60)
            print(f"{'Numéro':<10} {'Probabilité':<15} {'Intervalle de confiance':<30}")
            print("-" * 60)

            for num, (prob, (lower, upper)) in predictions:
                print(f"{num:<10} {prob:.4f}        [{lower:.4f}, {upper:.4f}]")

            # Visualiser les intervalles de confiance pour quelques numéros
            for num, _ in predictions[:3]:
                print(f"\nVisualisation des intervalles de confiance pour le numéro {num}")
                viz_path = self.confidence_estimator.visualize_confidence_intervals(num)
                if viz_path:
                    print(f"Visualisation sauvegardée dans {viz_path}")
        else:
            print("Aucune prédiction disponible")

        return predictions

    def predict_with_ml(self, num_predictions=10):
        """Prédit les prochains numéros en utilisant les modèles d'apprentissage automatique"""
        # Import numpy et sys ici pour éviter les erreurs
        import numpy as np
        import time
        import sys
        import pickle
        start_time = time.time()
        print("Début de la prédiction par apprentissage automatique...")

        # Essayer de charger le cache persistant s'il n'existe pas déjà en mémoire
        if not hasattr(self, '_ml_prediction_cache'):
            try:
                cache_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ml_prediction_cache.pkl')
                if os.path.exists(cache_file):
                    with open(cache_file, 'rb') as f:
                        self._ml_prediction_cache = pickle.load(f)
                    self._ml_prediction_cache_time = os.path.getmtime(cache_file)
                    print(f"Cache de prédiction ML chargé depuis {cache_file}")
            except Exception as e:
                print(f"Erreur lors du chargement du cache: {e}")

        # Vérifier si un cache existe et est récent (moins de 4 heures)
        if hasattr(self, '_ml_prediction_cache') and hasattr(self, '_ml_prediction_cache_time'):
            cache_age = time.time() - self._ml_prediction_cache_time
            cache_hours = cache_age / 3600
            # Utiliser le cache s'il a moins de 4 heures ou si aucun nouveau tirage n'a été ajouté
            if (cache_age < 14400 and self._ml_prediction_cache.get('num_predictions') >= num_predictions):  # 4 heures
                print(f"Utilisation du cache de prédiction ML (age: {cache_hours:.2f} heures)")
                return self._ml_prediction_cache['predictions'][:num_predictions]

        if not self.models or not self.models.get('targets'):
            print("Aucun modèle d'apprentissage automatique disponible. Utilisez l'auto-amélioration pour entraîner des modèles.")
            return []

        if self.df is None or self.df.empty:
            return []

        # Obtenir le dernier tirage
        last_row = self.df.iloc[-1].copy()

        # Préparer les prédictions pour chaque numéro
        predictions = {}

        # Utiliser le threading pour accélérer les prédictions
        import threading
        import queue

        # File pour stocker les résultats
        results_queue = queue.Queue()

        # Vérifier que nous ne prédisons que pour des numéros valides (1-70 pour le Keno)
        valid_max_number = min(70, self.max_number)  # Limiter à 70 maximum

        # Fonction pour prédire un numéro dans un thread
        def predict_number(num, target_info):
            try:
                # Import numpy, pandas et sys ici pour éviter les erreurs
                import numpy as np
                import pandas as pd
                import sys

                feature_info = self.models['features'][num]
                scaler = feature_info['scaler']
                feature_cols = feature_info['feature_cols']

                # Préparer les caractéristiques pour ce numéro
                features = {}

                for col in feature_cols:
                    if col in last_row:
                        features[col] = last_row[col]
                    elif col.startswith('has_') and '_lag_' in col:
                        # Gérer les caractéristiques de lag
                        parts = col.split('_lag_')
                        base_col = parts[0]
                        lag = int(parts[1])

                        if lag <= len(self.df):
                            features[col] = self.df.iloc[-lag][base_col]
                        else:
                            features[col] = 0
                    elif col.startswith('has_neighbor_'):
                        # Gérer les caractéristiques de voisinage
                        neighbor = int(col.split('_')[-1])
                        features[col] = 1 if neighbor in self.data_manager.draws[-1].draw_numbers else 0
                    else:
                        features[col] = 0

                # Convertir en DataFrame
                X = pd.DataFrame([features])

                # Normaliser
                X_scaled = scaler.transform(X)

                # Prédire (en supprimant les avertissements)
                import warnings
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")

                    # Vérifier si nous avons un ensemble de modèles ou un modèle unique
                    if target_info.get('type') == 'ensemble' or target_info.get('model_type') == 'ensemble':
                        # Utiliser l'ensemble de modèles avancé
                        models = target_info.get('models', [])
                        weights = target_info.get('weights', [])
                        thresholds = target_info.get('thresholds', [])
                        ensemble_method = target_info.get('ensemble_method', 'simple_weighted')
                        method_weights = target_info.get('method_weights', [0.15, 0.75, 0.10])  # Poids optimisés (plus de poids sur les probabilités)
                        optimal_threshold = target_info.get('optimal_threshold', 0.5)

                        # Récupérer les seuils spécifiques si disponibles
                        roc_threshold = target_info.get('roc_threshold', optimal_threshold)
                        pr_threshold = target_info.get('pr_threshold', optimal_threshold)
                        dist_threshold = target_info.get('dist_threshold', optimal_threshold)
                        threshold_weights = target_info.get('threshold_weights', [0.2, 0.7, 0.1])  # Plus de poids sur le seuil PR

                        if len(models) > 0 and len(weights) == len(models) and len(thresholds) == len(models):
                            # Collecter les probabilités brutes de tous les modèles
                            model_probas = []
                            model_decisions = []

                            for i, model in enumerate(models):
                                try:
                                    # Obtenir la probabilité du modèle
                                    model_proba = model.predict_proba(X_scaled)[0][1]
                                    model_probas.append(model_proba)

                                    # Obtenir la décision binaire avec le seuil optimal
                                    model_decision = 1.0 if model_proba >= thresholds[i] else 0.0
                                    model_decisions.append(model_decision)
                                except Exception as e:
                                    # En cas d'erreur avec un modèle, utiliser des valeurs par défaut
                                    print(f"Erreur avec le modèle {i}: {e}")
                                    model_probas.append(0.5)  # Valeur neutre
                                    model_decisions.append(0.0)  # Pas de prédiction positive par défaut

                            # Méthode 1: Moyenne pondérée des probabilités
                            ensemble_proba = 0.0
                            for i, proba in enumerate(model_probas):
                                if i < len(weights):
                                    ensemble_proba += proba * weights[i]

                            # Méthode 2: Vote pondéré des décisions binaires
                            ensemble_decision = 0.0
                            for i, decision in enumerate(model_decisions):
                                if i < len(weights):
                                    ensemble_decision += decision * weights[i]

                            # Méthode 3: Vote majoritaire pondéré (plus robuste)
                            ensemble_vote = 0.0
                            total_weight = sum(weights)
                            for i, decision in enumerate(model_decisions):
                                if i < len(weights) and decision > 0.5:
                                    ensemble_vote += weights[i]
                            ensemble_vote = ensemble_vote / total_weight if total_weight > 0 else 0.5

                            # Méthode 4: Analyse des tendances récentes (si disponible)
                            trend_factor = 1.0  # Valeur par défaut (pas d'ajustement)
                            if 'feature_importance' in target_info:
                                # Analyser les caractéristiques importantes pour détecter les tendances
                                # Si les caractéristiques de tendance récente sont importantes, ajuster la prédiction
                                trend_features = [f for model_info in target_info.get('feature_importance', {}).values()
                                                for f in model_info.get('features', [])
                                                if 'trend' in f or 'recent' in f or 'hot_cold' in f]
                                if trend_features:
                                    # Augmenter légèrement l'influence des tendances récentes
                                    trend_factor = 1.1

                            # Combiner les méthodes selon les poids spécifiés
                            if ensemble_method == 'advanced_weighted_combination':
                                # Utiliser la combinaison avancée avec les poids optimisés
                                combined_prob = (method_weights[0] * ensemble_decision +
                                               method_weights[1] * ensemble_proba +
                                               method_weights[2] * ensemble_vote) * trend_factor

                                # Appliquer une combinaison pondérée des différents seuils optimaux
                                weighted_threshold = (threshold_weights[0] * roc_threshold +
                                                   threshold_weights[1] * pr_threshold +
                                                   threshold_weights[2] * dist_threshold)

                                # Appliquer le seuil pondéré optimal
                                final_decision = 1.0 if combined_prob >= weighted_threshold else 0.0

                                # Combiner la décision finale avec la probabilité brute pour plus de nuance
                                # Donner beaucoup plus de poids à la décision finale pour stabiliser les prédictions
                                prob = 0.10 * combined_prob + 0.90 * final_decision

                                # Donner plus de poids aux modèles Random Forest et XGBoost dans l'ensemble
                                # Vérifier si nous avons des modèles Random Forest ou XGBoost dans l'ensemble
                                rf_xgb_indices = []
                                rf_indices = []
                                xgb_indices = []

                                # Récupérer les paramètres d'activation des modèles
                                rf_params = self.model_params.get('random_forest', {})
                                xgb_params = self.model_params.get('xgboost', {})
                                use_rf = rf_params.get('use_model', True)
                                use_xgb = xgb_params.get('use_model', True)

                                for i, model in enumerate(models):
                                    model_name = str(model.__class__.__name__).lower()
                                    if 'randomforest' in model_name and use_rf:
                                        rf_xgb_indices.append(i)
                                        rf_indices.append(i)
                                    elif 'xgb' in model_name and use_xgb:
                                        rf_xgb_indices.append(i)
                                        xgb_indices.append(i)

                                # Si nous avons des modèles Random Forest ou XGBoost activés, leur donner plus de poids
                                if rf_xgb_indices:
                                    rf_xgb_prob = 0.0
                                    rf_xgb_weight_sum = 0.0

                                    # Appliquer des poids différents selon les modèles activés
                                    for i in rf_xgb_indices:
                                        if i < len(model_probas) and i < len(weights):
                                            # Appliquer un poids plus élevé selon le type de modèle
                                            if i in rf_indices and i in xgb_indices:
                                                # Les deux modèles sont activés, donner un poids équilibré
                                                weight_multiplier = 2.0
                                            elif i in rf_indices and use_rf and not use_xgb:
                                                # Seul Random Forest est activé, lui donner un poids très élevé
                                                weight_multiplier = 3.0
                                            elif i in xgb_indices and use_xgb and not use_rf:
                                                # Seul XGBoost est activé, lui donner un poids très élevé
                                                weight_multiplier = 3.0
                                            else:
                                                # Cas par défaut
                                                weight_multiplier = 2.0

                                            rf_xgb_prob += model_probas[i] * weights[i] * weight_multiplier
                                            rf_xgb_weight_sum += weights[i] * weight_multiplier

                                    if rf_xgb_weight_sum > 0:
                                        rf_xgb_prob /= rf_xgb_weight_sum

                                        # Ajuster la combinaison en fonction des modèles activés
                                        if use_rf and use_xgb:
                                            # Les deux modèles sont activés
                                            prob = 0.2 * prob + 0.8 * rf_xgb_prob
                                            print(f"Bonus appliqué pour RF et XGB dans l'ensemble: {prob}")
                                        elif use_rf and not use_xgb:
                                            # Seul Random Forest est activé
                                            prob = 0.1 * prob + 0.9 * rf_xgb_prob
                                            print(f"Bonus appliqué pour Random Forest uniquement: {prob}")
                                        elif use_xgb and not use_rf:
                                            # Seul XGBoost est activé
                                            prob = 0.1 * prob + 0.9 * rf_xgb_prob
                                            print(f"Bonus appliqué pour XGBoost uniquement: {prob}")
                                        else:
                                            # Cas par défaut (ne devrait pas arriver)
                                            prob = 0.3 * prob + 0.7 * rf_xgb_prob
                                            print(f"Bonus appliqué pour RF/XGB dans l'ensemble: {prob}")

                                # Ajouter un facteur de confiance basé sur la cohérence des modèles
                                # Si tous les modèles sont d'accord, augmenter encore plus la confiance
                                if all(d > 0.5 for d in model_decisions) or all(d < 0.5 for d in model_decisions):
                                    # Renforcer fortement la décision si tous les modèles sont d'accord
                                    prob = 0.02 * prob + 0.98 * final_decision

                                # Si la probabilité est très élevée ou très basse, renforcer encore plus la décision
                                if combined_prob > 0.85 or combined_prob < 0.15:
                                    prob = 0.01 * prob + 0.99 * final_decision
                            else:
                                # Méthode de repli simple si la méthode avancée n'est pas disponible
                                prob = 0.3 * ensemble_proba + 0.7 * ensemble_decision
                        else:
                            # Fallback si l'ensemble est mal configuré
                            prob = 0.5
                    else:
                        # Modèle unique
                        model = target_info['model']
                        raw_prob = model.predict_proba(X_scaled)[0][1]  # Probabilité brute

                        # Appliquer le seuil optimal pour obtenir une décision binaire
                        optimal_threshold = target_info.get('threshold', 0.5)
                        decision = 1.0 if raw_prob >= optimal_threshold else 0.0

                        # Combiner probabilité et décision pour plus de stabilité
                        # La décision binaire a beaucoup plus de poids (85%) pour stabiliser les prédictions
                        prob = 0.15 * raw_prob + 0.85 * decision

                        # Si la probabilité est très élevée ou très basse, renforcer encore plus la décision
                        if raw_prob > 0.85 or raw_prob < 0.15:
                            prob = 0.05 * raw_prob + 0.95 * decision

                # Ajouter le résultat à la file
                results_queue.put((num, {
                    'probability': prob,
                    'model_accuracy': target_info['accuracy'],
                    'model_type': target_info.get('model_type', target_info.get('type', 'unknown')),
                    'f1': target_info.get('f1', 0)  # Ajouter le F1-score si disponible
                }))
            except Exception as e:
                print(f"Erreur lors de la prédiction pour le numéro {num}: {e}")

        # Créer et démarrer les threads (par lots pour éviter de surcharger le système)
        batch_size = 10  # Nombre de threads à exécuter simultanément

        # Filtrer pour ne garder que les numéros valides (1-70)
        all_nums = [(num, info) for num, info in self.models['targets'].items() if 1 <= num <= valid_max_number]
        print(f"Prédiction pour {len(all_nums)} numéros valides (1-{valid_max_number})")

        for i in range(0, len(all_nums), batch_size):
            batch = all_nums[i:i+batch_size]
            threads = []

            for num, target_info in batch:
                thread = threading.Thread(target=predict_number, args=(num, target_info))
                thread.daemon = True
                thread.start()
                threads.append(thread)

            # Attendre que tous les threads du lot terminent
            for thread in threads:
                thread.join()

        # Récupérer tous les résultats
        while not results_queue.empty():
            num, pred_info = results_queue.get()
            predictions[num] = pred_info

        # Trier les numéros par probabilité décroissante
        sorted_nums = sorted(predictions.keys(), key=lambda x: predictions[x]['probability'], reverse=True)

        # Sélectionner les N premiers numéros valides (entre 1 et max_number)
        selected_nums = []
        for num in sorted_nums:
            # Vérifier que le numéro est valide (entre 1 et max_number)
            if 1 <= num <= self.max_number:
                selected_nums.append(num)
                if len(selected_nums) >= num_predictions:
                    break

        # Ajouter des informations détaillées
        result = []
        for num in selected_nums:
            result.append({
                'number': num,
                'probability': predictions[num]['probability'],
                'model_accuracy': predictions[num]['model_accuracy'],
                'model_type': predictions[num]['model_type']
            })

        # Stabiliser les prédictions en tenant compte des prédictions précédentes
        if hasattr(self, '_ml_prediction_cache') and 'predictions' in self._ml_prediction_cache:
            previous_predictions = self._ml_prediction_cache['predictions']
            # Créer un dictionnaire des prédictions précédentes pour un accès facile
            prev_pred_dict = {p['number']: p for p in previous_predictions}

            # Facteur de stabilité - plus il est élevé, plus les prédictions sont stables
            stability_factor = 0.5  # 50% de stabilité

            # Ajuster les probabilités en fonction des prédictions précédentes
            for pred in result:
                num = pred['number']
                if num in prev_pred_dict:
                    # Combiner la probabilité actuelle avec la précédente
                    prev_prob = prev_pred_dict[num]['probability']
                    current_prob = pred['probability']
                    # La nouvelle probabilité est une moyenne pondérée
                    pred['probability'] = (1 - stability_factor) * current_prob + stability_factor * prev_prob
                    # Ajouter un indicateur de stabilité
                    pred['stability'] = 'stable' if abs(current_prob - prev_prob) < 0.1 else 'changing'
                else:
                    # Nouvelle prédiction
                    pred['stability'] = 'new'

            # Retrier les numéros par probabilité décroissante après stabilisation
            result.sort(key=lambda x: x['probability'], reverse=True)

        # Stocker dans le cache
        self._ml_prediction_cache = {
            'predictions': result,
            'num_predictions': num_predictions,
            'time': time.time()
        }
        self._ml_prediction_cache_time = time.time()

        # Sauvegarder le cache dans un fichier pour le conserver entre les sessions
        try:
            import pickle
            cache_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ml_prediction_cache.pkl')
            with open(cache_file, 'wb') as f:
                pickle.dump(self._ml_prediction_cache, f)
            print(f"Cache de prédiction ML sauvegardé dans {cache_file}")
        except Exception as e:
            print(f"Erreur lors de la sauvegarde du cache: {e}")

        # Afficher le temps d'exécution
        end_time = time.time()
        print(f"Prédiction ML terminée en {end_time - start_time:.2f} secondes")

        return result

    def predict_with_patterns(self, num_predictions=10):
        """Prédit les prochains numéros en utilisant l'analyse des motifs"""
        if not self.patterns:
            print("Aucun motif disponible. Utilisez l'auto-amélioration pour analyser les motifs.")
            return []

        if not self.data_manager.draws:
            return []

        # Obtenir le dernier tirage
        last_draw = self.data_manager.draws[-1]
        last_nums = last_draw.draw_numbers

        # Calculer les caractéristiques du dernier tirage
        last_even_count = sum(1 for num in last_nums if num % 2 == 0)
        last_odd_count = self.numbers_per_draw - last_even_count

        mid_point = self.max_number // 2
        last_high_count = sum(1 for num in last_nums if num > mid_point)
        last_low_count = self.numbers_per_draw - last_high_count

        # Trouver les motifs les plus fréquents
        if 'even_odd_distribution' in self.patterns:
            common_even_odd = self.patterns['even_odd_distribution'].most_common(3)
            target_even = common_even_odd[0][0][0]  # Nombre de pairs le plus fréquent
        else:
            target_even = self.numbers_per_draw // 2  # Par défaut, équilibré

        if 'high_low_distribution' in self.patterns:
            common_high_low = self.patterns['high_low_distribution'].most_common(3)
            target_high = common_high_low[0][0][0]  # Nombre de hauts le plus fréquent
        else:
            target_high = self.numbers_per_draw // 2  # Par défaut, équilibré

        # Calculer les scores pour chaque numéro
        scores = {}

        # Vérifier que max_number est correct (ne devrait pas dépasser 70 pour le Keno)
        valid_max_number = min(70, self.max_number)  # Limiter à 70 maximum
        print(f"Analyse des motifs pour les numéros de 1 à {valid_max_number}")

        for num in range(1, valid_max_number + 1):
            # Ignorer les numéros du dernier tirage
            if num in last_nums:
                continue

            score = 0

            # Favoriser les numéros qui équilibrent la distribution pairs/impairs
            is_even = (num % 2 == 0)
            if is_even and last_even_count < target_even:
                score += 2
            elif not is_even and last_odd_count < (self.numbers_per_draw - target_even):
                score += 2

            # Favoriser les numéros qui équilibrent la distribution haut/bas
            is_high = (num > mid_point)
            if is_high and last_high_count < target_high:
                score += 2
            elif not is_high and last_low_count < (self.numbers_per_draw - target_high):
                score += 2

            # Favoriser les numéros qui créent des écarts fréquents
            if 'consecutive_gaps' in self.patterns:
                for last_num in last_nums:
                    gap = abs(num - last_num)
                    if gap in self.patterns['consecutive_gaps']:
                        score += self.patterns['consecutive_gaps'][gap] / 100

            # Vérifier si le numéro contribue à une somme typique
            if 'draw_sums' in self.patterns:
                current_sum = sum(last_nums)
                new_sum = current_sum + num
                target_sum = self.patterns['draw_sums']['mean']

                # Favoriser les numéros qui rapprochent de la somme moyenne
                if abs(new_sum - target_sum) < abs(current_sum - target_sum):
                    score += 1

            scores[num] = score

        # Trier les numéros par score décroissant
        sorted_nums = sorted(scores.keys(), key=lambda x: scores[x], reverse=True)

        # Sélectionner les N premiers numéros valides (entre 1 et max_number)
        selected_nums = []
        for num in sorted_nums:
            # Vérifier que le numéro est valide (entre 1 et max_number)
            if 1 <= num <= self.max_number:
                selected_nums.append(num)
                if len(selected_nums) >= num_predictions:
                    break

        # Ajouter des informations détaillées
        result = []
        for num in selected_nums:
            result.append({
                'number': num,
                'pattern_score': scores[num]
            })

        return result

    def predict_with_series(self, num_predictions=10):
        """Prédit les prochains numéros en utilisant l'analyse des séries"""
        if not self.series:
            print("Aucune série disponible. Utilisez l'auto-amélioration pour analyser les séries.")
            return []

        if not self.data_manager.draws or not self.series:
            return []

        # Obtenir le dernier tirage
        last_draw = self.data_manager.draws[-1]
        last_nums = last_draw.draw_numbers

        # Calculer les scores pour chaque numéro
        scores = {}

        # Vérifier que max_number est correct (ne devrait pas dépasser 70 pour le Keno)
        valid_max_number = min(70, self.max_number)  # Limiter à 70 maximum
        print(f"Analyse des séries pour les numéros de 1 à {valid_max_number}")

        for num in range(1, valid_max_number + 1):
            # Ignorer les numéros du dernier tirage
            if num in last_nums:
                continue

            score = 0

            # Vérifier si le numéro forme une séquence connue avec les numéros du dernier tirage
            if 'common_sequences' in self.series:
                for seq, count in self.series['common_sequences']:
                    # Vérifier si le numéro peut compléter une séquence
                    for last_num in last_nums:
                        if (last_num, num) in seq or (num, last_num) in seq:
                            score += count / 10

            # Vérifier si le numéro forme une paire connue avec les numéros du dernier tirage
            if 'common_pairs' in self.series:
                for pair, count in self.series['common_pairs']:
                    for last_num in last_nums:
                        if (min(last_num, num), max(last_num, num)) == pair:
                            score += count / 5

            # Vérifier le taux de répétition du numéro
            if 'follow_patterns' in self.series and num in self.series['follow_patterns']:
                repeat_rate = self.series['follow_patterns'][num]['repeat_rate']
                score += repeat_rate * 3

            scores[num] = score

        # Trier les numéros par score décroissant
        sorted_nums = sorted(scores.keys(), key=lambda x: scores[x], reverse=True)

        # Sélectionner les N premiers numéros valides (entre 1 et max_number)
        selected_nums = []
        for num in sorted_nums:
            # Vérifier que le numéro est valide (entre 1 et 70)
            if 1 <= num <= 70:
                selected_nums.append(num)
                if len(selected_nums) >= num_predictions:
                    break

        # Ajouter des informations détaillées
        result = []
        for num in selected_nums:
            result.append({
                'number': num,
                'series_score': scores[num]
            })

        return result

    def predict_combined_advanced(self, num_predictions=10):
        """Combine toutes les méthodes avancées pour une prédiction optimale"""
        import time
        start_time = time.time()
        print("Génération de prédictions avancées combinées...")

        # Vérifier si un cache existe et est récent (moins de 5 minutes)
        if hasattr(self, '_combined_advanced_cache') and hasattr(self, '_combined_advanced_cache_time'):
            cache_age = time.time() - self._combined_advanced_cache_time
            if cache_age < 300 and self._combined_advanced_cache.get('num_predictions') >= num_predictions:  # 5 minutes
                print(f"Utilisation du cache de prédiction combinée avancée (age: {cache_age:.1f} secondes)")
                return self._combined_advanced_cache['predictions'][:num_predictions]

        # Utiliser le threading pour obtenir les prédictions en parallèle
        import threading
        import queue

        # File pour stocker les résultats
        results_queue = queue.Queue()

        # Fonction pour exécuter une méthode de prédiction dans un thread
        def run_prediction(method_name, num_preds):
            try:
                if method_name == 'ml':
                    print("Obtention des prédictions par apprentissage automatique...")
                    predictions = self.predict_with_ml(num_preds)
                    results_queue.put(('ml', predictions))
                elif method_name == 'patterns':
                    print("Obtention des prédictions par motifs...")
                    predictions = self.predict_with_patterns(num_preds)
                    results_queue.put(('patterns', predictions))
                elif method_name == 'series':
                    print("Obtention des prédictions par séries...")
                    predictions = self.predict_with_series(num_preds)
                    results_queue.put(('series', predictions))
                elif method_name == 'frequency':
                    print("Calcul des scores de fréquence...")
                    scores = self._calculate_frequency_scores()
                    results_queue.put(('frequency', scores))
            except Exception as e:
                print(f"Erreur lors de la prédiction {method_name}: {e}")
                if method_name == 'frequency':
                    results_queue.put((method_name, {}))
                else:
                    results_queue.put((method_name, []))

        # Créer et démarrer les threads
        threads = []
        for method in ['ml', 'patterns', 'series', 'frequency']:
            thread = threading.Thread(target=run_prediction, args=(method, num_predictions * 2))
            thread.daemon = True
            thread.start()
            threads.append(thread)

        # Attendre que tous les threads terminent (avec timeout)
        timeout = 30  # secondes
        for thread in threads:
            thread.join(timeout)

        # Récupérer les résultats
        ml_predictions = []
        pattern_predictions = []
        series_predictions = []
        frequency_scores = {}

        while not results_queue.empty():
            method, result = results_queue.get()
            if method == 'ml':
                ml_predictions = result
            elif method == 'patterns':
                pattern_predictions = result
            elif method == 'series':
                series_predictions = result
            elif method == 'frequency':
                frequency_scores = result

        # Créer un dictionnaire pour combiner les scores
        combined_scores = {}

        # Ajouter les scores des modèles ML avec un poids plus important
        for pred in ml_predictions:
            num = pred['number']
            if num not in combined_scores:
                combined_scores[num] = {'number': num, 'total_score': 0, 'details': {}}

            # Pondérer par la probabilité et la précision du modèle (poids beaucoup plus élevé)
            ml_score = pred['probability'] * pred['model_accuracy'] * 20  # Augmenté de 15 à 20

            # Récupérer les paramètres d'activation des modèles
            rf_params = self.model_params.get('random_forest', {})
            xgb_params = self.model_params.get('xgboost', {})
            use_rf = rf_params.get('use_model', True)
            use_xgb = xgb_params.get('use_model', True)

            # Donner encore plus de poids aux modèles Random Forest et XGBoost selon leur activation
            model_type = pred.get('model_type', '').lower()

            if 'random_forest' in model_type and use_rf:
                # Si seul Random Forest est activé, lui donner un poids encore plus élevé
                if use_rf and not use_xgb:
                    ml_score *= 2.5  # Bonus très important pour Random Forest seul
                    print(f"Bonus majeur appliqué pour Random Forest (seul modèle activé): {ml_score}")
                else:
                    ml_score *= 1.8  # Bonus important pour Random Forest
                    print(f"Bonus appliqué pour Random Forest: {ml_score}")
            elif 'xgboost' in model_type and use_xgb:
                # Si seul XGBoost est activé, lui donner un poids encore plus élevé
                if use_xgb and not use_rf:
                    ml_score *= 2.5  # Bonus très important pour XGBoost seul
                    print(f"Bonus majeur appliqué pour XGBoost (seul modèle activé): {ml_score}")
                else:
                    ml_score *= 1.8  # Bonus important pour XGBoost
                    print(f"Bonus appliqué pour XGBoost: {ml_score}")

            # Donner encore plus de poids aux prédictions avec une forte probabilité
            if pred['probability'] > 0.8:
                ml_score *= 1.5
            # Donner plus de poids aux modèles avec un bon F1-score si disponible
            if 'f1' in pred and pred['f1'] > 0.7:
                ml_score *= 1.3
            combined_scores[num]['total_score'] += ml_score
            combined_scores[num]['details']['ml_score'] = ml_score
            combined_scores[num]['details']['ml_probability'] = pred['probability']
            combined_scores[num]['details']['ml_model_type'] = pred['model_type']

        # Ajouter les scores des motifs
        for pred in pattern_predictions:
            num = pred['number']
            if num not in combined_scores:
                combined_scores[num] = {'number': num, 'total_score': 0, 'details': {}}

            pattern_score = pred['pattern_score'] * 2.5  # Augmenter davantage l'importance des motifs
            combined_scores[num]['total_score'] += pattern_score
            combined_scores[num]['details']['pattern_score'] = pattern_score

        # Ajouter les scores des séries
        for pred in series_predictions:
            num = pred['number']
            if num not in combined_scores:
                combined_scores[num] = {'number': num, 'total_score': 0, 'details': {}}

            series_score = pred['series_score'] * 2.0  # Augmenter davantage l'importance des séries
            combined_scores[num]['total_score'] += series_score
            combined_scores[num]['details']['series_score'] = series_score

        # Ajouter les scores de fréquence
        for num, score in frequency_scores.items():
            if num not in combined_scores:
                combined_scores[num] = {'number': num, 'total_score': 0, 'details': {}}

            # Ajouter le score de fréquence avec un poids plus faible
            frequency_score = score * 0.8  # Réduire l'importance des fréquences
            combined_scores[num]['total_score'] += frequency_score
            combined_scores[num]['details']['frequency_score'] = frequency_score

        # Appliquer un facteur de correction pour éviter les biais
        self._apply_balance_correction(combined_scores)

        # Trier par score total décroissant
        sorted_predictions = sorted(combined_scores.values(), key=lambda x: x['total_score'], reverse=True)

        # Sélectionner les N premiers numéros
        final_predictions = sorted_predictions[:num_predictions]

        # Stocker dans le cache
        self._combined_advanced_cache = {
            'predictions': final_predictions,
            'num_predictions': num_predictions,
            'time': time.time()
        }
        self._combined_advanced_cache_time = time.time()

        # Afficher le temps d'exécution
        end_time = time.time()
        print(f"Prédiction combinée avancée terminée en {end_time - start_time:.2f} secondes")
        print(f"Prédiction combinée générée avec {len(final_predictions)} numéros")

        return final_predictions

    def _calculate_frequency_scores(self):
        """Calcule les scores basés sur la fréquence d'apparition des numéros"""
        if not self.data_manager.draws:
            return {}

        # Vérifier que max_number est correct (ne devrait pas dépasser 70 pour le Keno)
        valid_max_number = min(70, self.max_number)  # Limiter à 70 maximum

        # Compter les occurrences de chaque numéro
        counts = {i: 0 for i in range(1, valid_max_number + 1)}

        # Donner plus de poids aux tirages récents
        recent_draws = self.data_manager.draws[-50:] if len(self.data_manager.draws) > 50 else self.data_manager.draws

        for i, draw in enumerate(recent_draws):
            weight = 1 + (i / len(recent_draws))  # Les tirages plus récents ont un poids plus élevé
            for num in draw.draw_numbers:
                counts[num] = counts.get(num, 0) + weight

        # Normaliser les scores
        max_count = max(counts.values()) if counts else 1
        scores = {num: (count / max_count) * 3 for num, count in counts.items()}

        return scores

    def _apply_balance_correction(self, combined_scores):
        """Applique une correction pour équilibrer les prédictions"""
        if not combined_scores:
            return

        # Vérifier la distribution pairs/impairs
        even_count = 0
        odd_count = 0
        high_count = 0
        low_count = 0
        mid_point = self.max_number // 2

        # Compter les numéros pairs/impairs et hauts/bas parmi les meilleurs scores
        top_nums = sorted(combined_scores.keys(), key=lambda x: combined_scores[x]['total_score'], reverse=True)[:self.numbers_per_draw]

        for num in top_nums:
            if num % 2 == 0:
                even_count += 1
            else:
                odd_count += 1

            if num > mid_point:
                high_count += 1
            else:
                low_count += 1

        # Appliquer une correction plus agressive pour équilibrer les prédictions
        for num in combined_scores:
            # Correction pairs/impairs
            if even_count > self.numbers_per_draw * 0.65:  # Trop de pairs (seuil abaissé)
                if num % 2 != 0:  # Favoriser les impairs
                    # Facteur de correction plus élevé
                    correction_factor = 1.5 + (even_count / self.numbers_per_draw - 0.65) * 2
                    combined_scores[num]['total_score'] *= correction_factor
                    combined_scores[num]['details']['even_odd_correction'] = correction_factor
            elif odd_count > self.numbers_per_draw * 0.65:  # Trop d'impairs (seuil abaissé)
                if num % 2 == 0:  # Favoriser les pairs
                    # Facteur de correction plus élevé
                    correction_factor = 1.5 + (odd_count / self.numbers_per_draw - 0.65) * 2
                    combined_scores[num]['total_score'] *= correction_factor
                    combined_scores[num]['details']['even_odd_correction'] = correction_factor

            # Correction hauts/bas
            if high_count > self.numbers_per_draw * 0.65:  # Trop de hauts (seuil abaissé)
                if num <= mid_point:  # Favoriser les bas
                    # Facteur de correction plus élevé
                    correction_factor = 1.5 + (high_count / self.numbers_per_draw - 0.65) * 2
                    combined_scores[num]['total_score'] *= correction_factor
                    combined_scores[num]['details']['high_low_correction'] = correction_factor
            elif low_count > self.numbers_per_draw * 0.65:  # Trop de bas (seuil abaissé)
                if num > mid_point:  # Favoriser les hauts
                    # Facteur de correction plus élevé
                    correction_factor = 1.5 + (low_count / self.numbers_per_draw - 0.65) * 2
                    combined_scores[num]['total_score'] *= correction_factor
                    combined_scores[num]['details']['high_low_correction'] = correction_factor

            # Correction pour les numéros consécutifs (favoriser la diversité)
            consecutive_count = 0
            for top_num in top_nums:
                if abs(num - top_num) == 1:  # Numéros adjacents
                    consecutive_count += 1

            if consecutive_count > 3:  # Trop de numéros consécutifs
                # Chercher des numéros plus éloignés
                is_isolated = True
                for top_num in top_nums:
                    if abs(num - top_num) <= 2:  # Numéro trop proche
                        is_isolated = False
                        break

                if is_isolated:  # Favoriser les numéros isolés
                    correction_factor = 1.3 + (consecutive_count - 3) * 0.1
                    combined_scores[num]['total_score'] *= correction_factor
                    combined_scores[num]['details']['isolation_correction'] = correction_factor

    def save_single_model(self, num, target_info, feature_info=None, test_size=0.2, random_state=42, fast_mode=False, ultra_fast=False):
        """Sauvegarde un modèle individuel pour un numéro spécifique

        Args:
            num: Le numéro du modèle à sauvegarder
            target_info: Les informations du modèle cible
            feature_info: Les informations des caractéristiques (optionnel)
            test_size: Taille du jeu de test utilisé pour l'entraînement
            random_state: Graine aléatoire utilisée pour l'entraînement
            fast_mode: Si le mode rapide a été utilisé
            ultra_fast: Si le mode ultra-rapide a été utilisé

        Returns:
            bool: True si la sauvegarde a réussi, False sinon
        """
        # Vérifier si c'est un modèle de deep learning ou un ensemble
        is_deep_learning = False
        is_ensemble = False
        model_type = target_info.get('type', '')

        if model_type and 'deep_learning' in model_type:
            is_deep_learning = True
            if 'ensemble' in model_type:
                is_ensemble = True
                print(f"  Numéro {num}: Détection d'un ensemble de modèles de deep learning")
            else:
                print(f"  Numéro {num}: Détection d'un modèle de deep learning")

            # Sauvegarder le modèle de deep learning séparément
            if self.deep_learning and self.deep_learning_available:
                # Créer un chemin pour le modèle de deep learning
                script_dir = os.path.dirname(os.path.abspath(__file__))
                dl_dir = os.path.join(script_dir, 'ml_models', 'deep_learning')
                os.makedirs(dl_dir, exist_ok=True)

                if is_ensemble:
                    # Pour les ensembles, nous devons sauvegarder chaque modèle individuellement
                    ensemble_dir = os.path.join(dl_dir, f"ensemble_{num}")
                    os.makedirs(ensemble_dir, exist_ok=True)

                    print(f"  Numéro {num}: Sauvegarde de l'ensemble de modèles dans {ensemble_dir}...")

                    # Créer une copie des données pour éviter de modifier l'original
                    target_info_copy = {}
                    for key, value in target_info.items():
                        if key != 'model' and key != 'models':
                            target_info_copy[key] = value

                    # Stocker le répertoire de l'ensemble
                    target_info_copy['ensemble_dir'] = ensemble_dir

                    # Sauvegarder les modèles individuels de l'ensemble
                    if 'models' in target_info and isinstance(target_info['models'], list):
                        saved_models = []
                        for i, model_data in enumerate(target_info['models']):
                            model_path = os.path.join(ensemble_dir, f"model_{i}.pkl")
                            try:
                                success = self.deep_learning.save_model(model_data, model_path)
                                if success:
                                    saved_models.append({
                                        'path': model_path,
                                        'type': model_data.get('type', 'unknown')
                                    })
                            except Exception as e:
                                print(f"  Erreur lors de la sauvegarde du modèle {i} de l'ensemble: {e}")

                        # Stocker les chemins des modèles sauvegardés
                        target_info_copy['saved_models'] = saved_models

                    # Utiliser la copie modifiée
                    target_info = target_info_copy
                else:
                    # Pour un modèle simple
                    dl_path = os.path.join(dl_dir, f"dl_model_{num}.pkl")

                    print(f"  Numéro {num}: Sauvegarde du modèle de deep learning dans {dl_path}...")
                    success = self.deep_learning.save_model(target_info, dl_path)

                    if success:
                        # Créer une copie des données sans le modèle TensorFlow
                        target_info_copy = {}
                        for key, value in target_info.items():
                            if key != 'model':
                                target_info_copy[key] = value

                        # Stocker le chemin du modèle de deep learning
                        target_info_copy['dl_model_path'] = dl_path

                        # Sauvegarder les métriques séparément pour plus de robustesse
                        metrics_path = os.path.splitext(dl_path)[0] + "_metrics.json"
                        try:
                            import json
                            metrics_data = {
                                'accuracy': target_info.get('accuracy', 0),
                                'precision': target_info.get('precision', 0),
                                'recall': target_info.get('recall', 0),
                                'f1': target_info.get('f1', 0),
                                'auc': target_info.get('auc', 0),
                                'input_dim': target_info.get('input_dim', 0)
                            }
                            with open(metrics_path, 'w') as f:
                                json.dump(metrics_data, f, indent=2)
                            target_info_copy['metrics_path'] = metrics_path
                            print(f"  Métriques sauvegardées dans {metrics_path}")
                        except Exception as e:
                            print(f"  Erreur lors de la sauvegarde des métriques: {e}")

                        # Utiliser la copie modifiée
                        target_info = target_info_copy
                    else:
                        print(f"  Erreur lors de la sauvegarde du modèle de deep learning pour le numéro {num}")

                        # Créer une sauvegarde de secours sans le modèle TensorFlow
                        try:
                            backup_path = os.path.join(dl_dir, f"dl_model_{num}_backup.pkl")
                            backup_data = {}
                            for key, value in target_info.items():
                                if key != 'model':
                                    backup_data[key] = value

                            # Ajouter des informations supplémentaires pour la reconstruction
                            if 'model' in target_info and target_info['model'] is not None:
                                if hasattr(target_info['model'], 'model'):
                                    tf_model = target_info['model'].model
                                    backup_data['input_dim'] = tf_model.input_shape[1] if len(tf_model.input_shape) > 1 else 10

                            # Sauvegarder les données de secours
                            joblib.dump(backup_data, backup_path)
                            print(f"  Sauvegarde de secours créée dans {backup_path}")

                            # Mettre à jour target_info avec le chemin de la sauvegarde de secours
                            target_info_copy = {}
                            for key, value in target_info.items():
                                if key != 'model':
                                    target_info_copy[key] = value
                            target_info_copy['backup_path'] = backup_path
                            target_info = target_info_copy
                        except Exception as e:
                            print(f"  Erreur lors de la création de la sauvegarde de secours: {e}")
        # Obtenir le répertoire du script en cours
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # Créer le chemin absolu vers le dossier de sauvegarde incrémentielle
        incremental_save_dir = os.path.join(script_dir, 'ml_models', 'incremental')

        # Créer le répertoire s'il n'existe pas
        os.makedirs(incremental_save_dir, exist_ok=True)

        # Chemin de sauvegarde pour ce numéro
        num_save_path = os.path.join(incremental_save_dir, f'number_{num}.pkl')

        # Si feature_info n'est pas fourni, essayer de le récupérer depuis self.models
        if feature_info is None and hasattr(self, 'models') and self.models and 'features' in self.models and num in self.models['features']:
            feature_info = self.models['features'][num]
        else:
            feature_info = feature_info or {}

        # Extraire les données pour ce numéro
        num_data = {
            'target_info': target_info,
            'feature_info': feature_info,
            'timestamp': datetime.now(),
            'training_params': {
                'test_size': test_size,
                'random_state': random_state,
                'fast_mode': fast_mode,
                'ultra_fast': ultra_fast
            }
        }

        # Sauvegarder les données
        try:
            # Sauvegarder le fichier
            print(f"  Numéro {num}: Sauvegarde automatique dans {num_save_path}...")
            with open(num_save_path, 'wb') as f:
                pickle.dump(num_data, f)
            print(f"  Numéro {num}: Sauvegarde réussie")
            return True
        except Exception as e:
            print(f"  Erreur lors de la sauvegarde du numéro {num}: {e}")

            # Essayer de sauvegarder dans le répertoire courant en dernier recours
            try:
                fallback_path = f'number_{num}.pkl'
                with open(fallback_path, 'wb') as f:
                    pickle.dump(num_data, f)
                print(f"  Sauvegarde de secours réussie dans {fallback_path}")
                return True
            except Exception as e2:
                print(f"  Impossible de sauvegarder le numéro {num}, même en secours: {e2}")
                return False

    def save_models(self, filepath='keno_ml_models.pkl'):
        """Sauvegarde les modèles entraînés"""
        print("\nDébut de la sauvegarde des modèles...")

        # Vérifier si les modèles existent
        if not hasattr(self, 'models') or not self.models:
            print("Aucun modèle à sauvegarder")
            return False

        # Vérifier le contenu des modèles
        if 'targets' not in self.models or not self.models['targets']:
            print("Aucun modèle cible à sauvegarder")
            return False

        # Afficher des informations sur les modèles à sauvegarder
        num_models = len(self.models['targets'])
        print(f"Sauvegarde de {num_models} modèles dans {filepath}")
        print(f"Numéros avec modèles: {sorted(list(self.models['targets'].keys()))}")

        try:
            # Créer le répertoire parent si nécessaire
            import os
            os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)

            # Vérifier si nous avons des modèles de deep learning
            has_deep_learning = False
            deep_learning_models = {}

            # Parcourir les modèles pour identifier ceux de deep learning
            if 'targets' in self.models:
                for num, target_info in self.models['targets'].items():
                    model_type = target_info.get('type', '')
                    if model_type and 'deep_learning' in model_type:
                        has_deep_learning = True
                        # Sauvegarder le modèle de deep learning séparément
                        dl_path = os.path.splitext(filepath)[0] + f"_dl_{num}.pkl"
                        if self.deep_learning and self.deep_learning_available:
                            print(f"Sauvegarde du modèle de deep learning pour le numéro {num}...")
                            success = self.deep_learning.save_model(target_info, dl_path)
                            if success:
                                # Stocker le chemin du modèle de deep learning
                                deep_learning_models[num] = dl_path
                                # Remplacer le modèle par None pour éviter les problèmes de sérialisation
                                self.models['targets'][num]['model'] = None
                                self.models['targets'][num]['dl_model_path'] = dl_path
                            else:
                                print(f"Erreur lors de la sauvegarde du modèle de deep learning pour le numéro {num}")

            # Sauvegarder les modèles
            joblib.dump(self.models, filepath)

            # Vérifier que le fichier a bien été créé
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath) / (1024 * 1024)  # Taille en Mo
                print(f"Modèles sauvegardés dans {filepath} (taille: {file_size:.2f} Mo)")

                # Restaurer les modèles de deep learning en mémoire
                if has_deep_learning and self.deep_learning and self.deep_learning_available:
                    for num, dl_path in deep_learning_models.items():
                        print(f"Restauration du modèle de deep learning pour le numéro {num}...")
                        dl_model = self.deep_learning.load_model(dl_path)
                        if dl_model:
                            self.models['targets'][num]['model'] = dl_model['model']

                return True
            else:
                print(f"ERREUR: Le fichier {filepath} n'a pas été créé")
                return False
        except Exception as e:
            print(f"Erreur lors de la sauvegarde des modèles: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _safe_load_tensorflow_model(self, model_path):
        """Charge un modèle TensorFlow de manière sécurisée avec plusieurs méthodes de secours

        Args:
            model_path (str): Chemin vers le modèle TensorFlow

        Returns:
            object: Modèle TensorFlow chargé ou None en cas d'échec
        """
        if not os.path.exists(model_path):
            print(f"  Chemin de modèle introuvable: {model_path}")
            return None

        try:
            import tensorflow as tf
            import numpy as np

            # Méthode 1: Utiliser l'option experimental_io_device
            try:
                options = tf.saved_model.LoadOptions(
                    experimental_io_device='/job:localhost'
                )
                model = tf.keras.models.load_model(model_path, options=options)
                print(f"  Modèle chargé avec succès en utilisant experimental_io_device")
                return model
            except Exception as e:
                print(f"  Échec de la méthode 1 (experimental_io_device): {e}")

            # Méthode 2: Essayer sans options spéciales
            try:
                model = tf.keras.models.load_model(model_path)
                print(f"  Modèle chargé avec succès sans options spéciales")
                return model
            except Exception as e:
                print(f"  Échec de la méthode 2 (sans options): {e}")

            # Méthode 3: Essayer de charger uniquement les poids
            try:
                # Créer un modèle simple
                input_dim = 10  # Valeur par défaut, à ajuster si nécessaire
                inputs = tf.keras.Input(shape=(input_dim,))
                x = tf.keras.layers.Dense(64, activation='relu')(inputs)
                x = tf.keras.layers.Dropout(0.2)(x)
                x = tf.keras.layers.Dense(32, activation='relu')(x)
                x = tf.keras.layers.Dropout(0.2)(x)
                outputs = tf.keras.layers.Dense(1, activation='sigmoid')(x)
                model = tf.keras.Model(inputs=inputs, outputs=outputs)

                # Essayer de charger les poids
                weights_path = os.path.join(os.path.dirname(model_path), "weights.h5")
                if os.path.exists(weights_path):
                    model.load_weights(weights_path)
                    print(f"  Modèle recréé et poids chargés depuis {weights_path}")
                    return model
            except Exception as e:
                print(f"  Échec de la méthode 3 (chargement des poids): {e}")

            # Toutes les méthodes ont échoué
            print("  Toutes les méthodes de chargement ont échoué")
            return None
        except Exception as e:
            print(f"  Erreur lors du chargement du modèle TensorFlow: {e}")
            return None

    def check_and_repair_model_files(self):
        """Vérifie et répare les fichiers de modèles corrompus"""
        print("\nVérification des fichiers de modèles...")

        # Vérifier les fichiers dans le répertoire courant
        corrupted_files = []
        try:
            current_dir_files = [f for f in os.listdir('.') if f.startswith('number_') and f.endswith('.pkl')]
            if current_dir_files:
                print(f"  {len(current_dir_files)} fichiers de modèles trouvés dans le répertoire courant")
                print(f"  Fichiers disponibles dans le répertoire courant: {current_dir_files}")

                for file in current_dir_files:
                    try:
                        # Essayer d'ouvrir le fichier pour vérifier s'il est valide
                        with open(file, 'rb') as f:
                            num_data = pickle.load(f)

                        # Vérifier si les données sont complètes
                        if 'target_info' not in num_data or 'feature_info' not in num_data:
                            print(f"  Fichier {file} incomplet")
                            print(f"  ATTENTION: Fichier potentiellement incomplet: {file}")
                            print(f"  Vous pouvez le supprimer manuellement ou réentraîner le modèle pour ce numéro")
                            # Ne pas ajouter à corrupted_files pour éviter la suppression automatique
                    except Exception as e:
                        print(f"  Erreur lors de la vérification du fichier {file}: {e}")
                        print(f"  ATTENTION: Fichier potentiellement corrompu: {file}")
                        print(f"  Vous pouvez le supprimer manuellement ou réentraîner le modèle pour ce numéro")
                        # Ne pas ajouter à corrupted_files pour éviter la suppression automatique

                # Ne pas supprimer automatiquement les fichiers corrompus
                if corrupted_files:
                    print(f"  {len(corrupted_files)} fichiers potentiellement corrompus détectés")
                    print(f"  Vous pouvez les supprimer manuellement ou réentraîner les modèles correspondants")
                    # Extraire les numéros des fichiers corrompus pour information seulement
                    corrupted_numbers = []
                    for file in corrupted_files:
                        try:
                            num = int(file.replace('number_', '').replace('.pkl', ''))
                            corrupted_numbers.append(num)
                        except ValueError:
                            pass

                    if corrupted_numbers:
                        print(f"  Numéros concernés: {corrupted_numbers}")
                        print("  Ces modèles seront recréés lors de la prochaine amélioration automatique")
                else:
                    print("  Tous les fichiers sont valides")
        except Exception as e:
            print(f"  Erreur lors de la vérification des fichiers: {e}")

    def load_models(self, filepath='keno_ml_models.pkl'):
        """Charge les modèles entraînés"""
        print("\nDébut du chargement des modèles...")

        # Vérifier et réparer les fichiers corrompus avant de charger les modèles
        self.check_and_repair_model_files()

        # Initialiser les modèles
        if not hasattr(self, 'models') or not self.models:
            self.models = {'targets': {}, 'features': {}}

        # Charger d'abord les fichiers incrémentiels s'ils existent
        try:
            # Obtenir le répertoire du script en cours
            script_dir = os.path.dirname(os.path.abspath(__file__))
            # Créer le chemin absolu vers le dossier de sauvegarde incrémentielle
            incremental_save_dir = os.path.join(script_dir, 'ml_models', 'incremental')

            # Vérifier si le dossier existe
            if os.path.exists(incremental_save_dir) and os.path.isdir(incremental_save_dir):
                print(f"Recherche de fichiers incrémentiels dans {incremental_save_dir}...")
                incremental_files = [f for f in os.listdir(incremental_save_dir) if f.startswith('number_') and f.endswith('.pkl')]

                if incremental_files:
                    print(f"  {len(incremental_files)} fichiers incrémentiels trouvés dans {incremental_save_dir}")
                    print(f"  Fichiers disponibles: {incremental_files}")
                    for file in incremental_files:
                        try:
                            # Extraire le numéro du nom de fichier
                            num = int(file.replace('number_', '').replace('.pkl', ''))
                            file_path = os.path.join(incremental_save_dir, file)
                            print(f"  Numéro {num}: Fichier trouvé dans {file_path}")

                            # Essayer d'ouvrir le fichier pour vérifier s'il est valide
                            with open(file_path, 'rb') as f:
                                num_data = pickle.load(f)

                            # Vérifier si les données sont complètes
                            if 'target_info' in num_data and 'feature_info' in num_data:
                                # Vérifier si c'est un modèle de deep learning
                                target_info = num_data['target_info']
                                model_type = target_info.get('type', '')

                                if 'deep_learning' in model_type and self.deep_learning and self.deep_learning_available:
                                    # Pour les modèles de deep learning, vérifier si le modèle peut être chargé
                                    dl_path = target_info.get('dl_model_path', None)
                                    if dl_path and os.path.exists(dl_path):
                                        # Essayer de charger le modèle avec notre méthode sécurisée
                                        try:
                                            # D'abord essayer avec la méthode load_model de deep_learning
                                            dl_model = self.deep_learning.load_model(dl_path)
                                            if dl_model and 'model' in dl_model:
                                                # Mettre à jour le modèle dans les données
                                                target_info['model'] = dl_model['model']
                                                print(f"  Numéro {num}: Modèle de deep learning chargé avec succès via deep_learning.load_model")
                                            else:
                                                # Si ça échoue, essayer avec notre méthode sécurisée
                                                tf_model = self._safe_load_tensorflow_model(dl_path)
                                                if tf_model:
                                                    # Créer un wrapper pour le modèle
                                                    import numpy as np
                                                    class ModelWrapper:
                                                        def __init__(self, model):
                                                            self.model = model

                                                        def predict(self, X):
                                                            return (self.model.predict(X) > 0.5).astype(int)

                                                        def predict_proba(self, X):
                                                            proba = self.model.predict(X)
                                                            return np.hstack([1-proba, proba])

                                                    # Mettre à jour le modèle dans les données
                                                    target_info['model'] = ModelWrapper(tf_model)
                                                    print(f"  Numéro {num}: Modèle de deep learning chargé avec succès via _safe_load_tensorflow_model")
                                        except Exception as e:
                                            print(f"  Erreur lors du chargement du modèle de deep learning pour le numéro {num}: {e}")
                                            # Continuer sans le modèle, mais garder les autres informations
                                            target_info['model'] = None
                                            print(f"  Numéro {num}: Modèle de deep learning non disponible, mais métadonnées chargées")

                                # Charger les données dans le modèle global
                                self.models['targets'][num] = target_info
                                self.models['features'][num] = num_data['feature_info']
                                print(f"  Numéro {num}: Chargé depuis le fichier incrémentiel {file}")
                            else:
                                print(f"  Numéro {num}: Fichier incomplet, manque target_info ou feature_info")
                                # Ne pas supprimer automatiquement, juste afficher un avertissement
                                print(f"  ATTENTION: Fichier potentiellement incomplet: {file_path}")
                                print(f"  Vous pouvez le supprimer manuellement ou réentraîner le modèle pour ce numéro")
                        except Exception as e:
                            print(f"  Erreur lors du chargement du fichier incrémentiel {file}: {e}")
                            # Ne pas supprimer automatiquement, juste afficher un avertissement
                            print(f"  ATTENTION: Fichier potentiellement corrompu: {file_path}")
                            print(f"  Vous pouvez le supprimer manuellement ou réentraîner le modèle pour ce numéro")
                else:
                    print("Aucun fichier incrémentiel trouvé")
        except Exception as e:
            print(f"Erreur lors de la recherche de fichiers incrémentiels: {e}")

        # Ensuite, charger le fichier principal s'il existe
        if os.path.exists(filepath):
            # Afficher des informations sur le fichier
            file_size = os.path.getsize(filepath) / (1024 * 1024)  # Taille en Mo
            print(f"Chargement du fichier principal {filepath} (taille: {file_size:.2f} Mo)")

            try:
                # Charger les modèles
                main_models = joblib.load(filepath)

                # Fusionner avec les modèles déjà chargés (les fichiers incrémentiels ont priorité)
                if 'targets' in main_models:
                    for num, target_info in main_models['targets'].items():
                        if num not in self.models['targets']:
                            self.models['targets'][num] = target_info
                            print(f"  Numéro {num}: Chargé depuis le fichier principal")

                if 'features' in main_models:
                    for num, feature_info in main_models['features'].items():
                        if num not in self.models['features']:
                            self.models['features'][num] = feature_info
            except Exception as e:
                print(f"Erreur lors du chargement du fichier principal: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"Fichier principal {filepath} introuvable")

        # Vérifier que les modèles ont été correctement chargés
        if hasattr(self, 'models') and self.models:
            if 'targets' in self.models and self.models['targets']:
                num_models = len(self.models['targets'])
                print(f"Total des modèles chargés: {num_models} modèles")
                print(f"Numéros avec modèles: {sorted(list(self.models['targets'].keys()))}")

                # Charger les modèles de deep learning si nécessaire
                if self.deep_learning and self.deep_learning_available:
                    dl_models_loaded = 0
                    for num, target_info in self.models['targets'].items():
                        # Vérifier si c'est un modèle de deep learning
                        model_type = target_info.get('type', '')
                        dl_path = target_info.get('dl_model_path', None)

                        if 'deep_learning' in model_type and dl_path and os.path.exists(dl_path):
                            print(f"Chargement du modèle de deep learning pour le numéro {num}...")
                            try:
                                # D'abord essayer avec la méthode load_model de deep_learning
                                dl_model = self.deep_learning.load_model(dl_path)
                                if dl_model and 'model' in dl_model:
                                    # Mettre à jour le modèle dans les données
                                    self.models['targets'][num]['model'] = dl_model['model']
                                    dl_models_loaded += 1
                                    print(f"Modèle de deep learning chargé pour le numéro {num} via deep_learning.load_model")
                                else:
                                    # Si ça échoue, essayer avec notre méthode sécurisée
                                    tf_model = self._safe_load_tensorflow_model(dl_path)
                                    if tf_model:
                                        # Créer un wrapper pour le modèle
                                        import numpy as np
                                        class ModelWrapper:
                                            def __init__(self, model):
                                                self.model = model

                                            def predict(self, X):
                                                return (self.model.predict(X) > 0.5).astype(int)

                                            def predict_proba(self, X):
                                                proba = self.model.predict(X)
                                                return np.hstack([1-proba, proba])

                                        # Mettre à jour le modèle dans les données
                                        self.models['targets'][num]['model'] = ModelWrapper(tf_model)
                                        dl_models_loaded += 1
                                        print(f"Modèle de deep learning chargé pour le numéro {num} via _safe_load_tensorflow_model")
                                    else:
                                        print(f"Erreur lors du chargement du modèle de deep learning pour le numéro {num}")
                            except Exception as e:
                                print(f"Erreur lors du chargement du modèle de deep learning pour le numéro {num}: {e}")

                    if dl_models_loaded > 0:
                        print(f"{dl_models_loaded} modèles de deep learning chargés avec succès")

                return True
            else:
                print(f"ERREUR: Les modèles chargés ne contiennent pas de cibles valides")
                return False
        else:
            print(f"ERREUR: Les modèles n'ont pas été correctement chargés")
            return False

    def visualize_correlations(self):
        """Visualise la matrice de corrélation entre les numéros"""
        if self.correlations is None:
            self.analyze_correlations()

        if self.correlations is None:
            return None

        plt.figure(figsize=(12, 10))
        mask = np.triu(np.ones_like(self.correlations, dtype=bool))
        sns.heatmap(self.correlations, mask=mask, annot=False, cmap='coolwarm', center=0,
                   square=True, linewidths=.5, cbar_kws={"shrink": .5})
        plt.title('Matrice de corrélation entre les numéros')

        return plt.gcf()

    def auto_improve(self, callback=None, fast_mode=False, timeout=1800, ultra_fast=False, max_numbers=None, resume=True):
        """
        Améliore automatiquement les modèles de prédiction

        Args:
            callback (function): Fonction de rappel pour mettre à jour la progression
            fast_mode (bool): Si True, utilise le mode rapide pour l'entraînement

        Returns:
            dict: Résultats de l'amélioration
        """
        print("Début de l'amélioration automatique des modèles...")

        # Vérifier qu'il y a suffisamment de données
        if len(self.data_manager.draws) < 50:
            print("Pas assez de données pour l'amélioration automatique (minimum 50 tirages)")
            return {
                'success': False,
                'message': "Pas assez de données pour l'amélioration automatique (minimum 50 tirages)"
            }

        # Préparer les données si nécessaire
        if self.df is None:
            if callback:
                callback(1, 10, "Préparation des données...")
            self.prepare_data()

        # Définir les étapes totales pour la progression
        total_steps = 10
        current_step = 1

        # Analyser les corrélations
        if callback:
            callback(current_step, total_steps, "Analyse des corrélations entre numéros...")
            # Vérifier si l'arrêt a été demandé
            if callback(current_step, total_steps, "Analyse des corrélations entre numéros...") is False:
                return {'success': False, 'message': "Arrêt demandé par l'utilisateur"}

        self.analyze_correlations()
        current_step += 1

        # Analyser les motifs
        if callback:
            if callback(current_step, total_steps, "Analyse des motifs dans les tirages...") is False:
                return {'success': False, 'message': "Arrêt demandé par l'utilisateur"}

        self.analyze_patterns()
        current_step += 1

        # Analyser les séries temporelles
        if callback:
            if callback(current_step, total_steps, "Analyse des séries temporelles...") is False:
                return {'success': False, 'message': "Arrêt demandé par l'utilisateur"}

        self.analyze_time_series()
        current_step += 1

        # Entraîner les modèles
        if callback:
            if callback(current_step, total_steps, "Entraînement des modèles d'apprentissage automatique...") is False:
                return {'success': False, 'message': "Arrêt demandé par l'utilisateur"}

        # Définir une fonction de vérification d'arrêt qui utilise le callback
        def check_stop_requested():
            if callback:
                return callback(current_step, total_steps, "Entraînement des modèles en cours...") is False
            return False

        # Entraîner les modèles avec la fonction de vérification d'arrêt et le timeout
        # Utiliser le mode ultra-rapide et limiter le nombre de numéros si demandé
        if ultra_fast:
            print("Mode ULTRA-RAPIDE activé: entraînement accéléré avec paramètres minimaux")
            # Réduire le timeout en mode ultra-rapide
            actual_timeout = min(timeout, 600)  # Maximum 10 minutes en mode ultra-rapide
        else:
            actual_timeout = timeout

        # Limiter le nombre de numéros à entraîner si spécifié
        if max_numbers is not None and max_numbers > 0:
            print(f"Limitation à {max_numbers} numéros pour l'entraînement")

        success = self.train_models(fast_mode=fast_mode, stop_flag=check_stop_requested,
                               timeout=actual_timeout, ultra_fast=ultra_fast, max_numbers=max_numbers,
                               resume=resume, save_incremental=True)

        if not success:
            return {'success': False, 'message': "Arrêt demandé par l'utilisateur pendant l'entraînement des modèles"}

        current_step += 5  # Augmenter de plusieurs étapes car l'entraînement est l'opération la plus longue

        # Sauvegarder les modèles
        if callback:
            if callback(current_step, total_steps, "Sauvegarde des modèles...") is False:
                return {'success': False, 'message': "Arrêt demandé par l'utilisateur"}

        save_success = self.save_models()
        current_step += 1

        # Finaliser
        if callback:
            callback(total_steps, total_steps, "Amélioration terminée!")

        # Retourner les résultats
        return {
            'success': True,
            'message': "Amélioration automatique terminée avec succès",
            'stats': {
                'models_trained': len(self.models.get('targets', {})) if hasattr(self, 'models') and self.models else 0,
                'fast_mode': fast_mode,
                'models_saved': save_success
            }
        }

    def visualize_patterns(self):
        """Visualise les motifs détectés"""
        if not self.patterns:
            self.analyze_patterns()

        if not self.patterns:
            return None

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # Distribution pairs/impairs
        if 'even_odd_distribution' in self.patterns:
            data = self.patterns['even_odd_distribution']
            labels = [f"{even}/{self.numbers_per_draw-even}" for even, _ in data.keys()]
            values = list(data.values())

            axes[0, 0].bar(labels, values)
            axes[0, 0].set_title('Distribution Pairs/Impairs')
            axes[0, 0].set_xlabel('Nombre de pairs/impairs')
            axes[0, 0].set_ylabel('Fréquence')

        # Distribution haut/bas
        if 'high_low_distribution' in self.patterns:
            data = self.patterns['high_low_distribution']
            labels = [f"{high}/{self.numbers_per_draw-high}" for high, _ in data.keys()]
            values = list(data.values())

            axes[0, 1].bar(labels, values)
            axes[0, 1].set_title('Distribution Haut/Bas')
            axes[0, 1].set_xlabel('Nombre de hauts/bas')
            axes[0, 1].set_ylabel('Fréquence')

        # Écarts consécutifs
        if 'consecutive_gaps' in self.patterns:
            data = self.patterns['consecutive_gaps']
            gaps = sorted(data.keys())
            values = [data[gap] for gap in gaps]

            axes[1, 0].bar(gaps, values)
            axes[1, 0].set_title('Écarts entre numéros consécutifs')
            axes[1, 0].set_xlabel('Écart')
            axes[1, 0].set_ylabel('Fréquence')

        # Somme des tirages
        if 'draw_sums' in self.patterns:
            if 'distribution' in self.patterns['draw_sums']:
                hist, bins = self.patterns['draw_sums']['distribution']
                axes[1, 1].bar(bins[:-1], hist, width=np.diff(bins), align='edge')
                axes[1, 1].set_title('Distribution des sommes de tirage')
                axes[1, 1].set_xlabel('Somme')
                axes[1, 1].set_ylabel('Fréquence')

                # Ajouter une ligne pour la moyenne
                mean = self.patterns['draw_sums']['mean']
                axes[1, 1].axvline(mean, color='r', linestyle='--', label=f'Moyenne: {mean:.1f}')
                axes[1, 1].legend()

        plt.tight_layout()

        return fig
