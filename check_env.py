"""
Script pour vérifier l'environnement Python et les packages installés
"""

import sys
import os
import subprocess

def check_package(package_name):
    """Vérifie si un package est installé"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

# Afficher les informations sur l'environnement Python
print(f"Version Python: {sys.version}")
print(f"Exécutable Python: {sys.executable}")
print(f"Répertoire de travail: {os.getcwd()}")

# Vérifier les packages
packages = ["numpy", "pandas", "sklearn", "tensorflow", "xgboost", "lightgbm", "tqdm"]
print("\nPackages installés:")
for package in packages:
    status = "✓ Installé" if check_package(package) else "✗ Non installé"
    print(f"  {package}: {status}")

# Essayer d'installer les packages manquants
print("\nTentative d'installation des packages manquants dans l'environnement actuel:")
for package in packages:
    if not check_package(package):
        print(f"Installation de {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"  ✓ {package} installé avec succès")
        except subprocess.CalledProcessError:
            print(f"  ✗ Échec de l'installation de {package}")

print("\nVérification après installation:")
for package in packages:
    status = "✓ Installé" if check_package(package) else "✗ Non installé"
    print(f"  {package}: {status}")
