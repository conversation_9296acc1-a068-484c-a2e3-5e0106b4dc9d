#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module pour l'intégration du deep learning dans l'application Keno
Utilise TensorFlow/Keras pour créer et entraîner des modèles de réseaux de neurones
Inclut des fonctionnalités avancées comme l'optimisation des hyperparamètres et l'apprentissage par ensemble
"""

import os
import numpy as np
import pandas as pd
import joblib
import time
import warnings
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score

# Désactiver les messages de log verbeux de TensorFlow avant l'import
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 0=DEBUG, 1=INFO, 2=WARNING, 3=ERROR
# Désactiver les opérations oneDNN personnalisées
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

# Définir la variable par défaut
TENSORFLOW_AVAILABLE = False

# Importer TensorFlow de manière sécurisée
try:
    import tensorflow as tf
    print(f"TensorFlow importé avec succès (version {tf.__version__})")
    TENSORFLOW_AVAILABLE = True
except ImportError:
    print("TensorFlow n'est pas disponible. Les fonctionnalités de deep learning seront désactivées.")
except Exception as e:
    print(f"Erreur lors de l'initialisation de TensorFlow: {e}")
    print("Les fonctionnalités de deep learning seront désactivées.")

def predict_ensemble(models, X):
    """
    Fonction de prédiction par ensemble pour les modèles de deep learning

    Args:
        models: Liste des modèles à utiliser pour la prédiction
        X: Caractéristiques d'entrée

    Returns:
        array: Probabilités prédites (moyenne des prédictions)
    """
    try:
        # Faire la prédiction avec chaque modèle
        predictions = []
        for model_info in models:
            try:
                pred = model_info['model'].predict_proba(X)[:, 1]

                # Vérifier et nettoyer les valeurs NaN dans les prédictions
                if np.isnan(pred).any() or np.isinf(pred).any():
                    print("Attention: Des valeurs NaN ou inf détectées dans les prédictions d'un modèle de l'ensemble. Nettoyage automatique.")
                    pred = np.nan_to_num(pred, nan=0.5, posinf=1.0, neginf=0.0)

                # S'assurer que les prédictions sont dans l'intervalle [0, 1]
                pred = np.clip(pred, 0, 1)

                predictions.append(pred)
            except Exception as e:
                print(f"Erreur avec un modèle de l'ensemble: {e}")
                # Ne pas ajouter ce modèle aux prédictions

        # Vérifier qu'il y a au moins une prédiction valide
        if not predictions:
            print("Aucune prédiction valide dans l'ensemble. Utilisation de valeurs par défaut.")
            return np.full(X.shape[0], 0.5)

        # Calculer la moyenne des prédictions
        ensemble_pred = np.mean(predictions, axis=0)

        # Vérifier et nettoyer les valeurs NaN dans la prédiction finale
        if np.isnan(ensemble_pred).any() or np.isinf(ensemble_pred).any():
            print("Attention: Des valeurs NaN ou inf détectées dans la prédiction finale de l'ensemble. Nettoyage automatique.")
            ensemble_pred = np.nan_to_num(ensemble_pred, nan=0.5, posinf=1.0, neginf=0.0)

        # S'assurer que les prédictions sont dans l'intervalle [0, 1]
        ensemble_pred = np.clip(ensemble_pred, 0, 1)

        return ensemble_pred
    except Exception as e:
        print(f"Erreur dans predict_ensemble: {e}")
        # En cas d'erreur, retourner des probabilités neutres (0.5)
        return np.full(X.shape[0], 0.5)

class EnsembleWrapper:
    """
    Classe wrapper pour les ensembles de modèles de deep learning
    Implémente l'interface predict_proba pour être compatible avec scikit-learn
    """
    def __init__(self, models):
        self.models = models

    def predict_proba(self, X):
        try:
            # Utiliser directement la fonction predict_ensemble définie au niveau du module
            pred = predict_ensemble(self.models, X)

            # Retourner un tableau de forme (n_samples, n_classes)
            return np.column_stack((1 - pred, pred))
        except Exception as e:
            print(f"Erreur dans predict_proba de l'ensemble: {e}")
            # En cas d'erreur, retourner des probabilités neutres (0.5)
            n_samples = X.shape[0] if hasattr(X, 'shape') else 1
            return np.full((n_samples, 2), 0.5)

    def predict(self, X):
        try:
            # Obtenir les probabilités
            probas = self.predict_proba(X)
            # Retourner la classe avec la plus haute probabilité
            return (probas[:, 1] > 0.5).astype(int)
        except Exception as e:
            print(f"Erreur dans predict de l'ensemble: {e}")
            # En cas d'erreur, retourner des prédictions neutres (0)
            n_samples = X.shape[0] if hasattr(X, 'shape') else 1
            return np.zeros(n_samples, dtype=int)

class ModelWrapper:
    """
    Classe wrapper pour les modèles de deep learning
    Implémente l'interface predict_proba pour être compatible avec scikit-learn
    """
    def __init__(self, model, scaler):
        self.model = model
        self.scaler = scaler

    def predict_proba(self, X):
        try:
            # Convertir X en DataFrame pour un nettoyage robuste
            X_df = pd.DataFrame(X)

            # Nettoyer les données d'entrée
            X_df = X_df.fillna(0).replace([np.inf, -np.inf], 0)

            # Convertir en tableau numpy
            X_clean = X_df.values.astype(np.float32)

            # Appliquer le scaler
            X_scaled = self.scaler.transform(X_clean)

            # Vérifier et nettoyer les valeurs NaN ou inf après scaling
            X_scaled = np.nan_to_num(X_scaled, nan=0.0, posinf=0.0, neginf=0.0)

            # Faire la prédiction
            y_pred = self.model.predict(X_scaled, verbose=0)

            # Convertir en format compatible avec scikit-learn
            if len(y_pred.shape) == 1 or y_pred.shape[1] == 1:
                # Cas binaire: retourner [1-p, p]
                y_pred = y_pred.flatten()
                return np.vstack([1 - y_pred, y_pred]).T
            else:
                # Cas multi-classe: déjà au bon format
                return y_pred
        except Exception as e:
            print(f"Erreur dans predict_proba: {e}")
            # En cas d'erreur, retourner des probabilités neutres (0.5)
            return np.array([[0.5, 0.5]] * X.shape[0])

    def predict(self, X):
        try:
            # Obtenir les probabilités
            probas = self.predict_proba(X)
            # Retourner la classe avec la plus haute probabilité
            return (probas[:, 1] > 0.5).astype(int)
        except Exception as e:
            print(f"Erreur dans predict: {e}")
            # En cas d'erreur, retourner des prédictions neutres (0)
            return np.zeros(X.shape[0], dtype=int)

class KenoDeepLearning:
    """
    Classe pour l'intégration du deep learning dans l'application Keno
    """

    def __init__(self, data_manager=None):
        """
        Initialise la classe de deep learning

        Args:
            data_manager: Gestionnaire de données Keno
        """
        self.data_manager = data_manager
        self.models = {}
        self.tensorflow_available = TENSORFLOW_AVAILABLE
        self.gpu_available = False
        self.npu_available = False

        # Vérifier la disponibilité du GPU/NPU si TensorFlow est disponible
        if self.tensorflow_available:
            try:
                # Configurer TensorFlow pour utiliser la mémoire GPU de manière optimale
                gpus = tf.config.list_physical_devices('GPU')
                if gpus:
                    print(f"Deep Learning: GPU détecté: {len(gpus)} dispositif(s)")
                    self.gpu_available = True

                    # Configurer TensorFlow pour utiliser le GPU de manière optimale
                    for gpu in gpus:
                        try:
                            # Permettre à TensorFlow d'allouer la mémoire au besoin
                            tf.config.experimental.set_memory_growth(gpu, True)

                            # Configurer pour utiliser jusqu'à 80% de la mémoire GPU disponible
                            # Cela permet d'utiliser plus de mémoire pour accélérer l'entraînement
                            try:
                                gpu_mem = tf.config.experimental.get_memory_info(gpu)['total']
                                memory_limit = int(gpu_mem * 0.8)  # Utiliser 80% de la mémoire disponible
                                if memory_limit > 0:
                                    tf.config.set_logical_device_configuration(
                                        gpu,
                                        [tf.config.LogicalDeviceConfiguration(memory_limit=memory_limit)]
                                    )
                                    print(f"  GPU {gpu.name}: Limite de mémoire configurée à {memory_limit // (1024*1024)} Mo (80% du total)")
                            except Exception:
                                # Si nous ne pouvons pas obtenir la mémoire totale, ne pas définir de limite
                                print(f"  GPU {gpu.name}: Utilisation de toute la mémoire disponible")
                        except Exception as gpu_err:
                            print(f"  Erreur lors de la configuration du GPU {gpu.name}: {gpu_err}")

                # Vérifier les dispositifs NPU/TPU
                try:
                    # Vérifier les TPU
                    tpus = tf.distribute.cluster_resolver.TPUClusterResolver()
                    tf.config.experimental_connect_to_cluster(tpus)
                    tf.tpu.experimental.initialize_tpu_system(tpus)
                    tpu_strategy = tf.distribute.TPUStrategy(tpus)
                    print(f"Deep Learning: TPU détecté avec {tpu_strategy.num_replicas_in_sync} répliques")
                    self.npu_available = True
                except:
                    # Vérifier les NPU (Intel/AMD/autres)
                    devices = tf.config.list_physical_devices()
                    for device in devices:
                        if 'NPU' in device.name.upper() or 'NEURAL' in device.name.upper():
                            print(f"Deep Learning: NPU détecté: {device.name}")
                            self.npu_available = True
                            break

                # Configurer OpenVINO pour Intel NPU si disponible
                try:
                    import openvino.runtime as ov
                    core = ov.Core()
                    devices = core.available_devices
                    for device in devices:
                        if 'NPU' in device.upper() or 'NEURAL' in device.upper() or 'GPU' in device.upper():
                            print(f"Deep Learning: OpenVINO détecté avec dispositif: {device}")
                            self.npu_available = True
                except ImportError:
                    pass
                except Exception as ov_err:
                    print(f"Erreur lors de la vérification d'OpenVINO: {ov_err}")

                # Configurer CUDA pour optimiser les performances
                if self.gpu_available:
                    try:
                        # Configurer cuDNN pour utiliser les algorithmes les plus rapides
                        tf.config.optimizer.set_jit(True)  # Activer XLA JIT
                        tf.config.optimizer.set_experimental_options({
                            "auto_mixed_precision": True,  # Activer la précision mixte
                            "layout_optimizer": True,      # Optimiser la disposition des tenseurs
                            "constant_folding": True,      # Plier les constantes
                            "shape_optimization": True,    # Optimiser les formes
                            "remapping": True,            # Réorganiser les opérations
                            "arithmetic_optimization": True,  # Optimiser les opérations arithmétiques
                            "dependency_optimization": True,  # Optimiser les dépendances
                            "loop_optimization": True,     # Optimiser les boucles
                            "function_optimization": True, # Optimiser les fonctions
                            "debug_stripper": True,       # Supprimer les opérations de débogage
                        })

                        # Configurer cuDNN pour utiliser les algorithmes les plus rapides
                        tf.keras.mixed_precision.set_global_policy('mixed_float16')  # Utiliser la précision mixte float16

                        print("Deep Learning: Optimisations CUDA avancées activées (XLA JIT, précision mixte float16, optimisations du graphe)")
                    except Exception as cuda_err:
                        print(f"Erreur lors de la configuration des optimisations CUDA: {cuda_err}")
                        # Essayer une configuration plus simple
                        try:
                            tf.config.optimizer.set_jit(True)  # Activer XLA JIT
                            print("Deep Learning: Optimisation XLA JIT activée (configuration minimale)")
                        except Exception:
                            print("Impossible d'activer les optimisations GPU. Utilisation des paramètres par défaut.")
            except Exception as e:
                print(f"Erreur lors de la vérification des accélérateurs matériels: {e}")

    def is_available(self):
        """
        Vérifie si le deep learning est disponible

        Returns:
            bool: True si TensorFlow est disponible, False sinon
        """
        return self.tensorflow_available

    def create_model(self, input_dim, model_type='simple', neurons=64, dropout_rate=0.2, base_model=None):
        """
        Crée un modèle de réseau de neurones

        Args:
            input_dim (int): Dimension d'entrée (nombre de caractéristiques)
            model_type (str): Type de modèle ('simple', 'deep', 'lstm', 'cnn')
            neurons (int): Nombre de neurones dans les couches cachées
            dropout_rate (float): Taux de dropout pour la régularisation
            base_model: Modèle de base pour le transfert d'apprentissage

        Returns:
            model: Modèle TensorFlow/Keras ou None si TensorFlow n'est pas disponible
        """
        if not self.tensorflow_available:
            print("TensorFlow n'est pas disponible. Impossible de créer un modèle de deep learning.")
            return None

        try:
            # Vérifier si nous utilisons le transfert d'apprentissage
            if base_model is not None and hasattr(base_model, 'layers'):
                print(f"Utilisation du transfert d'apprentissage à partir d'un modèle existant")
                # Créer un modèle de transfert d'apprentissage
                return self.create_transfer_model(base_model, input_dim, neurons, dropout_rate)

            # Créer un modèle en fonction du type spécifié
            if model_type == 'simple':
                # Modèle feed-forward simple optimisé pour la vitesse
                model = tf.keras.Sequential([
                    tf.keras.layers.Dense(neurons, activation='relu', input_shape=(input_dim,),
                                         kernel_initializer='he_normal',  # Meilleure initialisation pour ReLU
                                         use_bias=True),
                    tf.keras.layers.BatchNormalization(),  # Ajouter la normalisation par lots pour accélérer la convergence
                    tf.keras.layers.Dropout(dropout_rate),
                    tf.keras.layers.Dense(neurons // 2, activation='relu',
                                         kernel_initializer='he_normal'),
                    tf.keras.layers.BatchNormalization(),
                    tf.keras.layers.Dropout(dropout_rate),
                    tf.keras.layers.Dense(1, activation='sigmoid',
                                         kernel_initializer='glorot_uniform')  # Meilleure initialisation pour sigmoid
                ])
            elif model_type == 'deep':
                # Modèle feed-forward profond optimisé pour la vitesse et la précision
                model = tf.keras.Sequential([
                    # Couche d'entrée avec normalisation
                    tf.keras.layers.Dense(neurons, activation=None, input_shape=(input_dim,),
                                         kernel_initializer='he_normal'),
                    tf.keras.layers.BatchNormalization(),
                    tf.keras.layers.Activation('relu'),  # Séparer l'activation pour une meilleure performance avec BatchNorm
                    tf.keras.layers.Dropout(dropout_rate),

                    # Première couche cachée
                    tf.keras.layers.Dense(neurons, activation=None,
                                         kernel_initializer='he_normal'),
                    tf.keras.layers.BatchNormalization(),
                    tf.keras.layers.Activation('relu'),
                    tf.keras.layers.Dropout(dropout_rate),

                    # Deuxième couche cachée
                    tf.keras.layers.Dense(neurons // 2, activation=None,
                                         kernel_initializer='he_normal'),
                    tf.keras.layers.BatchNormalization(),
                    tf.keras.layers.Activation('relu'),
                    tf.keras.layers.Dropout(dropout_rate),

                    # Troisième couche cachée
                    tf.keras.layers.Dense(neurons // 4, activation='relu',
                                         kernel_initializer='he_normal'),

                    # Couche de sortie
                    tf.keras.layers.Dense(1, activation='sigmoid',
                                         kernel_initializer='glorot_uniform')
                ])
            elif model_type == 'lstm':
                # Modèle LSTM pour les séquences temporelles
                # Reshape input pour LSTM: (batch_size, timesteps, features)
                model = tf.keras.Sequential([
                    tf.keras.layers.Reshape((1, input_dim), input_shape=(input_dim,)),
                    tf.keras.layers.LSTM(neurons, return_sequences=True),
                    tf.keras.layers.Dropout(dropout_rate),
                    tf.keras.layers.LSTM(neurons // 2),
                    tf.keras.layers.Dropout(dropout_rate),
                    tf.keras.layers.Dense(neurons // 4, activation='relu'),
                    tf.keras.layers.Dense(1, activation='sigmoid')
                ])
            elif model_type == 'cnn':
                # Modèle CNN 1D optimisé pour détecter des motifs avec une meilleure performance
                # Reshape input pour CNN: (batch_size, timesteps, features)
                model = tf.keras.Sequential([
                    # Reshape pour le format CNN 1D
                    tf.keras.layers.Reshape((input_dim, 1), input_shape=(input_dim,)),

                    # Première couche convolutive avec normalisation par lots
                    tf.keras.layers.Conv1D(filters=neurons, kernel_size=3, padding='same',
                                          kernel_initializer='he_normal'),
                    tf.keras.layers.BatchNormalization(),
                    tf.keras.layers.Activation('relu'),
                    tf.keras.layers.MaxPooling1D(pool_size=2),

                    # Deuxième couche convolutive avec normalisation par lots
                    tf.keras.layers.Conv1D(filters=neurons // 2, kernel_size=3, padding='same',
                                          kernel_initializer='he_normal'),
                    tf.keras.layers.BatchNormalization(),
                    tf.keras.layers.Activation('relu'),

                    # Pooling global pour réduire la dimension
                    tf.keras.layers.GlobalAveragePooling1D(),

                    # Couche dense finale
                    tf.keras.layers.Dense(neurons // 4, activation='relu',
                                         kernel_initializer='he_normal'),
                    tf.keras.layers.BatchNormalization(),
                    tf.keras.layers.Dropout(dropout_rate),

                    # Couche de sortie
                    tf.keras.layers.Dense(1, activation='sigmoid',
                                         kernel_initializer='glorot_uniform')
                ])
            elif model_type == 'transformer':
                # Modèle Transformer pour capturer les dépendances à long terme
                # Reshape input pour Transformer: (batch_size, timesteps, features)
                # Utiliser une couche d'embedding pour transformer les caractéristiques
                model = tf.keras.Sequential([
                    tf.keras.layers.Reshape((input_dim, 1), input_shape=(input_dim,)),
                    tf.keras.layers.Conv1D(filters=neurons, kernel_size=1, activation='linear'),  # Embedding
                    tf.keras.layers.LayerNormalization(),
                    # Ajouter un bloc Transformer (attention multi-tête)
                    tf.keras.layers.MultiHeadAttention(num_heads=4, key_dim=neurons//4),
                    tf.keras.layers.LayerNormalization(),
                    tf.keras.layers.GlobalAveragePooling1D(),
                    tf.keras.layers.Dense(neurons // 2, activation='relu'),
                    tf.keras.layers.Dropout(dropout_rate),
                    tf.keras.layers.Dense(1, activation='sigmoid')
                ])
            else:
                # Type de modèle non reconnu, utiliser le modèle simple par défaut
                print(f"Type de modèle '{model_type}' non reconnu. Utilisation du modèle simple par défaut.")
                model = tf.keras.Sequential([
                    tf.keras.layers.Dense(neurons, activation='relu', input_shape=(input_dim,)),
                    tf.keras.layers.Dropout(dropout_rate),
                    tf.keras.layers.Dense(neurons // 2, activation='relu'),
                    tf.keras.layers.Dropout(dropout_rate),
                    tf.keras.layers.Dense(1, activation='sigmoid')
                ])

            # Compiler le modèle avec gestion d'erreur et optimisations
            try:
                # Utiliser un optimiseur plus avancé avec un taux d'apprentissage adapté
                if self.gpu_available:
                    # Sur GPU, utiliser Adam avec précision mixte et un taux d'apprentissage plus élevé
                    optimizer = tf.keras.optimizers.Adam(
                        learning_rate=0.002,  # Taux d'apprentissage légèrement plus élevé
                        beta_1=0.9,           # Paramètre de décroissance exponentielle pour les estimations du premier moment
                        beta_2=0.999,         # Paramètre de décroissance exponentielle pour les estimations du second moment
                        epsilon=1e-07,        # Petite constante pour éviter la division par zéro
                        amsgrad=True          # Appliquer la variante AMSGrad de Adam
                    )
                    # Utiliser une fonction de perte optimisée pour la précision mixte
                    loss = tf.keras.losses.BinaryCrossentropy(from_logits=False)
                else:
                    # Sur CPU, utiliser un optimiseur plus simple mais efficace
                    optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
                    loss = 'binary_crossentropy'

                # Compiler avec des métriques complètes
                model.compile(
                    optimizer=optimizer,
                    loss=loss,
                    metrics=[
                        'accuracy',
                        tf.keras.metrics.AUC(),
                        tf.keras.metrics.Precision(),
                        tf.keras.metrics.Recall()
                    ]
                )
            except Exception as e:
                print(f"Erreur lors de la compilation du modèle avec optimisations avancées: {e}")
                # Essayer avec une configuration intermédiaire
                try:
                    model.compile(
                        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
                        loss='binary_crossentropy',
                        metrics=['accuracy', tf.keras.metrics.AUC()]
                    )
                except Exception as e2:
                    print(f"Erreur lors de la compilation du modèle (tentative intermédiaire): {e2}")
                    # Essayer avec une configuration minimale
                    try:
                        model.compile(
                            optimizer='adam',
                            loss='binary_crossentropy',
                            metrics=['accuracy']
                        )
                    except Exception as e3:
                        print(f"Erreur lors de la compilation du modèle (tentative minimale): {e3}")
                        return None

            return model

        except Exception as e:
            print(f"Erreur lors de la création du modèle de deep learning: {e}")
            return None

    def train_model(self, X, y, model_type='simple', test_size=0.2, random_state=42,
                   batch_size=32, epochs=50, early_stopping=True, verbose=1, neurons=64, dropout_rate=0.2,
                   timeout=180, ultra_fast=False):  # Réduire le timeout par défaut à 3 minutes (180 secondes) et ajouter mode ultra-rapide
        """
        Entraîne un modèle de deep learning

        Args:
            X: Caractéristiques d'entrée
            y: Cibles
            model_type (str): Type de modèle ('simple', 'deep', 'lstm', 'cnn')
            test_size (float): Proportion des données à utiliser pour le test
            random_state (int): Graine aléatoire pour la reproductibilité
            batch_size (int): Taille des lots pour l'entraînement
            epochs (int): Nombre d'époques d'entraînement
            early_stopping (bool): Utiliser l'arrêt anticipé
            verbose (int): Niveau de verbosité (0=silencieux, 1=barre de progression, 2=une ligne par époque)

        Returns:
            dict: Dictionnaire contenant le modèle et les métriques d'évaluation
        """
        if not self.tensorflow_available:
            print("TensorFlow n'est pas disponible. Impossible d'entraîner un modèle de deep learning.")
            return None

        try:
            # Diviser les données en ensembles d'entraînement et de test
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state)

            # Approche radicale pour éliminer les valeurs NaN
            # Utiliser pandas pour un nettoyage plus robuste
            import pandas as pd

            # Convertir en DataFrame
            X_train_df = pd.DataFrame(X_train)
            X_test_df = pd.DataFrame(X_test)

            # Remplacer les valeurs NaN par 0
            X_train_df = X_train_df.fillna(0)
            X_test_df = X_test_df.fillna(0)

            # Remplacer les valeurs infinies par 0
            X_train_df = X_train_df.replace([np.inf, -np.inf], 0)
            X_test_df = X_test_df.replace([np.inf, -np.inf], 0)

            # Convertir en tableaux numpy
            X_train = X_train_df.values
            X_test = X_test_df.values

            # Convertir y_train et y_test en Series pandas et remplacer les valeurs NaN
            y_train_series = pd.Series(y_train).fillna(0)
            y_test_series = pd.Series(y_test).fillna(0)

            # Convertir en tableaux numpy
            y_train = y_train_series.values
            y_test = y_test_series.values

            # Normaliser les données
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)

            # Convertir à nouveau en DataFrame pour un nettoyage final
            X_train_scaled_df = pd.DataFrame(X_train_scaled)
            X_test_scaled_df = pd.DataFrame(X_test_scaled)

            # Remplacer les valeurs NaN et infinies
            X_train_scaled_df = X_train_scaled_df.fillna(0).replace([np.inf, -np.inf], 0)
            X_test_scaled_df = X_test_scaled_df.fillna(0).replace([np.inf, -np.inf], 0)

            # Convertir en tableaux numpy avec type spécifié
            X_train_scaled = X_train_scaled_df.values.astype(np.float32)
            X_test_scaled = X_test_scaled_df.values.astype(np.float32)

            # Convertir y_train et y_test en type spécifié
            y_train = y_train.astype(np.float32)
            y_test = y_test.astype(np.float32)

            print("Données nettoyées de manière radicale pour l'entraînement du modèle de deep learning")

            # Créer le modèle avec les paramètres spécifiés
            model = self.create_model(X_train.shape[1], model_type=model_type, neurons=neurons, dropout_rate=dropout_rate)

            if model is None:
                return None

            # Configurer les callbacks optimisés
            callbacks = []

            if early_stopping:
                # Callback d'arrêt anticipé amélioré
                early_stop = tf.keras.callbacks.EarlyStopping(
                    monitor='val_loss',
                    patience=5,  # Réduire la patience pour accélérer l'arrêt
                    restore_best_weights=True,
                    min_delta=0.001  # Arrêter si l'amélioration est inférieure à 0.1%
                )
                callbacks.append(early_stop)

                # Ajouter un callback de réduction du taux d'apprentissage
                reduce_lr = tf.keras.callbacks.ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,  # Réduire le taux d'apprentissage de moitié
                    patience=3,  # Attendre 3 époques sans amélioration
                    min_lr=0.0001,  # Taux d'apprentissage minimal
                    verbose=0
                )
                callbacks.append(reduce_lr)

            # Ajouter un callback pour libérer la mémoire GPU après chaque époque
            class MemoryCleanupCallback(tf.keras.callbacks.Callback):
                def on_epoch_end(self, epoch, logs=None):
                    # Libérer la mémoire GPU si disponible
                    if tf.config.experimental.get_memory_growth:
                        tf.keras.backend.clear_session()
                        import gc
                        gc.collect()

            # Ajouter le callback de nettoyage de mémoire
            callbacks.append(MemoryCleanupCallback())

            # Réduire la verbosité en production
            if verbose == 0:
                # Désactiver complètement les sorties de TensorFlow
                os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
                tf.get_logger().setLevel('ERROR')

            # Entraîner le modèle avec gestion d'erreur et timeout
            try:
                # Les données ont déjà été nettoyées de manière radicale plus haut
                # Nous pouvons directement procéder à l'entraînement

                # Ajuster les paramètres en fonction du mode ultra-rapide et du type de modèle
                model_timeout = timeout

                if ultra_fast:
                    # Mode ultra-rapide: réduire drastiquement le nombre d'époques et la taille du lot
                    if model_type == 'cnn':
                        actual_epochs = 5  # Limiter à 5 époques pour les CNN en mode ultra-rapide
                        batch_size = max(64, batch_size)  # Augmenter la taille du lot pour accélérer
                        model_timeout = 60  # Limiter à 1 minute
                        print(f"Mode ULTRA-RAPIDE: Modèle CNN limité à {actual_epochs} époques, batch_size={batch_size}, timeout={model_timeout}s")
                    else:
                        actual_epochs = 10  # Limiter à 10 époques pour les autres modèles en mode ultra-rapide
                        batch_size = max(64, batch_size)  # Augmenter la taille du lot pour accélérer
                        model_timeout = 60  # Limiter à 1 minute
                        print(f"Mode ULTRA-RAPIDE: Modèle {model_type} limité à {actual_epochs} époques, batch_size={batch_size}, timeout={model_timeout}s")
                else:
                    # Mode normal
                    if model_type == 'cnn':
                        # Les CNN sont plus complexes, réduire le nombre d'époques
                        actual_epochs = min(epochs, 15)  # Réduit de 20 à 15 époques maximum pour les CNN
                        print(f"Modèle CNN: limitation à {actual_epochs} époques avec timeout de {model_timeout} secondes")
                    else:
                        # Pour les autres modèles, limiter à 20 époques (réduit de 30)
                        actual_epochs = min(epochs, 20)  # Limiter à 20 époques maximum

                # Ajouter un callback pour le timeout
                import time
                class TimeoutCallback(tf.keras.callbacks.Callback):
                    def __init__(self, timeout):
                        super().__init__()
                        self.timeout = timeout
                        self.start_time = None
                        self.stopped = False

                    def on_train_begin(self, logs=None):
                        self.start_time = time.time()

                    def on_epoch_end(self, epoch, logs=None):
                        elapsed_time = time.time() - self.start_time
                        if elapsed_time > self.timeout:
                            print(f"\nTimeout atteint après {elapsed_time:.1f} secondes. Arrêt de l'entraînement.")
                            self.model.stop_training = True
                            self.stopped = True

                # Ajouter le callback de timeout aux autres callbacks
                timeout_callback = TimeoutCallback(model_timeout)
                callbacks.append(timeout_callback)

                # Entraîner le modèle avec les callbacks
                history = model.fit(
                    X_train_scaled, y_train,
                    validation_data=(X_test_scaled, y_test),
                    batch_size=batch_size,
                    epochs=actual_epochs,
                    callbacks=callbacks,
                    verbose=verbose
                )

                # Vérifier si l'entraînement a été arrêté par timeout
                if hasattr(timeout_callback, 'stopped') and timeout_callback.stopped:
                    print("L'entraînement a été arrêté par timeout. Le modèle peut être incomplet.")
            except Exception as e:
                print(f"Erreur lors de l'entraînement du modèle: {e}")
                # Essayer avec une configuration plus simple
                try:
                    # Réduire la taille du lot et le nombre d'époques
                    reduced_batch_size = max(8, batch_size // 4)
                    reduced_epochs = min(10, epochs // 3)

                    print(f"Tentative avec configuration simplifiée: batch_size={reduced_batch_size}, epochs={reduced_epochs}")

                    history = model.fit(
                        X_train_scaled, y_train,
                        validation_data=(X_test_scaled, y_test),
                        batch_size=reduced_batch_size,
                        epochs=reduced_epochs,
                        verbose=0  # Réduire la verbosité
                    )
                except Exception as e2:
                    print(f"Erreur lors de l'entraînement du modèle (tentative de secours): {e2}")
                    return None

            # Évaluer le modèle
            y_pred_proba = model.predict(X_test_scaled, verbose=0)
            y_pred = (y_pred_proba > 0.5).astype(int).flatten()

            # Calculer les métriques
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, zero_division=0)
            recall = recall_score(y_test, y_pred, zero_division=0)
            f1 = f1_score(y_test, y_pred, zero_division=0)
            auc = roc_auc_score(y_test, y_pred_proba) if len(np.unique(y_test)) > 1 else 0.5

            # Utiliser la classe ModelWrapper définie au niveau du module

            # Créer le wrapper
            model_wrapper = ModelWrapper(model, scaler)

            # Retourner les résultats
            return {
                'model': model_wrapper,
                'scaler': scaler,
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'auc': auc,
                'history': history.history,
                'type': f'deep_learning_{model_type}'
            }

        except Exception as e:
            print(f"Erreur lors de l'entraînement du modèle de deep learning: {e}")
            return None

    def save_model(self, model_data, filepath):
        """
        Sauvegarde un modèle de deep learning

        Args:
            model_data (dict): Dictionnaire contenant le modèle et les métriques
            filepath (str): Chemin du fichier où sauvegarder le modèle

        Returns:
            bool: True si la sauvegarde a réussi, False sinon
        """
        if not self.tensorflow_available:
            print("TensorFlow n'est pas disponible. Impossible de sauvegarder un modèle de deep learning.")
            return False

        try:
            # Vérifier si model_data contient un modèle valide
            if 'model' not in model_data or model_data['model'] is None:
                print("Aucun modèle à sauvegarder dans les données fournies.")
                return False

            # Créer le répertoire parent si nécessaire
            os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)

            # Créer une copie des données sans le modèle TensorFlow
            model_data_copy = {}
            for key, value in model_data.items():
                if key != 'model':
                    model_data_copy[key] = value

            # Extraire le scaler pour le sauvegarder séparément
            if 'scaler' in model_data:
                scaler_path = os.path.splitext(filepath)[0] + "_scaler.pkl"
                try:
                    joblib.dump(model_data['scaler'], scaler_path)
                    model_data_copy['scaler_path'] = scaler_path
                except Exception as e:
                    print(f"Erreur lors de la sauvegarde du scaler: {e}")
                    # Continuer même si le scaler n'a pas pu être sauvegardé

            # Extraire le modèle TensorFlow du wrapper
            if hasattr(model_data['model'], 'model'):
                tf_model = model_data['model'].model
            else:
                tf_model = model_data['model']

            # Sauvegarder les poids du modèle au format HDF5 (plus fiable)
            try:
                # Sauvegarder les poids du modèle
                weights_path = os.path.splitext(filepath)[0] + "_weights.h5"
                tf_model.save_weights(weights_path)
                model_data_copy['weights_path'] = weights_path

                # Sauvegarder la configuration du modèle
                config_path = os.path.splitext(filepath)[0] + "_config.json"
                with open(config_path, 'w') as f:
                    f.write(tf_model.to_json())
                model_data_copy['config_path'] = config_path

                print(f"Poids et configuration du modèle sauvegardés dans {weights_path} et {config_path}")

                # Sauvegarder les couches du modèle (architecture)
                try:
                    # Créer un dictionnaire avec les informations sur les couches
                    layers_info = []
                    for layer in tf_model.layers:
                        layer_info = {
                            'name': layer.name,
                            'class_name': layer.__class__.__name__,
                            'config': layer.get_config()
                        }
                        layers_info.append(layer_info)

                    # Sauvegarder les informations sur les couches
                    layers_path = os.path.splitext(filepath)[0] + "_layers.json"
                    with open(layers_path, 'w') as f:
                        import json
                        json.dump(layers_info, f, indent=2)
                    model_data_copy['layers_path'] = layers_path
                    print(f"Informations sur les couches sauvegardées dans {layers_path}")
                except Exception as e:
                    print(f"Erreur lors de la sauvegarde des informations sur les couches: {e}")
                    # Continuer même si les informations sur les couches n'ont pas pu être sauvegardées

                # Sauvegarder les métriques d'entraînement
                try:
                    metrics_path = os.path.splitext(filepath)[0] + "_metrics.json"
                    metrics_data = {
                        'metrics': model_data.get('metrics', {}),
                        'accuracy': model_data.get('accuracy', 0),
                        'precision': model_data.get('precision', 0),
                        'recall': model_data.get('recall', 0),
                        'f1': model_data.get('f1', 0),
                        'auc': model_data.get('auc', 0)
                    }
                    with open(metrics_path, 'w') as f:
                        import json
                        json.dump(metrics_data, f, indent=2)
                    model_data_copy['metrics_path'] = metrics_path
                    print(f"Métriques d'entraînement sauvegardées dans {metrics_path}")
                except Exception as e:
                    print(f"Erreur lors de la sauvegarde des métriques: {e}")
                    # Continuer même si les métriques n'ont pas pu être sauvegardées

            except Exception as e:
                print(f"Erreur lors de la sauvegarde des poids du modèle: {e}")
                # Ne pas essayer de sauvegarder le modèle complet, cela cause des problèmes
                print("Sauvegarde des poids et de la configuration impossible, utilisation d'une méthode alternative")

                # Sauvegarder les prédictions du modèle sur un ensemble de données de test
                try:
                    # Générer des données de test aléatoires
                    import numpy as np
                    n_features = tf_model.input_shape[1] if len(tf_model.input_shape) > 1 else 10
                    test_data = np.random.random((100, n_features))

                    # Faire des prédictions
                    predictions = tf_model.predict(test_data)

                    # Sauvegarder les données de test et les prédictions
                    test_data_path = os.path.splitext(filepath)[0] + "_test_data.npy"
                    predictions_path = os.path.splitext(filepath)[0] + "_predictions.npy"
                    np.save(test_data_path, test_data)
                    np.save(predictions_path, predictions)

                    model_data_copy['test_data_path'] = test_data_path
                    model_data_copy['predictions_path'] = predictions_path
                    print(f"Données de test et prédictions sauvegardées dans {test_data_path} et {predictions_path}")
                except Exception as e:
                    print(f"Erreur lors de la sauvegarde des prédictions: {e}")
                    # Continuer même si les prédictions n'ont pas pu être sauvegardées

            # Sauvegarder les métadonnées avec joblib
            joblib.dump(model_data_copy, filepath)

            print(f"Métadonnées du modèle sauvegardées dans {filepath}")
            return True

        except Exception as e:
            print(f"Erreur lors de la sauvegarde du modèle de deep learning: {e}")
            import traceback
            traceback.print_exc()
            return False

    def load_model(self, filepath):
        """
        Charge un modèle de deep learning

        Args:
            filepath (str): Chemin du fichier contenant le modèle

        Returns:
            dict: Dictionnaire contenant le modèle et les métriques ou None en cas d'erreur
        """
        if not self.tensorflow_available:
            print("TensorFlow n'est pas disponible. Impossible de charger un modèle de deep learning.")
            return None

        try:
            # Vérifier si le fichier existe
            if not os.path.exists(filepath):
                print(f"Fichier {filepath} introuvable")
                return None

            # Charger les métadonnées avec joblib
            try:
                model_data = joblib.load(filepath)
                print(f"Métadonnées chargées depuis {filepath}")
            except Exception as e:
                print(f"Erreur lors du chargement des métadonnées: {e}")
                return None

            # Charger le scaler si disponible
            scaler = None
            if 'scaler' in model_data:
                scaler = model_data['scaler']
            elif 'scaler_path' in model_data and os.path.exists(model_data['scaler_path']):
                try:
                    scaler = joblib.load(model_data['scaler_path'])
                    print(f"Scaler chargé depuis {model_data['scaler_path']}")
                except Exception as e:
                    print(f"Erreur lors du chargement du scaler: {e}")
                    # Continuer même si le scaler n'a pas pu être chargé

            # Essayer de charger le modèle TensorFlow de différentes façons
            tf_model = None

            # Méthode 1: Charger à partir de la configuration et des poids
            if 'config_path' in model_data and 'weights_path' in model_data:
                if os.path.exists(model_data['config_path']) and os.path.exists(model_data['weights_path']):
                    try:
                        # Charger la configuration du modèle
                        with open(model_data['config_path'], 'r') as f:
                            model_config = f.read()

                        # Créer le modèle à partir de la configuration
                        tf_model = tf.keras.models.model_from_json(model_config)

                        # Charger les poids
                        tf_model.load_weights(model_data['weights_path'])

                        # Compiler le modèle
                        tf_model.compile(
                            optimizer=tf.keras.optimizers.Adam(),
                            loss='binary_crossentropy',
                            metrics=['accuracy']
                        )

                        print(f"Modèle chargé depuis la configuration et les poids")
                    except Exception as e:
                        print(f"Erreur lors du chargement du modèle à partir de la configuration et des poids: {e}")
                        tf_model = None

            # Méthode 2: Essayer de reconstruire le modèle à partir des informations sur les couches
            if tf_model is None and 'layers_path' in model_data:
                if os.path.exists(model_data['layers_path']):
                    try:
                        # Charger les informations sur les couches
                        import json
                        with open(model_data['layers_path'], 'r') as f:
                            layers_info = json.load(f)

                        # Reconstruire le modèle couche par couche
                        inputs = tf.keras.Input(shape=(layers_info[0]['config']['batch_input_shape'][1],))
                        x = inputs

                        for layer_info in layers_info[1:]:  # Ignorer la couche d'entrée
                            layer_class = getattr(tf.keras.layers, layer_info['class_name'])
                            layer_config = layer_info['config']

                            # Supprimer les paramètres non compatibles avec la création de couche
                            if 'batch_input_shape' in layer_config:
                                del layer_config['batch_input_shape']
                            if 'name' in layer_config:
                                del layer_config['name']

                            # Créer la couche
                            x = layer_class(**layer_config)(x)

                        # Créer le modèle
                        tf_model = tf.keras.Model(inputs=inputs, outputs=x)

                        # Compiler le modèle
                        tf_model.compile(
                            optimizer=tf.keras.optimizers.Adam(),
                            loss='binary_crossentropy',
                            metrics=['accuracy']
                        )

                        # Charger les poids si disponibles
                        if 'weights_path' in model_data and os.path.exists(model_data['weights_path']):
                            try:
                                tf_model.load_weights(model_data['weights_path'])
                                print(f"Poids chargés depuis {model_data['weights_path']}")
                            except Exception as e:
                                print(f"Erreur lors du chargement des poids: {e}")

                        print(f"Modèle reconstruit à partir des informations sur les couches")
                    except Exception as e:
                        print(f"Erreur lors de la reconstruction du modèle à partir des informations sur les couches: {e}")
                        tf_model = None

            # Méthode 3: Essayer de reconstruire le modèle à partir des données de test et des prédictions
            if tf_model is None and 'test_data_path' in model_data and 'predictions_path' in model_data:
                if os.path.exists(model_data['test_data_path']) and os.path.exists(model_data['predictions_path']):
                    try:
                        # Charger les données de test et les prédictions
                        import numpy as np
                        test_data = np.load(model_data['test_data_path'])
                        predictions = np.load(model_data['predictions_path'])

                        # Créer un modèle simple qui reproduit les prédictions
                        input_dim = test_data.shape[1]

                        # Créer un modèle simple
                        inputs = tf.keras.Input(shape=(input_dim,))
                        x = tf.keras.layers.Dense(64, activation='relu')(inputs)
                        x = tf.keras.layers.Dropout(0.2)(x)
                        x = tf.keras.layers.Dense(32, activation='relu')(x)
                        x = tf.keras.layers.Dropout(0.2)(x)
                        outputs = tf.keras.layers.Dense(1, activation='sigmoid')(x)

                        tf_model = tf.keras.Model(inputs=inputs, outputs=outputs)

                        # Compiler le modèle
                        tf_model.compile(
                            optimizer=tf.keras.optimizers.Adam(),
                            loss='binary_crossentropy',
                            metrics=['accuracy']
                        )

                        # Entraîner le modèle sur les données de test et les prédictions
                        tf_model.fit(test_data, predictions, epochs=50, verbose=0)

                        print(f"Modèle reconstruit à partir des données de test et des prédictions")
                    except Exception as e:
                        print(f"Erreur lors de la reconstruction du modèle à partir des données de test et des prédictions: {e}")
                        tf_model = None

            # Méthode 4: Charger le modèle complet si les autres méthodes ont échoué
            if tf_model is None and 'model_path' in model_data:
                if os.path.exists(model_data['model_path']):
                    try:
                        # Utiliser l'option experimental_io_device pour résoudre le problème de chargement
                        options = tf.saved_model.LoadOptions(
                            experimental_io_device='/job:localhost'
                        )
                        tf_model = tf.keras.models.load_model(
                            model_data['model_path'],
                            options=options
                        )
                        print(f"Modèle chargé depuis {model_data['model_path']} avec options spéciales")
                    except Exception as e:
                        print(f"Erreur lors du chargement du modèle avec options spéciales: {e}")
                        # Essayer sans options spéciales comme plan B
                        try:
                            tf_model = tf.keras.models.load_model(model_data['model_path'])
                            print(f"Modèle chargé depuis {model_data['model_path']} sans options spéciales")
                        except Exception as e2:
                            print(f"Erreur lors du chargement du modèle sans options spéciales: {e2}")
                            tf_model = None

            # Méthode 5: Créer un modèle par défaut si toutes les autres méthodes ont échoué
            if tf_model is None:
                try:
                    print("Création d'un modèle par défaut...")
                    # Créer un modèle simple par défaut
                    input_dim = 10  # Valeur par défaut

                    # Essayer de déterminer la dimension d'entrée à partir des métadonnées
                    if 'input_dim' in model_data:
                        input_dim = model_data['input_dim']

                    # Créer un modèle simple
                    inputs = tf.keras.Input(shape=(input_dim,))
                    x = tf.keras.layers.Dense(64, activation='relu')(inputs)
                    x = tf.keras.layers.Dropout(0.2)(x)
                    x = tf.keras.layers.Dense(32, activation='relu')(x)
                    x = tf.keras.layers.Dropout(0.2)(x)
                    outputs = tf.keras.layers.Dense(1, activation='sigmoid')(x)

                    tf_model = tf.keras.Model(inputs=inputs, outputs=outputs)

                    # Compiler le modèle
                    tf_model.compile(
                        optimizer=tf.keras.optimizers.Adam(),
                        loss='binary_crossentropy',
                        metrics=['accuracy']
                    )

                    print(f"Modèle par défaut créé avec succès")
                except Exception as e:
                    print(f"Erreur lors de la création du modèle par défaut: {e}")
                    return None

            # Créer le wrapper avec le modèle et le scaler
            model_wrapper = ModelWrapper(tf_model, scaler)

            # Mettre à jour les données avec le modèle chargé
            model_data['model'] = model_wrapper

            # Charger les métriques si disponibles
            if 'metrics_path' in model_data and os.path.exists(model_data['metrics_path']):
                try:
                    import json
                    with open(model_data['metrics_path'], 'r') as f:
                        metrics_data = json.load(f)

                    # Mettre à jour les métriques dans les données
                    for key, value in metrics_data.items():
                        if key not in model_data:
                            model_data[key] = value

                    print(f"Métriques chargées depuis {model_data['metrics_path']}")
                except Exception as e:
                    print(f"Erreur lors du chargement des métriques: {e}")

            print(f"Modèle de deep learning chargé avec succès depuis {filepath}")
            return model_data

        except Exception as e:
            print(f"Erreur lors du chargement du modèle de deep learning: {e}")
            import traceback
            traceback.print_exc()
            return None

    def predict(self, model_data, X):
        """
        Fait une prédiction avec un modèle de deep learning

        Args:
            model_data (dict): Dictionnaire contenant le modèle et les métriques
            X: Caractéristiques d'entrée

        Returns:
            array: Probabilités prédites
        """
        if not self.tensorflow_available:
            print("TensorFlow n'est pas disponible. Impossible de faire une prédiction avec un modèle de deep learning.")
            return None

        try:
            # Vérifier que le modèle est valide
            if model_data is None or 'model' not in model_data or model_data['model'] is None:
                print("Modèle de deep learning invalide.")
                return None

            # Faire la prédiction
            return model_data['model'].predict_proba(X)[:, 1]

        except Exception as e:
            print(f"Erreur lors de la prédiction avec le modèle de deep learning: {e}")
            return None

    def optimize_hyperparameters(self, X, y, model_type='simple', n_trials=10):
        """
        Optimise les hyperparamètres d'un modèle de deep learning

        Args:
            X: Caractéristiques d'entrée
            y: Cibles
            model_type (str): Type de modèle ('simple', 'deep', 'lstm', 'cnn')
            n_trials (int): Nombre d'essais pour l'optimisation

        Returns:
            dict: Meilleurs hyperparamètres trouvés et modèle entraîné avec ces paramètres
        """
        if not self.tensorflow_available:
            print("TensorFlow n'est pas disponible. Impossible d'optimiser les hyperparamètres.")
            return None

        try:
            # Méthode d'optimisation des hyperparamètres par recherche en grille
            print("Optimisation des hyperparamètres par recherche en grille...")

            # Définir les combinaisons de paramètres à tester
            param_combinations = [
                # Petits réseaux
                {'neurons': 32, 'dropout_rate': 0.1, 'batch_size': 16, 'epochs': 30},
                {'neurons': 32, 'dropout_rate': 0.2, 'batch_size': 32, 'epochs': 40},
                {'neurons': 32, 'dropout_rate': 0.3, 'batch_size': 64, 'epochs': 50},

                # Réseaux moyens
                {'neurons': 64, 'dropout_rate': 0.1, 'batch_size': 16, 'epochs': 30},
                {'neurons': 64, 'dropout_rate': 0.2, 'batch_size': 32, 'epochs': 40},
                {'neurons': 64, 'dropout_rate': 0.3, 'batch_size': 64, 'epochs': 50},

                # Grands réseaux
                {'neurons': 128, 'dropout_rate': 0.1, 'batch_size': 32, 'epochs': 30},
                {'neurons': 128, 'dropout_rate': 0.2, 'batch_size': 64, 'epochs': 40},
                {'neurons': 128, 'dropout_rate': 0.3, 'batch_size': 128, 'epochs': 50},

                # Très grands réseaux
                {'neurons': 256, 'dropout_rate': 0.2, 'batch_size': 64, 'epochs': 40},
                {'neurons': 256, 'dropout_rate': 0.3, 'batch_size': 128, 'epochs': 50},
                {'neurons': 256, 'dropout_rate': 0.4, 'batch_size': 128, 'epochs': 60}
            ]

            # Limiter le nombre d'essais si n_trials est inférieur au nombre de combinaisons
            if n_trials < len(param_combinations):
                import random
                random.seed(42)  # Pour la reproductibilité
                param_combinations = random.sample(param_combinations, n_trials)

            print(f"Test de {len(param_combinations)} combinaisons de paramètres...")

            # Initialiser les variables pour suivre le meilleur modèle
            best_score = 0
            best_model = None
            best_params = None

            # Tester chaque combinaison de paramètres
            for i, params in enumerate(param_combinations):
                print(f"Essai {i+1}/{len(param_combinations)} avec les paramètres: {params}")

                # Entraîner le modèle avec les paramètres actuels
                model = self.train_model(
                    X, y, model_type=model_type, test_size=0.2, random_state=42,
                    batch_size=params['batch_size'], epochs=params['epochs'],
                    early_stopping=True, verbose=0,
                    neurons=params['neurons'], dropout_rate=params['dropout_rate']
                )

                # Vérifier si ce modèle est meilleur que le précédent
                if model:
                    # Utiliser F1-score ou accuracy comme métrique
                    score = model.get('f1', model.get('accuracy', 0))
                    print(f"  Score: {score:.4f}")

                    if score > best_score:
                        best_score = score
                        best_params = params
                        best_model = model
                        print(f"  Nouveau meilleur modèle trouvé!")

            # Afficher les résultats
            if best_model:
                print(f"\nMeilleurs paramètres trouvés: {best_params}")
                print(f"Meilleur score: {best_score:.4f}")

                # Retourner les résultats
                return {
                    'best_params': best_params,
                    'model': best_model,
                    'score': best_score
                }
            else:
                print("Aucun modèle valide n'a pu être entraîné.")
                return None

        except Exception as e:
            print(f"Erreur lors de l'optimisation des hyperparamètres: {e}")
            import traceback
            traceback.print_exc()
            return None

    def create_ensemble_model(self, X, y, n_models=5, model_type='simple'):
        """
        Crée un ensemble de modèles de deep learning

        Args:
            X: Caractéristiques d'entrée
            y: Cibles
            n_models (int): Nombre de modèles dans l'ensemble
            model_type (str): Type de modèle ('simple', 'deep', 'lstm', 'cnn')

        Returns:
            dict: Dictionnaire contenant l'ensemble de modèles
        """
        if not self.tensorflow_available:
            print("TensorFlow n'est pas disponible. Impossible de créer un ensemble de modèles.")
            return None

        try:
            print(f"Création d'un ensemble de {n_models} modèles de type {model_type}...")

            # Créer plusieurs modèles avec des initialisations différentes
            models = []
            for i in range(n_models):
                print(f"Entraînement du modèle {i+1}/{n_models}...")
                model = self.train_model(
                    X, y, model_type=model_type, test_size=0.2, random_state=i,
                    batch_size=32, epochs=30, early_stopping=True, verbose=0
                )
                if model:
                    models.append(model)

            if not models:
                print("Aucun modèle n'a pu être entraîné.")
                return None

            print(f"{len(models)} modèles entraînés avec succès.")

            # Utiliser la classe EnsembleWrapper définie au niveau du module
            # qui utilise directement la fonction predict_ensemble

            # Créer le wrapper
            ensemble_wrapper = EnsembleWrapper(models)

            # Calculer les métriques de l'ensemble
            from sklearn.model_selection import train_test_split
            _, X_test, _, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            y_pred = ensemble_wrapper.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred, zero_division=0)

            print(f"Performance de l'ensemble: Accuracy={accuracy:.4f}, F1-score={f1:.4f}")

            # Retourner les résultats
            return {
                'model': ensemble_wrapper,
                'accuracy': accuracy,
                'f1': f1,
                'type': f'deep_learning_ensemble_{model_type}',
                'models': models,
                'n_models': len(models)
            }

        except Exception as e:
            print(f"Erreur lors de la création de l'ensemble de modèles: {e}")
            import traceback
            traceback.print_exc()
            return None

    def create_transfer_model(self, base_model, input_dim, neurons=64, dropout_rate=0.2):
        """
        Crée un modèle de transfert à partir d'un modèle de base

        Args:
            base_model: Modèle de base à utiliser pour le transfert
            input_dim (int): Dimension d'entrée
            neurons (int): Nombre de neurones dans les couches cachées
            dropout_rate (float): Taux de dropout

        Returns:
            model: Modèle de transfert
        """
        if not self.tensorflow_available:
            print("TensorFlow n'est pas disponible. Impossible de créer un modèle de transfert.")
            return None

        try:
            # Extraire le modèle TensorFlow si c'est un wrapper
            if hasattr(base_model, 'model'):
                base_model = base_model.model

            # Vérifier que le modèle de base est valide
            if not hasattr(base_model, 'layers'):
                print("Modèle de base invalide pour le transfert d'apprentissage.")
                return None

            # Geler les couches du modèle de base (sauf les dernières couches)
            for layer in base_model.layers[:-2]:
                layer.trainable = False

            # Créer un nouveau modèle avec les couches du modèle de base
            # Extraire toutes les couches sauf la dernière (couche de sortie)
            base_layers = base_model.layers[:-1]

            # Créer un nouveau modèle
            model = tf.keras.Sequential()

            # Ajouter une couche d'entrée pour s'assurer que la dimension est correcte
            model.add(tf.keras.layers.InputLayer(input_shape=(input_dim,)))

            # Ajouter les couches du modèle de base (sauf la couche de sortie)
            for layer in base_layers:
                # Vérifier si la couche est compatible (ignorer les couches de reshape)
                if not isinstance(layer, tf.keras.layers.Reshape):
                    model.add(layer)

            # Ajouter de nouvelles couches pour le transfert
            model.add(tf.keras.layers.Dense(neurons, activation='relu'))
            model.add(tf.keras.layers.Dropout(dropout_rate))
            model.add(tf.keras.layers.Dense(1, activation='sigmoid'))

            # Compiler le modèle
            model.compile(
                optimizer=tf.keras.optimizers.Adam(learning_rate=0.0005),  # Taux d'apprentissage plus faible pour le transfert
                loss='binary_crossentropy',
                metrics=['accuracy', tf.keras.metrics.AUC()]
            )

            print(f"Modèle de transfert créé avec succès. Architecture: {model.summary()}")

            return model

        except Exception as e:
            print(f"Erreur lors de la création du modèle de transfert: {e}")
            import traceback
            traceback.print_exc()
            return None

    def train_transfer_model(self, base_model_data, X, y, test_size=0.2, random_state=42, batch_size=32, epochs=30):
        """
        Entraîne un modèle de transfert à partir d'un modèle existant

        Args:
            base_model_data (dict): Dictionnaire contenant le modèle de base
            X: Caractéristiques d'entrée
            y: Cibles
            test_size (float): Proportion des données à utiliser pour le test
            random_state (int): Graine aléatoire pour la reproductibilité
            batch_size (int): Taille des lots pour l'entraînement
            epochs (int): Nombre d'époques d'entraînement

        Returns:
            dict: Dictionnaire contenant le modèle et les métriques d'évaluation
        """
        if not self.tensorflow_available:
            print("TensorFlow n'est pas disponible. Impossible d'entraîner un modèle de transfert.")
            return None

        try:
            # Vérifier que le modèle de base est valide
            if base_model_data is None or 'model' not in base_model_data:
                print("Modèle de base invalide pour le transfert d'apprentissage.")
                return None

            base_model = base_model_data['model']

            # Diviser les données en ensembles d'entraînement et de test
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state)

            # Normaliser les données
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)

            # Créer le modèle de transfert
            model = self.create_transfer_model(base_model, X_train.shape[1])

            if model is None:
                return None

            # Configurer les callbacks
            callbacks = [
                tf.keras.callbacks.EarlyStopping(
                    monitor='val_loss',
                    patience=10,
                    restore_best_weights=True
                )
            ]

            # Entraîner le modèle
            history = model.fit(
                X_train_scaled, y_train,
                validation_data=(X_test_scaled, y_test),
                batch_size=batch_size,
                epochs=epochs,
                callbacks=callbacks,
                verbose=1
            )

            # Évaluer le modèle
            y_pred_proba = model.predict(X_test_scaled, verbose=0)
            y_pred = (y_pred_proba > 0.5).astype(int).flatten()

            # Calculer les métriques
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, zero_division=0)
            recall = recall_score(y_test, y_pred, zero_division=0)
            f1 = f1_score(y_test, y_pred, zero_division=0)
            auc = roc_auc_score(y_test, y_pred_proba) if len(np.unique(y_test)) > 1 else 0.5

            # Utiliser la classe ModelWrapper définie au niveau du module

            # Créer le wrapper
            model_wrapper = ModelWrapper(model, scaler)

            # Retourner les résultats
            return {
                'model': model_wrapper,
                'scaler': scaler,
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'auc': auc,
                'history': history.history,
                'type': 'deep_learning_transfer',
                'base_model_type': base_model_data.get('type', 'unknown')
            }

        except Exception as e:
            print(f"Erreur lors de l'entraînement du modèle de transfert: {e}")
            import traceback
            traceback.print_exc()
            return None

    def visualize_model(self, model, X, y, num=None):
        """
        Visualise les prédictions d'un modèle de deep learning

        Args:
            model: Modèle à visualiser
            X: Caractéristiques d'entrée
            y: Cibles
            num (int): Numéro du modèle (pour le titre)

        Returns:
            str: Chemin du fichier image généré
        """
        if not self.tensorflow_available:
            print("TensorFlow n'est pas disponible. Impossible de visualiser le modèle.")
            return None

        try:
            # Vérifier si matplotlib et seaborn sont disponibles
            try:
                import matplotlib.pyplot as plt
                import seaborn as sns
            except ImportError:
                print("Matplotlib ou Seaborn n'est pas installé. Installation en cours...")
                import subprocess
                subprocess.check_call(["pip", "install", "matplotlib", "seaborn"])
                import matplotlib.pyplot as plt
                import seaborn as sns

            print(f"Génération de la visualisation du modèle...")

            # Faire la prédiction
            if hasattr(model, 'predict_proba'):
                y_pred_proba = model.predict_proba(X)[:, 1]
            else:
                # Si le modèle n'a pas de méthode predict_proba, utiliser predict
                y_pred_proba = model.predict(X).flatten()

            y_pred = (y_pred_proba > 0.5).astype(int)

            # Créer la figure
            plt.figure(figsize=(12, 8))

            # Tracer la distribution des probabilités
            plt.subplot(2, 2, 1)
            sns.histplot(y_pred_proba, bins=20, kde=True)
            plt.title('Distribution des probabilités')
            plt.xlabel('Probabilité')
            plt.ylabel('Fréquence')

            # Tracer la courbe ROC
            from sklearn.metrics import roc_curve, auc

            # Vérifier et nettoyer les valeurs NaN dans y_pred_proba
            if np.isnan(y_pred_proba).any():
                print("Attention: Des valeurs NaN détectées dans y_pred_proba. Remplacement par 0.5.")
                y_pred_proba = np.nan_to_num(y_pred_proba, nan=0.5)

            # Vérifier et nettoyer les valeurs NaN dans y
            if np.isnan(y).any():
                print("Attention: Des valeurs NaN détectées dans y. Remplacement par 0.")
                y = np.nan_to_num(y, nan=0)

            fpr, tpr, _ = roc_curve(y, y_pred_proba)
            roc_auc = auc(fpr, tpr)

            plt.subplot(2, 2, 2)
            plt.plot(fpr, tpr, label=f'AUC = {roc_auc:.4f}')
            plt.plot([0, 1], [0, 1], 'k--')
            plt.xlabel('Taux de faux positifs')
            plt.ylabel('Taux de vrais positifs')
            plt.title('Courbe ROC')
            plt.legend()

            # Tracer la matrice de confusion
            from sklearn.metrics import confusion_matrix

            # Vérifier et nettoyer les valeurs NaN dans y_pred
            if np.isnan(y_pred).any():
                print("Attention: Des valeurs NaN détectées dans y_pred. Remplacement par 0.")
                y_pred = np.nan_to_num(y_pred, nan=0)

            # y a déjà été nettoyé plus haut
            cm = confusion_matrix(y, y_pred)

            plt.subplot(2, 2, 3)
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
            plt.xlabel('Prédit')
            plt.ylabel('Réel')
            plt.title('Matrice de confusion')

            # Tracer les prédictions vs réalité
            plt.subplot(2, 2, 4)
            plt.scatter(range(len(y)), y, label='Réel', alpha=0.5)
            plt.scatter(range(len(y_pred)), y_pred, label='Prédit', alpha=0.5)
            plt.xlabel('Échantillon')
            plt.ylabel('Classe')
            plt.title('Prédictions vs Réalité')
            plt.legend()

            # Ajouter un titre global
            if num is not None:
                plt.suptitle(f'Analyse du modèle pour le numéro {num}', fontsize=16)
            else:
                plt.suptitle('Analyse du modèle', fontsize=16)

            plt.tight_layout()
            plt.subplots_adjust(top=0.9)

            # Sauvegarder la figure
            import os
            os.makedirs('visualisations', exist_ok=True)

            if num is not None:
                filename = f'visualisations/model_analysis_{num}.png'
            else:
                filename = 'visualisations/model_analysis.png'

            plt.savefig(filename)
            print(f"Visualisation sauvegardée dans {filename}")

            # Fermer la figure pour libérer la mémoire
            plt.close()

            return filename

        except Exception as e:
            print(f"Erreur lors de la visualisation du modèle: {e}")
            import traceback
            traceback.print_exc()
            return None
