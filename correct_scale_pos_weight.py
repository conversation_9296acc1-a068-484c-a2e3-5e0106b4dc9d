"""
Script pour corriger le problème de scale_pos_weight identique pour tous les numéros Keno
Ce script garantit que chaque numéro utilise son propre historique pour calculer scale_pos_weight.
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, f1_score

# Essayer d'importer XGBoost
try:
    from xgboost import XGBClassifier
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("XGBoost n'est pas disponible. Veuillez l'installer avec 'pip install xgboost'.")

# Essayer d'importer le gestionnaire de données Keno
try:
    from keno_data import KenoDataManager, KenoDrawData
    print("Module de gestion des données Keno importé avec succès")
except ImportError:
    print("Erreur: Module de gestion des données Keno non disponible")
    sys.exit(1)

def prepare_raw_data(data_manager):
    """
    Prépare les données brutes à partir du gestionnaire de données
    
    Args:
        data_manager: Gestionnaire de données Keno
        
    Returns:
        pd.DataFrame: DataFrame avec les données brutes
    """
    if not data_manager.draws:
        print("Aucun tirage disponible dans le gestionnaire de données")
        return None
    
    # Convertir les tirages en DataFrame pandas
    data = []
    for draw in data_manager.draws:
        # Vérifier si le tirage est valide
        if not hasattr(draw, 'draw_numbers') or not draw.draw_numbers:
            continue
        if not hasattr(draw, 'draw_date') or not draw.draw_date:
            continue
        
        row = {
            'date': draw.draw_date,
            'draw_id': draw.draw_id if hasattr(draw, 'draw_id') else None,
            'day_of_week': draw.draw_date.weekday(),
            'hour': draw.draw_date.hour,
            'is_weekend': 1 if draw.draw_date.weekday() >= 5 else 0,
            'is_afternoon': 1 if draw.draw_date.hour >= 12 else 0,
            'day': draw.draw_date.day,
            'month': draw.draw_date.month,
            'year': draw.draw_date.year
        }
        
        # Ajouter des indicateurs pour chaque numéro possible
        for i in range(1, data_manager.max_number + 1):
            row[f'has_{i}'] = 1 if i in draw.draw_numbers else 0
        
        data.append(row)
    
    # Créer le DataFrame
    df = pd.DataFrame(data)
    if df.empty:
        print("Aucune donnée valide après filtrage")
        return None
    
    # Trier par date
    df = df.sort_values('date')
    print(f"DataFrame créé avec {len(df)} tirages")
    
    return df

def prepare_features_for_number(df, num, use_lag=True, use_neighbors=True):
    """
    Prépare les caractéristiques pour un numéro spécifique
    
    Args:
        df: DataFrame avec les données brutes
        num: Numéro Keno à analyser
        use_lag: Utiliser les caractéristiques de lag
        use_neighbors: Utiliser les caractéristiques de voisins
        
    Returns:
        tuple: (X, y) données et étiquettes
    """
    if df is None or df.empty:
        return None, None
    
    # Créer une copie du DataFrame
    num_data = df.copy()
    
    # Ajouter des caractéristiques de lag (décalage temporel) si demandé
    if use_lag:
        for i in range(1, 6):  # Regarder les 5 derniers tirages
            num_data[f'has_{num}_lag_{i}'] = num_data[f'has_{num}'].shift(i).fillna(0)
    
    # Ajouter des caractéristiques pour les numéros voisins si demandé
    if use_neighbors:
        max_number = max([int(col.split('_')[1]) for col in df.columns if col.startswith('has_')])
        for offset in [-2, -1, 1, 2]:
            neighbor = num + offset
            if 1 <= neighbor <= max_number:
                num_data[f'has_neighbor_{neighbor}'] = num_data[f'has_{neighbor}']
    
    # Ajouter des caractéristiques de fréquence mobile
    window_sizes = [5, 10, 20]
    for window in window_sizes:
        num_data[f'freq_{num}_{window}'] = num_data[f'has_{num}'].rolling(window=window, min_periods=1).mean()
    
    # Supprimer les lignes avec des valeurs manquantes
    num_data = num_data.dropna()
    
    if len(num_data) < 100:
        print(f"Pas assez de données pour le numéro {num} après nettoyage. Minimum 100 tirages requis.")
        return None, None
    
    # Définir les caractéristiques et la cible
    feature_cols = []
    
    # Ajouter les caractéristiques de lag
    if use_lag:
        feature_cols += [f'has_{num}_lag_{i}' for i in range(1, 6)]
    
    # Ajouter les caractéristiques de voisins
    if use_neighbors:
        for offset in [-2, -1, 1, 2]:
            neighbor = num + offset
            if 1 <= neighbor <= max_number and f'has_neighbor_{neighbor}' in num_data.columns:
                feature_cols.append(f'has_neighbor_{neighbor}')
    
    # Ajouter les caractéristiques de fréquence
    feature_cols += [f'freq_{num}_{window}' for window in window_sizes]
    
    # Ajouter les caractéristiques temporelles
    temporal_features = ['day_of_week', 'hour', 'is_weekend', 'is_afternoon', 'day', 'month', 'year']
    feature_cols += temporal_features
    
    # Vérifier que toutes les colonnes existent
    feature_cols = [col for col in feature_cols if col in num_data.columns]
    
    X = num_data[feature_cols]
    y = num_data[f'has_{num}']
    
    return X, y, feature_cols

def calculate_scale_pos_weight(y):
    """
    Calcule la valeur de scale_pos_weight basée sur la distribution des classes
    
    Args:
        y: Étiquettes
        
    Returns:
        float: Valeur de scale_pos_weight
    """
    # Compter les exemples positifs et négatifs
    neg_count = np.sum(y == 0)
    pos_count = np.sum(y == 1)
    
    # Éviter la division par zéro
    if pos_count > 0:
        # Calculer le ratio
        ratio = neg_count / pos_count
        return ratio
    else:
        # Valeur par défaut si aucun exemple positif
        return 1.0

def analyze_all_numbers():
    """
    Analyse tous les numéros Keno et calcule leur scale_pos_weight
    """
    # Créer le gestionnaire de données
    data_manager = KenoDataManager()
    
    # Charger les données
    print("Chargement des données Keno...")
    data_loaded = data_manager.load_data()
    
    if not data_loaded or not data_manager.draws:
        print("Erreur: Impossible de charger les données Keno")
        return
    
    print(f"Données chargées: {len(data_manager.draws)} tirages")
    
    # Préparer les données brutes
    raw_df = prepare_raw_data(data_manager)
    if raw_df is None:
        print("Erreur: Impossible de préparer les données brutes")
        return
    
    # Analyser chaque numéro
    results = []
    for num in range(1, data_manager.max_number + 1):
        print(f"Analyse du numéro {num}...")
        
        # Préparer les caractéristiques pour ce numéro
        X, y, feature_cols = prepare_features_for_number(raw_df, num)
        
        if X is None or y is None:
            print(f"Pas assez de données pour le numéro {num}")
            continue
        
        # Calculer scale_pos_weight
        spw = calculate_scale_pos_weight(y)
        
        # Calculer les statistiques
        pos_count = np.sum(y == 1)
        neg_count = np.sum(y == 0)
        pos_ratio = pos_count / len(y)
        
        # Stocker les résultats
        results.append({
            'number': num,
            'pos_count': pos_count,
            'neg_count': neg_count,
            'pos_ratio': pos_ratio,
            'scale_pos_weight': spw,
            'total_samples': len(y)
        })
        
        print(f"  Numéro {num}: {pos_count} positifs, {neg_count} négatifs, ratio={pos_ratio:.4f}, scale_pos_weight={spw:.4f}")
    
    # Créer un DataFrame avec les résultats
    results_df = pd.DataFrame(results)
    
    # Afficher les statistiques
    print("\nStatistiques de scale_pos_weight:")
    print(f"Valeur moyenne: {results_df['scale_pos_weight'].mean():.4f}")
    print(f"Écart-type: {results_df['scale_pos_weight'].std():.4f}")
    print(f"Valeur minimum: {results_df['scale_pos_weight'].min():.4f} (numéro {results_df.loc[results_df['scale_pos_weight'].idxmin()]['number']:.0f})")
    print(f"Valeur maximum: {results_df['scale_pos_weight'].max():.4f} (numéro {results_df.loc[results_df['scale_pos_weight'].idxmax()]['number']:.0f})")
    
    # Vérifier si toutes les valeurs sont identiques
    if results_df['scale_pos_weight'].std() < 0.01:
        print("\nATTENTION: Tous les numéros ont pratiquement la même valeur de scale_pos_weight!")
        print("Cela suggère un problème dans les données ou dans leur prétraitement.")
    else:
        print("\nLes valeurs de scale_pos_weight sont différentes pour chaque numéro, comme attendu.")
    
    # Sauvegarder les résultats dans un fichier CSV
    output_file = 'scale_pos_weight_analysis.csv'
    results_df.to_csv(output_file, index=False)
    print(f"\nRésultats sauvegardés dans {output_file}")
    
    # Créer un graphique des valeurs de scale_pos_weight
    plt.figure(figsize=(10, 6))
    plt.bar(results_df['number'], results_df['scale_pos_weight'])
    plt.axhline(y=results_df['scale_pos_weight'].mean(), color='r', linestyle='-', label=f'Moyenne ({results_df["scale_pos_weight"].mean():.4f})')
    plt.xlabel('Numéro Keno')
    plt.ylabel('scale_pos_weight')
    plt.title('Valeurs de scale_pos_weight pour chaque numéro Keno')
    plt.legend()
    plt.savefig('scale_pos_weight_distribution.png')
    print("Graphique sauvegardé dans scale_pos_weight_distribution.png")
    
    return results_df

def compare_models_with_different_spw(num, results_df):
    """
    Compare les performances des modèles avec différentes valeurs de scale_pos_weight
    
    Args:
        num: Numéro Keno à tester
        results_df: DataFrame avec les résultats de l'analyse
    """
    if not XGBOOST_AVAILABLE:
        print("XGBoost n'est pas disponible. Impossible de comparer les modèles.")
        return
    
    # Vérifier si le numéro existe dans les résultats
    if num not in results_df['number'].values:
        print(f"Le numéro {num} n'existe pas dans les résultats")
        return
    
    # Récupérer la valeur correcte de scale_pos_weight
    correct_spw = results_df.loc[results_df['number'] == num, 'scale_pos_weight'].values[0]
    
    # Créer le gestionnaire de données
    data_manager = KenoDataManager()
    
    # Charger les données
    data_loaded = data_manager.load_data()
    
    if not data_loaded or not data_manager.draws:
        print("Erreur: Impossible de charger les données Keno")
        return
    
    # Préparer les données brutes
    raw_df = prepare_raw_data(data_manager)
    if raw_df is None:
        print("Erreur: Impossible de préparer les données brutes")
        return
    
    # Préparer les caractéristiques pour ce numéro
    X, y, feature_cols = prepare_features_for_number(raw_df, num)
    
    if X is None or y is None:
        print(f"Pas assez de données pour le numéro {num}")
        return
    
    # Diviser les données en ensembles d'entraînement et de test
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    
    # Valeurs de scale_pos_weight à tester
    spw_values = [
        1.0,  # Pas de pondération
        2.57,  # Valeur par défaut observée
        correct_spw,  # Valeur correcte calculée
        correct_spw * 0.75,  # 75% de la valeur correcte
        correct_spw * 1.25   # 125% de la valeur correcte
    ]
    
    results = []
    
    # Tester chaque valeur
    for spw in spw_values:
        # Entraîner un modèle avec cette valeur
        model = XGBClassifier(
            n_estimators=100,
            max_depth=5,
            learning_rate=0.1,
            scale_pos_weight=spw,
            random_state=42
        )
        
        model.fit(X_train, y_train)
        y_pred = model.predict(X_test)
        
        # Calculer les métriques
        accuracy = accuracy_score(y_test, y_pred)
        precision, recall, f1, _ = precision_recall_fscore_support(
            y_test, y_pred, average='binary', zero_division=0
        )
        
        # Stocker les résultats
        results.append({
            'scale_pos_weight': spw,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1
        })
        
        print(f"scale_pos_weight={spw:.4f}: Accuracy={accuracy:.4f}, Precision={precision:.4f}, Recall={recall:.4f}, F1={f1:.4f}")
    
    # Créer un DataFrame avec les résultats
    results_df = pd.DataFrame(results)
    
    # Créer un graphique des résultats
    plt.figure(figsize=(12, 8))
    
    # Graphique de l'accuracy
    plt.subplot(2, 2, 1)
    plt.plot(results_df['scale_pos_weight'], results_df['accuracy'], marker='o')
    plt.xlabel('scale_pos_weight')
    plt.ylabel('Accuracy')
    plt.title(f'Accuracy vs scale_pos_weight (Numéro {num})')
    
    # Graphique de la précision
    plt.subplot(2, 2, 2)
    plt.plot(results_df['scale_pos_weight'], results_df['precision'], marker='o')
    plt.xlabel('scale_pos_weight')
    plt.ylabel('Precision')
    plt.title(f'Precision vs scale_pos_weight (Numéro {num})')
    
    # Graphique du rappel
    plt.subplot(2, 2, 3)
    plt.plot(results_df['scale_pos_weight'], results_df['recall'], marker='o')
    plt.xlabel('scale_pos_weight')
    plt.ylabel('Recall')
    plt.title(f'Recall vs scale_pos_weight (Numéro {num})')
    
    # Graphique du F1-score
    plt.subplot(2, 2, 4)
    plt.plot(results_df['scale_pos_weight'], results_df['f1'], marker='o')
    plt.xlabel('scale_pos_weight')
    plt.ylabel('F1-score')
    plt.title(f'F1-score vs scale_pos_weight (Numéro {num})')
    
    plt.tight_layout()
    plt.savefig(f'spw_comparison_num_{num}.png')
    print(f"Graphique sauvegardé dans spw_comparison_num_{num}.png")

def create_optimized_xgboost_params_file(results_df):
    """
    Crée un fichier JSON avec les paramètres optimisés pour XGBoost
    
    Args:
        results_df: DataFrame avec les résultats de l'analyse
    """
    import json
    
    # Créer un dictionnaire avec les paramètres optimisés
    params = {}
    
    for _, row in results_df.iterrows():
        num = int(row['number'])
        spw = float(row['scale_pos_weight'])
        
        # Paramètres de base pour XGBoost
        params[str(num)] = {
            'n_estimators': 200,
            'max_depth': 6,
            'learning_rate': 0.05,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'min_child_weight': 3,
            'gamma': 0.1,
            'scale_pos_weight': spw,
            'objective': 'binary:logistic',
            'eval_metric': 'auc',
            'verbosity': 0,
            'tree_method': 'hist',
            'predictor': 'cpu_predictor'
        }
    
    # Sauvegarder dans un fichier JSON
    output_file = 'xgboost_optimal_params.json'
    
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Sauvegarder dans le répertoire data
    output_path = os.path.join('data', output_file)
    
    with open(output_path, 'w') as f:
        json.dump(params, f, indent=2)
    
    print(f"Paramètres optimisés sauvegardés dans {output_path}")

def main():
    """Fonction principale"""
    # Analyser tous les numéros
    results_df = analyze_all_numbers()
    
    if results_df is not None and len(results_df) > 0:
        # Créer le fichier de paramètres optimisés
        create_optimized_xgboost_params_file(results_df)
        
        # Comparer les modèles pour quelques numéros
        test_nums = [
            int(results_df['number'].iloc[0]),  # Premier numéro
            int(results_df['number'].iloc[-1]),  # Dernier numéro
            int(results_df.loc[results_df['scale_pos_weight'].idxmin()]['number']),  # Numéro avec le plus petit scale_pos_weight
            int(results_df.loc[results_df['scale_pos_weight'].idxmax()]['number'])   # Numéro avec le plus grand scale_pos_weight
        ]
        
        for num in test_nums:
            print(f"\nComparaison des modèles pour le numéro {num}:")
            compare_models_with_different_spw(num, results_df)

if __name__ == "__main__":
    main()
