"""
Script pour protéger le fichier de poids contre l'écrasement accidentel.
Ce script crée une copie de sauvegarde du fichier de poids et vérifie périodiquement s'il a été modifié.
"""

import os
import sys
import json
import shutil
import time
from datetime import datetime

def backup_weights_file():
    """
    Crée une copie de sauvegarde du fichier de poids
    
    Returns:
        tuple: (chemin du fichier original, chemin de la sauvegarde)
    """
    # Chemin du fichier de poids
    weights_file = os.path.join('data', 'real_scale_pos_weight.json')
    
    # Vérifier si le fichier existe
    if not os.path.exists(weights_file):
        print(f"Le fichier {weights_file} n'existe pas")
        return None, None
    
    # Créer une copie de sauvegarde
    backup_file = weights_file + '.backup'
    shutil.copy2(weights_file, backup_file)
    
    print(f"Sauvegarde créée: {backup_file}")
    
    return weights_file, backup_file

def compare_files(file1, file2):
    """
    Compare deux fichiers pour vérifier s'ils sont identiques
    
    Args:
        file1: Chemin du premier fichier
        file2: Chemin du deuxième fichier
        
    Returns:
        bool: True si les fichiers sont identiques, False sinon
    """
    try:
        with open(file1, 'r') as f1, open(file2, 'r') as f2:
            content1 = f1.read()
            content2 = f2.read()
            
            return content1 == content2
    except Exception as e:
        print(f"Erreur lors de la comparaison des fichiers: {e}")
        return False

def restore_weights_file(original_file, backup_file):
    """
    Restaure le fichier de poids à partir de la sauvegarde
    
    Args:
        original_file: Chemin du fichier original
        backup_file: Chemin de la sauvegarde
        
    Returns:
        bool: True si la restauration a réussi, False sinon
    """
    try:
        shutil.copy2(backup_file, original_file)
        print(f"Fichier {original_file} restauré à partir de {backup_file}")
        return True
    except Exception as e:
        print(f"Erreur lors de la restauration du fichier: {e}")
        return False

def monitor_weights_file(interval=5):
    """
    Surveille le fichier de poids pour détecter les modifications
    
    Args:
        interval: Intervalle de vérification en secondes
    """
    # Créer une sauvegarde
    original_file, backup_file = backup_weights_file()
    
    if not original_file or not backup_file:
        print("Impossible de créer une sauvegarde")
        return
    
    print(f"Surveillance du fichier {original_file} (intervalle: {interval} secondes)")
    print("Appuyez sur Ctrl+C pour arrêter la surveillance")
    
    try:
        while True:
            # Vérifier si le fichier a été modifié
            if not compare_files(original_file, backup_file):
                print(f"Le fichier {original_file} a été modifié!")
                
                # Demander à l'utilisateur s'il souhaite restaurer le fichier
                print("Souhaitez-vous restaurer le fichier à partir de la sauvegarde? (o/n)")
                choice = input().lower()
                
                if choice == 'o':
                    restore_weights_file(original_file, backup_file)
                else:
                    # Mettre à jour la sauvegarde
                    shutil.copy2(original_file, backup_file)
                    print(f"Sauvegarde mise à jour: {backup_file}")
            
            # Attendre l'intervalle spécifié
            time.sleep(interval)
    except KeyboardInterrupt:
        print("\nSurveillance arrêtée")

def create_read_only_copy():
    """
    Crée une copie en lecture seule du fichier de poids
    
    Returns:
        str: Chemin de la copie en lecture seule
    """
    # Chemin du fichier de poids
    weights_file = os.path.join('data', 'real_scale_pos_weight.json')
    
    # Vérifier si le fichier existe
    if not os.path.exists(weights_file):
        print(f"Le fichier {weights_file} n'existe pas")
        return None
    
    # Créer une copie en lecture seule
    readonly_file = os.path.join('data', 'real_scale_pos_weight.readonly.json')
    shutil.copy2(weights_file, readonly_file)
    
    # Rendre le fichier en lecture seule
    try:
        # Pour Windows
        if os.name == 'nt':
            os.system(f'attrib +r "{readonly_file}"')
        # Pour Unix/Linux
        else:
            os.chmod(readonly_file, 0o444)
        
        print(f"Copie en lecture seule créée: {readonly_file}")
    except Exception as e:
        print(f"Erreur lors de la création de la copie en lecture seule: {e}")
    
    return readonly_file

def modify_code_to_use_readonly_file():
    """
    Modifie le code pour utiliser la copie en lecture seule
    
    Returns:
        bool: True si la modification a réussi, False sinon
    """
    # Fichiers à modifier
    files_to_modify = [
        'keno_advanced_analyzer.py',
        'keno_xgboost_optimizer.py',
        'keno_xgboost_optimizer_simple.py'
    ]
    
    success = True
    
    for file_path in files_to_modify:
        if not os.path.exists(file_path):
            print(f"Le fichier {file_path} n'existe pas")
            continue
        
        try:
            # Lire le contenu du fichier
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Remplacer le chemin du fichier
            old_path = 'real_scale_pos_weight.json'
            new_path = 'real_scale_pos_weight.readonly.json'
            
            new_content = content.replace(old_path, new_path)
            
            # Écrire le contenu modifié
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"Fichier {file_path} modifié pour utiliser la copie en lecture seule")
        except Exception as e:
            print(f"Erreur lors de la modification du fichier {file_path}: {e}")
            success = False
    
    return success

def main():
    """Fonction principale"""
    print("Protection du fichier de poids contre l'écrasement accidentel")
    
    # Créer une copie en lecture seule
    readonly_file = create_read_only_copy()
    
    if not readonly_file:
        print("Impossible de créer une copie en lecture seule")
        return
    
    # Modifier le code pour utiliser la copie en lecture seule
    success = modify_code_to_use_readonly_file()
    
    if success:
        print("\nProtection terminée!")
        print("Le fichier de poids est maintenant protégé contre l'écrasement accidentel.")
        print("Une copie en lecture seule a été créée et le code a été modifié pour l'utiliser.")
    else:
        print("\nÉchec de la protection")
        print("Veuillez vérifier manuellement les fichiers et appliquer les modifications nécessaires.")
    
    # Proposer de surveiller le fichier
    print("\nSouhaitez-vous surveiller le fichier de poids pour détecter les modifications? (o/n)")
    choice = input().lower()
    
    if choice == 'o':
        monitor_weights_file()

if __name__ == "__main__":
    main()
