#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module pour l'analyse avancée de séquences temporelles dans les tirages Keno
Utilise des techniques avancées pour détecter des motifs temporels et faire des prédictions
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import joblib
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
import warnings

# Désactiver les avertissements pour les modèles de séries temporelles
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

class KenoTimeSeriesAnalyzer:
    """
    Classe pour l'analyse avancée de séquences temporelles dans les tirages Keno
    """
    
    def __init__(self, data_manager=None):
        """
        Initialise l'analyseur de séquences temporelles
        
        Args:
            data_manager: Gestionnaire de données Keno
        """
        self.data_manager = data_manager
        self.models = {}
        self.df = None
        self.time_series = {}
        self.forecasts = {}
        self.max_number = data_manager.max_number if data_manager else 70
        self.numbers_per_draw = data_manager.numbers_per_draw if data_manager else 20
    
    def prepare_data(self, df=None, resample_freq='D'):
        """
        Prépare les données pour l'analyse de séquences temporelles
        
        Args:
            df: DataFrame pandas contenant les données (si None, utilise les données du data_manager)
            resample_freq: Fréquence de rééchantillonnage ('D' pour jour, 'W' pour semaine, etc.)
            
        Returns:
            dict: Dictionnaire contenant les séries temporelles pour chaque numéro
        """
        if df is not None:
            self.df = df
        elif self.data_manager is not None and self.data_manager.draws:
            # Convertir les tirages en DataFrame pandas
            data = []
            for draw in self.data_manager.draws:
                row = {
                    'date': draw.draw_date,
                    'id': draw.draw_id
                }
                
                # Ajouter des indicateurs pour chaque numéro possible
                for i in range(1, self.max_number + 1):
                    row[f'has_{i}'] = 1 if i in draw.draw_numbers else 0
                
                data.append(row)
            
            # Créer le DataFrame
            self.df = pd.DataFrame(data)
            self.df = self.df.sort_values('date')
        else:
            print("Aucune donnée disponible pour l'analyse de séquences temporelles")
            return None
        
        # Définir la date comme index
        if 'date' in self.df.columns:
            self.df = self.df.set_index('date')
        
        # Créer des séries temporelles pour chaque numéro
        time_series = {}
        
        for num in range(1, self.max_number + 1):
            # Extraire la série temporelle pour le numéro
            if f'has_{num}' in self.df.columns:
                # Rééchantillonner la série temporelle à la fréquence spécifiée
                # Utiliser la somme pour compter le nombre d'apparitions dans chaque période
                ts = self.df[f'has_{num}'].resample(resample_freq).sum()
                
                # Calculer la fréquence d'apparition (nombre d'apparitions / nombre de tirages)
                ts_count = self.df[f'has_{num}'].resample(resample_freq).count()
                ts_freq = ts / ts_count
                
                # Stocker les séries temporelles
                time_series[num] = {
                    'count': ts,
                    'frequency': ts_freq
                }
        
        self.time_series = time_series
        
        return time_series
    
    def analyze_seasonality(self, num, series_type='frequency', period=7):
        """
        Analyse la saisonnalité d'une série temporelle
        
        Args:
            num: Numéro à analyser
            series_type: Type de série temporelle ('count' ou 'frequency')
            period: Période de la saisonnalité (7 pour hebdomadaire, 30 pour mensuelle, etc.)
            
        Returns:
            dict: Dictionnaire contenant les résultats de l'analyse de saisonnalité
        """
        if self.time_series is None or num not in self.time_series:
            print(f"Aucune série temporelle disponible pour le numéro {num}")
            return None
        
        # Récupérer la série temporelle
        ts = self.time_series[num][series_type]
        
        # Vérifier que la série temporelle est suffisamment longue
        if len(ts) < period * 2:
            print(f"Série temporelle trop courte pour l'analyse de saisonnalité (période {period})")
            return None
        
        try:
            # Décomposer la série temporelle en tendance, saisonnalité et résidus
            decomposition = seasonal_decompose(ts, model='additive', period=period)
            
            # Extraire les composantes
            trend = decomposition.trend
            seasonal = decomposition.seasonal
            residual = decomposition.resid
            
            # Calculer la force de la saisonnalité
            # Variance de la composante saisonnière / Variance de la série désaisonnalisée
            var_seasonal = np.nanvar(seasonal)
            var_deseasonalized = np.nanvar(trend + residual)
            
            if var_deseasonalized > 0:
                seasonality_strength = var_seasonal / var_deseasonalized
            else:
                seasonality_strength = 0
            
            # Calculer la force de la tendance
            # Variance de la tendance / Variance de la série sans tendance
            var_trend = np.nanvar(trend)
            var_detrended = np.nanvar(seasonal + residual)
            
            if var_detrended > 0:
                trend_strength = var_trend / var_detrended
            else:
                trend_strength = 0
            
            # Calculer l'autocorrélation
            acf_values = pd.Series(ts).autocorr(lag=period)
            
            # Stocker les résultats
            results = {
                'trend': trend,
                'seasonal': seasonal,
                'residual': residual,
                'seasonality_strength': seasonality_strength,
                'trend_strength': trend_strength,
                'autocorrelation': acf_values
            }
            
            return results
        
        except Exception as e:
            print(f"Erreur lors de l'analyse de saisonnalité pour le numéro {num}: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def train_arima_model(self, num, series_type='frequency', order=(1, 0, 1), seasonal_order=(0, 0, 0, 0)):
        """
        Entraîne un modèle ARIMA pour une série temporelle
        
        Args:
            num: Numéro à analyser
            series_type: Type de série temporelle ('count' ou 'frequency')
            order: Ordre du modèle ARIMA (p, d, q)
            seasonal_order: Ordre saisonnier du modèle SARIMA (P, D, Q, s)
            
        Returns:
            dict: Dictionnaire contenant le modèle et les métriques d'évaluation
        """
        if self.time_series is None or num not in self.time_series:
            print(f"Aucune série temporelle disponible pour le numéro {num}")
            return None
        
        # Récupérer la série temporelle
        ts = self.time_series[num][series_type]
        
        # Vérifier que la série temporelle est suffisamment longue
        if len(ts) < 30:
            print(f"Série temporelle trop courte pour l'entraînement d'un modèle ARIMA")
            return None
        
        try:
            # Diviser la série temporelle en ensembles d'entraînement et de test
            train_size = int(len(ts) * 0.8)
            train, test = ts[:train_size], ts[train_size:]
            
            # Entraîner le modèle ARIMA
            if sum(seasonal_order) > 0:
                # Utiliser SARIMA si un ordre saisonnier est spécifié
                model = SARIMAX(train, order=order, seasonal_order=seasonal_order)
            else:
                # Utiliser ARIMA sinon
                model = ARIMA(train, order=order)
            
            model_fit = model.fit(disp=False)
            
            # Faire des prédictions sur l'ensemble de test
            predictions = model_fit.forecast(steps=len(test))
            
            # Calculer les métriques d'erreur
            mse = mean_squared_error(test, predictions)
            rmse = np.sqrt(mse)
            mae = mean_absolute_error(test, predictions)
            
            # Stocker le modèle
            self.models[f'arima_{num}_{series_type}'] = {
                'model': model_fit,
                'order': order,
                'seasonal_order': seasonal_order,
                'mse': mse,
                'rmse': rmse,
                'mae': mae,
                'train': train,
                'test': test,
                'predictions': predictions
            }
            
            return self.models[f'arima_{num}_{series_type}']
        
        except Exception as e:
            print(f"Erreur lors de l'entraînement du modèle ARIMA pour le numéro {num}: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def forecast(self, num, series_type='frequency', steps=10):
        """
        Fait des prévisions pour une série temporelle
        
        Args:
            num: Numéro à analyser
            series_type: Type de série temporelle ('count' ou 'frequency')
            steps: Nombre de pas de temps à prévoir
            
        Returns:
            array: Prévisions
        """
        model_key = f'arima_{num}_{series_type}'
        
        if model_key not in self.models:
            print(f"Aucun modèle disponible pour le numéro {num}. Entraînement d'un modèle...")
            self.train_arima_model(num, series_type)
        
        if model_key not in self.models:
            print(f"Impossible d'entraîner un modèle pour le numéro {num}")
            return None
        
        try:
            # Récupérer le modèle
            model_fit = self.models[model_key]['model']
            
            # Faire des prévisions
            forecast = model_fit.forecast(steps=steps)
            
            # Stocker les prévisions
            self.forecasts[f'{num}_{series_type}'] = forecast
            
            return forecast
        
        except Exception as e:
            print(f"Erreur lors de la prévision pour le numéro {num}: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def analyze_all_numbers(self, series_type='frequency', period=7, steps=10):
        """
        Analyse tous les numéros et fait des prévisions
        
        Args:
            series_type: Type de série temporelle ('count' ou 'frequency')
            period: Période de la saisonnalité
            steps: Nombre de pas de temps à prévoir
            
        Returns:
            dict: Dictionnaire contenant les résultats pour chaque numéro
        """
        if self.time_series is None:
            print("Aucune série temporelle disponible")
            return None
        
        results = {}
        
        for num in range(1, self.max_number + 1):
            if num in self.time_series:
                print(f"Analyse du numéro {num}...")
                
                # Analyser la saisonnalité
                seasonality = self.analyze_seasonality(num, series_type, period)
                
                # Déterminer l'ordre du modèle ARIMA en fonction de l'analyse de saisonnalité
                if seasonality is not None:
                    # Utiliser un modèle SARIMA si la saisonnalité est forte
                    if seasonality['seasonality_strength'] > 0.3:
                        order = (1, 0, 1)
                        seasonal_order = (1, 0, 1, period)
                    else:
                        order = (1, 0, 1)
                        seasonal_order = (0, 0, 0, 0)
                else:
                    # Utiliser un modèle ARIMA simple par défaut
                    order = (1, 0, 1)
                    seasonal_order = (0, 0, 0, 0)
                
                # Entraîner le modèle
                model = self.train_arima_model(num, series_type, order, seasonal_order)
                
                # Faire des prévisions
                forecast = self.forecast(num, series_type, steps)
                
                # Stocker les résultats
                results[num] = {
                    'seasonality': seasonality,
                    'model': model,
                    'forecast': forecast
                }
        
        return results
    
    def visualize_forecast(self, num, series_type='frequency', steps=10):
        """
        Visualise les prévisions pour un numéro
        
        Args:
            num: Numéro à analyser
            series_type: Type de série temporelle ('count' ou 'frequency')
            steps: Nombre de pas de temps à prévoir
            
        Returns:
            str: Chemin du fichier image généré
        """
        model_key = f'arima_{num}_{series_type}'
        
        if model_key not in self.models:
            print(f"Aucun modèle disponible pour le numéro {num}. Entraînement d'un modèle...")
            self.train_arima_model(num, series_type)
        
        if model_key not in self.models:
            print(f"Impossible d'entraîner un modèle pour le numéro {num}")
            return None
        
        try:
            # Récupérer le modèle et les données
            model_data = self.models[model_key]
            train = model_data['train']
            test = model_data['test']
            predictions = model_data['predictions']
            
            # Faire des prévisions futures
            forecast = self.forecast(num, series_type, steps)
            
            # Créer la figure
            plt.figure(figsize=(12, 8))
            
            # Tracer les données d'entraînement
            plt.plot(train.index, train.values, label='Entraînement')
            
            # Tracer les données de test et les prédictions
            plt.plot(test.index, test.values, label='Test')
            plt.plot(test.index, predictions, label='Prédictions', linestyle='--')
            
            # Tracer les prévisions futures
            if forecast is not None:
                # Créer un index pour les prévisions futures
                last_date = test.index[-1]
                forecast_index = pd.date_range(start=last_date, periods=steps + 1)[1:]
                plt.plot(forecast_index, forecast, label='Prévisions', linestyle='--', color='red')
            
            # Ajouter les informations du modèle
            order = model_data['order']
            seasonal_order = model_data['seasonal_order']
            rmse = model_data['rmse']
            
            plt.title(f'Prévisions pour le numéro {num} - RMSE: {rmse:.4f}\n'
                     f'ARIMA{order} SARIMA{seasonal_order}')
            
            plt.xlabel('Date')
            plt.ylabel(f'{series_type.capitalize()}')
            plt.legend()
            plt.grid(True)
            
            # Créer le dossier de visualisations s'il n'existe pas
            os.makedirs('visualisations', exist_ok=True)
            
            # Sauvegarder la figure
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'visualisations/forecast_{num}_{series_type}_{timestamp}.png'
            plt.savefig(filename)
            print(f"Visualisation sauvegardée dans {filename}")
            
            # Fermer la figure pour libérer la mémoire
            plt.close()
            
            return filename
        
        except Exception as e:
            print(f"Erreur lors de la visualisation des prévisions pour le numéro {num}: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def visualize_seasonality(self, num, series_type='frequency', period=7):
        """
        Visualise la saisonnalité d'une série temporelle
        
        Args:
            num: Numéro à analyser
            series_type: Type de série temporelle ('count' ou 'frequency')
            period: Période de la saisonnalité
            
        Returns:
            str: Chemin du fichier image généré
        """
        if self.time_series is None or num not in self.time_series:
            print(f"Aucune série temporelle disponible pour le numéro {num}")
            return None
        
        # Récupérer la série temporelle
        ts = self.time_series[num][series_type]
        
        # Vérifier que la série temporelle est suffisamment longue
        if len(ts) < period * 2:
            print(f"Série temporelle trop courte pour l'analyse de saisonnalité (période {period})")
            return None
        
        try:
            # Analyser la saisonnalité
            seasonality = self.analyze_seasonality(num, series_type, period)
            
            if seasonality is None:
                print(f"Impossible d'analyser la saisonnalité pour le numéro {num}")
                return None
            
            # Créer la figure
            plt.figure(figsize=(12, 10))
            
            # Tracer la série temporelle originale
            plt.subplot(4, 1, 1)
            plt.plot(ts.index, ts.values)
            plt.title(f'Série temporelle originale - Numéro {num}')
            plt.grid(True)
            
            # Tracer la tendance
            plt.subplot(4, 1, 2)
            plt.plot(seasonality['trend'].index, seasonality['trend'].values)
            plt.title(f'Tendance - Force: {seasonality["trend_strength"]:.4f}')
            plt.grid(True)
            
            # Tracer la saisonnalité
            plt.subplot(4, 1, 3)
            plt.plot(seasonality['seasonal'].index, seasonality['seasonal'].values)
            plt.title(f'Saisonnalité - Force: {seasonality["seasonality_strength"]:.4f}')
            plt.grid(True)
            
            # Tracer les résidus
            plt.subplot(4, 1, 4)
            plt.plot(seasonality['residual'].index, seasonality['residual'].values)
            plt.title('Résidus')
            plt.grid(True)
            
            plt.tight_layout()
            
            # Créer le dossier de visualisations s'il n'existe pas
            os.makedirs('visualisations', exist_ok=True)
            
            # Sauvegarder la figure
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'visualisations/seasonality_{num}_{series_type}_{timestamp}.png'
            plt.savefig(filename)
            print(f"Visualisation sauvegardée dans {filename}")
            
            # Fermer la figure pour libérer la mémoire
            plt.close()
            
            return filename
        
        except Exception as e:
            print(f"Erreur lors de la visualisation de la saisonnalité pour le numéro {num}: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def save_models(self, filepath='keno_time_series_models.pkl'):
        """
        Sauvegarde les modèles de séries temporelles
        
        Args:
            filepath: Chemin du fichier où sauvegarder les modèles
            
        Returns:
            bool: True si la sauvegarde a réussi, False sinon
        """
        if self.models is None or len(self.models) == 0:
            print("Aucun modèle à sauvegarder")
            return False
        
        try:
            # Créer le répertoire parent si nécessaire
            os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)
            
            # Sauvegarder les modèles
            joblib.dump(self.models, filepath)
            
            print(f"Modèles de séries temporelles sauvegardés dans {filepath}")
            return True
        except Exception as e:
            print(f"Erreur lors de la sauvegarde des modèles: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def load_models(self, filepath='keno_time_series_models.pkl'):
        """
        Charge les modèles de séries temporelles
        
        Args:
            filepath: Chemin du fichier contenant les modèles
            
        Returns:
            bool: True si le chargement a réussi, False sinon
        """
        try:
            # Vérifier que le fichier existe
            if not os.path.exists(filepath):
                print(f"Fichier {filepath} introuvable")
                return False
            
            # Charger les modèles
            self.models = joblib.load(filepath)
            
            print(f"Modèles de séries temporelles chargés depuis {filepath}")
            return True
        except Exception as e:
            print(f"Erreur lors du chargement des modèles: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_hot_numbers(self, series_type='frequency', window=10):
        """
        Identifie les numéros "chauds" (en hausse) en fonction des prévisions
        
        Args:
            series_type: Type de série temporelle ('count' ou 'frequency')
            window: Fenêtre de temps pour l'analyse de tendance
            
        Returns:
            list: Liste des numéros "chauds" avec leur score
        """
        if self.time_series is None:
            print("Aucune série temporelle disponible")
            return None
        
        hot_numbers = []
        
        for num in range(1, self.max_number + 1):
            if num in self.time_series:
                # Récupérer la série temporelle
                ts = self.time_series[num][series_type]
                
                # Vérifier que la série temporelle est suffisamment longue
                if len(ts) < window:
                    continue
                
                # Calculer la tendance récente
                recent_trend = ts[-window:].mean() - ts[-2*window:-window].mean()
                
                # Faire des prévisions
                forecast = self.forecast(num, series_type, steps=5)
                
                if forecast is not None:
                    # Calculer la tendance prévue
                    forecast_trend = forecast.mean() - ts[-window:].mean()
                    
                    # Calculer un score combiné
                    score = recent_trend + forecast_trend
                    
                    # Ajouter le numéro à la liste s'il est "chaud"
                    if score > 0:
                        hot_numbers.append((num, score))
        
        # Trier les numéros par score décroissant
        hot_numbers.sort(key=lambda x: x[1], reverse=True)
        
        return hot_numbers
    
    def get_cold_numbers(self, series_type='frequency', window=10):
        """
        Identifie les numéros "froids" (en baisse) en fonction des prévisions
        
        Args:
            series_type: Type de série temporelle ('count' ou 'frequency')
            window: Fenêtre de temps pour l'analyse de tendance
            
        Returns:
            list: Liste des numéros "froids" avec leur score
        """
        if self.time_series is None:
            print("Aucune série temporelle disponible")
            return None
        
        cold_numbers = []
        
        for num in range(1, self.max_number + 1):
            if num in self.time_series:
                # Récupérer la série temporelle
                ts = self.time_series[num][series_type]
                
                # Vérifier que la série temporelle est suffisamment longue
                if len(ts) < window:
                    continue
                
                # Calculer la tendance récente
                recent_trend = ts[-window:].mean() - ts[-2*window:-window].mean()
                
                # Faire des prévisions
                forecast = self.forecast(num, series_type, steps=5)
                
                if forecast is not None:
                    # Calculer la tendance prévue
                    forecast_trend = forecast.mean() - ts[-window:].mean()
                    
                    # Calculer un score combiné
                    score = recent_trend + forecast_trend
                    
                    # Ajouter le numéro à la liste s'il est "froid"
                    if score < 0:
                        cold_numbers.append((num, score))
        
        # Trier les numéros par score croissant
        cold_numbers.sort(key=lambda x: x[1])
        
        return cold_numbers
    
    def predict_next_draw(self, series_type='frequency', top_n=20):
        """
        Prédit les numéros les plus probables pour le prochain tirage
        
        Args:
            series_type: Type de série temporelle ('count' ou 'frequency')
            top_n: Nombre de numéros à prédire
            
        Returns:
            list: Liste des numéros prédits avec leur probabilité
        """
        if self.time_series is None:
            print("Aucune série temporelle disponible")
            return None
        
        predictions = []
        
        for num in range(1, self.max_number + 1):
            if num in self.time_series:
                # Faire des prévisions
                forecast = self.forecast(num, series_type, steps=1)
                
                if forecast is not None:
                    # Récupérer la prévision pour le prochain tirage
                    next_value = forecast[0]
                    
                    # Ajouter le numéro à la liste
                    predictions.append((num, next_value))
        
        # Trier les numéros par probabilité décroissante
        predictions.sort(key=lambda x: x[1], reverse=True)
        
        # Retourner les top_n numéros
        return predictions[:top_n]
