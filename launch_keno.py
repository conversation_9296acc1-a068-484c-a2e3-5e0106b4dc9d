#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de lancement robuste pour l'application Keno
Ce script garantit l'utilisation du bon interpréteur Python
"""

import os
import sys
import subprocess

def find_python_executable():
    """Trouve le bon exécutable Python"""
    
    # Liste des exécutables Python possibles
    python_candidates = [
        sys.executable,  # L'interpréteur actuel
        'python',
        'python3',
        'py',
        'py -3'
    ]
    
    print("=== Recherche de l'interpréteur Python ===")
    
    for candidate in python_candidates:
        try:
            if candidate.startswith('py '):
                # Cas spécial pour py launcher
                result = subprocess.run(candidate.split(), 
                                      capture_output=True, 
                                      text=True, 
                                      input="import sys; print(sys.executable)")
                if result.returncode == 0:
                    executable = result.stdout.strip()
                    print(f"✓ {candidate} -> {executable}")
                    return executable
            else:
                # Test direct
                result = subprocess.run([candidate, '--version'], 
                                      capture_output=True, 
                                      text=True)
                if result.returncode == 0:
                    print(f"✓ {candidate} disponible: {result.stdout.strip()}")
                    return candidate
        except Exception as e:
            print(f"✗ {candidate} non disponible: {e}")
    
    print("✗ Aucun interpréteur Python trouvé")
    return None

def test_python_with_tkinter(python_exe):
    """Test si l'interpréteur Python peut importer tkinter"""
    try:
        result = subprocess.run([python_exe, '-c', 'import tkinter; print("tkinter OK")'], 
                              capture_output=True, 
                              text=True)
        if result.returncode == 0:
            print(f"✓ {python_exe} peut importer tkinter")
            return True
        else:
            print(f"✗ {python_exe} ne peut pas importer tkinter: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Erreur lors du test de tkinter avec {python_exe}: {e}")
        return False

def launch_main_script(python_exe):
    """Lance le script main.py avec l'interpréteur spécifié"""
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    main_script = os.path.join(script_dir, 'main.py')
    
    if not os.path.exists(main_script):
        print(f"✗ Script main.py non trouvé: {main_script}")
        return 1
    
    print(f"=== Lancement de l'application ===")
    print(f"Interpréteur: {python_exe}")
    print(f"Script: {main_script}")
    print(f"Répertoire de travail: {script_dir}")
    
    try:
        # Changer le répertoire de travail
        os.chdir(script_dir)
        
        # Lancer le script
        result = subprocess.run([python_exe, main_script], 
                              cwd=script_dir)
        
        print(f"Application terminée avec le code: {result.returncode}")
        return result.returncode
        
    except Exception as e:
        print(f"✗ Erreur lors du lancement: {e}")
        return 1

def main():
    """Fonction principale"""
    print("=== Lanceur d'application Keno ===")
    
    # Trouver l'interpréteur Python
    python_exe = find_python_executable()
    if not python_exe:
        print("Impossible de trouver un interpréteur Python valide")
        return 1
    
    # Tester tkinter
    if not test_python_with_tkinter(python_exe):
        print("L'interpréteur Python trouvé ne supporte pas tkinter")
        return 1
    
    # Lancer l'application
    return launch_main_script(python_exe)

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nInterruption par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
