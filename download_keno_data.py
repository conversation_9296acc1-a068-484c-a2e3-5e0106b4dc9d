#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script pour télécharger automatiquement les données Keno depuis l'API FDJ
Ce script télécharge les derniers tirages Keno et les ajoute au fichier de données.
"""

import os
import sys
import json
import datetime
import requests
import time
import csv
from tqdm import tqdm

# Ajouter le répertoire courant au chemin de recherche Python
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, BASE_DIR)

# Importer les modules nécessaires
from keno_data import KenoDataManager

# URL de l'API FDJ pour les tirages Keno
KENO_API_URL = "https://www.fdj.fr/api/service-draws/v1/games/keno/draws"

def download_keno_data(days=30):
    """
    Télécharge les données Keno depuis l'API FDJ
    
    Args:
        days (int): Nombre de jours à télécharger
    
    Returns:
        list: Liste des tirages téléchargés
    """
    print(f"Téléchargement des données Keno pour les {days} derniers jours...")
    
    # Calculer la date de début
    end_date = datetime.datetime.now()
    start_date = end_date - datetime.timedelta(days=days)
    
    # Formater les dates pour l'API
    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")
    
    # Construire l'URL avec les paramètres
    url = f"{KENO_API_URL}?startDate={start_date_str}&endDate={end_date_str}&range=0-100"
    
    # Télécharger les données
    try:
        response = requests.get(url)
        response.raise_for_status()  # Lever une exception si la requête a échoué
        
        # Analyser la réponse JSON
        data = response.json()
        
        # Vérifier si la réponse contient des tirages
        if 'draws' not in data:
            print("Aucun tirage trouvé dans la réponse de l'API")
            return []
        
        # Extraire les tirages
        draws = data['draws']
        print(f"Téléchargement réussi: {len(draws)} tirages trouvés")
        
        # Convertir les tirages au format attendu par KenoDataManager
        converted_draws = []
        for draw in draws:
            # Extraire les informations du tirage
            draw_date = draw.get('drawDate', '')
            draw_number = draw.get('drawNum', 0)
            multiplier = draw.get('multiplicator', 1)
            
            # Extraire les numéros tirés
            numbers = []
            if 'winningNumbers' in draw and 'list' in draw['winningNumbers']:
                numbers = [int(num) for num in draw['winningNumbers']['list']]
            
            # Créer le tirage au format attendu
            converted_draw = {
                'date': draw_date,
                'draw_number': draw_number,
                'numbers': numbers,
                'multiplier': multiplier
            }
            
            converted_draws.append(converted_draw)
        
        return converted_draws
    
    except requests.exceptions.RequestException as e:
        print(f"Erreur lors du téléchargement des données: {e}")
        return []

def save_draws_to_csv(draws, output_file):
    """
    Sauvegarde les tirages dans un fichier CSV
    
    Args:
        draws (list): Liste des tirages
        output_file (str): Chemin du fichier de sortie
    """
    print(f"Sauvegarde de {len(draws)} tirages dans {output_file}...")
    
    # Créer le répertoire de données si nécessaire
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # Vérifier si le fichier existe déjà
    file_exists = os.path.exists(output_file)
    
    # Ouvrir le fichier en mode append
    with open(output_file, 'a', newline='') as f:
        # Créer le writer CSV
        writer = csv.writer(f, delimiter=';')
        
        # Écrire l'en-tête si le fichier n'existe pas
        if not file_exists:
            writer.writerow(['date', 'draw_number', 'numbers', 'multiplier'])
        
        # Écrire les tirages
        for draw in draws:
            # Convertir la liste de numéros en chaîne
            numbers_str = ','.join(map(str, draw['numbers']))
            
            # Écrire la ligne
            writer.writerow([
                draw['date'],
                draw['draw_number'],
                numbers_str,
                draw['multiplier']
            ])
    
    print(f"Sauvegarde terminée")

def update_keno_data():
    """
    Met à jour les données Keno en téléchargeant les derniers tirages
    et en les ajoutant au fichier de données
    """
    print("Mise à jour des données Keno...")
    
    # Télécharger les derniers tirages
    draws = download_keno_data(days=30)
    
    if not draws:
        print("Aucun tirage téléchargé, fin de la mise à jour")
        return
    
    # Charger les données existantes
    data_manager = KenoDataManager()
    data_file = os.path.join(BASE_DIR, "data", "datafull.keno")
    
    # Vérifier si le fichier existe
    if os.path.exists(data_file):
        # Charger les données existantes
        data_manager.load_csv(data_file, clear_existing=True)
        print(f"Données existantes chargées: {len(data_manager.draws)} tirages")
    else:
        print("Aucun fichier de données existant, création d'un nouveau fichier")
    
    # Ajouter les nouveaux tirages
    new_draws_count = 0
    for draw in draws:
        # Vérifier si le tirage existe déjà
        draw_exists = False
        for existing_draw in data_manager.draws:
            if existing_draw.get('draw_number') == draw['draw_number']:
                draw_exists = True
                break
        
        # Ajouter le tirage s'il n'existe pas déjà
        if not draw_exists:
            data_manager.add_draw(draw)
            new_draws_count += 1
    
    print(f"{new_draws_count} nouveaux tirages ajoutés")
    
    # Sauvegarder les données
    if new_draws_count > 0:
        # Créer le répertoire de données si nécessaire
        os.makedirs(os.path.dirname(data_file), exist_ok=True)
        
        # Sauvegarder les données
        data_manager.save_csv(data_file)
        print(f"Données sauvegardées dans {data_file}")
    
    print("Mise à jour terminée")

if __name__ == "__main__":
    # Mettre à jour les données Keno
    update_keno_data()
