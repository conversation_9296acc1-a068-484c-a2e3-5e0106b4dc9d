"""
Script pour corriger directement le problème de scale_pos_weight identique
Ce script modifie directement le fichier source de l'optimiseur XGBoost.
"""

import os
import sys
import re
import shutil
import numpy as np
import pandas as pd
from datetime import datetime

def backup_file(file_path):
    """
    Crée une sauvegarde du fichier
    
    Args:
        file_path: Chemin du fichier à sauvegarder
        
    Returns:
        str: Chemin de la sauvegarde
    """
    backup_path = file_path + '.bak.' + datetime.now().strftime('%Y%m%d%H%M%S')
    shutil.copy2(file_path, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def find_optimizer_file():
    """
    Trouve le fichier de l'optimiseur XGBoost
    
    Returns:
        str: Chemin du fichier ou None si non trouvé
    """
    # Chemins possibles
    possible_paths = [
        'keno_xgboost_optimizer.py',
        'keno_xgboost_optimizer_simple.py',
        'keno_advanced_analyzer.py'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"Fichier optimiseur trouvé: {path}")
            return path
    
    print("Fichier optimiseur non trouvé")
    return None

def modify_optimizer_file(file_path):
    """
    Modifie le fichier de l'optimiseur XGBoost
    
    Args:
        file_path: Chemin du fichier à modifier
        
    Returns:
        bool: True si la modification a réussi, False sinon
    """
    # Créer une sauvegarde
    backup_file(file_path)
    
    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Rechercher la méthode de calcul de scale_pos_weight
    spw_method_pattern = r'def\s+calculate_optimal_scale_pos_weight\s*\(\s*self\s*,\s*y_train\s*(?:,\s*[^)]*)*\s*\):'
    spw_method_match = re.search(spw_method_pattern, content)
    
    if not spw_method_match:
        print("Méthode de calcul de scale_pos_weight non trouvée")
        
        # Rechercher une autre méthode similaire
        train_model_pattern = r'def\s+train_model\s*\(\s*self\s*,.*?\):'
        train_model_match = re.search(train_model_pattern, content, re.DOTALL)
        
        if train_model_match:
            print("Méthode train_model trouvée, recherche de scale_pos_weight dans cette méthode")
            
            # Rechercher le calcul de scale_pos_weight dans train_model
            spw_calc_pattern = r'scale_pos_weight\s*=\s*[^;\n]+'
            spw_calc_match = re.search(spw_calc_pattern, content)
            
            if spw_calc_match:
                print(f"Calcul de scale_pos_weight trouvé: {spw_calc_match.group(0)}")
                
                # Remplacer le calcul par notre version
                new_calc = """# Calculer scale_pos_weight spécifique à ce numéro
                raw_class_counts = np.bincount(y_train.astype(int).values, minlength=2)
                neg_count = raw_class_counts[0]
                pos_count = raw_class_counts[1]
                
                # Éviter la division par zéro
                if pos_count > 0:
                    # Calculer le ratio spécifique à ce numéro
                    num_specific_scale_pos_weight = neg_count / pos_count
                    print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
                    print(f"  Numéro {num}: scale_pos_weight spécifique = {num_specific_scale_pos_weight:.4f}")
                else:
                    # Valeur par défaut si aucun exemple positif
                    num_specific_scale_pos_weight = 1.0
                    print(f"  Numéro {num}: Aucun exemple positif trouvé, utilisation de scale_pos_weight=1.0")
                
                # Mettre à jour la configuration XGBoost avec le scale_pos_weight spécifique
                scale_pos_weight = num_specific_scale_pos_weight"""
                
                # Remplacer le calcul
                new_content = content.replace(spw_calc_match.group(0), new_calc)
                
                # Écrire le contenu modifié
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print("Calcul de scale_pos_weight modifié avec succès")
                return True
            else:
                print("Calcul de scale_pos_weight non trouvé dans train_model")
        else:
            print("Méthode train_model non trouvée")
        
        # Si nous n'avons pas trouvé la méthode, essayons de trouver directement le calcul de scale_pos_weight
        spw_calc_pattern = r'scale_pos_weight\s*=\s*[^;\n]+'
        spw_calc_matches = re.finditer(spw_calc_pattern, content)
        
        if spw_calc_matches:
            print("Calculs de scale_pos_weight trouvés directement")
            
            # Remplacer tous les calculs
            new_content = content
            for match in spw_calc_matches:
                # Vérifier si c'est un calcul basé sur les données
                if 'raw_class_counts' in match.group(0) or 'y_train' in match.group(0) or 'y' in match.group(0):
                    print(f"Calcul trouvé: {match.group(0)}")
                    
                    # Remplacer par notre version
                    new_calc = """# Calculer scale_pos_weight spécifique à ce numéro
                    if 'y_train' in locals():
                        y_data = y_train
                    elif 'y' in locals():
                        y_data = y
                    else:
                        y_data = None
                    
                    if y_data is not None:
                        neg_count = np.sum(y_data == 0)
                        pos_count = np.sum(y_data == 1)
                        
                        # Éviter la division par zéro
                        if pos_count > 0:
                            # Calculer le ratio spécifique à ce numéro
                            scale_pos_weight = neg_count / pos_count
                            print(f"  Numéro {num if 'num' in locals() else '?'}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
                            print(f"  Numéro {num if 'num' in locals() else '?'}: scale_pos_weight spécifique = {scale_pos_weight:.4f}")
                        else:
                            # Valeur par défaut si aucun exemple positif
                            scale_pos_weight = 1.0
                            print(f"  Numéro {num if 'num' in locals() else '?'}: Aucun exemple positif trouvé, utilisation de scale_pos_weight=1.0")
                    else:
                        # Valeur par défaut
                        scale_pos_weight = 2.57
                        print("Impossible de calculer scale_pos_weight, utilisation de la valeur par défaut: 2.57")"""
                    
                    new_content = new_content.replace(match.group(0), new_calc)
            
            # Écrire le contenu modifié
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("Calculs de scale_pos_weight modifiés avec succès")
            return True
        
        return False
    
    # Trouver la fin de la méthode
    method_start = spw_method_match.start()
    next_def = re.search(r'def\s+', content[method_start + 10:])
    
    if next_def:
        method_end = method_start + 10 + next_def.start()
    else:
        method_end = len(content)
    
    # Extraire la méthode
    method_content = content[method_start:method_end]
    print(f"Méthode trouvée: {method_content[:100]}...")
    
    # Créer la nouvelle méthode
    new_method = """def calculate_optimal_scale_pos_weight(self, y_train, num=None):
        \"\"\"
        Calcule la valeur optimale de scale_pos_weight basée sur la distribution des classes
        
        Args:
            y_train: Étiquettes d'entraînement
            num: Numéro Keno (pour le logging)
            
        Returns:
            float: Valeur optimale de scale_pos_weight
        \"\"\"
        # Compter les exemples positifs et négatifs
        neg_count = np.sum(y_train == 0)
        pos_count = np.sum(y_train == 1)
        
        # Éviter la division par zéro
        if pos_count > 0:
            # Calculer le ratio
            ratio = neg_count / pos_count
            
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
                print(f"  Numéro {num}: Ratio négatif/positif calculé = {ratio:.4f}")
            
            return ratio
        else:
            # Valeur par défaut si aucun exemple positif
            if num is not None and self.verbose > 0:
                print(f"  Numéro {num}: Aucun exemple positif trouvé, utilisation de scale_pos_weight=1.0")
            
            return 1.0"""
    
    # Remplacer la méthode
    new_content = content.replace(method_content, new_method)
    
    # Écrire le contenu modifié
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("Méthode de calcul de scale_pos_weight modifiée avec succès")
    
    # Maintenant, rechercher les appels à cette méthode
    call_pattern = r'self\.calculate_optimal_scale_pos_weight\s*\(\s*([^,)]+)(?:\s*,\s*[^)]+)*\s*\)'
    call_matches = re.finditer(call_pattern, new_content)
    
    if call_matches:
        print("Appels à la méthode trouvés")
        
        # Remplacer les appels
        updated_content = new_content
        for match in call_matches:
            old_call = match.group(0)
            arg = match.group(1).strip()
            
            # Vérifier si l'appel inclut déjà le paramètre num
            if 'num' in old_call:
                print(f"L'appel inclut déjà le paramètre num: {old_call}")
                continue
            
            # Créer le nouvel appel
            new_call = f"self.calculate_optimal_scale_pos_weight({arg}, num=num)"
            
            # Remplacer l'appel
            updated_content = updated_content.replace(old_call, new_call)
        
        # Écrire le contenu mis à jour
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print("Appels à la méthode modifiés avec succès")
    else:
        print("Aucun appel à la méthode trouvé")
    
    return True

def create_direct_patch():
    """
    Crée un patch direct pour corriger le problème de scale_pos_weight
    """
    # Trouver le fichier de l'optimiseur
    optimizer_file = find_optimizer_file()
    
    if not optimizer_file:
        print("Impossible de trouver le fichier de l'optimiseur")
        return False
    
    # Modifier le fichier
    success = modify_optimizer_file(optimizer_file)
    
    if not success:
        print("Échec de la modification du fichier")
        return False
    
    print("\nPatch direct appliqué avec succès!")
    print("Le problème de scale_pos_weight identique devrait être résolu.")
    print("Veuillez redémarrer votre application pour que les modifications prennent effet.")
    
    return True

def create_manual_patch_instructions():
    """
    Crée des instructions pour appliquer manuellement le patch
    """
    instructions = """
# Instructions pour corriger manuellement le problème de scale_pos_weight identique

## Problème
Tous les numéros Keno ont la même valeur de `scale_pos_weight` (2.57), ce qui indique que le code utilise la même distribution globale pour tous les numéros au lieu de calculer une distribution spécifique à chaque numéro.

## Solution manuelle

1. Localisez le fichier qui contient la méthode `calculate_optimal_scale_pos_weight` ou le calcul de `scale_pos_weight`. Il peut s'agir de :
   - `keno_xgboost_optimizer.py`
   - `keno_xgboost_optimizer_simple.py`
   - `keno_advanced_analyzer.py`

2. Remplacez la méthode `calculate_optimal_scale_pos_weight` par celle-ci :

```python
def calculate_optimal_scale_pos_weight(self, y_train, num=None):
    \"\"\"
    Calcule la valeur optimale de scale_pos_weight basée sur la distribution des classes
    
    Args:
        y_train: Étiquettes d'entraînement
        num: Numéro Keno (pour le logging)
        
    Returns:
        float: Valeur optimale de scale_pos_weight
    \"\"\"
    # Compter les exemples positifs et négatifs
    neg_count = np.sum(y_train == 0)
    pos_count = np.sum(y_train == 1)
    
    # Éviter la division par zéro
    if pos_count > 0:
        # Calculer le ratio
        ratio = neg_count / pos_count
        
        if num is not None and self.verbose > 0:
            print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
            print(f"  Numéro {num}: Ratio négatif/positif calculé = {ratio:.4f}")
        
        return ratio
    else:
        # Valeur par défaut si aucun exemple positif
        if num is not None and self.verbose > 0:
            print(f"  Numéro {num}: Aucun exemple positif trouvé, utilisation de scale_pos_weight=1.0")
        
        return 1.0
```

3. Recherchez tous les appels à cette méthode et ajoutez le paramètre `num` :

Remplacez :
```python
scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train)
```

Par :
```python
scale_pos_weight = self.calculate_optimal_scale_pos_weight(y_train, num=num)
```

4. Si vous ne trouvez pas la méthode, recherchez directement le calcul de `scale_pos_weight` :

Remplacez :
```python
# Utiliser les statistiques des données brutes pour calculer scale_pos_weight
raw_class_counts = np.bincount(y.astype(int).values, minlength=2)
```

Par :
```python
# Calculer scale_pos_weight spécifique à ce numéro
raw_class_counts = np.bincount(y_train.astype(int).values, minlength=2)
neg_count = raw_class_counts[0]
pos_count = raw_class_counts[1]

# Éviter la division par zéro
if pos_count > 0:
    # Calculer le ratio spécifique à ce numéro
    num_specific_scale_pos_weight = neg_count / pos_count
    print(f"  Numéro {num}: Distribution des classes [négatifs={neg_count}, positifs={pos_count}]")
    print(f"  Numéro {num}: scale_pos_weight spécifique = {num_specific_scale_pos_weight:.4f}")
else:
    # Valeur par défaut si aucun exemple positif
    num_specific_scale_pos_weight = 1.0
    print(f"  Numéro {num}: Aucun exemple positif trouvé, utilisation de scale_pos_weight=1.0")

# Mettre à jour la configuration XGBoost avec le scale_pos_weight spécifique
scale_pos_weight = num_specific_scale_pos_weight
```

5. Redémarrez votre application pour que les modifications prennent effet.
"""
    
    # Sauvegarder les instructions
    instructions_file = 'fix_scale_pos_weight_manual.md'
    
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print(f"Instructions manuelles sauvegardées dans {instructions_file}")

def main():
    """Fonction principale"""
    print("Correction directe du problème de scale_pos_weight identique")
    
    # Créer le patch direct
    success = create_direct_patch()
    
    # Créer les instructions manuelles
    create_manual_patch_instructions()
    
    if not success:
        print("\nLe patch direct a échoué.")
        print("Veuillez suivre les instructions manuelles dans le fichier 'fix_scale_pos_weight_manual.md'.")
    
    print("\nTerminé!")

if __name__ == "__main__":
    main()
