#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script pour activer le système ULTRA-PARALLÉLISÉ
Remplace le système actuel par une version qui exploite TOUS vos 24 cœurs
"""

import os
import sys

def integrate_ultra_parallel():
    """Intègre le système ultra-parallélisé dans l'interface"""
    
    print("🚀 ACTIVATION DU SYSTÈME ULTRA-PARALLÉLISÉ")
    print("💻 Optimisé pour votre Intel i9 24-cœurs")
    print("⚡ Exploitation maximale des ressources\n")
    
    gui_file = 'keno_gui.py'
    
    if not os.path.exists(gui_file):
        print(f"❌ Fichier {gui_file} non trouvé")
        return False
    
    try:
        # Lire le fichier
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifier si déjà modifié
        if 'KenoUltraParallel' in content:
            print("✅ Système ultra-parallélisé déjà intégré")
            return True
        
        # Ajouter l'import du système ultra-parallélisé
        ultra_import = """
# === SYSTÈME ULTRA-PARALLÉLISÉ ===
try:
    from keno_ultra_parallel import KenoUltraParallel
    ultra_parallel_available = True
    print("🚀 Système ULTRA-PARALLÉLISÉ disponible (24-cœurs)")
except ImportError as e:
    ultra_parallel_available = False
    print(f"❌ Système ULTRA-PARALLÉLISÉ non disponible: {e}")
"""
        
        # Trouver où insérer l'import
        import_pos = content.find("from keno_data import KenoDataManager")
        if import_pos != -1:
            content = content[:import_pos] + ultra_import + "\n" + content[import_pos:]
        
        # Remplacer la méthode _start_ultra_auto_improve
        method_start = content.find("def _start_ultra_auto_improve(self, mode):")
        if method_start != -1:
            # Trouver la fin de la méthode
            method_end = content.find("\n    def ", method_start + 1)
            if method_end == -1:
                method_end = content.find("\n    def setup_auto_save", method_start)
            
            if method_end != -1:
                # Nouvelle méthode ultra-parallélisée
                new_method = '''    def _start_ultra_auto_improve(self, mode):
        """Lance l'auto-amélioration ULTRA-PARALLÉLISÉE (exploite TOUS vos cœurs)"""
        try:
            # Vérifier la disponibilité du système ultra-parallélisé
            if not ultra_parallel_available:
                messagebox.showerror("Erreur", "Système ULTRA-PARALLÉLISÉ non disponible")
                return
            
            # Créer le système ultra-parallélisé
            ultra_parallel = KenoUltraParallel(self.data_manager, self.analyzer)
            
            # Déterminer les numéros selon le mode
            if mode == "ultra_fast":
                numbers_count = 5
                mode_name = "ULTRA-RAPIDE"
                estimated_time = "3-5 minutes"
            elif mode == "fast":
                numbers_count = 15
                mode_name = "RAPIDE"
                estimated_time = "8-12 minutes"
            else:  # complete
                numbers_count = 70
                mode_name = "COMPLET"
                estimated_time = "25-35 minutes"
            
            # Créer la fenêtre de progression ultra-parallélisée
            progress_window = tk.Toplevel(self)
            progress_window.title(f"🚀 ULTRA-PARALLÉLISÉ - {mode_name} ({ultra_parallel.max_workers} cœurs)")
            progress_window.geometry("900x700")
            progress_window.transient(self)
            progress_window.grab_set()
            progress_window.configure(bg='#0a0a0a')
            
            # Interface ultra-stylée
            title_label = tk.Label(progress_window, 
                                  text=f"🚀 ULTRA-PARALLÉLISÉ - {mode_name} 🚀", 
                                  font=('Helvetica', 18, 'bold'),
                                  fg='red', bg='#0a0a0a')
            title_label.pack(pady=15)
            
            # Informations sur les ressources
            resources_label = tk.Label(progress_window, 
                                      text=f"💻 Intel i9 24-cœurs | ⚡ {ultra_parallel.max_workers} processus parallèles | 🔥 EXPLOITATION MAXIMALE", 
                                      font=('Helvetica', 12, 'bold'),
                                      fg='lime', bg='#0a0a0a')
            resources_label.pack(pady=5)
            
            status_var = tk.StringVar(value=f"Initialisation ultra-parallélisée ({numbers_count} numéros, ~{estimated_time})...")
            status_label = tk.Label(progress_window, textvariable=status_var,
                                   fg='yellow', bg='#0a0a0a',
                                   font=('Helvetica', 12, 'bold'))
            status_label.pack(pady=10)
            
            # Zone de texte pour les détails
            details_frame = tk.Frame(progress_window, bg='#0a0a0a')
            details_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
            
            scrollbar = tk.Scrollbar(details_frame)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            details_text = tk.Text(details_frame, yscrollcommand=scrollbar.set,
                                  bg='#1a1a1a', fg='white', font=('Consolas', 9))
            details_text.pack(fill=tk.BOTH, expand=True)
            scrollbar.config(command=details_text.yview)
            
            def update_details(message):
                details_text.insert(tk.END, message + "\\n")
                details_text.see(tk.END)
                progress_window.update()
            
            # Lancer l'entraînement ultra-parallélisé dans un thread
            import threading
            
            def run_ultra_parallel_training():
                try:
                    update_details(f"🚀 DÉMARRAGE ULTRA-PARALLÉLISÉ - Mode {mode_name}")
                    update_details(f"💻 Machine: Intel i9 24-cœurs détectée")
                    update_details(f"⚡ Workers: {ultra_parallel.max_workers} processus simultanés")
                    update_details(f"🔥 Mode: EXPLOITATION MAXIMALE DES RESSOURCES")
                    update_details(f"📊 Entraînement: {numbers_count} numéros avec 50+ caractéristiques")
                    
                    status_var.set("🔥 PHASE 1: Création parallèle des caractéristiques...")
                    
                    # Lancer l'entraînement ultra-parallélisé
                    result = ultra_parallel.run_ultra_parallel_training(mode)
                    
                    if result['success']:
                        update_details(f"\\n🎉 ULTRA-PARALLÉLISÉ TERMINÉ AVEC SUCCÈS !")
                        update_details(f"✅ Modèles entraînés: {result['trained_models']}/{result['total_numbers']}")
                        update_details(f"🎯 F1-score moyen: {result['avg_f1']:.4f}")
                        update_details(f"📊 Précision moyenne: {result['avg_accuracy']:.4f}")
                        update_details(f"💻 Ressources exploitées: {ultra_parallel.max_workers} cœurs")
                        update_details(f"🚀 Système prêt pour des prédictions ultra-précises !")
                        
                        status_var.set("🎉 ULTRA-PARALLÉLISÉ TERMINÉ AVEC SUCCÈS ! 🎉")
                    else:
                        update_details(f"\\n❌ Échec de l'entraînement ultra-parallélisé")
                        status_var.set("❌ Échec de l'entraînement")
                        
                except Exception as e:
                    update_details(f"\\n💥 Erreur ultra-parallélisée: {e}")
                    status_var.set("💥 Erreur système")
                    import traceback
                    update_details(traceback.format_exc())
                
                # Bouton de fermeture
                def close_window():
                    progress_window.destroy()
                
                close_button = tk.Button(progress_window, text="🚀 FERMER", 
                                        command=close_window,
                                        bg="red", fg="white", 
                                        font=('Helvetica', 14, 'bold'),
                                        padx=30, pady=10)
                close_button.pack(pady=20)
            
            # Démarrer le thread ultra-parallélisé
            training_thread = threading.Thread(target=run_ultra_parallel_training)
            training_thread.daemon = True
            training_thread.start()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur ultra-parallélisée: {e}")

'''
                
                # Remplacer l'ancienne méthode
                content = content[:method_start] + new_method + content[method_end:]
        
        # Écrire le fichier modifié
        with open(gui_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Système ultra-parallélisé intégré avec succès")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'intégration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parallel_system():
    """Test du système ultra-parallélisé"""
    
    print("\\n🧪 TEST DU SYSTÈME ULTRA-PARALLÉLISÉ")
    
    try:
        from keno_ultra_parallel import test_ultra_parallel
        success = test_ultra_parallel()
        
        if success:
            print("✅ Test réussi - Système opérationnel")
            return True
        else:
            print("❌ Test échoué")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

def main():
    """Fonction principale"""
    
    print("🚀 ACTIVATION DU SYSTÈME ULTRA-PARALLÉLISÉ")
    print("💻 Optimisé pour Intel i9 24-cœurs")
    print("⚡ Exploitation maximale des ressources CPU")
    print("🔥 Performances ultra-avancées\\n")
    
    # 1. Tester le système
    test_success = test_parallel_system()
    
    if not test_success:
        print("\\n❌ Le test a échoué. Vérifiez les dépendances.")
        return 1
    
    # 2. Intégrer dans l'interface
    integration_success = integrate_ultra_parallel()
    
    if integration_success:
        print("\\n🎉 ACTIVATION RÉUSSIE !")
        print("✅ Système ultra-parallélisé intégré")
        print("💻 Votre Intel i9 24-cœurs sera exploité au maximum")
        print("\\n🚀 Relancez votre application:")
        print("  python main.py")
        print("\\n🎯 Cliquez sur 'LANCER L'AUTO-AMÉLIORATION' pour voir la différence !")
        print("⚡ Vous devriez maintenant voir 60-80% d'utilisation CPU !")
        return 0
    else:
        print("\\n❌ ÉCHEC DE L'ACTIVATION")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
