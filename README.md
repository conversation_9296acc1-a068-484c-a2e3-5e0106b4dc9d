# Analyseur et Prédicteur de Keno

Cette application permet d'analyser des données historiques de tirages Keno et de prédire des numéros pour les tirages futurs.

## Fonctionnalités

- Importation de données historiques à partir de fichiers CSV
- Analyse statistique des tirages (fréquences, numéros chauds/froids, etc.)
- Prédiction de numéros pour les tirages futurs avec différentes méthodes
- Visualisation graphique des statistiques
- Configuration personnalisable des paramètres du jeu

## Prérequis

- Python 3.6 ou supérieur
- Bibliothèques requises :
  - tkinter
  - matplotlib
  - numpy

## Installation

1. Clonez ce dépôt ou téléchargez les fichiers
2. Installez les dépendances :
   ```
   pip install matplotlib numpy
   ```
3. Exécutez l'application :
   ```
   python main.py
   ```

## Utilisation

### Configuration

1. Définissez les paramètres du jeu Keno :
   - Nombre maximum de numéros (généralement 70 ou 80)
   - Nombre de numéros par tirage (généralement 20)
   - Nombre de numéros à prédire
   - Méthode de prédiction

2. Cliquez sur "Appliquer" pour enregistrer les paramètres

### Importation de données

1. Cliquez sur "Parcourir" pour sélectionner un fichier CSV contenant les données historiques
2. Configurez les options d'importation :
   - Format de date
   - Colonne contenant la date
   - Colonne de début des numéros
   - Présence d'en-tête
   - Délimiteur
3. Cliquez sur "Importer CSV" pour charger les données

### Génération de prédictions

1. Cliquez sur "Générer prédiction" pour obtenir une prédiction basée sur les données importées
2. Les résultats s'affichent dans la zone de prédiction

### Statistiques

- L'onglet "Texte" affiche les statistiques détaillées
- L'onglet "Graphique" montre la fréquence d'apparition des numéros

## Format du fichier CSV

Le fichier CSV doit contenir :
- Une colonne avec la date du tirage
- Une série de colonnes contenant les numéros tirés

Exemple :
```
Date,Num1,Num2,Num3,...,Num20
2023-01-01 12:00:00,3,7,12,...,70
2023-01-01 19:00:00,1,5,10,...,70
...
```

## Méthodes de prédiction

- **Fréquence** : Prédit les numéros les plus fréquemment tirés
- **Pattern** : Recherche des motifs récurrents dans les tirages récents
- **Aléatoire** : Génère des numéros aléatoires (référence)
- **Combinée** : Utilise une combinaison des méthodes précédentes

## Avertissement

Cette application est fournie à des fins éducatives et de divertissement uniquement. Les jeux de loterie comme le Keno sont basés sur le hasard, et aucune méthode ne peut garantir des gains. Jouez de manière responsable.
