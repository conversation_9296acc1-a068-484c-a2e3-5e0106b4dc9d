#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de diagnostic pour comprendre la structure des données Keno
"""

import os
import sys

def diagnose_data_structure():
    """Diagnostique la structure des données chargées"""
    
    print("=== Diagnostic de la structure des données ===")
    
    try:
        # Importer les modules
        from keno_data import KenoDataManager
        
        # Créer le data manager
        data_manager = KenoDataManager()
        
        # Charger les données
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        data_files = [f for f in os.listdir(data_dir) if f.endswith('.json') or f.endswith('.keno')]
        latest_file = max(data_files, key=lambda f: os.path.getmtime(os.path.join(data_dir, f)))
        file_path = os.path.join(data_dir, latest_file)
        
        print(f"Chargement des données depuis: {file_path}")
        success = data_manager.load_database(file_path)
        
        if not success:
            print("✗ Échec du chargement des données")
            return False
        
        print(f"✓ {data_manager.get_draws_count()} tirages chargés")
        
        # Analyser la structure des tirages
        draws = data_manager.draws
        
        if not draws:
            print("✗ Aucun tirage trouvé")
            return False
        
        print(f"\n=== Analyse des tirages ===")
        print(f"Nombre total de tirages: {len(draws)}")
        
        # Analyser les premiers tirages
        for i, draw in enumerate(draws[:5]):
            print(f"\n--- Tirage {i+1} ---")
            print(f"Type: {type(draw)}")
            print(f"Attributs: {dir(draw)}")
            
            # Vérifier les attributs importants
            if hasattr(draw, 'numbers'):
                print(f"Numbers: {draw.numbers} (type: {type(draw.numbers)})")
            else:
                print("❌ Pas d'attribut 'numbers'")
            
            if hasattr(draw, 'draw_date'):
                print(f"Draw_date: {draw.draw_date} (type: {type(draw.draw_date)})")
            else:
                print("❌ Pas d'attribut 'draw_date'")
            
            if hasattr(draw, 'draw_id'):
                print(f"Draw_id: {draw.draw_id}")
            
            # Afficher tous les attributs qui ne commencent pas par _
            public_attrs = [attr for attr in dir(draw) if not attr.startswith('_')]
            print(f"Attributs publics: {public_attrs}")
            
            # Essayer d'accéder aux valeurs
            for attr in public_attrs[:10]:  # Limiter à 10 pour éviter le spam
                try:
                    value = getattr(draw, attr)
                    if not callable(value):
                        print(f"  {attr}: {value} (type: {type(value)})")
                except Exception as e:
                    print(f"  {attr}: Erreur - {e}")
        
        # Analyser la distribution des numéros
        print(f"\n=== Analyse de la distribution des numéros ===")
        
        all_numbers = []
        valid_draws = 0
        
        for draw in draws[:100]:  # Analyser les 100 premiers tirages
            if hasattr(draw, 'numbers') and draw.numbers:
                if isinstance(draw.numbers, (list, tuple)):
                    all_numbers.extend(draw.numbers)
                    valid_draws += 1
                elif isinstance(draw.numbers, str):
                    # Peut-être que les numéros sont stockés comme string
                    try:
                        # Essayer différents formats
                        if ',' in draw.numbers:
                            nums = [int(x.strip()) for x in draw.numbers.split(',')]
                        elif ' ' in draw.numbers:
                            nums = [int(x.strip()) for x in draw.numbers.split()]
                        else:
                            nums = [int(draw.numbers)]
                        all_numbers.extend(nums)
                        valid_draws += 1
                        print(f"  Format string détecté: '{draw.numbers}' -> {nums}")
                    except Exception as e:
                        print(f"  Erreur de parsing pour '{draw.numbers}': {e}")
        
        print(f"Tirages valides analysés: {valid_draws}")
        
        if all_numbers:
            print(f"Numéros trouvés: {len(all_numbers)}")
            print(f"Numéros uniques: {sorted(set(all_numbers))}")
            print(f"Min: {min(all_numbers)}, Max: {max(all_numbers)}")
            
            # Vérifier si c'est du Keno (1-70)
            if min(all_numbers) >= 1 and max(all_numbers) <= 70:
                print("✓ Format Keno valide (numéros 1-70)")
            else:
                print("⚠ Format inhabituel pour le Keno")
        else:
            print("✗ Aucun numéro trouvé")
        
        # Test de la fonction de création de caractéristiques
        print(f"\n=== Test de création de caractéristiques ===")
        
        # Créer une version simplifiée pour tester
        test_number = 7
        print(f"Test pour le numéro {test_number}")
        
        # Compter les apparitions manuellement
        appearances = 0
        for draw in draws[:100]:
            if hasattr(draw, 'numbers') and draw.numbers:
                if isinstance(draw.numbers, (list, tuple)):
                    if test_number in draw.numbers:
                        appearances += 1
                elif isinstance(draw.numbers, str):
                    try:
                        if ',' in draw.numbers:
                            nums = [int(x.strip()) for x in draw.numbers.split(',')]
                        elif ' ' in draw.numbers:
                            nums = [int(x.strip()) for x in draw.numbers.split()]
                        else:
                            nums = [int(draw.numbers)]
                        if test_number in nums:
                            appearances += 1
                    except:
                        pass
        
        print(f"Apparitions du numéro {test_number} dans les 100 premiers tirages: {appearances}")
        
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors du diagnostic: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_fixes():
    """Suggère des corrections basées sur l'analyse"""
    
    print(f"\n=== Suggestions de correction ===")
    
    print("1. Vérifiez que les tirages ont bien un attribut 'numbers'")
    print("2. Vérifiez le format des numéros (liste, tuple, ou string)")
    print("3. Vérifiez que les numéros sont dans la plage 1-70")
    print("4. Vérifiez que les dates sont présentes et valides")

def main():
    """Fonction principale"""
    
    success = diagnose_data_structure()
    
    if success:
        suggest_fixes()
        return 0
    else:
        print("Échec du diagnostic")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
