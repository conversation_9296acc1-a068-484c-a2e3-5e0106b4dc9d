"""
Script pour calculer les poids scale_pos_weight basés sur l'historique réel des tirages Keno
Ce script analyse l'historique complet des tirages et calcule le ratio négatif/positif correct pour chaque numéro.
"""

import os
import sys
import json
import numpy as np
import pandas as pd
from collections import Counter
import matplotlib.pyplot as plt

# Essayer d'importer le gestionnaire de données Keno
try:
    from keno_data import KenoDataManager
    print("Module de gestion des données Keno importé avec succès")
except ImportError:
    print("Erreur: Module de gestion des données Keno non disponible")
    sys.exit(1)

def calculate_historical_weights():
    """
    Calcule les poids scale_pos_weight basés sur l'historique réel des tirages
    
    Returns:
        dict: Dictionnaire des poids par numéro
    """
    # Créer le gestionnaire de données
    data_manager = KenoDataManager()
    
    # Charger les données
    print("Chargement des données Keno...")
    data_loaded = data_manager.load_data()
    
    if not data_loaded or not data_manager.draws:
        print("Erreur: Impossible de charger les données Keno")
        return {}
    
    print(f"Données chargées: {len(data_manager.draws)} tirages")
    
    # Compter les occurrences de chaque numéro
    occurrences = Counter()
    total_draws = len(data_manager.draws)
    
    for draw in data_manager.draws:
        if hasattr(draw, 'draw_numbers') and draw.draw_numbers:
            for num in draw.draw_numbers:
                if 1 <= num <= 70:  # Vérifier que le numéro est valide
                    occurrences[num] += 1
    
    # Calculer les poids pour chaque numéro
    weights = {}
    frequencies = {}
    
    for num in range(1, 71):
        # Nombre de fois où le numéro est apparu
        pos_count = occurrences.get(num, 0)
        
        # Nombre de fois où le numéro n'est pas apparu
        neg_count = total_draws - pos_count
        
        # Calculer le ratio négatif/positif (scale_pos_weight)
        if pos_count > 0:
            ratio = neg_count / pos_count
        else:
            ratio = 2.57  # Valeur par défaut si le numéro n'est jamais apparu
        
        # Calculer la fréquence d'apparition
        frequency = pos_count / total_draws if total_draws > 0 else 0
        
        # Stocker les valeurs
        weights[num] = ratio
        frequencies[num] = frequency
        
        print(f"Numéro {num}: Apparitions: {pos_count}/{total_draws} ({frequency:.4f}), scale_pos_weight: {ratio:.4f}")
    
    # Créer un DataFrame pour l'analyse
    df = pd.DataFrame({
        'number': list(range(1, 71)),
        'occurrences': [occurrences.get(num, 0) for num in range(1, 71)],
        'frequency': [frequencies.get(num, 0) for num in range(1, 71)],
        'scale_pos_weight': [weights.get(num, 0) for num in range(1, 71)]
    })
    
    # Afficher les statistiques
    print("\nStatistiques des poids:")
    print(f"Moyenne: {df['scale_pos_weight'].mean():.4f}")
    print(f"Écart-type: {df['scale_pos_weight'].std():.4f}")
    print(f"Minimum: {df['scale_pos_weight'].min():.4f} (numéro {df.loc[df['scale_pos_weight'].idxmin()]['number']:.0f})")
    print(f"Maximum: {df['scale_pos_weight'].max():.4f} (numéro {df.loc[df['scale_pos_weight'].idxmax()]['number']:.0f})")
    
    # Créer un graphique des poids
    plt.figure(figsize=(12, 6))
    plt.bar(df['number'], df['scale_pos_weight'])
    plt.axhline(y=df['scale_pos_weight'].mean(), color='r', linestyle='-', label=f'Moyenne ({df["scale_pos_weight"].mean():.4f})')
    plt.xlabel('Numéro Keno')
    plt.ylabel('scale_pos_weight')
    plt.title('Valeurs de scale_pos_weight basées sur l\'historique des tirages')
    plt.legend()
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Sauvegarder le graphique
    plt.savefig('historical_scale_pos_weight.png')
    print("Graphique sauvegardé dans historical_scale_pos_weight.png")
    
    # Créer un graphique des fréquences
    plt.figure(figsize=(12, 6))
    plt.bar(df['number'], df['frequency'])
    plt.axhline(y=df['frequency'].mean(), color='r', linestyle='-', label=f'Moyenne ({df["frequency"].mean():.4f})')
    plt.xlabel('Numéro Keno')
    plt.ylabel('Fréquence d\'apparition')
    plt.title('Fréquence d\'apparition de chaque numéro Keno')
    plt.legend()
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Sauvegarder le graphique
    plt.savefig('historical_frequencies.png')
    print("Graphique sauvegardé dans historical_frequencies.png")
    
    # Sauvegarder les résultats dans un fichier CSV
    df.to_csv('historical_analysis.csv', index=False)
    print("Résultats sauvegardés dans historical_analysis.csv")
    
    return weights

def save_weights_to_json(weights):
    """
    Sauvegarde les poids dans un fichier JSON
    
    Args:
        weights: Dictionnaire des poids par numéro
    """
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Chemin du fichier
    file_path = os.path.join('data', 'historical_scale_pos_weight.json')
    
    # Convertir les clés en chaînes pour la sérialisation JSON
    weights_str = {str(k): v for k, v in weights.items()}
    
    # Sauvegarder les poids
    with open(file_path, 'w') as f:
        json.dump(weights_str, f, indent=2)
    
    print(f"Poids sauvegardés dans {file_path}")
    
    return file_path

def update_injector(weights_file):
    """
    Met à jour le module d'injection pour utiliser les poids historiques
    
    Args:
        weights_file: Chemin du fichier de poids
    """
    # Chemin du module d'injection
    injector_file = 'force_scale_pos_weight_injector.py'
    
    # Vérifier si le fichier existe
    if not os.path.exists(injector_file):
        print(f"Le fichier {injector_file} n'existe pas")
        return False
    
    # Lire le contenu du fichier
    with open(injector_file, 'r') as f:
        content = f.read()
    
    # Remplacer le chemin du fichier de poids
    new_content = content.replace('SPW_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), \'data\', \'forced_scale_pos_weight.json\')',
                                 f'SPW_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), \'data\', \'historical_scale_pos_weight.json\')')
    
    # Écrire le contenu modifié
    with open(injector_file, 'w') as f:
        f.write(new_content)
    
    print(f"Module d'injection mis à jour pour utiliser les poids historiques")
    
    return True

def main():
    """Fonction principale"""
    print("Calcul des poids scale_pos_weight basés sur l'historique des tirages Keno")
    
    # Calculer les poids
    weights = calculate_historical_weights()
    
    if not weights:
        print("Erreur lors du calcul des poids")
        return
    
    # Sauvegarder les poids dans un fichier JSON
    weights_file = save_weights_to_json(weights)
    
    # Mettre à jour le module d'injection
    update_injector(weights_file)
    
    print("\nCalcul terminé!")
    print("Les poids scale_pos_weight basés sur l'historique des tirages ont été calculés et sauvegardés.")
    print("Pour les utiliser, importez le module d'injection dans votre code:")
    print("  import force_scale_pos_weight_injector")

if __name__ == "__main__":
    main()
