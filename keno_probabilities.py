"""
Module pour afficher les probabilités et gains du Keno
"""

import tkinter as tk
from tkinter import ttk

def setup_probabilities_tab(tab_frame, font_size=12):
    """
    Configure l'onglet des probabilités et gains du Keno

    Args:
        tab_frame (ttk.Frame): Le frame de l'onglet
        font_size (int): Taille de police
    """
    # Définir les polices
    title_font = ('Helvetica', font_size+4, 'bold')
    header_font = ('Helvetica', font_size+2, 'bold')
    text_font = ('Helvetica', font_size)

    # Créer un cadre principal avec scrollbar et fond coloré
    main_frame = tk.Frame(tab_frame, bg="#ecf0f1")
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # Ajouter une scrollbar
    canvas = tk.Canvas(main_frame, bg="#ecf0f1")
    scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas, style="Main.TFrame")

    # Style pour le frame scrollable
    style = ttk.Style()
    style.configure("Main.TFrame", background="#ecf0f1")

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # Titre principal
    title_label = tk.Label(scrollable_frame, text="Probabilités et Gains du Keno", font=title_font,
                          bg="#ecf0f1", fg="#2c3e50")
    title_label.pack(pady=20)

    # Description
    description = """Le tableau ci-dessous présente les probabilités de gagner au Keno selon le nombre de numéros cochés
et le nombre de numéros trouvés, ainsi que les montants des lots correspondants pour une mise de 1€."""

    desc_label = tk.Label(scrollable_frame, text=description, font=text_font, wraplength=800,
                         justify="center", bg="#ecf0f1", fg="#2c3e50")
    desc_label.pack(pady=10)

    # Créer le tableau avec bordure
    table_frame = ttk.Frame(scrollable_frame, style="Table.TFrame")
    table_frame.pack(padx=20, pady=20, fill="both", expand=True)

    # Définir les styles pour le tableau avec des couleurs plus contrastées
    style.configure("Table.TFrame", background="#34495e", borderwidth=2, relief="solid")
    style.configure("Header.TLabel", font=header_font, background="#2980b9", foreground="white", padding=5)
    style.configure("Row.TLabel", font=text_font, background="#d6eaf8", foreground="#333333", padding=5)
    style.configure("Alt.Row.TLabel", font=text_font, background="#2c3e50", foreground="white", padding=5)
    style.configure("Special.Row.TLabel", font=text_font, background="#c0392b", foreground="white", padding=5)

    # Créer les en-têtes du tableau
    headers = [
        "Numéros\ncochés par\ngrille",
        "Probabilité globale\nde gagner par grille\nselon le nombre de\nnuméros cochés",
        "Numéros\ntrouvés\npar grille",
        "Probabilité globale de\ngagner par grille selon le\nnombre de numéros\ncochés et trouvés",
        "Montants des lots en €\npour une mise de 1€"
    ]

    # Ajouter les en-têtes
    for col, header in enumerate(headers):
        label = ttk.Label(table_frame, text=header, style="Header.TLabel", anchor="center", width=25)
        label.grid(row=0, column=col, sticky="nsew", padx=1, pady=1)

    # Données du tableau
    table_data = [
        # 10 numéros
        [10, "1 chance sur 7,38", 10, "1 chance sur 2 147 181", "200 000 € cash\nou\n10 000 € par an à vie"],
        [10, "", 9, "1 chance sur 47 238", "2 000 €"],
        [10, "", 8, "1 chance sur 2 571", "100 €"],
        [10, "", 7, "1 chance sur 261", "10 €"],
        [10, "", 6, "1 chance sur 44", "5 €"],
        [10, "", 5, "1 chance sur 12", "2 €"],
        [10, "", 0, "1 chance sur 39", "2 €"],

        # 9 numéros
        [9, "1 chance sur 3,78", 9, "1 chance sur 387 197", "40 000 € cash\nou\n2 000 € par an à vie"],
        [9, "", 8, "1 chance sur 10 325", "100 €"],
        [9, "", 7, "1 chance sur 685", "20 €"],
        [9, "", 6, "1 chance sur 86", "8 €"],
        [9, "", 5, "1 chance sur 18", "2 €"],
        [9, "", 4, "1 chance sur 6", "1 €"],
        [9, "", 0, "1 chance sur 26", "1 €"],

        # 8 numéros
        [8, "1 chance sur 10,58", 8, "1 chance sur 74 941", "8 000 €"],
        [8, "", 7, "1 chance sur 2 436", "100 €"],
        [8, "", 6, "1 chance sur 199", "30 €"],
        [8, "", 5, "1 chance sur 31", "5 €"],
        [8, "", 0, "1 chance sur 18", "2 €"],

        # 7 numéros
        [7, "1 chance sur 10,34", 7, "1 chance sur 15 464", "3 000 €"],
        [7, "", 6, "1 chance sur 619", "70 €"],
        [7, "", 5, "1 chance sur 63", "5 €"],
        [7, "", 4, "1 chance sur 13", "2 €"],

        # 6 numéros
        [6, "1 chance sur 19,43", 6, "1 chance sur 3 383", "900 €"],
        [6, "", 5, "1 chance sur 169", "30 €"],
        [6, "", 4, "1 chance sur 22", "3 €"],

        # 5 numéros
        [5, "1 chance sur 7,32", 5, "1 chance sur 781", "80 €"],
        [5, "", 4, "1 chance sur 50", "10 €"],
        [5, "", 3, "1 chance sur 9", "2 €"],

        # 4 numéros
        [4, "1 chance sur 14,83", 4, "1 chance sur 189", "50 €"],
        [4, "", 3, "1 chance sur 16", "5 €"],

        # 3 numéros
        [3, "1 chance sur 5,14", 3, "1 chance sur 48", "10 €"],
        [3, "", 2, "1 chance sur 6", "2 €"],

        # 2 numéros
        [2, "1 chance sur 12,71", 2, "1 chance sur 13", "6 €"]
    ]

    # Ajouter les données au tableau
    current_num_coches = None
    row_style = "Row.TLabel"

    for row_idx, row_data in enumerate(table_data, 1):
        # Alterner les styles pour les différents groupes de numéros cochés
        if current_num_coches != row_data[0]:
            current_num_coches = row_data[0]
            row_style = "Alt.Row.TLabel" if row_style == "Row.TLabel" else "Row.TLabel"

        # Style spécial pour les lignes avec le maximum de numéros trouvés
        special_style = False
        if row_data[0] == row_data[2]:  # Si numéros cochés = numéros trouvés
            special_style = True

        # Ajouter les cellules
        for col, cell_data in enumerate(row_data):
            # Ne pas répéter les valeurs dans certaines colonnes
            if col == 1 and row_idx > 1 and table_data[row_idx-2][0] == cell_data:
                cell_data = ""

            # Utiliser le style spécial pour les lignes importantes
            style_to_use = "Special.Row.TLabel" if special_style else row_style

            label = ttk.Label(table_frame, text=str(cell_data), style=style_to_use, anchor="center", width=25)
            label.grid(row=row_idx, column=col, sticky="nsew", padx=1, pady=1)

    # Configurer les poids des lignes et colonnes pour qu'elles s'étendent correctement
    for i in range(len(table_data) + 1):
        table_frame.rowconfigure(i, weight=1)
    for i in range(len(headers)):
        table_frame.columnconfigure(i, weight=1)

    # Note de bas de page
    note_text = "* Les probabilités sont données à titre indicatif et sont basées sur les informations officielles de la FDJ."
    note_label = tk.Label(scrollable_frame, text=note_text, font=('Helvetica', font_size-2, 'italic'),
                         bg="#ecf0f1", fg="#34495e")
    note_label.pack(pady=10)

    # Permettre le défilement avec la molette de la souris
    def _on_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    canvas.bind_all("<MouseWheel>", _on_mousewheel)
