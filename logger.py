"""Module de logging pour l'application"""

import logging
import os
from datetime import datetime
from config import LOGS_DIR

def setup_logger():
    """Configure et retourne le logger principal"""
    
    # Créer le nom du fichier de log avec la date
    log_file = os.path.join(LOGS_DIR, f"keno_{datetime.now().strftime('%Y%m%d')}.log")
    
    # Configurer le logger
    logger = logging.getLogger('keno')
    logger.setLevel(logging.INFO)
    
    # Handler pour fichier
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    
    # Handler pour console
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # Ajouter les handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

