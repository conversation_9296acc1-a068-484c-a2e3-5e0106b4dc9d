"""
Module pour gérer les retours d'expérience sur les prédictions Keno
Ce module permet d'enregistrer les résultats réels des prédictions et d'ajuster
les modèles en fonction de ces résultats.
"""

import os
import json
import datetime
import numpy as np
from collections import defaultdict

# Vérifier si l'agent RL est disponible
try:
    from keno_rl import KenoRLAgent
    rl_agent_available = True
except ImportError:
    rl_agent_available = False
    print("Module d'apprentissage par renforcement non disponible.")

class KenoFeedback:
    """Classe pour gérer les retours d'expérience sur les prédictions Keno"""

    def __init__(self, data_manager=None, advanced_analyzer=None):
        """Initialise le gestionnaire de feedback

        Args:
            data_manager: Gestionnaire de données Keno
            advanced_analyzer: Analyseur avancé pour les prédictions
        """
        self.data_manager = data_manager
        self.advanced_analyzer = advanced_analyzer
        self.feedback_data = {
            'predictions': [],
            'model_performance': defaultdict(list),
            'number_performance': defaultdict(list),
            'last_update': None
        }
        self.feedback_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'keno_feedback.json')

        # Initialiser l'agent d'apprentissage par renforcement
        self.rl_agent = None
        if rl_agent_available:
            try:
                # Créer l'agent RL avec les paramètres par défaut
                self.rl_agent = KenoRLAgent()
                print("Agent d'apprentissage par renforcement initialisé avec succès")
            except Exception as e:
                print(f"Erreur lors de l'initialisation de l'agent RL: {e}")

        # Charger les données de feedback
        self.load_feedback()

    def load_feedback(self):
        """Charge les données de feedback depuis le fichier"""
        try:
            if os.path.exists(self.feedback_file):
                with open(self.feedback_file, 'r') as f:
                    data = json.load(f)
                    # Convertir les defaultdict
                    self.feedback_data = {
                        'predictions': data.get('predictions', []),
                        'model_performance': defaultdict(list, data.get('model_performance', {})),
                        'number_performance': defaultdict(list, data.get('number_performance', {})),
                        'last_update': data.get('last_update')
                    }
                print(f"Données de feedback chargées depuis {self.feedback_file}")
                return True
            else:
                print("Aucun fichier de feedback trouvé, création d'un nouveau fichier.")
                return False
        except Exception as e:
            print(f"Erreur lors du chargement des données de feedback: {e}")
            return False

    def save_feedback(self):
        """Sauvegarde les données de feedback dans le fichier"""
        try:
            # Créer le répertoire si nécessaire
            os.makedirs(os.path.dirname(self.feedback_file), exist_ok=True)

            # Mettre à jour la date de dernière mise à jour
            self.feedback_data['last_update'] = datetime.datetime.now().isoformat()

            # Convertir les defaultdict en dict pour la sérialisation JSON
            data_to_save = {
                'predictions': self.feedback_data['predictions'],
                'model_performance': dict(self.feedback_data['model_performance']),
                'number_performance': dict(self.feedback_data['number_performance']),
                'last_update': self.feedback_data['last_update']
            }

            with open(self.feedback_file, 'w') as f:
                json.dump(data_to_save, f, indent=2)
            print(f"Données de feedback sauvegardées dans {self.feedback_file}")
            return True
        except Exception as e:
            print(f"Erreur lors de la sauvegarde des données de feedback: {e}")
            return False

    def add_prediction_result(self, prediction_method, predicted_numbers, actual_numbers, score):
        """Ajoute un résultat de prédiction

        Args:
            prediction_method (str): Méthode de prédiction utilisée (ml, advanced, etc.)
            predicted_numbers (list): Liste des numéros prédits
            actual_numbers (list): Liste des numéros réellement sortis
            score (tuple): Score obtenu (ex: (3, 7) pour 3 numéros corrects sur 7 prédits)

        Returns:
            bool: True si l'ajout a réussi, False sinon
        """
        try:
            # Créer l'entrée de prédiction
            prediction_entry = {
                'date': datetime.datetime.now().isoformat(),
                'method': prediction_method,
                'predicted': predicted_numbers,
                'actual': actual_numbers,
                'score': score,
                'accuracy': score[0] / score[1] if score[1] > 0 else 0
            }

            # Ajouter l'entrée à la liste des prédictions
            self.feedback_data['predictions'].append(prediction_entry)

            # Mettre à jour les performances du modèle
            self.feedback_data['model_performance'][prediction_method].append({
                'date': prediction_entry['date'],
                'score': score,
                'accuracy': prediction_entry['accuracy']
            })

            # Mettre à jour les performances des numéros
            correct_numbers = set(predicted_numbers).intersection(set(actual_numbers))
            incorrect_numbers = set(predicted_numbers) - set(actual_numbers)

            for num in correct_numbers:
                self.feedback_data['number_performance'][str(num)].append({
                    'date': prediction_entry['date'],
                    'method': prediction_method,
                    'result': 'correct'
                })

            for num in incorrect_numbers:
                self.feedback_data['number_performance'][str(num)].append({
                    'date': prediction_entry['date'],
                    'method': prediction_method,
                    'result': 'incorrect'
                })

            # Mettre à jour l'agent d'apprentissage par renforcement
            if self.rl_agent:
                try:
                    # Mettre à jour l'agent RL avec les résultats
                    reward = self.update_rl_agent(predicted_numbers, actual_numbers)
                    print(f"Agent RL mis à jour avec une récompense de {reward}")

                    # Ajouter les informations RL à l'entrée de prédiction
                    prediction_entry['rl_reward'] = reward
                except Exception as e:
                    print(f"Erreur lors de la mise à jour de l'agent RL: {e}")

            # Sauvegarder les données
            self.save_feedback()
            return True
        except Exception as e:
            print(f"Erreur lors de l'ajout du résultat de prédiction: {e}")
            return False

    def get_model_performance(self, method=None):
        """Récupère les performances d'un modèle

        Args:
            method (str, optional): Méthode de prédiction. Si None, retourne toutes les méthodes.

        Returns:
            dict: Performances du modèle
        """
        if method:
            return self.feedback_data['model_performance'].get(method, [])
        else:
            return dict(self.feedback_data['model_performance'])

    def get_number_performance(self, number=None):
        """Récupère les performances d'un numéro

        Args:
            number (int, optional): Numéro à analyser. Si None, retourne tous les numéros.

        Returns:
            dict: Performances du numéro
        """
        if number:
            return self.feedback_data['number_performance'].get(str(number), [])
        else:
            return dict(self.feedback_data['number_performance'])

    def get_recent_predictions(self, limit=10):
        """Récupère les prédictions récentes

        Args:
            limit (int, optional): Nombre de prédictions à récupérer. Par défaut 10.

        Returns:
            list: Liste des prédictions récentes
        """
        return sorted(self.feedback_data['predictions'],
                     key=lambda x: x['date'],
                     reverse=True)[:limit]

    def calculate_model_weights(self):
        """Calcule les poids à appliquer aux différents modèles en fonction de leurs performances

        Returns:
            dict: Poids à appliquer à chaque modèle
        """
        weights = {}

        # Récupérer les performances récentes (derniers 10 résultats) pour chaque modèle
        for method, performances in self.feedback_data['model_performance'].items():
            recent_performances = sorted(performances, key=lambda x: x['date'], reverse=True)[:10]
            if recent_performances:
                # Calculer la précision moyenne
                avg_accuracy = sum(p['accuracy'] for p in recent_performances) / len(recent_performances)
                weights[method] = avg_accuracy

        # Normaliser les poids pour qu'ils somment à 1
        total_weight = sum(weights.values())
        if total_weight > 0:
            for method in weights:
                weights[method] /= total_weight

        return weights

    def calculate_number_weights(self):
        """Calcule les poids à appliquer aux différents numéros en fonction de leurs performances

        Returns:
            dict: Poids à appliquer à chaque numéro
        """
        weights = {}

        # Récupérer les performances récentes (derniers 20 résultats) pour chaque numéro
        for number, performances in self.feedback_data['number_performance'].items():
            recent_performances = sorted(performances, key=lambda x: x['date'], reverse=True)[:20]
            if recent_performances:
                # Calculer le taux de succès
                correct_count = sum(1 for p in recent_performances if p['result'] == 'correct')
                success_rate = correct_count / len(recent_performances)
                weights[int(number)] = success_rate

        return weights

    def adjust_model_parameters(self):
        """Ajuste les paramètres des modèles en fonction des performances

        Returns:
            bool: True si l'ajustement a réussi, False sinon
        """
        if not self.advanced_analyzer:
            print("Aucun analyseur avancé disponible pour ajuster les paramètres")
            return False

        try:
            # Calculer les poids des modèles
            model_weights = self.calculate_model_weights()

            # Ajuster les paramètres des modèles
            if hasattr(self.advanced_analyzer, 'model_params'):
                # Récupérer les performances de Random Forest et XGBoost
                rf_performance = model_weights.get('ml', 0.5) if 'random_forest' in self.advanced_analyzer.model_params else 0
                xgb_performance = model_weights.get('advanced', 0.5) if 'xgboost' in self.advanced_analyzer.model_params else 0

                # Ajuster les paramètres en fonction des performances
                if rf_performance > 0 and xgb_performance > 0:
                    # Si Random Forest est plus performant, augmenter son poids
                    if rf_performance > xgb_performance:
                        rf_weight = 0.6
                        xgb_weight = 0.4
                    # Si XGBoost est plus performant, augmenter son poids
                    elif xgb_performance > rf_performance:
                        rf_weight = 0.4
                        xgb_weight = 0.6
                    # Sinon, poids égaux
                    else:
                        rf_weight = 0.5
                        xgb_weight = 0.5

                    # Mettre à jour les poids dans l'analyseur avancé
                    if hasattr(self.advanced_analyzer, 'ensemble_weights'):
                        self.advanced_analyzer.ensemble_weights = {
                            'random_forest': rf_weight,
                            'xgboost': xgb_weight
                        }

                    print(f"Poids ajustés: Random Forest = {rf_weight}, XGBoost = {xgb_weight}")

            return True
        except Exception as e:
            print(f"Erreur lors de l'ajustement des paramètres des modèles: {e}")
            return False

    def get_performance_summary(self):
        """Génère un résumé des performances

        Returns:
            dict: Résumé des performances
        """
        summary = {
            'models': {},
            'numbers': {},
            'overall': {
                'total_predictions': len(self.feedback_data['predictions']),
                'average_accuracy': 0
            }
        }

        # Calculer les performances des modèles
        for method, performances in self.feedback_data['model_performance'].items():
            if performances:
                # Calculer la précision moyenne
                avg_accuracy = sum(p['accuracy'] for p in performances) / len(performances)
                # Calculer la tendance (amélioration ou dégradation)
                if len(performances) >= 2:
                    recent_performances = sorted(performances, key=lambda x: x['date'], reverse=True)
                    recent_avg = sum(p['accuracy'] for p in recent_performances[:5]) / min(5, len(recent_performances))
                    older_avg = sum(p['accuracy'] for p in recent_performances[-5:]) / min(5, len(recent_performances[-5:]))
                    trend = recent_avg - older_avg
                else:
                    trend = 0

                summary['models'][method] = {
                    'count': len(performances),
                    'average_accuracy': avg_accuracy,
                    'trend': trend
                }

        # Calculer les performances des numéros
        for number, performances in self.feedback_data['number_performance'].items():
            if performances:
                correct_count = sum(1 for p in performances if p['result'] == 'correct')
                success_rate = correct_count / len(performances)

                summary['numbers'][number] = {
                    'count': len(performances),
                    'success_rate': success_rate,
                    'correct_count': correct_count
                }

        # Calculer la précision globale
        if self.feedback_data['predictions']:
            total_accuracy = sum(p['accuracy'] for p in self.feedback_data['predictions'])
            summary['overall']['average_accuracy'] = total_accuracy / len(self.feedback_data['predictions'])

        # Ajouter les statistiques de l'agent RL si disponible
        if self.rl_agent:
            try:
                rl_stats = self.rl_agent.get_performance_stats()
                summary['reinforcement_learning'] = rl_stats
            except Exception as e:
                print(f"Erreur lors de la récupération des statistiques RL: {e}")

        return summary

    def train_rl_agent(self, batch_size=32):
        """Entraîne l'agent d'apprentissage par renforcement

        Args:
            batch_size (int): Taille du batch d'entraînement

        Returns:
            float: Perte moyenne de l'entraînement
        """
        if not self.rl_agent:
            print("Agent d'apprentissage par renforcement non disponible")
            return 0

        try:
            # Entraîner l'agent
            loss = self.rl_agent.train(batch_size=batch_size)

            # Sauvegarder l'agent
            self.rl_agent.save()

            return loss
        except Exception as e:
            print(f"Erreur lors de l'entraînement de l'agent RL: {e}")
            return 0

    def predict_with_rl(self, num_predictions=7):
        """Prédit les prochains numéros en utilisant l'apprentissage par renforcement

        Args:
            num_predictions (int): Nombre de numéros à prédire

        Returns:
            list: Liste des numéros prédits
        """
        if not self.rl_agent:
            print("Agent d'apprentissage par renforcement non disponible")
            return []

        try:
            # Générer l'état actuel
            if not self.data_manager or not hasattr(self.data_manager, 'draws'):
                print("Gestionnaire de données non disponible")
                return []

            # Récupérer l'historique des tirages
            draw_history = []
            for draw in self.data_manager.draws[-20:]:  # Utiliser les 20 derniers tirages
                if hasattr(draw, 'draw_numbers'):
                    draw_history.append(draw.draw_numbers)

            # Récupérer les fréquences
            frequency_data = None
            if hasattr(self.data_manager, 'get_number_frequency'):
                frequency_data = self.data_manager.get_number_frequency()

            # Générer l'état
            state = self.rl_agent.get_state_from_history(draw_history, frequency_data)

            # Prédire les numéros
            predicted_numbers = self.rl_agent.act(state, num_predictions, exploration=False)

            return predicted_numbers
        except Exception as e:
            print(f"Erreur lors de la prédiction avec l'agent RL: {e}")
            return []

    def update_rl_agent(self, predicted_numbers, actual_numbers):
        """Met à jour l'agent RL avec les résultats réels

        Args:
            predicted_numbers (list): Numéros prédits
            actual_numbers (list): Numéros réels

        Returns:
            float: Récompense obtenue
        """
        if not self.rl_agent:
            print("Agent d'apprentissage par renforcement non disponible")
            return 0

        try:
            # Générer l'état avant la prédiction
            draw_history_before = []
            for draw in self.data_manager.draws[:-1]:  # Tous les tirages sauf le dernier
                if hasattr(draw, 'draw_numbers'):
                    draw_history_before.append(draw.draw_numbers)

            # Générer l'état après la prédiction
            draw_history_after = []
            for draw in self.data_manager.draws:  # Tous les tirages
                if hasattr(draw, 'draw_numbers'):
                    draw_history_after.append(draw.draw_numbers)

            # Récupérer les fréquences
            frequency_data = None
            if hasattr(self.data_manager, 'get_number_frequency'):
                frequency_data = self.data_manager.get_number_frequency()

            # Générer les états
            state = self.rl_agent.get_state_from_history(draw_history_before, frequency_data)
            next_state = self.rl_agent.get_state_from_history(draw_history_after, frequency_data)

            # Calculer la récompense
            reward = self.rl_agent.calculate_reward(predicted_numbers, actual_numbers)

            # Mettre à jour la mémoire de l'agent
            for num in predicted_numbers:
                self.rl_agent.remember(state, num, reward, next_state, True)

            # Entraîner l'agent
            self.train_rl_agent()

            return reward
        except Exception as e:
            print(f"Erreur lors de la mise à jour de l'agent RL: {e}")
            return 0
