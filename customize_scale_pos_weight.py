"""
Script pour personnaliser les poids scale_pos_weight pour chaque numéro Keno
Ce script permet de modifier manuellement les poids pour chaque numéro.
"""

import os
import sys
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

def load_current_weights():
    """
    Charge les poids actuels depuis le fichier JSON
    
    Returns:
        dict: Dictionnaire des poids par numéro
    """
    # Chemin du fichier
    file_path = os.path.join('data', 'forced_scale_pos_weight.json')
    
    # Vérifier si le fichier existe
    if not os.path.exists(file_path):
        print(f"Le fichier {file_path} n'existe pas")
        return {}
    
    # Charger les poids
    try:
        with open(file_path, 'r') as f:
            weights = json.load(f)
        
        # Convertir les clés en entiers
        weights = {int(k): v for k, v in weights.items()}
        
        print(f"Poids actuels chargés depuis {file_path}")
        return weights
    except Exception as e:
        print(f"Erreur lors du chargement des poids: {e}")
        return {}

def save_weights_to_json(weights, file_name='custom_scale_pos_weight.json'):
    """
    Sauvegarde les poids dans un fichier JSON
    
    Args:
        weights: Dictionnaire des poids par numéro
        file_name: Nom du fichier de sortie
        
    Returns:
        str: Chemin du fichier créé
    """
    # Créer le répertoire data s'il n'existe pas
    os.makedirs('data', exist_ok=True)
    
    # Chemin du fichier
    file_path = os.path.join('data', file_name)
    
    # Convertir les clés en chaînes pour la sérialisation JSON
    weights_str = {str(k): v for k, v in weights.items()}
    
    # Sauvegarder les poids
    with open(file_path, 'w') as f:
        json.dump(weights_str, f, indent=2)
    
    print(f"Poids sauvegardés dans {file_path}")
    
    return file_path

def update_injector(weights_file):
    """
    Met à jour le module d'injection pour utiliser les poids personnalisés
    
    Args:
        weights_file: Chemin du fichier de poids
        
    Returns:
        bool: True si la mise à jour a réussi, False sinon
    """
    # Chemin du module d'injection
    injector_file = 'force_scale_pos_weight_injector.py'
    
    # Vérifier si le fichier existe
    if not os.path.exists(injector_file):
        print(f"Le fichier {injector_file} n'existe pas")
        return False
    
    # Lire le contenu du fichier
    with open(injector_file, 'r') as f:
        content = f.read()
    
    # Extraire le nom du fichier de poids
    file_name = os.path.basename(weights_file)
    
    # Remplacer le chemin du fichier de poids
    new_content = content.replace('SPW_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), \'data\', \'forced_scale_pos_weight.json\')',
                                 f'SPW_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), \'data\', \'{file_name}\')')
    
    # Écrire le contenu modifié
    with open(injector_file, 'w') as f:
        f.write(new_content)
    
    print(f"Module d'injection mis à jour pour utiliser les poids personnalisés")
    
    return True

def customize_weights():
    """
    Permet à l'utilisateur de personnaliser les poids pour chaque numéro
    
    Returns:
        dict: Dictionnaire des poids personnalisés
    """
    # Charger les poids actuels
    weights = load_current_weights()
    
    if not weights:
        print("Impossible de charger les poids actuels")
        return {}
    
    # Créer un DataFrame pour l'affichage
    df = pd.DataFrame({
        'number': list(range(1, 71)),
        'scale_pos_weight': [weights.get(num, 2.57) for num in range(1, 71)]
    })
    
    # Afficher les poids actuels
    print("\nPoids actuels:")
    for num in range(1, 71):
        print(f"Numéro {num}: {weights.get(num, 2.57):.4f}")
    
    # Créer un graphique des poids
    plt.figure(figsize=(12, 6))
    plt.bar(df['number'], df['scale_pos_weight'])
    plt.axhline(y=df['scale_pos_weight'].mean(), color='r', linestyle='-', label=f'Moyenne ({df["scale_pos_weight"].mean():.4f})')
    plt.xlabel('Numéro Keno')
    plt.ylabel('scale_pos_weight')
    plt.title('Valeurs actuelles de scale_pos_weight')
    plt.legend()
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Sauvegarder le graphique
    plt.savefig('current_scale_pos_weight.png')
    print("Graphique sauvegardé dans current_scale_pos_weight.png")
    
    # Demander à l'utilisateur s'il souhaite personnaliser les poids
    print("\nVoulez-vous personnaliser les poids? (o/n)")
    choice = input().lower()
    
    if choice != 'o':
        print("Personnalisation annulée")
        return weights
    
    # Options de personnalisation
    print("\nOptions de personnalisation:")
    print("1. Modifier un numéro spécifique")
    print("2. Modifier une plage de numéros")
    print("3. Appliquer une formule à tous les numéros")
    print("4. Terminer la personnalisation")
    
    custom_weights = weights.copy()
    
    while True:
        print("\nChoisissez une option (1-4):")
        option = input()
        
        if option == '1':
            # Modifier un numéro spécifique
            print("Entrez le numéro à modifier (1-70):")
            try:
                num = int(input())
                if num < 1 or num > 70:
                    print("Numéro invalide")
                    continue
                
                print(f"Valeur actuelle pour le numéro {num}: {custom_weights.get(num, 2.57):.4f}")
                print("Entrez la nouvelle valeur:")
                try:
                    value = float(input())
                    custom_weights[num] = value
                    print(f"Valeur mise à jour pour le numéro {num}: {value:.4f}")
                except ValueError:
                    print("Valeur invalide")
            except ValueError:
                print("Numéro invalide")
        
        elif option == '2':
            # Modifier une plage de numéros
            print("Entrez le premier numéro de la plage (1-70):")
            try:
                start = int(input())
                if start < 1 or start > 70:
                    print("Numéro invalide")
                    continue
                
                print("Entrez le dernier numéro de la plage (1-70):")
                try:
                    end = int(input())
                    if end < 1 or end > 70 or end < start:
                        print("Numéro invalide")
                        continue
                    
                    print("Entrez la nouvelle valeur:")
                    try:
                        value = float(input())
                        for num in range(start, end + 1):
                            custom_weights[num] = value
                        print(f"Valeur mise à jour pour les numéros {start} à {end}: {value:.4f}")
                    except ValueError:
                        print("Valeur invalide")
                except ValueError:
                    print("Numéro invalide")
            except ValueError:
                print("Numéro invalide")
        
        elif option == '3':
            # Appliquer une formule à tous les numéros
            print("Options de formule:")
            print("1. Multiplier par un facteur")
            print("2. Ajouter une valeur")
            print("3. Soustraire une valeur")
            print("4. Définir une valeur minimale")
            print("5. Définir une valeur maximale")
            
            print("Choisissez une option (1-5):")
            formula_option = input()
            
            if formula_option == '1':
                print("Entrez le facteur de multiplication:")
                try:
                    factor = float(input())
                    for num in range(1, 71):
                        if num in custom_weights:
                            custom_weights[num] *= factor
                    print(f"Toutes les valeurs ont été multipliées par {factor:.4f}")
                except ValueError:
                    print("Valeur invalide")
            
            elif formula_option == '2':
                print("Entrez la valeur à ajouter:")
                try:
                    value = float(input())
                    for num in range(1, 71):
                        if num in custom_weights:
                            custom_weights[num] += value
                    print(f"La valeur {value:.4f} a été ajoutée à toutes les valeurs")
                except ValueError:
                    print("Valeur invalide")
            
            elif formula_option == '3':
                print("Entrez la valeur à soustraire:")
                try:
                    value = float(input())
                    for num in range(1, 71):
                        if num in custom_weights:
                            custom_weights[num] -= value
                    print(f"La valeur {value:.4f} a été soustraite de toutes les valeurs")
                except ValueError:
                    print("Valeur invalide")
            
            elif formula_option == '4':
                print("Entrez la valeur minimale:")
                try:
                    min_value = float(input())
                    for num in range(1, 71):
                        if num in custom_weights and custom_weights[num] < min_value:
                            custom_weights[num] = min_value
                    print(f"Toutes les valeurs inférieures à {min_value:.4f} ont été définies à {min_value:.4f}")
                except ValueError:
                    print("Valeur invalide")
            
            elif formula_option == '5':
                print("Entrez la valeur maximale:")
                try:
                    max_value = float(input())
                    for num in range(1, 71):
                        if num in custom_weights and custom_weights[num] > max_value:
                            custom_weights[num] = max_value
                    print(f"Toutes les valeurs supérieures à {max_value:.4f} ont été définies à {max_value:.4f}")
                except ValueError:
                    print("Valeur invalide")
            
            else:
                print("Option invalide")
        
        elif option == '4':
            # Terminer la personnalisation
            break
        
        else:
            print("Option invalide")
    
    # Créer un graphique des poids personnalisés
    plt.figure(figsize=(12, 6))
    plt.bar(range(1, 71), [custom_weights.get(num, 2.57) for num in range(1, 71)])
    plt.axhline(y=np.mean([custom_weights.get(num, 2.57) for num in range(1, 71)]), color='r', linestyle='-', label=f'Moyenne ({np.mean([custom_weights.get(num, 2.57) for num in range(1, 71)]):.4f})')
    plt.xlabel('Numéro Keno')
    plt.ylabel('scale_pos_weight')
    plt.title('Valeurs personnalisées de scale_pos_weight')
    plt.legend()
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Sauvegarder le graphique
    plt.savefig('custom_scale_pos_weight.png')
    print("Graphique sauvegardé dans custom_scale_pos_weight.png")
    
    return custom_weights

def main():
    """Fonction principale"""
    print("Personnalisation des poids scale_pos_weight pour chaque numéro Keno")
    
    # Personnaliser les poids
    custom_weights = customize_weights()
    
    if not custom_weights:
        print("Erreur lors de la personnalisation des poids")
        return
    
    # Sauvegarder les poids personnalisés
    weights_file = save_weights_to_json(custom_weights)
    
    # Mettre à jour le module d'injection
    update_injector(weights_file)
    
    print("\nPersonnalisation terminée!")
    print("Les poids scale_pos_weight personnalisés ont été sauvegardés.")
    print("Pour les utiliser, importez le module d'injection dans votre code:")
    print("  import force_scale_pos_weight_injector")

if __name__ == "__main__":
    main()
