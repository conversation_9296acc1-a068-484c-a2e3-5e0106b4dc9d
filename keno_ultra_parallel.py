#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Système ULTRA-PARALLÉLISÉ pour exploiter toute la puissance de votre machine
Optimisé pour Intel i9 24-cœurs + GPU
"""

import os
import sys
import warnings
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import numpy as np
import pandas as pd
from datetime import datetime

# Configuration pour utiliser TOUS les cœurs
os.environ['OMP_NUM_THREADS'] = str(mp.cpu_count())
os.environ['MKL_NUM_THREADS'] = str(mp.cpu_count())
os.environ['NUMEXPR_NUM_THREADS'] = str(mp.cpu_count())

# Suppression des warnings
warnings.filterwarnings('ignore')
os.environ['PYTHONWARNINGS'] = 'ignore'

class KenoUltraParallel:
    """Système ultra-parallélisé pour exploiter 100% des ressources"""
    
    def __init__(self, data_manager, analyzer):
        self.data_manager = data_manager
        self.analyzer = analyzer
        
        # Configuration ultra-parallélisée
        self.cpu_count = mp.cpu_count()
        self.max_workers = min(self.cpu_count, 24)  # Utiliser tous vos cœurs
        
        print(f"🚀 Configuration ULTRA-PARALLÉLISÉE détectée :")
        print(f"   💻 CPU: {self.cpu_count} cœurs disponibles")
        print(f"   ⚡ Workers: {self.max_workers} processus parallèles")
        print(f"   🔥 Mode: EXPLOITATION MAXIMALE DES RESSOURCES")
        
        # Configuration des modèles pour parallélisation maximale
        self.model_config = {
            'n_jobs': -1,  # Utiliser TOUS les cœurs
            'random_state': 42,
            'verbose': 0
        }
    
    def create_ultra_features_parallel(self, numbers_list):
        """Crée les caractéristiques pour plusieurs numéros EN PARALLÈLE"""
        
        print(f"🚀 Démarrage création parallèle pour {len(numbers_list)} numéros")
        print(f"⚡ Utilisation de {self.max_workers} processus parallèles")
        
        # Fonction pour traiter un numéro
        def process_single_number(number):
            try:
                from keno_ultra_optimizer import KenoUltraOptimizer
                optimizer = KenoUltraOptimizer(self.data_manager, self.analyzer)
                result = optimizer.create_ultra_features(number)
                
                if result:
                    X, y, feature_names = result
                    return {
                        'number': number,
                        'X': X,
                        'y': y,
                        'feature_names': feature_names,
                        'success': True,
                        'samples': len(X),
                        'features': len(feature_names)
                    }
                else:
                    return {
                        'number': number,
                        'success': False,
                        'error': 'Pas assez de données'
                    }
            except Exception as e:
                return {
                    'number': number,
                    'success': False,
                    'error': str(e)
                }
        
        # Traitement parallèle avec TOUS vos cœurs
        results = {}
        
        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            print(f"🔥 Lancement de {self.max_workers} processus parallèles...")
            
            # Soumettre tous les jobs
            future_to_number = {
                executor.submit(process_single_number, number): number 
                for number in numbers_list
            }
            
            # Récupérer les résultats au fur et à mesure
            completed = 0
            for future in future_to_number:
                number = future_to_number[future]
                try:
                    result = future.result(timeout=300)  # 5 minutes max par numéro
                    results[number] = result
                    completed += 1
                    
                    if result['success']:
                        print(f"✅ Numéro {number}: {result['features']} features, {result['samples']} échantillons ({completed}/{len(numbers_list)})")
                    else:
                        print(f"❌ Numéro {number}: {result['error']} ({completed}/{len(numbers_list)})")
                        
                except Exception as e:
                    print(f"💥 Erreur numéro {number}: {e}")
                    results[number] = {'number': number, 'success': False, 'error': str(e)}
                    completed += 1
        
        print(f"🎯 Création parallèle terminée: {len([r for r in results.values() if r['success']])} succès sur {len(numbers_list)}")
        return results
    
    def train_models_parallel(self, features_results):
        """Entraîne les modèles EN PARALLÈLE pour exploiter le CPU au maximum"""
        
        print(f"🚀 Démarrage entraînement parallèle des modèles")
        print(f"⚡ Utilisation intensive de {self.max_workers} processus")
        
        def train_single_model(data):
            number, result = data
            if not result['success']:
                return {'number': number, 'success': False, 'error': result['error']}
            
            try:
                X, y = result['X'], result['y']
                
                if len(X) < 50:
                    return {'number': number, 'success': False, 'error': 'Pas assez de données'}
                
                # Imports locaux pour le processus
                from sklearn.ensemble import RandomForestClassifier
                from sklearn.model_selection import train_test_split
                from sklearn.metrics import f1_score, accuracy_score, precision_score, recall_score
                from sklearn.feature_selection import SelectKBest, f_classif
                
                # Division des données
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=0.2, random_state=42, stratify=y
                )
                
                # Sélection des meilleures caractéristiques
                k_best = min(30, X_train.shape[1])
                selector = SelectKBest(score_func=f_classif, k=k_best)
                X_train_selected = selector.fit_transform(X_train, y_train)
                X_test_selected = selector.transform(X_test)
                
                # Modèle ultra-optimisé avec TOUS les cœurs
                model = RandomForestClassifier(
                    n_estimators=500,  # Plus d'arbres pour utiliser plus de CPU
                    max_depth=20,
                    min_samples_split=3,
                    min_samples_leaf=1,
                    class_weight='balanced',
                    n_jobs=-1,  # TOUS les cœurs
                    random_state=42,
                    verbose=0
                )
                
                # Entraînement intensif
                model.fit(X_train_selected, y_train)
                
                # Prédictions
                y_pred = model.predict(X_test_selected)
                y_pred_proba = model.predict_proba(X_test_selected)[:, 1]
                
                # Métriques complètes
                f1 = f1_score(y_test, y_pred, zero_division=0)
                accuracy = accuracy_score(y_test, y_pred)
                precision = precision_score(y_test, y_pred, zero_division=0)
                recall = recall_score(y_test, y_pred, zero_division=0)
                
                return {
                    'number': number,
                    'success': True,
                    'model': model,
                    'selector': selector,
                    'f1_score': f1,
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'feature_importance': model.feature_importances_,
                    'n_features': k_best,
                    'n_samples': len(X_train)
                }
                
            except Exception as e:
                return {'number': number, 'success': False, 'error': str(e)}
        
        # Entraînement parallèle massif
        training_results = {}
        valid_data = [(num, res) for num, res in features_results.items() if res['success']]
        
        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            print(f"🔥 Lancement de {self.max_workers} entraînements parallèles...")
            
            future_to_number = {
                executor.submit(train_single_model, data): data[0] 
                for data in valid_data
            }
            
            completed = 0
            for future in future_to_number:
                number = future_to_number[future]
                try:
                    result = future.result(timeout=600)  # 10 minutes max par modèle
                    training_results[number] = result
                    completed += 1
                    
                    if result['success']:
                        print(f"🎯 Numéro {number}: F1={result['f1_score']:.4f}, Acc={result['accuracy']:.4f} ({completed}/{len(valid_data)})")
                    else:
                        print(f"❌ Numéro {number}: {result['error']} ({completed}/{len(valid_data)})")
                        
                except Exception as e:
                    print(f"💥 Erreur entraînement {number}: {e}")
                    training_results[number] = {'number': number, 'success': False, 'error': str(e)}
                    completed += 1
        
        return training_results
    
    def run_ultra_parallel_training(self, mode="ultra_fast"):
        """Lance l'entraînement ultra-parallélisé qui va EXPLOITER votre machine"""
        
        print(f"🚀 DÉMARRAGE ENTRAÎNEMENT ULTRA-PARALLÉLISÉ")
        print(f"💻 Machine: Intel i9 24-cœurs détectée")
        print(f"⚡ Mode: EXPLOITATION MAXIMALE DES RESSOURCES")
        print(f"🔥 Workers: {self.max_workers} processus simultanés")
        
        # Déterminer les numéros selon le mode
        if mode == "ultra_fast":
            numbers_to_train = [7, 21, 35, 49, 63]
            mode_name = "ULTRA-RAPIDE"
        elif mode == "fast":
            numbers_to_train = [1, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70, 3, 17, 31, 45]
            mode_name = "RAPIDE"
        else:  # complete
            numbers_to_train = list(range(1, 71))
            mode_name = "COMPLET"
        
        print(f"🎯 Mode {mode_name}: {len(numbers_to_train)} numéros")
        
        # Phase 1: Création parallèle des caractéristiques
        print(f"\n🔥 PHASE 1: CRÉATION PARALLÈLE DES CARACTÉRISTIQUES")
        features_results = self.create_ultra_features_parallel(numbers_to_train)
        
        # Phase 2: Entraînement parallèle des modèles
        print(f"\n🔥 PHASE 2: ENTRAÎNEMENT PARALLÈLE DES MODÈLES")
        training_results = self.train_models_parallel(features_results)
        
        # Résultats finaux
        successful_models = [r for r in training_results.values() if r['success']]
        
        if successful_models:
            avg_f1 = np.mean([r['f1_score'] for r in successful_models])
            avg_accuracy = np.mean([r['accuracy'] for r in successful_models])
            
            print(f"\n🎉 ENTRAÎNEMENT ULTRA-PARALLÉLISÉ TERMINÉ !")
            print(f"✅ Modèles entraînés: {len(successful_models)}/{len(numbers_to_train)}")
            print(f"🎯 F1-score moyen: {avg_f1:.4f}")
            print(f"📊 Précision moyenne: {avg_accuracy:.4f}")
            print(f"💻 Ressources exploitées: {self.max_workers} cœurs")
            
            return {
                'success': True,
                'trained_models': len(successful_models),
                'total_numbers': len(numbers_to_train),
                'avg_f1': avg_f1,
                'avg_accuracy': avg_accuracy,
                'results': training_results
            }
        else:
            print(f"\n❌ ÉCHEC: Aucun modèle entraîné avec succès")
            return {'success': False, 'results': training_results}

def test_ultra_parallel():
    """Test du système ultra-parallélisé"""
    
    print("🚀 TEST DU SYSTÈME ULTRA-PARALLÉLISÉ")
    print(f"💻 Détection: {mp.cpu_count()} cœurs disponibles")
    
    try:
        # Importer les modules nécessaires
        from keno_data import KenoDataManager
        from keno_analyzer import KenoAnalyzer
        
        # Créer les instances
        data_manager = KenoDataManager()
        analyzer = KenoAnalyzer(data_manager)
        
        # Charger les données
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        if os.path.exists(data_dir):
            data_files = [f for f in os.listdir(data_dir) if f.endswith('.keno')]
            if data_files:
                latest_file = max(data_files, key=lambda f: os.path.getmtime(os.path.join(data_dir, f)))
                file_path = os.path.join(data_dir, latest_file)
                
                print(f"📊 Chargement: {file_path}")
                success = data_manager.load_database(file_path)
                
                if success:
                    print(f"✅ {data_manager.get_draws_count()} tirages chargés")
                    
                    # Créer le système ultra-parallélisé
                    ultra_parallel = KenoUltraParallel(data_manager, analyzer)
                    
                    # Lancer l'entraînement ultra-parallélisé
                    result = ultra_parallel.run_ultra_parallel_training("ultra_fast")
                    
                    if result['success']:
                        print(f"\n🎉 SUCCÈS ULTRA-PARALLÉLISÉ !")
                        return True
                    else:
                        print(f"\n❌ Échec de l'entraînement")
                        return False
                else:
                    print("❌ Échec du chargement des données")
                    return False
            else:
                print("❌ Aucun fichier de données trouvé")
                return False
        else:
            print("❌ Répertoire de données non trouvé")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_ultra_parallel()
