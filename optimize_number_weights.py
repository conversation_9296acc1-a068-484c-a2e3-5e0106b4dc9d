#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module pour optimiser les poids des numéros Keno
Ce module calcule les poids optimaux pour chaque numéro en fonction des données historiques
et utilise ces poids pour améliorer les prédictions.
"""

import os
import sys
import numpy as np
import pandas as pd
import json
import matplotlib.pyplot as plt
from collections import Counter, defaultdict
from sklearn.preprocessing import MinMaxScaler

# Ajouter le répertoire courant au chemin de recherche Python
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, BASE_DIR)

# Importer les modules nécessaires
from keno_data import KenoDataManager
from keno_advanced_analyzer import KenoAdvancedAnalyzer

# Supprimer les avertissements
import warnings
warnings.filterwarnings('ignore')

def calculate_historical_weights():
    """
    Calcule les poids historiques pour chaque numéro Keno
    en fonction de leur fréquence d'apparition
    """
    print("Calcul des poids historiques pour chaque numéro Keno...")

    # Charger les données
    data_manager = KenoDataManager()
    data_file = os.path.join(BASE_DIR, "data", "datafull.keno")

    # Vérifier si le fichier existe
    if not os.path.exists(data_file):
        print(f"Fichier de données {data_file} introuvable.")
        return None

    # Charger les données avec la méthode load_csv
    data_manager.load_csv(data_file, clear_existing=True)

    # Obtenir les tirages
    draws = data_manager.draws

    # Vérifier qu'il y a suffisamment de données
    if len(draws) < 10:
        print("Pas assez de données pour l'analyse (minimum 10 tirages)")
        return None

    # Compter la fréquence de chaque numéro
    number_counts = Counter()
    for draw in draws:
        if 'numbers' in draw:
            number_counts.update(draw['numbers'])

    # Calculer les poids en fonction de la fréquence
    total_draws = len(draws)
    weights = {}
    for num in range(1, data_manager.max_number + 1):
        count = number_counts.get(num, 0)
        frequency = count / total_draws
        weights[num] = frequency

    # Normaliser les poids entre 0.1 et 1.0
    min_weight = min(weights.values())
    max_weight = max(weights.values())
    normalized_weights = {}
    for num, weight in weights.items():
        # Normaliser entre 0.1 et 1.0
        normalized_weight = 0.1 + 0.9 * (weight - min_weight) / (max_weight - min_weight)
        normalized_weights[num] = normalized_weight

    # Sauvegarder les poids
    save_weights(normalized_weights)

    # Afficher les résultats
    print_weights_summary(normalized_weights)

    return normalized_weights

def calculate_recency_weights(window_size=100):
    """
    Calcule les poids de récence pour chaque numéro Keno
    en fonction de leur apparition récente

    Args:
        window_size (int): Taille de la fenêtre pour les tirages récents
    """
    print(f"Calcul des poids de récence pour chaque numéro Keno (fenêtre de {window_size} tirages)...")

    # Charger les données
    data_manager = KenoDataManager()
    data_file = os.path.join(BASE_DIR, "data", "datafull.keno")

    # Vérifier si le fichier existe
    if not os.path.exists(data_file):
        print(f"Fichier de données {data_file} introuvable.")
        return None

    # Charger les données avec la méthode load_csv
    data_manager.load_csv(data_file, clear_existing=True)

    # Obtenir les tirages
    draws = data_manager.draws

    # Vérifier qu'il y a suffisamment de données
    if len(draws) < window_size:
        print(f"Pas assez de données pour l'analyse (minimum {window_size} tirages)")
        return None

    # Obtenir les tirages récents
    recent_draws = draws[-window_size:]

    # Compter la fréquence de chaque numéro dans les tirages récents
    number_counts = Counter()
    for draw in recent_draws:
        if 'numbers' in draw:
            number_counts.update(draw['numbers'])

    # Calculer les poids en fonction de la fréquence récente
    weights = {}
    for num in range(1, data_manager.max_number + 1):
        count = number_counts.get(num, 0)
        frequency = count / window_size
        weights[num] = frequency

    # Normaliser les poids entre 0.1 et 1.0
    min_weight = min(weights.values())
    max_weight = max(weights.values())
    normalized_weights = {}
    for num, weight in weights.items():
        # Normaliser entre 0.1 et 1.0
        normalized_weight = 0.1 + 0.9 * (weight - min_weight) / (max_weight - min_weight)
        normalized_weights[num] = normalized_weight

    # Sauvegarder les poids
    save_weights(normalized_weights, filename="keno_recency_weights.json")

    # Afficher les résultats
    print_weights_summary(normalized_weights, title="Poids de récence")

    return normalized_weights

def calculate_combined_weights():
    """
    Calcule les poids combinés pour chaque numéro Keno
    en combinant les poids historiques et de récence
    """
    print("Calcul des poids combinés pour chaque numéro Keno...")

    # Charger les poids historiques
    historical_weights_file = os.path.join(BASE_DIR, "data", "keno_historical_weights.json")
    if not os.path.exists(historical_weights_file):
        print(f"Fichier de poids historiques {historical_weights_file} introuvable. Calcul des poids historiques...")
        historical_weights = calculate_historical_weights()
    else:
        with open(historical_weights_file, 'r') as f:
            historical_weights = json.load(f)

    # Charger les poids de récence
    recency_weights_file = os.path.join(BASE_DIR, "data", "keno_recency_weights.json")
    if not os.path.exists(recency_weights_file):
        print(f"Fichier de poids de récence {recency_weights_file} introuvable. Calcul des poids de récence...")
        recency_weights = calculate_recency_weights()
    else:
        with open(recency_weights_file, 'r') as f:
            recency_weights = json.load(f)

    # Combiner les poids (moyenne pondérée)
    combined_weights = {}
    for num in historical_weights.keys():
        # Convertir les clés en entiers
        num_int = int(num)
        # Pondération: 60% historique, 40% récence
        historical_weight = historical_weights[num]
        recency_weight = recency_weights.get(str(num_int), 0.1)
        combined_weight = 0.6 * historical_weight + 0.4 * recency_weight
        combined_weights[num_int] = combined_weight

    # Sauvegarder les poids combinés
    save_weights(combined_weights, filename="keno_combined_weights.json")

    # Afficher les résultats
    print_weights_summary(combined_weights, title="Poids combinés")

    return combined_weights

def save_weights(weights, filename="keno_historical_weights.json"):
    """
    Sauvegarde les poids dans un fichier JSON

    Args:
        weights (dict): Dictionnaire des poids
        filename (str): Nom du fichier de sortie
    """
    # Sauvegarder les poids
    output_file = os.path.join(BASE_DIR, "data", filename)
    with open(output_file, 'w') as f:
        json.dump(weights, f, indent=2)

    print(f"Poids sauvegardés dans {output_file}")

def print_weights_summary(weights, title="Poids historiques"):
    """
    Affiche un résumé des poids

    Args:
        weights (dict): Dictionnaire des poids
        title (str): Titre du résumé
    """
    print(f"\nRésumé des {title}:")

    # Convertir les clés en entiers
    weights_int = {int(k): v for k, v in weights.items()}

    # Trouver les numéros avec les poids les plus élevés
    top_numbers = sorted(weights_int.items(), key=lambda x: x[1], reverse=True)[:10]
    print("\nTop 10 des numéros avec les poids les plus élevés:")
    for num, weight in top_numbers:
        print(f"  Numéro {num}: {weight:.4f}")

    # Trouver les numéros avec les poids les plus faibles
    bottom_numbers = sorted(weights_int.items(), key=lambda x: x[1])[:10]
    print("\nTop 10 des numéros avec les poids les plus faibles:")
    for num, weight in bottom_numbers:
        print(f"  Numéro {num}: {weight:.4f}")

def optimize_scale_pos_weight_with_weights():
    """
    Optimise les valeurs de scale_pos_weight en utilisant les poids calculés
    """
    print("Optimisation des valeurs de scale_pos_weight en utilisant les poids calculés...")

    # Charger les poids combinés
    combined_weights_file = os.path.join(BASE_DIR, "data", "keno_combined_weights.json")
    if not os.path.exists(combined_weights_file):
        print(f"Fichier de poids combinés {combined_weights_file} introuvable. Calcul des poids combinés...")
        combined_weights = calculate_combined_weights()
    else:
        with open(combined_weights_file, 'r') as f:
            combined_weights = json.load(f)

    # Charger les valeurs de scale_pos_weight actuelles
    spw_file = os.path.join(BASE_DIR, "data", "direct_scale_pos_weight.json")
    if os.path.exists(spw_file):
        with open(spw_file, 'r') as f:
            spw_values = json.load(f)
    else:
        spw_values = {}

    # Ajuster les valeurs de scale_pos_weight en fonction des poids
    new_spw_values = {}
    for num, weight in combined_weights.items():
        num_str = str(num)
        if num_str in spw_values:
            # Ajuster la valeur en fonction du poids
            current_spw = float(spw_values[num_str])
            # Réduire scale_pos_weight pour les numéros avec des poids élevés
            # et augmenter pour les numéros avec des poids faibles
            new_spw = current_spw * (1.5 - weight)
            # Limiter la valeur entre 0.5 et 5.0
            new_spw = max(0.5, min(5.0, new_spw))
            new_spw_values[num_str] = new_spw
        else:
            # Utiliser une valeur par défaut ajustée en fonction du poids
            new_spw = 2.5 * (1.5 - weight)
            # Limiter la valeur entre 0.5 et 5.0
            new_spw = max(0.5, min(5.0, new_spw))
            new_spw_values[num_str] = new_spw

    # Sauvegarder les nouvelles valeurs
    output_file = os.path.join(BASE_DIR, "data", "optimized_scale_pos_weight.json")
    with open(output_file, 'w') as f:
        json.dump(new_spw_values, f, indent=2)

    print(f"Valeurs de scale_pos_weight optimisées sauvegardées dans {output_file}")

    return new_spw_values

if __name__ == "__main__":
    # Calculer les poids historiques
    historical_weights = calculate_historical_weights()

    # Calculer les poids de récence
    recency_weights = calculate_recency_weights()

    # Calculer les poids combinés
    combined_weights = calculate_combined_weights()

    # Optimiser les valeurs de scale_pos_weight
    optimize_scale_pos_weight_with_weights()
