import tkinter as tk
from tkinter import ttk, messagebox
import multiprocessing

class KenoConfigDialog:
    """Dialogue de configuration pour l'application Keno"""

    def __init__(self, parent, advanced_analyzer):
        """Initialise le dialogue de configuration

        Args:
            parent: Fenêtre parente
            advanced_analyzer: Instance de KenoAdvancedAnalyzer
        """
        self.parent = parent
        self.advanced_analyzer = advanced_analyzer

        # Créer une nouvelle fenêtre modale avec une taille beaucoup plus grande
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Configuration avancée")
        self.dialog.geometry("1200x900")  # Taille encore plus grande pour une meilleure lisibilité
        self.dialog.resizable(True, True)  # Permettre le redimensionnement
        self.dialog.minsize(1000, 800)  # Taille minimale augmentée
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Définir l'icône de la fenêtre si disponible
        try:
            self.dialog.iconbitmap("icon.ico")
        except:
            pass  # Ignorer si l'icône n'est pas disponible

        # Centrer la fenêtre
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

        # Définir la fonction pour basculer en mode plein écran
        def toggle_fullscreen():
            self.fullscreen = not self.fullscreen
            self.dialog.attributes("-fullscreen", self.fullscreen)
            if self.fullscreen:
                self.fullscreen_button.config(text="Quitter le plein écran (F11)")
            else:
                self.fullscreen_button.config(text="Plein écran (F11)")

        # Ajouter un raccourci clavier F11 pour le plein écran et Escape pour quitter
        self.dialog.bind("<F11>", lambda event: toggle_fullscreen())
        self.dialog.bind("<Escape>", lambda event: self.dialog.attributes("-fullscreen", False)
                         if self.fullscreen else None)

        # Créer un cadre pour le bouton de plein écran en haut
        top_frame = ttk.Frame(self.dialog)
        top_frame.pack(fill=tk.X, padx=10, pady=5)

        # Bouton pour passer en plein écran
        self.fullscreen_button = ttk.Button(top_frame, text="Plein écran (F11)",
                                         command=toggle_fullscreen, width=20)
        self.fullscreen_button.pack(side=tk.RIGHT, padx=10, pady=5)

        # Créer un cadre principal avec barres de défilement
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Ajouter des barres de défilement
        main_canvas = tk.Canvas(main_frame)
        scrollbar_y = ttk.Scrollbar(main_frame, orient="vertical", command=main_canvas.yview)
        scrollbar_x = ttk.Scrollbar(main_frame, orient="horizontal", command=main_canvas.xview)

        # Configurer le canvas pour utiliser les barres de défilement
        main_canvas.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # Placer les éléments dans la fenêtre
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        main_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Créer un cadre à l'intérieur du canvas pour contenir le contenu
        content_frame = ttk.Frame(main_canvas)
        canvas_window = main_canvas.create_window((0, 0), window=content_frame, anchor="nw")

        # Fonction pour ajuster la taille du canvas quand le contenu change
        def configure_canvas(event):
            main_canvas.configure(scrollregion=main_canvas.bbox("all"))
            # Ajuster la largeur du cadre de contenu à celle du canvas
            main_canvas.itemconfig(canvas_window, width=main_canvas.winfo_width())

        # Lier la fonction aux événements de redimensionnement
        content_frame.bind("<Configure>", configure_canvas)
        main_canvas.bind("<Configure>", lambda e: main_canvas.itemconfig(canvas_window, width=main_canvas.winfo_width()))

        # Créer un notebook (onglets) avec une taille plus grande
        self.notebook = ttk.Notebook(content_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

        # Onglet 1: Configuration du matériel
        self.tab_hardware = ttk.Frame(self.notebook)
        self.notebook.add(self.tab_hardware, text="Matériel")
        self._create_hardware_tab()

        # Onglet 2: Configuration des modèles
        self.tab_models = ttk.Frame(self.notebook)
        self.notebook.add(self.tab_models, text="Modèles")
        self._create_models_tab()

        # Initialiser la variable pour le mode plein écran
        self.fullscreen = False

        # Boutons en bas avec style amélioré - Utiliser un cadre fixe en bas de la fenêtre
        self.button_frame = ttk.Frame(self.dialog)
        self.button_frame.pack(fill=tk.X, padx=20, pady=20, side=tk.BOTTOM)

        # Style pour les boutons plus grands
        style = ttk.Style()
        style.configure('Big.TButton', font=('Helvetica', 12, 'bold'), padding=10)
        style.configure('Apply.TButton', font=('Helvetica', 12, 'bold'), padding=10, background='#4CAF50', foreground='white')

        # Fonction pour appliquer les paramètres et fermer la fenêtre
        def apply_and_close():
            self._apply_settings()
            self.dialog.destroy()

        # Bouton Appliquer et fermer (principal)
        self.apply_close_button = ttk.Button(self.button_frame, text="Appliquer et fermer",
                                          command=apply_and_close, style='Apply.TButton', width=20)
        self.apply_close_button.pack(side=tk.RIGHT, padx=15)

        # Bouton Appliquer (sans fermer)
        self.save_button = ttk.Button(self.button_frame, text="Appliquer", command=self._apply_settings,
                                    style='Big.TButton', width=15)
        self.save_button.pack(side=tk.RIGHT, padx=15)

        # Bouton Fermer (sans appliquer)
        self.cancel_button = ttk.Button(self.button_frame, text="Fermer", command=self.dialog.destroy,
                                      style='Big.TButton', width=15)
        self.cancel_button.pack(side=tk.RIGHT, padx=15)

        # Initialiser les valeurs
        self._init_values()

    def _create_hardware_tab(self):
        """Crée l'onglet de configuration du matériel"""
        # Style pour les éléments plus grands
        style = ttk.Style()
        style.configure('Big.TLabel', font=('Helvetica', 12))
        style.configure('Big.TLabelframe.Label', font=('Helvetica', 12, 'bold'))
        style.configure('Big.TLabelframe', padding=10)
        style.configure('Big.TCombobox', font=('Helvetica', 12), padding=5)

        # Frame pour les options CPU avec plus d'espace
        cpu_frame = ttk.LabelFrame(self.tab_hardware, text="Configuration CPU", style='Big.TLabelframe')
        cpu_frame.pack(fill=tk.X, padx=20, pady=20)

        # Nombre de cœurs CPU avec étiquette plus grande
        ttk.Label(cpu_frame, text="Nombre de cœurs CPU à utiliser:", style='Big.TLabel').grid(row=0, column=0, sticky=tk.W, padx=10, pady=15)

        # Déterminer le nombre de cœurs disponibles
        num_cores = multiprocessing.cpu_count()

        # Créer une liste d'options: -1 (tous), 1, 2, ..., num_cores
        core_options = ["-1 (Tous)"] + [str(i) for i in range(1, num_cores + 1)]

        self.cpu_cores_var = tk.StringVar()
        self.cpu_cores_combo = ttk.Combobox(cpu_frame, textvariable=self.cpu_cores_var, values=core_options, state="readonly", width=15, font=('Helvetica', 12))
        self.cpu_cores_combo.grid(row=0, column=1, sticky=tk.W, padx=10, pady=15)

        ttk.Label(cpu_frame, text=f"({num_cores} cœurs détectés)", style='Big.TLabel').grid(row=0, column=2, sticky=tk.W, padx=10, pady=15)

        # Nombre maximum de workers pour le traitement parallèle
        ttk.Label(cpu_frame, text="Nombre de workers parallèles:", style='Big.TLabel').grid(row=1, column=0, sticky=tk.W, padx=10, pady=15)

        # Créer une liste des options de workers
        workers_options = ["Auto"] + [str(i) for i in range(1, max(num_cores * 2, 17) + 1)]  # Permettre jusqu'à 2x le nombre de cœurs ou au moins 16
        self.max_workers_var = tk.StringVar(value="Auto")
        self.max_workers_combo = ttk.Combobox(cpu_frame, textvariable=self.max_workers_var,
                                           values=workers_options, state="readonly",
                                           width=15, font=('Helvetica', 12))
        self.max_workers_combo.grid(row=1, column=1, sticky=tk.W, padx=10, pady=15)

        # Description du nombre de workers
        ttk.Label(cpu_frame, text=f"(Recommandé: {num_cores} workers)", style='Big.TLabel').grid(row=1, column=2, sticky=tk.W, padx=10, pady=15)

        # Frame pour les options d'accélération matérielle
        accel_frame = ttk.LabelFrame(self.tab_hardware, text="Accélération matérielle", style='Big.TLabelframe')
        accel_frame.pack(fill=tk.X, padx=20, pady=20)

        # Mode d'accélération
        ttk.Label(accel_frame, text="Mode d'accélération:", style='Big.TLabel').grid(row=0, column=0, sticky=tk.W, padx=10, pady=15)

        self.accel_mode_var = tk.StringVar(value="auto")
        self.accel_mode_combo = ttk.Combobox(accel_frame, textvariable=self.accel_mode_var,
                                           values=["auto", "cpu", "gpu", "npu"], state="readonly",
                                           width=15, font=('Helvetica', 12))
        self.accel_mode_combo.grid(row=0, column=1, sticky=tk.W, padx=10, pady=15)

        # Description du mode
        self.accel_desc_var = tk.StringVar(value="Détection automatique du meilleur accélérateur")
        ttk.Label(accel_frame, textvariable=self.accel_desc_var, style='Big.TLabel').grid(row=0, column=2, sticky=tk.W, padx=10, pady=15)

        # Ajouter un gestionnaire d'événement pour mettre à jour la description
        def update_accel_desc(*args):
            # La fonction est appelée par tkinter avec des arguments supplémentaires
            # que nous n'utilisons pas, donc nous utilisons *args pour les capturer
            mode = self.accel_mode_var.get()
            if mode == "auto":
                self.accel_desc_var.set("Détection automatique du meilleur accélérateur")
            elif mode == "cpu":
                self.accel_desc_var.set("Utilisation du CPU uniquement (plus stable)")
            elif mode == "gpu":
                self.accel_desc_var.set("Utilisation du GPU si disponible (plus rapide)")
            elif mode == "npu":
                self.accel_desc_var.set("Utilisation du NPU si disponible (optimisé pour IA)")

        self.accel_mode_var.trace_add("write", update_accel_desc)

        # Bouton pour tester les accélérateurs
        self.test_accel_button = ttk.Button(accel_frame, text="Détecter les accélérateurs",
                                          command=self._test_accelerators, style='Big.TButton', width=25)
        self.test_accel_button.grid(row=1, column=0, sticky=tk.W, padx=10, pady=15)

        # Statut des accélérateurs
        self.accel_status_frame = ttk.Frame(accel_frame)
        self.accel_status_frame.grid(row=1, column=1, columnspan=2, sticky=tk.W, padx=10, pady=15)

        # Variables pour le statut des accélérateurs
        self.cpu_status_var = tk.StringVar(value="Disponible")
        self.gpu_status_var = tk.StringVar(value="Non testé")
        self.npu_status_var = tk.StringVar(value="Non testé")
        self.active_accel_var = tk.StringVar(value="CPU")

        # Affichage du statut
        ttk.Label(self.accel_status_frame, text="CPU:", style='Big.TLabel').grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(self.accel_status_frame, textvariable=self.cpu_status_var, style='Big.TLabel').grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.accel_status_frame, text="GPU:", style='Big.TLabel').grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(self.accel_status_frame, textvariable=self.gpu_status_var, style='Big.TLabel').grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.accel_status_frame, text="NPU:", style='Big.TLabel').grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(self.accel_status_frame, textvariable=self.npu_status_var, style='Big.TLabel').grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.accel_status_frame, text="Actif:", style='Big.TLabel').grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(self.accel_status_frame, textvariable=self.active_accel_var, style='Big.TLabel').grid(row=3, column=1, sticky=tk.W, padx=5, pady=2)

        # Ajouter une description explicative
        description_frame = ttk.Frame(self.tab_hardware)
        description_frame.pack(fill=tk.X, padx=20, pady=20)

        description_text = """L'accélération matérielle peut considérablement améliorer les performances:

- Mode AUTO: Détecte et utilise automatiquement le meilleur accélérateur disponible
- Mode CPU: Utilise uniquement le processeur (plus stable mais plus lent)
- Mode GPU: Utilise la carte graphique pour accélérer les calculs (plus rapide)
- Mode NPU: Utilise le processeur neuronal dédié à l'IA (très rapide si disponible)

Le nombre de cœurs CPU détermine combien de processus parallèles seront utilisés.
Utiliser tous les cœurs (-1) offre les meilleures performances, mais peut ralentir d'autres applications."""

        description_label = ttk.Label(description_frame, text=description_text, wraplength=700,
                                    justify=tk.LEFT, style='Big.TLabel')
        description_label.pack(fill=tk.X, pady=10)

        # Boutons de contrôle pour l'onglet Matériel
        hardware_button_frame = ttk.Frame(self.tab_hardware)
        hardware_button_frame.pack(fill=tk.X, padx=20, pady=20)

        # Style pour les boutons
        style = ttk.Style()
        style.configure('Validate.TButton', font=('Helvetica', 12, 'bold'), foreground='#006600')

        # Bouton de validation pour l'onglet Matériel
        self.hardware_validate_button = ttk.Button(hardware_button_frame, text="Valider les paramètres matériel",
                                                command=self._apply_settings, style='Validate.TButton', width=30)
        self.hardware_validate_button.pack(side=tk.RIGHT, padx=10, pady=10)

    def _create_models_tab(self):
        """Crée l'onglet de configuration des modèles"""
        # Frame pour les options de Deep Learning avec style amélioré
        dl_frame = ttk.LabelFrame(self.tab_models, text="Deep Learning", style='Big.TLabelframe')
        dl_frame.pack(fill=tk.X, padx=20, pady=20)

        # Créer une grille avec plus d'espace
        for i in range(3):
            dl_frame.columnconfigure(i, weight=1, minsize=150)

        # Ajouter une section pour activer/désactiver les modèles de deep learning
        dl_models_frame = ttk.LabelFrame(dl_frame, text="Modèles à utiliser", style='Big.TLabelframe')
        dl_models_frame.grid(row=0, column=0, columnspan=3, sticky=tk.W+tk.E, padx=10, pady=15)

        # Variables pour activer/désactiver les modèles
        self.use_dl_simple_var = tk.BooleanVar(value=True)
        self.use_dl_deep_var = tk.BooleanVar(value=True)
        self.use_dl_cnn_var = tk.BooleanVar(value=True)
        self.use_dl_ensemble_var = tk.BooleanVar(value=True)

        # Style pour les cases à cocher plus grandes
        style = ttk.Style()
        style.configure('Big.TCheckbutton', font=('Helvetica', 12))

        # Créer un cadre pour les cases à cocher avec plus d'espace
        check_frame = ttk.Frame(dl_models_frame, padding=10)
        check_frame.pack(fill=tk.BOTH, expand=True)

        # Cases à cocher pour chaque type de modèle avec descriptions détaillées
        cb1 = ttk.Checkbutton(check_frame, text="Modèle simple (rapide)",
                       variable=self.use_dl_simple_var, style='Big.TCheckbutton')
        cb1.grid(row=0, column=0, sticky=tk.W, padx=20, pady=10)
        ttk.Label(check_frame, text="Temps d'entraînement: Rapide | Précision: Moyenne",
                font=('Helvetica', 10, 'italic')).grid(row=0, column=1, sticky=tk.W, padx=10)

        cb2 = ttk.Checkbutton(check_frame, text="Modèle profond (plus précis)",
                       variable=self.use_dl_deep_var, style='Big.TCheckbutton')
        cb2.grid(row=1, column=0, sticky=tk.W, padx=20, pady=10)
        ttk.Label(check_frame, text="Temps d'entraînement: Moyen | Précision: Bonne",
                font=('Helvetica', 10, 'italic')).grid(row=1, column=1, sticky=tk.W, padx=10)

        cb3 = ttk.Checkbutton(check_frame, text="Modèle CNN (lent mais précis)",
                       variable=self.use_dl_cnn_var, style='Big.TCheckbutton')
        cb3.grid(row=2, column=0, sticky=tk.W, padx=20, pady=10)
        ttk.Label(check_frame, text="Temps d'entraînement: Lent | Précision: Très bonne",
                font=('Helvetica', 10, 'italic')).grid(row=2, column=1, sticky=tk.W, padx=10)

        cb4 = ttk.Checkbutton(check_frame, text="Ensemble de modèles (très lent)",
                       variable=self.use_dl_ensemble_var, style='Big.TCheckbutton')
        cb4.grid(row=3, column=0, sticky=tk.W, padx=20, pady=10)
        ttk.Label(check_frame, text="Temps d'entraînement: Très lent | Précision: Excellente",
                font=('Helvetica', 10, 'italic')).grid(row=3, column=1, sticky=tk.W, padx=10)

        # Ajouter une séparation
        ttk.Separator(check_frame, orient='horizontal').grid(row=4, column=0, columnspan=2, sticky=tk.E+tk.W, pady=10)

        # Bouton pour tout sélectionner/désélectionner avec style amélioré
        def toggle_all_models():
            new_state = not (self.use_dl_simple_var.get() and self.use_dl_deep_var.get() and
                           self.use_dl_cnn_var.get() and self.use_dl_ensemble_var.get())
            self.use_dl_simple_var.set(new_state)
            self.use_dl_deep_var.set(new_state)
            self.use_dl_cnn_var.set(new_state)
            self.use_dl_ensemble_var.set(new_state)

        button_frame = ttk.Frame(check_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=10)

        ttk.Button(button_frame, text="Tout sélectionner/désélectionner",
                  command=toggle_all_models, style='Big.TButton', width=30).pack(pady=5)

        # Créer un cadre pour les paramètres numériques
        params_frame = ttk.LabelFrame(dl_frame, text="Paramètres d'entraînement", style='Big.TLabelframe')
        params_frame.grid(row=1, column=0, columnspan=3, sticky=tk.W+tk.E, padx=10, pady=15)

        # Configurer les colonnes du cadre des paramètres
        params_frame.columnconfigure(0, weight=2, minsize=250)  # Colonne du label
        params_frame.columnconfigure(1, weight=1, minsize=100)  # Colonne de l'entrée
        params_frame.columnconfigure(2, weight=2, minsize=200)  # Colonne de la recommandation

        # Nombre d'époques
        ttk.Label(params_frame, text="Nombre d'époques:", style='Big.TLabel').grid(row=0, column=0, sticky=tk.W, padx=20, pady=15)
        self.dl_epochs_var = tk.StringVar()
        self.dl_epochs_entry = ttk.Entry(params_frame, textvariable=self.dl_epochs_var, width=15, font=('Helvetica', 12))
        self.dl_epochs_entry.grid(row=0, column=1, sticky=tk.W, padx=10, pady=15)
        ttk.Label(params_frame, text="(Recommandé: 20)", style='Big.TLabel', foreground='#0066cc').grid(row=0, column=2, sticky=tk.W, padx=10, pady=15)

        # Taille du lot (batch size)
        ttk.Label(params_frame, text="Taille du lot (batch size):", style='Big.TLabel').grid(row=1, column=0, sticky=tk.W, padx=20, pady=15)
        self.dl_batch_size_var = tk.StringVar()
        self.dl_batch_size_entry = ttk.Entry(params_frame, textvariable=self.dl_batch_size_var, width=15, font=('Helvetica', 12))
        self.dl_batch_size_entry.grid(row=1, column=1, sticky=tk.W, padx=10, pady=15)
        ttk.Label(params_frame, text="(Recommandé: 32)", style='Big.TLabel', foreground='#0066cc').grid(row=1, column=2, sticky=tk.W, padx=10, pady=15)

        # Nombre de neurones
        ttk.Label(params_frame, text="Nombre de neurones:", style='Big.TLabel').grid(row=2, column=0, sticky=tk.W, padx=20, pady=15)
        self.dl_neurons_var = tk.StringVar()
        self.dl_neurons_entry = ttk.Entry(params_frame, textvariable=self.dl_neurons_var, width=15, font=('Helvetica', 12))
        self.dl_neurons_entry.grid(row=2, column=1, sticky=tk.W, padx=10, pady=15)
        ttk.Label(params_frame, text="(Recommandé: 64)", style='Big.TLabel', foreground='#0066cc').grid(row=2, column=2, sticky=tk.W, padx=10, pady=15)

        # Taux de dropout
        ttk.Label(params_frame, text="Taux de dropout:", style='Big.TLabel').grid(row=3, column=0, sticky=tk.W, padx=20, pady=15)
        self.dl_dropout_var = tk.StringVar()
        self.dl_dropout_entry = ttk.Entry(params_frame, textvariable=self.dl_dropout_var, width=15, font=('Helvetica', 12))
        self.dl_dropout_entry.grid(row=3, column=1, sticky=tk.W, padx=10, pady=15)
        ttk.Label(params_frame, text="(Recommandé: 0.2)", style='Big.TLabel', foreground='#0066cc').grid(row=3, column=2, sticky=tk.W, padx=10, pady=15)

        # Ajouter une explication des paramètres
        explanation_frame = ttk.Frame(dl_frame, padding=10)
        explanation_frame.grid(row=2, column=0, columnspan=3, sticky=tk.W+tk.E, padx=10, pady=15)

        explanation_text = """Note: Augmenter le nombre d'époques améliore la précision mais ralentit l'entraînement.
        Une taille de lot plus grande accélère l'entraînement mais peut réduire la précision.
        Le taux de dropout aide à prévenir le sur-apprentissage."""

        ttk.Label(explanation_frame, text=explanation_text, style='Big.TLabel',
                 font=('Helvetica', 10, 'italic'), wraplength=800, justify=tk.LEFT).pack(fill=tk.X)

        # Frame pour les options de Random Forest avec style amélioré
        rf_frame = ttk.LabelFrame(self.tab_models, text="Random Forest", style='Big.TLabelframe')
        rf_frame.pack(fill=tk.X, padx=20, pady=20)

        # Créer une grille avec plus d'espace
        for i in range(3):
            rf_frame.columnconfigure(i, weight=1, minsize=150)

        # Variable pour activer/désactiver Random Forest
        self.use_rf_var = tk.BooleanVar(value=True)

        # Case à cocher pour activer/désactiver Random Forest
        rf_check_frame = ttk.Frame(rf_frame, padding=10)
        rf_check_frame.pack(fill=tk.X, expand=False)

        rf_cb = ttk.Checkbutton(rf_check_frame, text="Utiliser Random Forest",
                       variable=self.use_rf_var, style='Big.TCheckbutton')
        rf_cb.pack(side=tk.LEFT, padx=20, pady=10)
        ttk.Label(rf_check_frame, text="(Recommandé: Activé)",
                font=('Helvetica', 10, 'italic')).pack(side=tk.LEFT, padx=10)

        # Séparateur
        ttk.Separator(rf_frame, orient='horizontal').pack(fill=tk.X, padx=20, pady=5)

        # Créer un cadre pour les paramètres
        rf_params_frame = ttk.Frame(rf_frame, padding=10)
        rf_params_frame.pack(fill=tk.BOTH, expand=True)

        # Configurer les colonnes
        rf_params_frame.columnconfigure(0, weight=2, minsize=250)  # Colonne du label
        rf_params_frame.columnconfigure(1, weight=1, minsize=100)  # Colonne de l'entrée
        rf_params_frame.columnconfigure(2, weight=2, minsize=200)  # Colonne de la recommandation

        ttk.Label(rf_params_frame, text="Nombre d'arbres:", style='Big.TLabel').grid(row=0, column=0, sticky=tk.W, padx=20, pady=15)
        self.rf_n_estimators_var = tk.StringVar()
        self.rf_n_estimators_entry = ttk.Entry(rf_params_frame, textvariable=self.rf_n_estimators_var, width=15, font=('Helvetica', 12))
        self.rf_n_estimators_entry.grid(row=0, column=1, sticky=tk.W, padx=10, pady=15)
        ttk.Label(rf_params_frame, text="(Recommandé: 300)", style='Big.TLabel', foreground='#0066cc').grid(row=0, column=2, sticky=tk.W, padx=10, pady=15)

        ttk.Label(rf_params_frame, text="Profondeur maximale:", style='Big.TLabel').grid(row=1, column=0, sticky=tk.W, padx=20, pady=15)
        self.rf_max_depth_var = tk.StringVar()
        self.rf_max_depth_entry = ttk.Entry(rf_params_frame, textvariable=self.rf_max_depth_var, width=15, font=('Helvetica', 12))
        self.rf_max_depth_entry.grid(row=1, column=1, sticky=tk.W, padx=10, pady=15)
        ttk.Label(rf_params_frame, text="(Recommandé: 15)", style='Big.TLabel', foreground='#0066cc').grid(row=1, column=2, sticky=tk.W, padx=10, pady=15)

        # Ajouter une explication
        rf_explanation = ttk.Label(rf_params_frame, text="Random Forest est un algorithme robuste qui fonctionne bien sur divers types de données.",
                                 font=('Helvetica', 10, 'italic'), wraplength=800, justify=tk.LEFT)
        rf_explanation.grid(row=2, column=0, columnspan=3, sticky=tk.W, padx=20, pady=10)

        # Frame pour les options de XGBoost avec style amélioré
        xgb_frame = ttk.LabelFrame(self.tab_models, text="XGBoost", style='Big.TLabelframe')
        xgb_frame.pack(fill=tk.X, padx=20, pady=20)

        # Créer une grille avec plus d'espace
        for i in range(3):
            xgb_frame.columnconfigure(i, weight=1, minsize=150)

        # Variable pour activer/désactiver XGBoost
        self.use_xgb_var = tk.BooleanVar(value=True)

        # Case à cocher pour activer/désactiver XGBoost
        xgb_check_frame = ttk.Frame(xgb_frame, padding=10)
        xgb_check_frame.pack(fill=tk.X, expand=False)

        xgb_cb = ttk.Checkbutton(xgb_check_frame, text="Utiliser XGBoost",
                       variable=self.use_xgb_var, style='Big.TCheckbutton')
        xgb_cb.pack(side=tk.LEFT, padx=20, pady=10)
        ttk.Label(xgb_check_frame, text="(Recommandé: Activé)",
                font=('Helvetica', 10, 'italic')).pack(side=tk.LEFT, padx=10)

        # Séparateur
        ttk.Separator(xgb_frame, orient='horizontal').pack(fill=tk.X, padx=20, pady=5)

        # Créer un cadre pour les paramètres
        xgb_params_frame = ttk.Frame(xgb_frame, padding=10)
        xgb_params_frame.pack(fill=tk.BOTH, expand=True)

        # Configurer les colonnes
        xgb_params_frame.columnconfigure(0, weight=2, minsize=250)  # Colonne du label
        xgb_params_frame.columnconfigure(1, weight=1, minsize=100)  # Colonne de l'entrée
        xgb_params_frame.columnconfigure(2, weight=2, minsize=200)  # Colonne de la recommandation

        ttk.Label(xgb_params_frame, text="Nombre d'arbres:", style='Big.TLabel').grid(row=0, column=0, sticky=tk.W, padx=20, pady=15)
        self.xgb_n_estimators_var = tk.StringVar()
        self.xgb_n_estimators_entry = ttk.Entry(xgb_params_frame, textvariable=self.xgb_n_estimators_var, width=15, font=('Helvetica', 12))
        self.xgb_n_estimators_entry.grid(row=0, column=1, sticky=tk.W, padx=10, pady=15)
        ttk.Label(xgb_params_frame, text="(Recommandé: 300)", style='Big.TLabel', foreground='#0066cc').grid(row=0, column=2, sticky=tk.W, padx=10, pady=15)

        ttk.Label(xgb_params_frame, text="Profondeur maximale:", style='Big.TLabel').grid(row=1, column=0, sticky=tk.W, padx=20, pady=15)
        self.xgb_max_depth_var = tk.StringVar()
        self.xgb_max_depth_entry = ttk.Entry(xgb_params_frame, textvariable=self.xgb_max_depth_var, width=15, font=('Helvetica', 12))
        self.xgb_max_depth_entry.grid(row=1, column=1, sticky=tk.W, padx=10, pady=15)
        ttk.Label(xgb_params_frame, text="(Recommandé: 8)", style='Big.TLabel', foreground='#0066cc').grid(row=1, column=2, sticky=tk.W, padx=10, pady=15)

        ttk.Label(xgb_params_frame, text="Taux d'apprentissage:", style='Big.TLabel').grid(row=2, column=0, sticky=tk.W, padx=20, pady=15)
        self.xgb_learning_rate_var = tk.StringVar()
        self.xgb_learning_rate_entry = ttk.Entry(xgb_params_frame, textvariable=self.xgb_learning_rate_var, width=15, font=('Helvetica', 12))
        self.xgb_learning_rate_entry.grid(row=2, column=1, sticky=tk.W, padx=10, pady=15)
        ttk.Label(xgb_params_frame, text="(Recommandé: 0.03)", style='Big.TLabel', foreground='#0066cc').grid(row=2, column=2, sticky=tk.W, padx=10, pady=15)

        # Ajouter une explication
        xgb_explanation = ttk.Label(xgb_params_frame, text="XGBoost est un algorithme puissant qui offre souvent les meilleures performances prédictives.",
                                  font=('Helvetica', 10, 'italic'), wraplength=800, justify=tk.LEFT)
        xgb_explanation.grid(row=3, column=0, columnspan=3, sticky=tk.W, padx=20, pady=10)

        # Ajouter un cadre pour le bouton de sélection des modèles ML
        ml_button_frame = ttk.Frame(self.tab_models)
        ml_button_frame.pack(fill=tk.X, padx=20, pady=10)

        # Fonction pour basculer l'état des deux modèles ML
        def toggle_ml_models():
            new_state = not (self.use_rf_var.get() and self.use_xgb_var.get())
            self.use_rf_var.set(new_state)
            self.use_xgb_var.set(new_state)

        # Bouton pour sélectionner/désélectionner les deux modèles ML
        ttk.Button(ml_button_frame, text="Sélectionner/Désélectionner Random Forest et XGBoost",
                  command=toggle_ml_models, style='Big.TButton', width=50).pack(pady=5)

        # Ajouter une description explicative dans un cadre avec bordure
        description_frame = ttk.LabelFrame(self.tab_models, text="Conseils d'optimisation", style='Big.TLabelframe')
        description_frame.pack(fill=tk.X, padx=20, pady=20)

        description_text = """Ces paramètres contrôlent la précision et la vitesse des modèles d'apprentissage automatique.

• Pour une prédiction rapide mais moins précise : Utilisez uniquement le modèle simple, réduisez le nombre d'époques et d'arbres.

• Pour une prédiction précise mais plus lente : Activez tous les modèles, augmentez le nombre d'époques et d'arbres.

• Pour un bon équilibre : Utilisez les modèles simple et profond, désactivez CNN et ensemble, gardez les valeurs recommandées.

Les modèles CNN et ensemble sont les plus lents mais peuvent offrir une meilleure précision pour certains numéros."""

        description_label = ttk.Label(description_frame, text=description_text, wraplength=900,
                                    justify=tk.LEFT, style='Big.TLabel', font=('Helvetica', 11))
        description_label.pack(fill=tk.X, padx=20, pady=15)

        # Boutons de contrôle dans un cadre avec style
        button_frame = ttk.Frame(self.tab_models)
        button_frame.pack(fill=tk.X, padx=20, pady=20)

        # Style pour les boutons
        style = ttk.Style()
        style.configure('Reset.TButton', font=('Helvetica', 12, 'bold'), foreground='#990000')
        style.configure('Validate.TButton', font=('Helvetica', 12, 'bold'), foreground='#006600')

        # Créer un cadre pour les boutons avec disposition horizontale
        buttons_row = ttk.Frame(button_frame)
        buttons_row.pack(fill=tk.X, pady=15)

        # Bouton de réinitialisation
        self.reset_button = ttk.Button(buttons_row, text="Réinitialiser",
                                     command=self._reset_model_params, style='Reset.TButton', width=20)
        self.reset_button.pack(side=tk.LEFT, padx=10)

        # Bouton de validation
        self.validate_button = ttk.Button(buttons_row, text="Valider les paramètres",
                                       command=self._apply_settings, style='Validate.TButton', width=20)
        self.validate_button.pack(side=tk.RIGHT, padx=10)

    def _init_values(self):
        """Initialise les valeurs des widgets"""
        # CPU cores
        current_n_jobs = getattr(self.advanced_analyzer, 'n_jobs', -1)
        if current_n_jobs == -1:
            self.cpu_cores_var.set("-1 (Tous)")
        else:
            self.cpu_cores_var.set(str(current_n_jobs))

        # Nombre maximum de workers
        current_max_workers = getattr(self.advanced_analyzer, 'max_workers', None)
        if current_max_workers is None:
            self.max_workers_var.set("Auto")
        else:
            self.max_workers_var.set(str(current_max_workers))

        # Accélération matérielle
        current_accel = getattr(self.advanced_analyzer, 'hardware_acceleration', 'auto')
        self.accel_mode_var.set(current_accel)

        # Mettre à jour les statuts
        if hasattr(self.advanced_analyzer, 'cuda_available') and self.advanced_analyzer.cuda_available:
            self.gpu_status_var.set("Disponible")
        else:
            self.gpu_status_var.set("Non testé")

        if hasattr(self.advanced_analyzer, 'npu_available') and self.advanced_analyzer.npu_available:
            self.npu_status_var.set("Disponible")
        else:
            self.npu_status_var.set("Non testé")

        self.active_accel_var.set(current_accel.upper())

        # Deep Learning parameters - Charger depuis model_params si disponible
        if hasattr(self.advanced_analyzer, 'model_params') and self.advanced_analyzer.model_params:
            dl_params = self.advanced_analyzer.model_params.get('deep_learning', {})

            # Charger les paramètres numériques
            self.dl_epochs_var.set(str(dl_params.get('epochs', 20)))
            self.dl_batch_size_var.set(str(dl_params.get('batch_size', 32)))
            self.dl_neurons_var.set(str(dl_params.get('neurons', 64)))
            self.dl_dropout_var.set(str(dl_params.get('dropout_rate', 0.2)))

            # Charger les états des cases à cocher pour les modèles
            self.use_dl_simple_var.set(dl_params.get('use_simple', True))
            self.use_dl_deep_var.set(dl_params.get('use_deep', True))
            self.use_dl_cnn_var.set(dl_params.get('use_cnn', True))
            self.use_dl_ensemble_var.set(dl_params.get('use_ensemble', True))
        else:
            # Valeurs par défaut si aucun paramètre n'est disponible
            self.dl_epochs_var.set("20")
            self.dl_batch_size_var.set("32")
            self.dl_neurons_var.set("64")
            self.dl_dropout_var.set("0.2")

            # Valeurs par défaut pour les modèles
            self.use_dl_simple_var.set(True)
            self.use_dl_deep_var.set(True)
            self.use_dl_cnn_var.set(True)
            self.use_dl_ensemble_var.set(True)

        # Random Forest parameters - Charger depuis model_params si disponible
        if hasattr(self.advanced_analyzer, 'model_params') and self.advanced_analyzer.model_params:
            rf_params = self.advanced_analyzer.model_params.get('random_forest', {})
            self.rf_n_estimators_var.set(str(rf_params.get('n_estimators', 300)))
            self.rf_max_depth_var.set(str(rf_params.get('max_depth', 15)))
            # Charger l'état d'activation de Random Forest
            self.use_rf_var.set(rf_params.get('use_model', True))
        else:
            self.rf_n_estimators_var.set("300")
            self.rf_max_depth_var.set("15")
            self.use_rf_var.set(True)

        # XGBoost parameters - Charger depuis model_params si disponible
        if hasattr(self.advanced_analyzer, 'model_params') and self.advanced_analyzer.model_params:
            xgb_params = self.advanced_analyzer.model_params.get('xgboost', {})
            self.xgb_n_estimators_var.set(str(xgb_params.get('n_estimators', 300)))
            self.xgb_max_depth_var.set(str(xgb_params.get('max_depth', 8)))
            self.xgb_learning_rate_var.set(str(xgb_params.get('learning_rate', 0.03)))
            # Charger l'état d'activation de XGBoost
            self.use_xgb_var.set(xgb_params.get('use_model', True))
        else:
            self.xgb_n_estimators_var.set("300")
            self.xgb_max_depth_var.set("8")
            self.xgb_learning_rate_var.set("0.03")
            self.use_xgb_var.set(True)

    def _test_accelerators(self):
        """Teste les accélérateurs matériels disponibles"""
        try:
            # Utiliser la nouvelle méthode pour détecter tous les accélérateurs
            result = self.advanced_analyzer.set_hardware_acceleration('auto')

            # Mettre à jour les variables de statut
            self.cpu_status_var.set("Disponible")

            if result['gpu']:
                self.gpu_status_var.set("Disponible")
            else:
                self.gpu_status_var.set("Non disponible")

            if result['npu']:
                self.npu_status_var.set("Disponible")
            else:
                self.npu_status_var.set("Non disponible")

            # Mettre à jour l'accélérateur actif
            self.active_accel_var.set(result['active'].upper())

            # Afficher un message récapitulatif
            available_accels = [k.upper() for k, v in result.items() if v and k != 'active']
            messagebox.showinfo("Détection des accélérateurs",
                              f"Accélérateurs disponibles: {', '.join(available_accels)}\n\n" +
                              f"Mode actif: {result['active'].upper()}")

        except Exception as e:
            self.gpu_status_var.set("Erreur")
            self.npu_status_var.set("Erreur")
            self.active_accel_var.set("CPU")
            messagebox.showerror("Erreur", f"Erreur lors de la détection des accélérateurs: {e}")

    def _test_gpu(self):
        """Teste si le GPU est disponible (méthode maintenue pour compatibilité)"""
        try:
            # Essayer d'activer le GPU
            if self.advanced_analyzer.set_gpu_usage(True):
                self.gpu_status_var.set("Disponible")
                messagebox.showinfo("Test GPU", "GPU détecté et activé avec succès!")
            else:
                self.gpu_status_var.set("Non disponible")
                messagebox.showwarning("Test GPU", "Aucun GPU compatible n'a été détecté.")
        except Exception as e:
            self.gpu_status_var.set("Erreur")
            messagebox.showerror("Test GPU", f"Erreur lors du test du GPU: {e}")

    def _reset_model_params(self):
        """Réinitialise les paramètres des modèles aux valeurs par défaut"""
        # Deep Learning
        self.dl_epochs_var.set("20")
        self.dl_batch_size_var.set("32")
        self.dl_neurons_var.set("64")
        self.dl_dropout_var.set("0.2")

        # Réinitialiser les préférences des modèles
        self.use_dl_simple_var.set(True)
        self.use_dl_deep_var.set(True)
        self.use_dl_cnn_var.set(True)
        self.use_dl_ensemble_var.set(True)

        # Random Forest
        self.rf_n_estimators_var.set("300")
        self.rf_max_depth_var.set("15")
        self.use_rf_var.set(True)

        # XGBoost
        self.xgb_n_estimators_var.set("300")
        self.xgb_max_depth_var.set("8")
        self.xgb_learning_rate_var.set("0.03")
        self.use_xgb_var.set(True)

        messagebox.showinfo("Réinitialisation", "Les paramètres ont été réinitialisés aux valeurs par défaut.")

    def _apply_settings(self):
        """Applique les paramètres configurés"""
        try:
            # CPU cores
            cpu_cores = self.cpu_cores_var.get()
            if cpu_cores == "-1 (Tous)":
                n_jobs = -1
            else:
                n_jobs = int(cpu_cores)

            self.advanced_analyzer.set_cpu_cores(n_jobs)

            # Nombre maximum de workers
            max_workers = self.max_workers_var.get()
            if max_workers == "Auto":
                self.advanced_analyzer.set_max_workers(None)
            else:
                self.advanced_analyzer.set_max_workers(int(max_workers))

            # Configuration de l'accélération matérielle
            accel_mode = self.accel_mode_var.get()
            self.advanced_analyzer.set_hardware_acceleration(accel_mode)

            # Mettre à jour l'affichage du statut
            self._test_accelerators()

            # Sauvegarder les paramètres des modèles pour une utilisation ultérieure
            # (ces valeurs seront utilisées lors du prochain entraînement)
            self.advanced_analyzer.model_params = {
                'deep_learning': {
                    'epochs': int(self.dl_epochs_var.get()),
                    'batch_size': int(self.dl_batch_size_var.get()),
                    'neurons': int(self.dl_neurons_var.get()),
                    'dropout_rate': float(self.dl_dropout_var.get()),
                    # Ajouter les préférences pour les modèles à utiliser
                    'use_simple': self.use_dl_simple_var.get(),
                    'use_deep': self.use_dl_deep_var.get(),
                    'use_cnn': self.use_dl_cnn_var.get(),
                    'use_ensemble': self.use_dl_ensemble_var.get()
                },
                'random_forest': {
                    'use_model': self.use_rf_var.get(),
                    'n_estimators': int(self.rf_n_estimators_var.get()),
                    'max_depth': int(self.rf_max_depth_var.get())
                },
                'xgboost': {
                    'use_model': self.use_xgb_var.get(),
                    'n_estimators': int(self.xgb_n_estimators_var.get()),
                    'max_depth': int(self.xgb_max_depth_var.get()),
                    'learning_rate': float(self.xgb_learning_rate_var.get())
                }
            }

            # Sauvegarder les paramètres dans un fichier de configuration
            try:
                from keno_settings import save_advanced_settings
                save_advanced_settings(self.advanced_analyzer)
            except Exception as e:
                print(f"Erreur lors de la sauvegarde des paramètres: {e}")

            messagebox.showinfo("Configuration", "Les paramètres ont été appliqués et sauvegardés avec succès!")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'application des paramètres: {e}")
